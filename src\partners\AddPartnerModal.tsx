import { Autocomplete, Checkbox, Stack } from "@mui/material";
import { FormikProvider, useFormik } from "formik";
import { CeButton, CeTextField, MainModal } from "src/common/components";
import { Company } from "src/common/types";
import { CreatePartnerDto, PartnerFormValues } from "src/common/types/partners";
import { turnPartnerFormValuesintoDto } from "src/common/utils/partner";
import * as yup from "yup";

interface AddPartnerModalTypes {
  isModalOpen: boolean;
  handleClosePartnerModal: () => void;
  initialFormValues: PartnerFormValues;
  onCreatenNewPartners: (operatorCompanyIds: CreatePartnerDto) => void;
  operatorCompanies: Company[];
}

const AddPartnerModal = ({
  isModalOpen,
  handleClosePartnerModal,
  onCreatenNewPartners,
  initialFormValues,
  operatorCompanies,
}: AddPartnerModalTypes) => {
  const formik = useFormik({
    initialValues: initialFormValues,
    enableReinitialize: true,
    validationSchema: yup.object({
      operatorCompanies: yup
        .array()
        .of(
          yup.object({
            id: yup.number().required(),
          })
        )
        .min(1, "At least one partner is required")
        .required("Partner is required"),
    }),
    onSubmit: (values) => {
      const payload = turnPartnerFormValuesintoDto(values.operatorCompanies);
      onCreatenNewPartners(payload);
    },
  });
  return (
    <MainModal
      title="Add Partner/s"
      isOpen={isModalOpen}
      handleClose={handleClosePartnerModal}
    >
      <FormikProvider value={formik}>
        <Stack component="form" onSubmit={formik.handleSubmit} rowGap={4}>
          <Autocomplete
            multiple
            limitTags={2}
            id="partner"
            options={operatorCompanies}
            value={formik.values.operatorCompanies}
            onChange={(_, newValue: Company[]) => {
              formik.setFieldValue("operatorCompanies", newValue);
            }}
            getOptionLabel={(option: Company) => option.name || ""}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            disableCloseOnSelect
            renderOption={(props, option, { selected }) => (
              <li {...props}>
                <Checkbox style={{ marginRight: 8 }} checked={selected} />
                {option.name}
              </li>
            )}
            ListboxProps={{
              style: {
                maxHeight: "25vh",
              },
            }}
            renderInput={(params) => (
              <CeTextField
                {...params}
                fullWidth
                label="Select new partner(s)"
                error={
                  formik.touched.operatorCompanies &&
                  Boolean(formik.errors.operatorCompanies)
                }
                helperText={
                  formik.touched.operatorCompanies &&
                  formik.errors.operatorCompanies
                }
                size="small"
              />
            )}
            sx={{ width: 400 }}
          />

          <Stack
            direction="row"
            alignItems="center"
            justifyContent="flex-end"
            gap={1}
          >
            <CeButton
              onClick={handleClosePartnerModal}
              size="medium"
              variant="text"
            >
              Cancel
            </CeButton>
            <CeButton type="submit" size="medium">
              Add Partner/s
            </CeButton>
          </Stack>
        </Stack>
      </FormikProvider>
    </MainModal>
  );
};

export default AddPartnerModal;
