import { MainModal } from "src/common/components";
import { ReservationFormValues } from "src/common/types";
import { ReservationForm } from "./ReservationForm";

interface ReservationModalProps {
  isLoading: boolean;
  initialFormValues: ReservationFormValues;
  handleCloseReservationModal: () => void;
  handleSubmitReservationModal: (values: ReservationFormValues) => void;
}
export const ReservationModal: React.FC<ReservationModalProps> = ({
  isLoading,
  initialFormValues,
  handleCloseReservationModal,
  handleSubmitReservationModal,
}) => {
  return (
    <MainModal
      title={`${initialFormValues.flow || ""} Reservation`}
      isOpen={!!initialFormValues.flow}
      handleClose={handleCloseReservationModal}
    >
      <ReservationForm
        initialFormValues={initialFormValues}
        isLoading={isLoading}
        handleSubmit={handleSubmitReservationModal}
        handleClose={handleCloseReservationModal}
      />
    </MainModal>
  );
};
