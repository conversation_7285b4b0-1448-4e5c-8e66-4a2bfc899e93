import {
  DataGrid,
  GridColDef,
  GridFilterModel,
  GridRenderCellParams,
  GridSortModel,
} from "@mui/x-data-grid";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { CeDataGridToolbar } from "src/common/components/custom/company/CeDataGridToolbar";
import {
  CreatePartnerDto,
  Partner,
  PartnerFormValues,
} from "src/common/types/partners";
import {
  GridStateSnapshot,
  UpdateGridStatePart,
} from "src/common/utils/gridState";
import AddPartnerModal from "../AddPartnerModal";
import { Company } from "src/common/types";
import { IconButton, Stack } from "@mui/material";
import { Delete01Icon, Edit02Icon } from "@hugeicons/react";
import DeletePartnerModal from "../DeletePartnerModal";

interface PartnersDataGridTypes {
  data: Partner[];
  shouldRenderAddButton?: boolean;
  shouldRenderRefreshButton?: boolean;
  isFetchingPartners: boolean;
  refetchPartners: () => void;
  shouldRenderEditActionsColumn: boolean;
  page: number;
  pageSize: number;
  total: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
  gridState: GridStateSnapshot;
  updateGridStatePart: UpdateGridStatePart;
  isServerDriven: boolean;
  handleSortModelChange?: (sortModel: GridSortModel) => void;
  onDatagridFiltersChange?: (datagridFilter: GridFilterModel) => void;
  onCreatenNewPartners: (operatorCompanyIds: CreatePartnerDto) => void;
  operatorCompanies: Company[];
  setPartnersFormsState: (partnerFormValues: PartnerFormValues) => void;
  partnersFormsState: PartnerFormValues;
  onDeletePartner: () => void;
  closePartnersActionModal: () => void;
}

const PartnersDatagrid = ({
  data,
  shouldRenderAddButton,
  shouldRenderRefreshButton,
  refetchPartners,
  page,
  pageSize,
  total,
  onPageChange,
  onPageSizeChange,
  gridState,
  updateGridStatePart,
  isServerDriven,
  handleSortModelChange,
  onDatagridFiltersChange,
  onCreatenNewPartners,
  operatorCompanies,
  onDeletePartner,
  setPartnersFormsState,
  partnersFormsState,
  closePartnersActionModal,
}: PartnersDataGridTypes) => {
  const { t } = useTranslation(["dispatcher", "common"]);

  const renderEditActions = (params: GridRenderCellParams) => {
    const partner: Partner = params.row;

    return (
      <Stack
        direction="row"
        justifyContent="center"
        alignItems="center"
        spacing={1}
      >
        <IconButton aria-label="update partner" disabled={true} size="small">
          <Edit02Icon size={16} variant={"stroke"} />
        </IconButton>

        <IconButton
          aria-label="delete partner"
          color="error"
          size="small"
          onClick={(event) => {
            event.preventDefault();
            event.stopPropagation();
            setPartnersFormsState({
              ...partnersFormsState,
              partnerId: partner.id,
              flow: "Delete",
            });
          }}
        >
          <Delete01Icon size={16} variant={"stroke"} />
        </IconButton>
      </Stack>
    );
  };

  const columns: GridColDef[] = [
    {
      field: "editActions",
      headerName: t("common:actions"),
      sortable: false,
      width: 150,
      renderCell: (params: GridRenderCellParams<string>) => {
        return renderEditActions(params);
      },
      filterable: false,
    },
    {
      field: "partner.id",
      headerName: "Id",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 50,
      renderCell: (params) => params.row.id,
    },

    {
      field: "operatorCompany.name",
      headerName: t("common:company-name"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 180,
      renderCell: (params) => params.row.operatorCompany.name,
    },

    {
      field: "operatorCompany.address",
      headerName: t("common:company-address"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 180,
      renderCell: (params) => params.row.operatorCompany.address,
    },

    {
      field: "operatorCompany.vatNumber",
      headerName: t("common:company-vat-number"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 180,

      renderCell: (params) => params.row.operatorCompany.vatNumber,
    },

    {
      field: "operatorCompany.contactEmail",
      headerName: t("common:email-address"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 180,
      renderCell: (params) => params.row.operatorCompany.contactEmail,
    },

    {
      field: "container.title",
      headerName: t("common:pricelist"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 180,
      renderCell: (params) => params.row.container.title || "-",
    },
  ];

  const onFilterChange = useCallback(
    (filterModel: GridFilterModel) => {
      if (onDatagridFiltersChange) {
        onDatagridFiltersChange(filterModel);
        updateGridStatePart("filterModel", filterModel);
      }
    },
    [onDatagridFiltersChange]
  );

  const onSortChange = useCallback(
    (sortModel: GridSortModel) => {
      if (handleSortModelChange) {
        handleSortModelChange(sortModel);
        updateGridStatePart("sortModel", sortModel);
      }
    },
    [handleSortModelChange]
  );

  return (
    <>
      <DataGrid
        sx={{
          border: "none",
          paddingTop: 0.5,
        }}
        pagination
        page={page - 1}
        pageSize={pageSize}
        rowsPerPageOptions={[20, 40, 60, 80, 100]}
        rowCount={total}
        onPageChange={(newPage) => {
          onPageChange(newPage);
        }}
        onPageSizeChange={(newPageSize) => {
          onPageSizeChange(newPageSize);
        }}
        columnVisibilityModel={gridState.columnVisibilityModel}
        onColumnVisibilityModelChange={(model) =>
          updateGridStatePart("columnVisibilityModel", model)
        }
        paginationMode="server"
        editMode="row"
        experimentalFeatures={{ newEditingApi: true }}
        columns={columns}
        rows={data}
        disableSelectionOnClick
        filterModel={gridState.filterModel}
        sortingMode={isServerDriven ? "server" : "client"}
        onSortModelChange={isServerDriven ? onSortChange : undefined}
        filterMode={isServerDriven ? "server" : "client"}
        onFilterModelChange={isServerDriven ? onFilterChange : undefined}
        sortModel={gridState.sortModel}
        components={{
          Toolbar: () => (
            <CeDataGridToolbar
              shouldRenderAddButton={shouldRenderAddButton}
              shouldRenderRefreshButton={shouldRenderRefreshButton}
              onRefreshButtonClick={refetchPartners}
              addButtonDescription="Add Partner/s"
              addButtonClickHandler={() =>
                setPartnersFormsState({
                  ...partnersFormsState,
                  flow: "Add",
                })
              }
            />
          ),
        }}
      />
      <AddPartnerModal
        isModalOpen={partnersFormsState.flow === "Add"}
        handleClosePartnerModal={closePartnersActionModal}
        initialFormValues={partnersFormsState}
        operatorCompanies={operatorCompanies}
        onCreatenNewPartners={onCreatenNewPartners}
      />
      <DeletePartnerModal
        isModalOpen={partnersFormsState.flow === "Delete"}
        handleCloseModal={closePartnersActionModal}
        onDeleteClick={onDeletePartner}
      />
      ;
    </>
  );
};

export default PartnersDatagrid;
