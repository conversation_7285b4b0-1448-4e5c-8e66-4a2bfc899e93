import { Alert, Box, Divider, Typography } from "@mui/material";
import { DeleteModal } from "src/common/components";
import {
  DeleteDispatcherDto,
  DispatcherFormValues,
  DispatcherModalDeleteFlow,
} from "src/common/types";
import { useTranslation } from "react-i18next";

interface DispatcherModalDeleteProps {
  flow: DispatcherModalDeleteFlow;
  dispatcherTitle?: string;
  isLoading: boolean;
  dispatcherId?: number;
  handleCloseDispatcherModalDelete: () => void;
  handleDeleteDispatcher: (args: DeleteDispatcherDto) => void;
}
export const DispatcherModalDelete: React.FC<DispatcherModalDeleteProps> = ({
  flow,
  dispatcherTitle,
  dispatcherId,
  isLoading,
  handleCloseDispatcherModalDelete,
  handleDeleteDispatcher,
}) => {
  const { t } = useTranslation(["common", "dispatcher"]);

  const onDeleteDispatcher = () => {
    if (dispatcherId) {
      handleDeleteDispatcher({ dispatcherId });
    }
  };

  const DeleteModalHelperText = () => (
    <Box>
      <Typography sx={{ marginBottom: 1 }}>
        {t("delete-dispatcher-message", {
          dispatcherTitle: dispatcherTitle || "unknown dispatcher",
        })}
      </Typography>
    </Box>
  );

  return (
    <DeleteModal
      isLoading={isLoading}
      title={t("dispatcher:delete-dispatcher")}
      helperText={DeleteModalHelperText()}
      isOpen={flow === "Delete"}
      handleSubmit={() => onDeleteDispatcher()}
      handleClose={handleCloseDispatcherModalDelete}
    />
  );
};
