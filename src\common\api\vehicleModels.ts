import axios, { AxiosError } from "axios";
import {
  CreateVehicleModelDto,
  DeleteVehicleModelDto,
  getVehicleModelsDto,
  UpdateVehicleModelDto,
  VehicleModels,
  VehicleModelsWithCount,
} from "../types/vehicleModels";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { processApiError } from "../utils/errors";
import { wrapEachFieldIntoDoubleQuotes } from "../utils";

const backendUrl = process.env.REACT_APP_API_URL;

export const getVehicleModels = async (attr: getVehicleModelsDto) => {
  return axios
    .post(`${backendUrl}/vehicle-size/get`, attr, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useVehicleModels = (
  attrs: getVehicleModelsDto,
  enabled: boolean = true
) => {
  const formattedSortModel = wrapEachFieldIntoDoubleQuotes(attrs.sortModel);
  const formattedAttrs = { ...attrs, sortModel: formattedSortModel };
  return useQuery<VehicleModelsWithCount, AxiosError | Error>(
    ["vehicleModels", formattedAttrs],
    () => getVehicleModels(formattedAttrs),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch vehicle models", err),
      enabled,
    }
  );
};

export const getVehicleModelById = async (vehicleModelId: number | null) => {
  if (!vehicleModelId) {
    throw new Error("the vehicleModelId ID was not provided");
  }
  return axios
    .get(`${backendUrl}/vehicle-size/${vehicleModelId}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useVehicleModel = (
  vehicleId: number | null,
  enabled: boolean = true
) => {
  return useQuery<VehicleModels, AxiosError | Error>(
    ["vehicleModel", vehicleId],
    () => getVehicleModelById(vehicleId),
    {
      onError: (err) =>
        processApiError("Unable to fetch vehicle model by id", err),
      enabled,
    }
  );
};

export const createNewVehicleModel = (attrs: CreateVehicleModelDto) => {
  return axios
    .post(`${backendUrl}/vehicle-size/`, attrs, { withCredentials: true })
    .then((response) => response.data);
};
export const useCreateNewVehicleModel = () => {
  const queryClient = useQueryClient();
  return useMutation<
    VehicleModels,
    AxiosError | Error,
    CreateVehicleModelDto,
    () => void
  >((a: CreateVehicleModelDto) => createNewVehicleModel(a), {
    onSuccess: (newVehicle) => {
      queryClient.invalidateQueries("vehicleModel");
      queryClient.invalidateQueries("vehicleModels");
    },
    onError: (err) => processApiError("Unable to create vehicle model", err),
  });
};

export const handleUpdateVehicleModel = (
  updateVehicleArgs: UpdateVehicleModelDto
) => {
  const { vehicleModelId, ...vehicle } = updateVehicleArgs;
  if (!vehicleModelId) {
    throw new Error("the vehicle model ID was not provided");
  }
  return axios
    .patch(`${backendUrl}/vehicle-size/${vehicleModelId}`, vehicle, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const useUpdateVehicleModel = () => {
  const queryClient = useQueryClient();
  return useMutation<
    VehicleModels,
    AxiosError | Error,
    UpdateVehicleModelDto,
    () => void
  >(
    (updateVehicleModelArgs: UpdateVehicleModelDto) =>
      handleUpdateVehicleModel(updateVehicleModelArgs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("vehicleModel");
        queryClient.invalidateQueries("vehicleModels");
      },
      onError: (err) => {
        processApiError("Unable to update vehicle model", err);
      },
    }
  );
};

export const deleteVehicleModel = (deleteVehicleDto: DeleteVehicleModelDto) => {
  const { vehicleModelId } = deleteVehicleDto;
  if (!vehicleModelId) {
    throw new Error("the vehicle model ID was not provided");
  }
  return axios
    .delete(`${backendUrl}/vehicle-size/${vehicleModelId}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const useDeleteVehicleModel = () => {
  const queryClient = useQueryClient();
  return useMutation<
    VehicleModels,
    AxiosError | Error,
    DeleteVehicleModelDto,
    () => void
  >(
    (deleteVehicleModelDto: DeleteVehicleModelDto) =>
      deleteVehicleModel(deleteVehicleModelDto),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("vehicleModel");
        queryClient.invalidateQueries("vehicleModels");
      },
      onError: (err) => processApiError("Unable to delete vehicle model", err),
    }
  );
};
