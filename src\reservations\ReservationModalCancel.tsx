import {
  Box,
  Button,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography
} from "@mui/material";
import { CeDialog } from "src/common/components";
import { buttonTextTransform } from "src/common/components/custom/customCss";
import { ReservationModalCancelFlow } from "src/common/types";

interface ReservationModalCancelProps {
  flow: ReservationModalCancelFlow;
  reservationTitle?: string;
  isLoading: boolean;
  handleCloseReservationModalCancel: () => void;
  handleCancelReservation: () => void;
}
export const ReservationModalCancel: React.FC<ReservationModalCancelProps> = ({
  flow,
  reservationTitle,
  isLoading,
  handleCloseReservationModalCancel,
  handleCancelReservation
}) => {
  return (
    <CeDialog
      open={flow === "Cancel"}
      onClose={handleCloseReservationModalCancel}
      maxWidth="md"
      aria-labelledby={`main-dialog-${reservationTitle}`}
    >
      <DialogTitle>{reservationTitle}</DialogTitle>
      <DialogContent dividers>
        <DialogContentText
          sx={{ marginBottom: 2, marginTop: 0, width: "400px" }}
        >
          <Box>
            <Typography sx={{ marginBottom: 1 }}>
              Cancel {reservationTitle || "unknown Reservation"} ?
            </Typography>
          </Box>
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => handleCloseReservationModalCancel()}
          disabled={isLoading}
          sx={buttonTextTransform}
        >
          Cancel
        </Button>
        <Button
          sx={buttonTextTransform}
          onClick={() => handleCancelReservation()}
          disabled={isLoading}
        >
          Submit
        </Button>
      </DialogActions>
    </CeDialog>
  );
};
