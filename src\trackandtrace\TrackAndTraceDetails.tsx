import { Box, Container, Divider, Paper, Typography } from '@mui/material';
import React, { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { rmTrackTraceTokenToStorage, useTrackAndTraceDetails } from 'src/common/api';
import { CePaper, PullToRefresh } from 'src/common/components';
import OrderInformation from './components/OrderInformation';
import LanguageSwitcher from 'src/common/components/languageSwitcher/LanguageSwitcher';

import TimelineItems from './components/TimelineItem';
import OrderInformationSkeleton from './components/loaderScreen/OrderInfoSkeletion';
import TimelineSkeleton from './components/loaderScreen/TimelineSkeleton';
import CharteredEquipment from './components/CharteredEquipment';
import { JobDetails } from './components/JobDetails';
import Media from 'src/common/components/reservationDetails/Media';
import GoogleMapComponent from 'src/common/components/reservationDetails/GoogleMapComponent';

export const TrackAndTraceDetails = () => {
    const { orderNumber } = useParams<{ orderNumber: string }>();
    const navigate = useNavigate();

    const {
        data: trackDetails,
        isError,
        isLoading,
        refetch: refetchTrackDetails,
    } = useTrackAndTraceDetails(
        orderNumber!,
        Boolean(orderNumber)
    );

    useEffect(() => {
        if (isError) {
            rmTrackTraceTokenToStorage();
            navigate("/track-and-trace", { replace: true });
        }
    }, [isError, navigate]);

    const orderInfoProps = {
        created: trackDetails?.reservation.dateFrom!,
        expected: trackDetails?.reservation.dateTo || '',
        status: trackDetails?.job.status || '',
        orderNumber: orderNumber || '',
        dispatcherPhoneNumber: trackDetails?.reservation.dispatcher?.phoneNumber || '-',
        operatorPhoneNumber: trackDetails?.reservation.operator?.phoneNumber || '-',
    };

    return (
        <PullToRefresh onRefresh={async () => { await refetchTrackDetails(); }}>
            <Container sx={{ py: 4 }}>
                <Box
                    display="flex"
                    justifyContent="space-between"
                    alignItems="center"
                    mb={4}
                    flexDirection={{ xs: 'column', sm: 'row' }}
                    width="100%"
                    sx={{ gap: { xs: 2, sm: 0 } }}
                >
                    <Typography
                        variant="h3"
                        component="h1"
                        align="center"
                        gutterBottom
                        sx={{
                            fontWeight: 'bold',
                            fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                            flex: 1,
                            textAlign: 'center',
                            m: 0
                        }}
                    >
                        {trackDetails?.company.name}
                    </Typography>
                    <LanguageSwitcher fullWidth={false} />
                </Box>
                <Box
                    display="flex"
                    flexDirection="column"
                    gap={2}
                    sx={{
                        flex: 1,
                        overflow: "auto",
                        paddingRight: 1
                    }}
                >
                    <Box flex={2}>
                        <CePaper
                            sx={{
                                padding: 3,
                                marginBottom: 2,
                            }}
                        >
                            {!isLoading ? <OrderInformation orderInfoProps={orderInfoProps} /> : <OrderInformationSkeleton />}
                        </CePaper>
                        <CePaper
                            sx={{
                                padding: 3,
                                marginBottom: 2,
                            }}
                        >
                            <GoogleMapComponent reservationLocation={trackDetails?.reservation.location!} events={trackDetails?.job.jobLocations!}/>                
                        </CePaper>
                        <Box sx={{
                            display: 'flex',
                            flexDirection: {
                                xs: 'column', 
                                md: 'row',   
                            },
                            gap: 2, 
                        }}
                        >
                            <CePaper
                                sx={{
                                    paddingX: 3,
                                    marginBottom: 2,
                                    width:{
                                        xs: '100%', 
                                        md: '40%', 
                                    }
                                }}
                            >

                                {!isLoading ? <TimelineItems
                                    jobDetails={trackDetails?.job!}
                                /> : <TimelineSkeleton />
                                }
                            </CePaper>
                            <CePaper
                                sx={{
                                    paddingY:2,
                                    marginBottom: 2,
                                }}
                            >
                                <CharteredEquipment reservation={trackDetails?.reservation} />
                                <Divider sx={{ marginBottom: 2 }} />
                                <JobDetails jobDetails={trackDetails?.job} orderNumber={orderNumber!} />
                                <Divider sx={{ marginBottom: 2 }} />
                                <Box sx={{padding: 3, paddingTop: 1}}>
                                     <Media job={trackDetails?.job}/> 
                                </Box>
                            </CePaper>
                        </Box>
                    </Box>
                </Box>
            </Container>
        </PullToRefresh>
    );
};
