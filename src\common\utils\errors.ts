import toast from 'react-hot-toast'
import { AxiosError } from 'axios'
import { green } from '@mui/material/colors'

export const formatErrorMessages = (messages: string[] | string) => {
  if (Array.isArray(messages)) {
    return messages.join(', ')
  }
  if (typeof messages === 'string' && messages.length) {
    return messages
  }
  return 'Unknown error occured'
}

export const getApiError = (msg: string, err: Error | AxiosError<any, any>) => {
  let error = ''
  // @ts-ignore
  if (err.response) {
    // @ts-ignore
    error = err.response.data.message
  } else {
    error = err.message
  }
  const errorMessage = formatErrorMessages(error)
  return errorMessage
}

export const processApiError = (msg: string, err: Error | AxiosError<any, any>) => {
  const errorMessage = getApiError(msg, err)
  toast.error(`${msg}: ${errorMessage}`, { duration: 10000 })
}

export const processApiSuccess = (msg: string) => {
  toast.success(msg, { duration: 10000 })
}

export const nameAlreadyExistsError = (msg: string) => {
  toast.error(msg, { duration: 10000 })
}

export const processApiWarning = (msg: string) => {
  toast.success(msg, {
    style: {
      border: '1px solid #F75B01',
      padding: '16px',
      color: '#F75B01',
    },
    iconTheme: {
      primary: '#F75B01',
      secondary: '#FFFAEE',
    },
    duration: 10000,
  })
}

export const processApiInfo = (msg: string) => {
  toast.success(msg, {
    style: {
      border: '1px solid #1976D2',
      padding: '16px',
      color: '#1976D2',
    },
    iconTheme: {
      primary: '#1976D2',
      secondary: '#FFFAEE',
    },
    duration: 5000,
  })
}
