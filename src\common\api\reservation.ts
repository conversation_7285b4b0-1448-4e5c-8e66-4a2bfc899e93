import { useMutation, useQuery, useQueryClient } from "react-query";
import { processApiError, processApiSuccess } from "../utils/errors";
import axios, { AxiosError } from "axios";
import {
  DeleteReservationDto,
  Reservation,
  ReservationWithCount,
  UpdateReservationDto,
  getReservationDto,
} from "../types";
import { wrapEachFieldIntoDoubleQuotes } from "../utils";

const backendUrl = process.env.REACT_APP_API_URL;

// GET ONE reservation
export const getReservation = async (reservationId: number) => {
  if (!reservationId) {
    throw new Error("the reservationId ID was not provided");
  }
  return axios
    .get(`${backendUrl}/reservation/${reservationId}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const useReservation = (
  reservationId: number,
  enabled: boolean = true
) => {
  return useQuery<Reservation, AxiosError | Error>(
    ["reservation", reservationId],
    () => getReservation(reservationId),
    {
      onError: (err) => processApiError("Unable to fetch reservation", err),
      enabled,
    }
  );
};

// GET MANY reservations
export const getReservations = async (attr: getReservationDto) => {
  return axios
    .post(`${backendUrl}/reservation/get`, attr, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const useReservations = (
  attrs: getReservationDto,
  enabled: boolean = true
) => {
  const formattedSortModel = wrapEachFieldIntoDoubleQuotes(attrs.sortModel);
  const formattedExpressions = attrs.expressions?.map((expression) => {
    if (expression.category === '"clientDetailsCompanyName"') {
      return {
        ...expression,
        category: "clientDetailsCompanyName",
      };
    }
    return expression;
  });
  const formattedAttrs = {
    ...attrs,
    sortModel: formattedSortModel,
    expressions: formattedExpressions,
  };
  return useQuery<ReservationWithCount, AxiosError | Error>(
    ["reservations", formattedAttrs],
    () => getReservations(formattedAttrs),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch reservations", err),
      enabled,
    }
  );
};

// CREATE NEW reservation
export const createNewReservation = (attrs: FormData) => {
  return axios
    .post(`${backendUrl}/reservation/`, attrs, {
      withCredentials: true,
      headers: {
        "Content-type": "multipart/form-data",
      },
    })
    .then((response) => response.data);
};

export const useCreateNewReservation = () => {
  const queryClient = useQueryClient();
  return useMutation<Reservation, AxiosError | Error, FormData, () => void>(
    (a: FormData) => createNewReservation(a),
    {
      onSuccess: (newreservation) => {
        queryClient.invalidateQueries("reservation");
        queryClient.invalidateQueries("reservations");
      },
      onError: (err) => processApiError("Unable to create reservation", err),
    }
  );
};

// UPDATE reservation BY ID
export const handleUpdateReservation = (args: {
  reservationId: number;
  updateReservationArgs: FormData;
}) => {
  if (!args.reservationId) {
    throw new Error("the reservation ID was not provided");
  }
  return axios
    .patch(
      `${backendUrl}/reservation/${args.reservationId}`,
      args.updateReservationArgs,
      {
        withCredentials: true,
        headers: {
          "Content-type": "multipart/form-data",
        },
      }
    )
    .then((response) => response.data);
};

export const useUpdateReservation = () => {
  const queryClient = useQueryClient();

  return useMutation<
    Reservation,
    AxiosError | Error,
    { reservationId: number; updateReservationArgs: FormData },
    void
  >((args) => handleUpdateReservation(args), {
    onSuccess: () => {
      queryClient.invalidateQueries("reservation");
      queryClient.invalidateQueries("reservations");
      queryClient.invalidateQueries("vehiclesWithReservations");
    },
    onError: (err) => {
      processApiError("Unable to update reservation", err);
    },
  });
};

// DELETE reservation BY ID
export const deleteReservation = (
  deleteReservationDto: DeleteReservationDto
) => {
  const { reservationId } = deleteReservationDto;
  if (!reservationId) {
    throw new Error("the reservation ID was not provided");
  }
  return axios
    .delete(`${backendUrl}/reservation/${reservationId}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useDeleteReservation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    Reservation,
    AxiosError | Error,
    DeleteReservationDto,
    () => void
  >(
    (deletereservationDto: DeleteReservationDto) =>
      deleteReservation(deletereservationDto),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("reservation");
        queryClient.invalidateQueries("reservations");
      },
      onError: (err) => processApiError("Unable to delete reservation", err),
    }
  );
};

export const markAsRead = (reservationId: number) => {
  if (!reservationId) {
    throw new Error("reservation ID was not provided");
  }
  return axios
    .post(
      `${backendUrl}/reservation/${reservationId}/markAsRead`,
      {},
      {
        withCredentials: true,
      }
    )
    .then((response) => response.data);
};

export const useMarkAsRead = () => {
  const queryClient = useQueryClient();

  return useMutation<
    Reservation,
    AxiosError | Error,
    { reservationId: number }
  >(({ reservationId }) => markAsRead(reservationId), {
    onSuccess: () => {
      queryClient.invalidateQueries("reservations");
    },
    onError: (err) => processApiError("Unable to mark as read", err),
  });
};

export const getFile = async (fileKey: string | null) => {
  const encodedKey = encodeURIComponent(fileKey!);
  return axios
    .get(`${backendUrl}/file/${encodedKey}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useFile = (fileKey: string | null, enabled: boolean = true) => {
  return useQuery<string, AxiosError | Error>(
    ["file", fileKey],
    () => getFile(fileKey),
    {
      onError: (err) => processApiError("Unable to fetch file", err),
      enabled,
      refetchInterval: 15 * 60 * 1000,
      refetchIntervalInBackground: true,
    }
  );
};

export const deleteFile = async (fileKey: string) => {
  const encodedKey = encodeURIComponent(fileKey!);
  return axios
    .delete(`${backendUrl}/file/${encodedKey}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useDeleteFile = () => {
  return useMutation<string, AxiosError | Error, string, () => void>(
    (fileKey: string) => deleteFile(fileKey),
    {
      onSuccess: () => {
        processApiSuccess("File deleted successfully!");
      },
      onError: (err: AxiosError | Error) => {
        console.log((err as AxiosError)?.response?.status);

        if ((err as AxiosError)?.response?.status === 204) {
          processApiSuccess("File deleted successfully!");
        } else {
          processApiError("Unable to delete file", err);
        }
      },
    }
  );
};

export const getReservationCost = async (reservationId?: number) => {
  if (!reservationId) {
    throw new Error("Reservation ID was not provided");
  }
  return axios
    .get(`${backendUrl}/reservation/${reservationId}/cost`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useReservationCost = (
  reservationId?: number,
  enabled = true
) => {
  return useQuery<any, AxiosError | Error>(
    ["reservationCost", reservationId],
    () => getReservationCost(reservationId),
    {
      onError: (err) =>
        processApiError("Unable to fetch reservation cost", err),
      enabled,
    }
  );
};

