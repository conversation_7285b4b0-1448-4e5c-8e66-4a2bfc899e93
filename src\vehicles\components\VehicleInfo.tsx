import React from 'react';
import { Box, Stack, Typography, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

export interface VehicleInfoField {
    label: string;
    value: React.ReactNode;
}

interface VehicleInfoProps {
    fields: VehicleInfoField[];
}

const VehicleInfo: React.FC<VehicleInfoProps> = ({ fields }) => {
    const theme = useTheme();
    const { t } = useTranslation("common");
    return (
        <Stack sx={{ mx: 2,}}>
            {fields.map((field) => (
                <Box
                    key={field.label}
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        backgroundColor: theme.palette.grey[50],
                        borderRadius: theme.shape.borderRadius,
                        padding: 2,
                        mb: 2
                    }}
                >
                    <Typography variant="subtitle2" color="text.secondary">
                        {field.label}
                    </Typography>
                    <Typography variant="subtitle2" fontWeight={500} sx={{display:'flex', alignItems:'center'}}>
                        {field.value}
                    </Typography>
                </Box>
            ))}
        </Stack>
    )

}

export default VehicleInfo;