import { Box, Stack, Typography } from "@mui/material";
import { Vehicle } from "src/common/types";

interface VehicleImageProps {
  vehicle?: Vehicle;
}

const typographyStyle = {
  fontWeight: "bold",
  fontVariant: "body2",
  position: "absolute",
  textAlign: "center",
};

const VehicleImageStack = ({ vehicle }: VehicleImageProps) => {
  return (
    <Box m={2}>
      <Stack direction="row" sx={{ justifyContent: "space-between" }}>
        <Box position="relative">
          <Typography
            sx={{
              ...typographyStyle,
              top: "48%",
              transform: "translateY(-50%)",
              left: "13px",
            }}
          >
            (A)
            <br />
            {vehicle?.minUnfoldingHeight
              ? vehicle.minUnfoldingHeight + "m"
              : "-"}
          </Typography>
          <img
            src="/images/imageOne.png"
            width={420}
            height="auto"
            alt="Unfolding Height"
          />
        </Box>
        <Box position="relative">
          <Typography
            sx={{
              ...typographyStyle,
              left: "160px",
              top: "200px",
            }}
          >
            (B) {vehicle?.length ? vehicle.length + "m" : "-"}
          </Typography>
          <img
            src="/images/imageTwo.png"
            width={420}
            height="auto"
            alt="Vehicle height and length"
          />
          <Typography
            sx={{
              ...typographyStyle,
              right: "3px",
              top: "80px",
            }}
          >
            (C) <br /> {vehicle?.height ? vehicle.height + "m" : "-"}
          </Typography>
        </Box>
        <Box position="relative">
          <Typography
            sx={{
              ...typographyStyle,
              left: "9px",
              top: "48%",
              transform: "translateY(-50%)",
            }}
          >
            (D)
            <br />
            {vehicle?.frontOutriggerSpan
              ? vehicle?.frontOutriggerSpan + "m"
              : "-"}
          </Typography>
          <Typography
            sx={{
              ...typographyStyle,
              left: "60px",
              top: "46%",
              transform: "translateY(-100%)",
              fontSize: "14px",
            }}
          >
            (F)
            <br />
            {vehicle?.frontSideOpening ? vehicle?.frontSideOpening + "m" : "-"}
          </Typography>
          <img
            src="/images/imageThree.png"
            width={420}
            height="auto"
            alt="Outrigger Span"
          />
          <Typography
            sx={{
              ...typographyStyle,
              right: "7px",
              top: "48%",
              transform: "translateY(-50%)",
            }}
          >
            (E) <br />
            {vehicle?.rearOutriggerSpan ? vehicle.rearOutriggerSpan + "m" : "-"}
          </Typography>
          <Typography
            sx={{
              ...typographyStyle,
              top: "46%",
              transform: "translateY(-100%)",
              right: "59px",
              fontSize: "14px",
            }}
          >
            (G)
            <br />
            {vehicle?.rearSideOpening ? vehicle.rearSideOpening + "m" : "-"}
          </Typography>
        </Box>
      </Stack>
    </Box>
  );
};

export default VehicleImageStack;
