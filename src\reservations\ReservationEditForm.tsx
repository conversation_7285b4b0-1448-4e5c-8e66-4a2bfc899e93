import { DialogActions, Stack } from "@mui/material";
import { isBefore } from "date-fns";
import enGb from "date-fns/locale/en-GB";

import { LocalizationProvider, DateTimePicker } from "@mui/x-date-pickers";

import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import * as yup from "yup";
import { useTranslation } from "react-i18next";

import { FormikProvider, useFormik } from "formik";
import { ReservationFormValues } from "src/common/types";
import { Calendar04Icon } from "@hugeicons/react";
import { CeTextField, CeButton } from "src/common/components";
import AutocompleteInput from "src/common/components/custom/AutocompleteGoogleInput";
import { useCheckVehicleSlotAvailability } from "src/common/api";
import toast from "react-hot-toast";
interface ReservationFormProps {
  isLoading: boolean;
  initialFormValues: ReservationFormValues;
  handleClose: () => void;
  handleSubmit: (values: ReservationFormValues) => void;
  fullUpdate?: boolean;
}

export const ReservationEditForm: React.FC<ReservationFormProps> = ({
  handleClose,
  handleSubmit,
  isLoading,
  initialFormValues,
  fullUpdate,
}) => {
  const { t } = useTranslation(["common", "manager", "dispatcher"]);

   const {
      mutateAsync: handleCheckVehicleAvailabilityAsync,
      isLoading: isCheckVehicleAvailabilityLoading,
    } = useCheckVehicleSlotAvailability();

  const formik = useFormik<ReservationFormValues>({
    initialValues: initialFormValues,
    validationSchema: yup.object({
      siteAddress: yup
        .string()
        .required("Address is required")
        .matches(/\d+/, "Street number is required"),
      city: yup.string(),
      plz: yup
        .number(),
      location: yup.object().nullable(),
      dateFrom: yup
        .date()
        .required("required")
        .typeError("invalid-date")
        .test(
          "isDateFromInPast",
          `${t("common:cannot-select-past-date-from")}`,
          function (value) {
            if (!value) {
              return false;
            }
            const currentTime = new Date();
            const fiveMinutesAgo = new Date(
              currentTime.getTime() - 5 * 60 * 1000
            );
            return !isBefore(value, fiveMinutesAgo);
          }
        )
        .test("dateFrom", `${t("common:date-from-error")}`, function (value) {
          if (!value) {
            return false;
          }
          const { dateTo } = this.parent;
          return value < dateTo;
        }),
      dateTo: yup
        .date()
        .required("required")
        .typeError("invalid-date")
        .test(
          "isDateToInPast",
          `${t("common:cannot-select-past-date-to")}`,
          function (value) {
            if (!value) {
              return false;
            }
            const currentTime = new Date();
            const fiveMinutesAgo = new Date(
              currentTime.getTime() - 5 * 60 * 1000
            );
            return !isBefore(value, fiveMinutesAgo);
          }
        )
        .test("dateTo", `${t("common:date-to-error")}`, function (value) {
          if (!value) {
            return false;
          }
          const { dateFrom } = this.parent;
          return value > dateFrom;
        }),
    }),
    onSubmit: async (values) => {
      if (initialFormValues.flow == "Edit") {
        const isSlotAvailable = await handleCheckVehicleAvailabilityAsync({
          id: values.vehicle?.id!,
          currentReservationId: Number(values.reservationId),
          dateFrom: values.dateFrom ? new Date(values.dateFrom).toISOString() : "",
          dateTo: values.dateTo ? new Date(values.dateTo).toISOString() : "",
        });
        if (!isSlotAvailable) {
          toast.error(t("common:slot-not-available"));
          return;
        }
        handleSubmit(values);
      }
    },
  });

  return (
    <FormikProvider value={formik}>
    <Stack
      component="form"
      spacing={3}
      noValidate
      onSubmit={formik.handleSubmit}
      sx={{ width: "100%" }}
    >
      {fullUpdate ? (
        <AutocompleteInput
          id="siteAddress"
          name="siteAddress"
          label={t("common:site-address")}
          variant="outlined"
          onAddressChange={({ city, postalCode, lat, lng, siteAddress }) => {
            formik.setFieldValue("siteAddress", siteAddress);
            formik.setFieldValue("location", {
              coordinates: [lat, lng],
            });
            formik.setFieldValue("plz", postalCode);
            formik.setFieldValue("city", city);
          }}
          existingInputValue={formik.values.siteAddress ||
            formik.values.plz ||
            formik.values.city
              ? `${formik.values.siteAddress || ""}, ${formik.values.plz || ""} ${formik.values.city || ""}`
                  .replace(/(^[,\s]+)|([,\s]+$)/g, "")
              : ""}
          disabled={isLoading}
        />
      ) : null}
      <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={enGb}>
        <DateTimePicker
          minutesStep={5}
          renderInput={(props: any) => (
            <CeTextField
              {...props}
              size="small"
              onBlur={() => formik.setFieldTouched("dateFrom", true)}
              error={!!formik.errors.dateFrom && formik.touched.dateFrom}
              helperText={formik.touched.dateFrom && formik.errors.dateFrom}
              required
            />
          )}
          label={t("date-from")}
          value={formik.values.dateFrom}
          onChange={(newValue) => {
            formik.setFieldValue("dateFrom", newValue);
          }}
          components={{
            OpenPickerIcon: Calendar04Icon,
          }}
        />
      </LocalizationProvider>
      <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={enGb}>
        <DateTimePicker
          minutesStep={5}
          renderInput={(props: any) => (
            <CeTextField
              {...props}
              size="small"
              error={!!formik.errors.dateTo && formik.touched.dateTo}
              helperText={formik.touched.dateTo && formik.errors.dateTo}
              onBlur={() => formik.setFieldTouched("dateTo", true)}
              required
            />
          )}
          label={t("date-to")}
          value={formik.values.dateTo}
          onChange={(newValue) => {
            formik.setFieldValue("dateTo", newValue);
          }}
          components={{
            OpenPickerIcon: Calendar04Icon,
          }}
        />
      </LocalizationProvider>

      <DialogActions sx={{ flexDirection: "column", gap: 2, padding: "8px" }}>
        <CeButton type="submit" disabled={isLoading} fullWidth>
          {fullUpdate ? t("proceed") : t("submit")}
        </CeButton>
        <CeButton
          onClick={handleClose}
          disabled={isLoading}
          variant="text"
          fullWidth
        >
          {t("cancel")}
        </CeButton>
      </DialogActions>
    </Stack>
    </FormikProvider>
  );
};
