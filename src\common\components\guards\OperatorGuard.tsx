import type { FC, ReactNode } from "react";
import { isAuthenticated } from "src/common/api/auth";
import { Role } from "src/common/types";

interface OperatorGuardProps {
  children: ReactNode;
}
export const OperatorGuard: FC<OperatorGuardProps> = ({ children }) => {
  const { role } = isAuthenticated();

  if (role === Role.OPERATOR) {
    return <>{children}</>;
  } else {
    return null;
  }
};
