import { useCallback } from "react";
import {
  ClientDetails,
  CreateJobDto,
  CreateReservationDto,
  Job,
  JobFormValues,
  JobStatus,
  Operator,
  Reservation,
  ReservationFullFormValues,
  Role,
  UpdateJobDto,
  UpdateReservationDto,
  User,
  VehicleReservation,
} from "../types";
import { WorkScheduleSettings } from "../types/companySettings";
import { PumpTier, TransportRate } from "../types/priceList";
import { parseTime } from "./formatDate";
import { useLoadScript } from "@react-google-maps/api";
import {
  addHours,
  addMinutes,
  differenceInMinutes,
  format,
  isBefore,
  set,
  startOfDay,
  subDays,
} from "date-fns";

const googleMapsApiKey = process.env.REACT_APP_GM_API_KEY || "";

export const turnReservationFormValuesIntoCreateDto = (
  values: ReservationFullFormValues
): CreateReservationDto => {
  const clientDetailsValues = turnClientDetailsIntoDto(values.clientDetails);
  const jobValues = turnJobFormValuesIntoCreatesDto(values.job);
  return {
    orderNumber: values.orderNumber,
    vehicleId: values.vehicle?.id!,
    operatorId: values.operator?.id!,
    managerId: values.manager?.id!,
    dispatcherId: values.dispatcher?.id,
    reservationType: "RESERVATION",
    dateFrom: values.dateFrom ? new Date(values.dateFrom).toISOString() : "",
    dateTo: values.dateTo ? new Date(values.dateTo).toISOString() : "",
    siteAddress: values.siteAddress || "",
    city: values?.city || "",
    plz: values?.plz!,
    location: values.location!,
    job: jobValues,
    pricelist: values.pricelist,
    clientDetails: clientDetailsValues,
    trafficPlanFile: values.trafficPlanFile,
    parkingPermitAcquiredFile: values.parkingPermitAcquiredFile,
    localAdministrationAuthorizationFile:
      values.localAdministrationAuthorizationFile,
  };
};

export const turnReservationFormValuesIntoUpdateDto = (
  values: ReservationFullFormValues
): UpdateReservationDto => {
  const jobValues = turnJobFormValuesIntoUpdateDto(values.job!);
  const reservationValuesToCreateDto =
    turnReservationFormValuesIntoCreateDto(values);
  const { job, ...reservationValuesToCreateDtoWithoutJob } =
    reservationValuesToCreateDto;
  return {
    ...reservationValuesToCreateDtoWithoutJob,
    job: jobValues,
    jobId: values.jobId,
    reservationId: values.reservationId!,
    hasBeenRead: values.hasBeenRead,
  };
};

export const turnReservationIntoFormValues = (
  reservation: Reservation
): ReservationFullFormValues => {
  const jobTransformed = turnJobIntoFormValues(reservation.job!);
  return {
    reservationId: reservation.id || null,
    orderNumber: reservation.orderNumber || undefined,
    manager: reservation.manager,
    dispatcher: reservation.dispatcher,
    operator: reservation.operator,
    vehicle: reservation.vehicle,
    reservationType: reservation.reservationType || undefined,
    dateFrom: reservation.dateFrom,
    dateTo: reservation.dateTo,
    siteAddress: reservation.siteAddress,
    city: reservation.city,
    plz: reservation.plz,
    location: reservation.location || undefined,
    clientDetails: reservation.clientDetails || undefined,
    localAdministrationAuthorizationKey:
      reservation.localAdministrationAuthorizationKey,
    parkingPermitAcquiredKey: reservation.parkingPermitAcquiredKey,
    trafficPlanKey: reservation.trafficPlanKey || undefined,
    job: jobTransformed,
    jobId: reservation.job?.id,
    pricelist: reservation.pricelist || null,
    hasBeenRead: false,
  };
};

export function turnJobIntoFormValues(job: Job): JobFormValues {
  return {
    id: job.id,
    amountOfConcrete: job.amountOfConcrete,
    balance: job.balance,
    flowRate: job.flowRate,
    flexiblePipeLength80Mm: job.flexiblePipeLength80Mm,
    flexiblePipeLength90Mm: job.flexiblePipeLength90Mm,
    frontOutriggersSpan: job.frontOutriggersSpan,
    rearOutriggersSpan: job.rearOutriggersSpan,
    rigidPipeLength100Mm: job.rigidPipeLength100Mm,
    rigidPipeLength120Mm: job.rigidPipeLength120Mm,
    extraCementBag: job.extraCementBag,
    units: job.units,
    presenceOfPowerLines: job.presenceOfPowerLines || undefined,
    voltage: job.voltage,
    pipeStartingFromBAC: job.pipeStartingFromBAC,
    comments: job.comments || undefined,
    status: job.status,
    terrainStability: job.terrainStability,
    tonnageRestriction: job.tonnageRestriction,
    authorizedWeight: job.authorizedWeight,
    heightRestriction: job.heightRestriction,
    heightLimit: job.heightLimit,
    enlistSecondTechnician: job.enlistSecondTechnician,
    barbotine: job.barbotine,
    supplyOfTheChemicalSlushie: job.supplyOfTheChemicalSlushie,
    parkingOn: job.parkingOn,
    cleaning: job.cleaning,
    jobType: job.jobType,
    ciaw: job.ciaw,
  };
}
export const turnJobFormValuesIntoUpdateDto = (
  values: JobFormValues
): UpdateJobDto => {
  return {
    jobId: values.id!,
    amountOfConcrete: values?.amountOfConcrete || null,
    balance: values?.balance || false,
    flowRate: values?.flowRate || null,
    flexiblePipeLength80Mm: values?.flexiblePipeLength80Mm || null,
    flexiblePipeLength90Mm: values?.flexiblePipeLength90Mm || null,
    extraCementBag: values?.extraCementBag || false,
    units: values?.units || null,
    frontOutriggersSpan: values?.frontOutriggersSpan || null,
    rearOutriggersSpan: values?.rearOutriggersSpan || null,
    pipeStartingFromBAC: values?.pipeStartingFromBAC || false,
    barbotine: values?.barbotine || false,
    rigidPipeLength100Mm: values?.rigidPipeLength100Mm || null,
    rigidPipeLength120Mm: values?.rigidPipeLength120Mm || null,
    presenceOfPowerLines: values?.presenceOfPowerLines || false,
    voltage: values?.voltage || null,
    comments: values?.comments || null,
    terrainStability: values?.terrainStability || null,
    tonnageRestriction: values?.tonnageRestriction || false,
    authorizedWeight: values?.authorizedWeight || null,
    heightRestriction: values?.heightRestriction || false,
    heightLimit: values?.heightLimit || null,
    enlistSecondTechnician: values?.enlistSecondTechnician || false,
    parkingOn: values?.parkingOn || null,
    status: JobStatus.NOT_STARTED,
    cleaning: values?.cleaning || null,
    jobType: values?.jobType || null,
    ciaw: values?.ciaw || null,
    supplyOfTheChemicalSlushie: values?.supplyOfTheChemicalSlushie || false,
    frontSideOpening: values?.frontSideOpening || null,
    rearSideOpening: values?.rearSideOpening || null,
  };
};

export const reservationPayloadIntoFormData = (
  values: CreateReservationDto | UpdateReservationDto
): FormData => {
  const formData = new FormData();

  if (values.orderNumber) {
    formData.append("orderNumber", String(values.orderNumber));
  }

  if (values.managerId) {
    formData.append("managerId", String(values.managerId));
  }

  if (values.dispatcherId) {
    formData.append("dispatcherId", String(values.dispatcherId));
  }

  if (values.vehicleId) {
    formData.append("vehicleId", String(values.vehicleId));
  }

  if (values.pricelist) {
    formData.append("pricelistId", String(values.pricelist.id));
  }

  if (values.operatorId) {
    formData.append("operatorId", String(values.operatorId));
  }

  formData.append("reservationType", String(values.reservationType));
  if (values.dateFrom && values.dateTo) {
    formData.append("dateFrom", String(values.dateFrom));
    formData.append("dateTo", String(values.dateTo));
  }

  if (values.siteAddress) {
    formData.append("siteAddress", String(values.siteAddress));
  }

  if (values.city) {
    formData.append("city", String(values.city));
  }

  if (values.plz) {
    formData.append("plz", String(values.plz));
  }

  if (values.location) {
    formData.append("location", JSON.stringify(values.location));
  }
  if (values.clientDetails) {
    formData.append("clientDetails", JSON.stringify(values.clientDetails));
  }
  if (values.job && Object.keys(values.job).length > 0) {
    formData.append("job", JSON.stringify(values.job));
  }
  if ("jobId" in values && values.jobId) {
    formData.append("jobId", String(values.jobId));
  }
  if (values.localAdministrationAuthorizationFile) {
    formData.append(
      "localAdministrationAuthorizationFile",
      values.localAdministrationAuthorizationFile
    );
  }
  if (values.trafficPlanFile) {
    formData.append("trafficPlanFile", values.trafficPlanFile);
  }
  if (values.parkingPermitAcquiredFile) {
    formData.append(
      "parkingPermitAcquiredFile",
      values.parkingPermitAcquiredFile
    );
  }
  if ("hasBeenRead" in values) {
    formData.append("hasBeenRead", String(values.hasBeenRead));
  }
  if ("pricelistId" in values) {
    formData.append("pricelistId", String(values.pricelistId));
  }

  return formData;
};

export const turnJobFormValuesIntoCreatesDto = (
  values?: JobFormValues
): CreateJobDto => {
  return {
    amountOfConcrete: values?.amountOfConcrete || null,
    balance: values?.balance || false,
    flowRate: values?.flowRate || null,
    flexiblePipeLength80Mm: values?.flexiblePipeLength80Mm || null,
    flexiblePipeLength90Mm: values?.flexiblePipeLength90Mm || null,
    extraCementBag: values?.extraCementBag || false,
    units: values?.units || null,
    frontOutriggersSpan: values?.frontOutriggersSpan || null,
    rearOutriggersSpan: values?.rearOutriggersSpan || null,
    pipeStartingFromBAC: values?.pipeStartingFromBAC || false,
    barbotine: values?.barbotine || false,
    rigidPipeLength100Mm: values?.rigidPipeLength100Mm || null,
    rigidPipeLength120Mm: values?.rigidPipeLength120Mm || null,
    presenceOfPowerLines: values?.presenceOfPowerLines || false,
    voltage: values?.voltage || null,
    comments: values?.comments || null,
    terrainStability: values?.terrainStability || null,
    tonnageRestriction: values?.tonnageRestriction || false,
    authorizedWeight: values?.authorizedWeight || null,
    heightRestriction: values?.heightRestriction || false,
    heightLimit: values?.heightLimit || null,
    enlistSecondTechnician: values?.enlistSecondTechnician || false,
    parkingOn: values?.parkingOn || null,
    status: JobStatus.NOT_STARTED,
    cleaning: values?.cleaning || null,
    jobType: values?.jobType || null,
    ciaw: values?.ciaw || null,
    supplyOfTheChemicalSlushie: values?.supplyOfTheChemicalSlushie || false,
    frontSideOpening: values?.frontSideOpening || null,
    rearSideOpening: values?.rearSideOpening || null,
  };
};

export const turnClientDetailsIntoDto = (values?: ClientDetails) => {
  return {
    name: values?.name || null,
    lastName: values?.lastName || null,
    email: values?.email || null,
    phoneNumber: values?.phoneNumber || null,
    companyName: values?.companyName || null,
    companyVatNumber: values?.companyVatNumber || null,
  };
};

export const getReservationsParamsByUserRoles = (
  role?: Role | null,
  userId?: number | null
) => {
  if (!role) {
    throw new Error("User role was not provided!");
  }

  if (!userId) {
    throw new Error("User id was not provided!");
  }

  if (role === Role.OEPRATOR_MANAGER) {
    return { managerIds: [userId] };
  } else if (role === Role.DISPATCHER) {
    return { dispatcherIds: [userId] };
  } else if (role === Role.DISPATCHER_MANAGER) {
    return { dispatcherIds: [userId] };
  } else {
    return {};
  }
};

export const calculateJobDuration = (
  start: string | null,
  end: string | null
): number => {
  if (!start || !end) {
    return 0;
  }
  const startDate = new Date(start);
  const endDate = new Date(end);
  const diffInMilliseconds = endDate.getTime() - startDate.getTime();
  const durationInHours = diffInMilliseconds / (1000 * 60 * 60);
  return durationInHours;
};

export const isWithinWorkingHours = (
  reservationTime: Date,
  workSchedule: WorkScheduleSettings[]
): boolean => {
  const day = reservationTime
    .toLocaleString("en-us", { weekday: "long" })
    .toLowerCase();
  const schedules = workSchedule.filter((w) => w.day.toLowerCase() === day);

  if (schedules.length === 0) return false;

  // Convert the reservation time into minutes since midnight for time comparison.
  const time = reservationTime.getHours() * 60 + reservationTime.getMinutes();

  // Check if the reservation time falls within any of the schedule ranges.
  return schedules.some((schedule) => {
    const start = parseTime(schedule.startTime); // Convert startTime to minutes.
    const end = parseTime(schedule.endTime); // Convert endTime to minutes.
    return time >= start && time < end; // Check if within range.
  });
};

export const isWeekend = (
  reservationTime: Date,
  workSchedule: WorkScheduleSettings[]
): boolean => {
  const day = reservationTime
    .toLocaleString("en-us", { weekday: "long" })
    .toLowerCase();
  const schedules = workSchedule.filter((w) => w.day.toLowerCase() === day);

  return schedules.length === 0;
};

export const isNightTime = (
  reservationTime: Date,
  workSchedule: WorkScheduleSettings[]
): boolean => {
  const day = reservationTime
    .toLocaleString("en-us", { weekday: "long" })
    .toLowerCase();
  const schedules = workSchedule.filter((w) => w.day.toLowerCase() === day);

  if (schedules.length === 0) return false;

  // Convert the reservation time into minutes since midnight for time comparison.
  const time = reservationTime.getHours() * 60 + reservationTime.getMinutes();

  // Check if the reservation time falls outside all of the schedule ranges.
  return schedules.every((schedule) => {
    const start = parseTime(schedule.startTime); // Convert startTime to minutes.
    const end = parseTime(schedule.endTime); // Convert endTime to minutes.
    return time < start || time > end; // Check if outside range.
  });
};

export const numberIntoTimePeriod = (
  number: number,
  type: "minutes" | "hours"
) => {
  const base = startOfDay(new Date()); // 00:00 of today
  let time: Date;
  if (number === 0) return 0;
  if (type === "hours") {
    time = addHours(base, number);
  } else {
    time = addMinutes(base, number);
  }
  const hour = time.getHours();
  const minutes = time.getMinutes();
  const hourFormat = hour < 10 ? "H" : "HH";
  const minutesFormat = !minutes ? "" : ":mm";
  return format(time, `${hourFormat}${minutesFormat}`);
};

export interface ClassifiedReservationSchedule {
  flatPackage: {
    regularWorkingTime: number;
    nightWorkingTime: number;
    weekendWorkingTime: number;
  };
  additionalHours: {
    regularWorkingTime: number;
    nightWorkingTime: number;
    weekendWorkingTime: number;
  };
}
const classifyPricingTime = (
  current: Date,
  workSchedule: WorkScheduleSettings[],
  hasWeekendPricing: boolean,
  hasNightPricing: boolean
): "regular" | "night" | "weekend" => {
  if (!hasWeekendPricing && !hasNightPricing) {
    return "regular";
  }

  const weekend = isWeekend(current, workSchedule);
  const withinWorking = isWithinWorkingHours(current, workSchedule);

  let classification: "regular" | "night" | "weekend";
  if (weekend) {
    classification = "weekend";
  } else if (withinWorking) {
    classification = "regular";
  } else {
    classification = "night";
  }

  if (classification === "weekend" && !hasWeekendPricing) {
    classification = withinWorking ? "regular" : "night";
  }

  if (classification === "night" && !hasNightPricing) {
    classification = weekend ? "weekend" : "regular";
  }

  return classification;
};

const scheduleIntoFlatAndAdditional = (
  blockLength: number,
  flatPackageRemaining: number
) => {
  let flatCategory = 0;
  let additionalCategory = 0;
  if (flatPackageRemaining > 0) {
    if (flatPackageRemaining >= blockLength) {
      flatCategory = blockLength;
      flatPackageRemaining -= blockLength;
    } else {
      flatCategory = flatPackageRemaining;
      additionalCategory = blockLength - flatPackageRemaining;
      flatPackageRemaining = 0;
    }
  } else {
    additionalCategory = blockLength;
  }
  return {
    flatCategory,
    additionalCategory,
    flatPackageRemaining,
  };
};

export const classifyReservationSchedule = (
  startDate: Date,
  endDate: Date,
  packageFlatDuration: number,
  workSchedule: WorkScheduleSettings[],
  hasWeekendPricing: boolean,
  hasNightPricing: boolean
): ClassifiedReservationSchedule => {
  let flatRegular = 0,
    flatNight = 0,
    flatWeekend = 0;
  let additionalRegular = 0,
    additionalNight = 0,
    additionalWeekend = 0;

  const intervalMinutes = 5;
  let flatPackageRemaining = packageFlatDuration * 60; // in minutes

  let current = startDate;

  while (isBefore(current, endDate)) {
    const remaining = differenceInMinutes(endDate, current);
    const blockLength = Math.min(intervalMinutes, remaining);

    const category = classifyPricingTime(
      current,
      workSchedule,
      hasWeekendPricing,
      hasNightPricing
    );

    const allocation = scheduleIntoFlatAndAdditional(
      blockLength,
      flatPackageRemaining
    );
    const flatCategory = allocation.flatCategory;
    const additionalCategory = allocation.additionalCategory;
    flatPackageRemaining = allocation.flatPackageRemaining;

    if (category === "regular") {
      flatRegular += flatCategory;
      additionalRegular += additionalCategory;
    } else if (category === "night") {
      flatNight += flatCategory;
      additionalNight += additionalCategory;
    } else if (category === "weekend") {
      flatWeekend += flatCategory;
      additionalWeekend += additionalCategory;
    }

    if (blockLength <= 0) break;

    current = addMinutes(current, blockLength);
  }

  return {
    flatPackage: {
      regularWorkingTime: flatRegular,
      nightWorkingTime: flatNight,
      weekendWorkingTime: flatWeekend,
    },
    additionalHours: {
      regularWorkingTime: additionalRegular / 60,
      nightWorkingTime: additionalNight / 60,
      weekendWorkingTime: additionalWeekend / 60,
    },
  };
};

export const calculateFlatFee = (
  packageFee: number,
  packageDuration: number,
  workingTime: number
) => {
  if (!packageDuration) {
    return 0;
  }
  const timeOfPackageFlat = workingTime / (packageDuration * 60);
  const flatFee = Number((packageFee * timeOfPackageFlat).toFixed(2));
  return flatFee;
};

export const transformVehicleWithReservationsIntoResources = (
  vehiclesWithReservations: VehicleReservation[]
) => {
  return vehiclesWithReservations.map((vehicle) => ({
    id: String(vehicle.id),
    name: vehicle.vehicleUniqueId,
    boomSize: vehicle.boomSize,
    extendedProps: {
      vehicleId: vehicle.id,
      operator: {
        id: vehicle.operatorId,
        name: vehicle.operatorName,
        phoneNumber: vehicle.operatorPhoneNumber,
        company: {
          phoneNumber: vehicle.companyPhoneNumber,
          contactEmail: vehicle.companyEmail,
          name: vehicle.companyName,
        },
      } as Operator,
      managerId: vehicle.managerId,
    },
  }));
};

export const calculateDayContract = (
  baseHours: number,
  dayContractHourFee: number,
  overtimeHourFee: number,
  workedHours: number
) => {
  const overtimeDuration = baseHours ? Math.max(0, workedHours - baseHours) : 0;
  const dayContractDuration = Math.min(workedHours, baseHours);

  const dayContractFee = dayContractDuration * dayContractHourFee;
  const dayContractOvertimeFee = overtimeDuration * overtimeHourFee;

  return {
    dayContractDuration,
    overtimeDuration,
    dayContractFee,
    dayContractOvertimeFee,
  };
};

type MultiDayResult = {
  date: string;
  workedHours: number;
} & ReturnType<typeof calculateDayContract>;

export const calculateDaysContract = (
  dateFrom: string | Date,
  dateTo: string | Date,
  baseHours: number,
  dayContractHourFee: number,
  overtimeHourFee: number
) => {
  const start = new Date(dateFrom);
  const end = new Date(dateTo);

  if (end <= start) throw new Error("dateTo must be after dateFrom");

  const results: MultiDayResult[] = [];
  let startDate = start;

  while (startDate < end) {
    const nextMidnight = new Date(startDate);
    nextMidnight.setHours(24, 0, 0, 0);

    const sliceEnd = nextMidnight < end ? nextMidnight : end;
    const workedMs = sliceEnd.getTime() - startDate.getTime();
    const workedHours = workedMs / 3_600_000; // ms → h

    const day = calculateDayContract(
      baseHours,
      dayContractHourFee,
      overtimeHourFee,
      workedHours
    );

    results.push({
      date: startDate.toISOString().slice(0, 10), // yyyy-mm-dd
      workedHours,
      ...day,
    });

    startDate = sliceEnd; // advance to next slice
  }

  const totals = results.reduce(
    (acc, day) => {
      acc.totalDayContractDuration += day.dayContractDuration;
      acc.totalOvertimeDuration += day.overtimeDuration;
      acc.totalDayContractFee += day.dayContractFee;
      acc.totalOvertimeFee += day.dayContractOvertimeFee;
      return acc;
    },
    {
      totalDayContractDuration: 0,
      totalOvertimeDuration: 0,
      totalDayContractFee: 0,
      totalOvertimeFee: 0,
      grandTotal: 0,
    }
  );
  const {
    totalDayContractDuration,
    totalOvertimeDuration,
    totalDayContractFee,
    totalOvertimeFee,
  } = totals;
  return {
    totalDayContractDuration: Math.round(totalDayContractDuration),
    totalOvertimeDuration: Math.round(totalOvertimeDuration),
    totalDayContractFee: Math.round(totalDayContractFee * 100) / 100,
    totalOvertimeFee: Math.round(totalOvertimeFee * 100) / 100,
  };
};

export type TabValue =
  | "clientDetailsTab"
  | "jobDetails"
  | "additionalInformation";

export const tabOrder: TabValue[] = [
  "clientDetailsTab",
  "jobDetails",
  "additionalInformation",
];

export const currentTabFields = {
  clientDetailsTab: [
    "clientDetails.name",
    "clientDetails.lastName",
    "clientDetails.email",
    "clientDetails.phoneNumber",
    "clientDetails.companyName",
    "clientDetails.companyVatNumber",
  ],
  jobDetails: [
    "job.amountOfConcrete",
    "job.flowRate",
    "job.frontOutriggersSpan",
    "job.rearOutriggersSpan",
    "job.cleaning",
    "job.parkingOn",
    "job.terrainStability",
    "job.units",
    "job.voltage",
    "job.authorizedWeight",
    "job.heightLimit",
    "job.jobType",
    "job.supplyOfTheChemicalSlushie",
    "job.ciaw",
  ],
  additionalInformation: [
    "localAdministrationAuthorizationFile",
    "trafficPlanFile",
    "parkingPermitAcquiredFile",
  ],
};

export const isMovingForward = (
  subjectArray: any[],
  currentUnit: any,
  newUnit: any
): boolean => {
  return subjectArray.indexOf(newUnit) > subjectArray.indexOf(currentUnit);
};

export function findPumpTier(
  tiers: PumpTier[],
  value: number
): PumpTier | null {
  const matchingTier = tiers.find((tier) => {
    if (tier.minimum == null || tier.maximum == null) {
      return false;
    }
    if (value >= tier.minimum && value <= tier.maximum) {
      return tier;
    }
  });
  if (matchingTier) {
    return matchingTier;
  }

  if (tiers.length > 0) {
    const lastTier = tiers[tiers.length - 1];
    if (
      lastTier.minimum != null &&
      lastTier.maximum != null &&
      value > lastTier.maximum
    ) {
      return lastTier;
    }
  }

  return null;
}

export function findTransportTier(
  tiers: TransportRate[],
  value: number
): TransportRate | null {
  const matchingTier = tiers.find((tier) => {
    if (tier.from == null || tier.to == null) {
      return false;
    }
    return value >= tier.from && value <= tier.to;
  });

  if (matchingTier) {
    return matchingTier;
  }

  if (tiers.length > 0) {
    const firstTier = tiers[0];
    const lastTier = tiers[tiers.length - 1];

    if (
      firstTier.from != null &&
      firstTier.to != null &&
      firstTier.from > value
    ) {
      return firstTier;
    }
    if (lastTier.from != null && lastTier.to != null && value > lastTier.to) {
      return lastTier;
    }
  }

  return null;
}

export type LatLng = {
  lat: number;
  lng: number;
};

export function useLocationsDistance() {
  const alreadyLoaded = Boolean(window.google?.maps);

  const getGeodesicDistanceInKm = useCallback(
    async (origin: LatLng, destination: LatLng): Promise<number> => {
      if (!alreadyLoaded) {
        throw new Error("Google Maps is not loaded yet.");
      }
      if (!window.google?.maps?.geometry) {
        throw new Error("Google Maps geometry library is not available.");
      }

      const p1 = new window.google.maps.LatLng(origin.lat, origin.lng);
      const p2 = new window.google.maps.LatLng(
        destination.lat,
        destination.lng
      );

      const distanceInMeters =
        window.google.maps.geometry.spherical.computeDistanceBetween(p1, p2);

      return distanceInMeters / 1000;
    },
    [alreadyLoaded]
  );

  return {
    isLoaded: alreadyLoaded,
    getGeodesicDistanceInKm,
  };
}

export const determineCancelationFee = (
  reservationDateTime: string,
  standardCancelationTime: string,
  lateCancelationTime: string,
  cancelationFee: number,
  lateCancelationFee: number
): number | null => {
  const cancellationTime = new Date();

  const earlyFeeDays = 2;
  const lateFeeDays = 1;
  const reservationDate = new Date(reservationDateTime);
  const [standardHour, standardMinute, standardSecond = 0] =
    standardCancelationTime.split(":").map(Number);

  const [lateHour, lateMinute, lateSecond = 0] = lateCancelationTime
    .split(":")
    .map(Number);

  let T1 = subDays(reservationDate, earlyFeeDays);
  T1 = set(T1, {
    hours: standardHour,
    minutes: standardMinute,
    seconds: standardSecond,
    milliseconds: 0,
  });

  let T2 = subDays(reservationDate, lateFeeDays);
  T2 = set(T2, {
    hours: lateHour,
    minutes: lateMinute,
    seconds: lateSecond,
    milliseconds: 0,
  });

  if (isBefore(cancellationTime, T1)) {
    return null;
  } else if (isBefore(cancellationTime, T2)) {
    return cancelationFee;
  } else {
    return lateCancelationFee;
  }
};
