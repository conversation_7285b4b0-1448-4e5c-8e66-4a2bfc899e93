import { useState } from "react";
import { useWindowSize } from "@react-hook/window-size";
import { useRecoilState } from "recoil";
import { themeState } from "src/common/state";
import { ClientsDataGrid } from "./ClientsDataGrid";
import { useClients } from "src/common/api/client";
import { CePaper } from "src/common/components";
import { useLocation } from "react-router-dom";
import usePersistentGridState from "src/common/utils/gridState";
import { turnDatagridFilterIntoExpressions } from "src/common/utils";
import { Expression } from "src/common/types";
import { GridFilterModel, GridSortModel } from "@mui/x-data-grid";

export const Clients = () => {

  const location = useLocation();
  const localStorageKey = location.pathname.slice(1);

  const [gridState, updateGridStatePart] = usePersistentGridState(
    localStorageKey,
    1,
    20,
    [{ field: "name", sort: "asc" }]
  );

  const expression = turnDatagridFilterIntoExpressions(gridState.filterModel);
  const [expressions, setExpressions] = useState<Expression[]>(expression);
  const [sortingModelOptions, setSortingModelOptions] = useState<GridSortModel>(
    gridState.sortModel
  );

  const {
    data: allClients,
    isLoading: isLoadingClients,
    refetch: refetchClients,
  } = useClients(
    {
      limit: gridState.pageSize,
      offset: (gridState.page - 1) * gridState.pageSize,
      expressions,
      sortModel: sortingModelOptions,
    },
    true
  );
  const [, height] = useWindowSize();
  const [theme] = useRecoilState(themeState);
  const isDarkMode = theme === "dark";

  const handlePageChange = (newPage: number) => {
    updateGridStatePart("page", newPage + 1);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    updateGridStatePart("pageSize", newPageSize);
  };

  const handleSortModelChange = (sortModel: GridSortModel) => {
    setSortingModelOptions(sortModel);
  };

  const onDatagridFiltersChange = (filterModel: GridFilterModel) => {
    const expressions = turnDatagridFilterIntoExpressions(filterModel);
    setExpressions(expressions);
  };
  return (
    <CePaper
      className={isDarkMode ? "dark-mode" : ""}
      sx={{
        background: (theme) => theme.palette.background.paper,
        height: `${height - 100}px`,
        p: 2,
      }}
    >
      <ClientsDataGrid
        data={allClients?.data || []}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        isServerDriven
        handleSortModelChange={handleSortModelChange}
        onDatagridFiltersChange={onDatagridFiltersChange}
        gridState={gridState}
        updateGridStatePart={updateGridStatePart}
        page={gridState.page}
        pageSize={gridState.pageSize}
        total={allClients?.totalCount || 0}
        isLoadingClients={isLoadingClients}
        refetchClients={refetchClients}
        shouldRenderRefreshButton
        shouldRenderAddButton
      />
    </CePaper>
  );
};
