import { Stack } from "@mui/material";
import React from "react";
import { useTranslation } from "react-i18next";
import { CeButton, MainModal } from "src/common/components";

interface DefaultContainerModalProps {
  onConfirm: () => void;
  flow: string;
  closeModal: () => void;
}

const DefaultContainerConfirmModal = ({
  flow,
  onConfirm,
  closeModal,
}: DefaultContainerModalProps) => {
  const { t } = useTranslation("common");

  return (
    <MainModal
      isOpen={!!flow}
      title={t("default-pricelist")}
      handleClose={closeModal}
      helperText={t("confirm-default-pricelist")}
    >
      <Stack
        justifyContent="flex-end"
        direction="row"
        gap={2}
        alignItems={"center"}
      >
        <CeButton
          onClick={closeModal}
          color="primary"
          variant="text"
          size="medium"
        >
          {t("cancel")}
        </CeButton>
        <CeButton
          color="primary"
          variant="contained"
          size="medium"
          onClick={onConfirm}
        >
          {t("confirm")}
        </CeButton>
      </Stack>
    </MainModal>
  );
};

export default DefaultContainerConfirmModal;
