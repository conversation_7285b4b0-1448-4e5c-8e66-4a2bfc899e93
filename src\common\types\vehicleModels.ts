import { GridSortModel } from "@mui/x-data-grid";
import { CommonEntity, PossibleSortDir } from "./common";
import { Manager } from "./manager";
import { PriceList } from "./priceList";
import { BoomUnfoldingSystemType, VehicleTypes } from "./vehicle";
import { VehicleType } from "./vehicleType";
import { Expression } from "./filters";

export interface VehicleModels extends CommonEntity {
  id: number;
  type: VehicleType | null;
  boomSize: number | null;
  operatorManagerId: number | null;
  operatorManager?: Manager;
  pricelists: PriceList[] | [];
}

export interface getVehicleModelsDto {
  sortModel: GridSortModel;
  expressions: Expression[];
  limit?: number;
  offset?: number;
  searchText?: string;
  operatorManagerId?: number | null;
  vehicleTypeId?: number | null;
}

export interface VehicleModelsWithCount {
  totalCount: number;
  data: VehicleModels[];
}

export interface CreateVehicleModelDto
  extends Omit<
    VehicleModels,
    "operatorManager" | "id" | "pricelists" | "type"
  > {
  operatorManagerId: number | null;
  vehicleTypeId: number;
}

export interface UpdateVehicleModelDto extends CreateVehicleModelDto {
  vehicleModelId: number;
}

export interface DeleteVehicleModelDto {
  vehicleModelId: number;
}

export interface DeleteVehicleModelModalValues {
  vehicleModelId?: number;
  vehicleModelTitle?: string;
  flow: VehicleModelDeleteModalFlow;
}

export interface VehicleModelFormValues {
  vehicleModelId: number | null;
  type: VehicleType | null;
  boomSize: number | null;
  operatorManagerId: number | null;
  flow: VehicleModelModalFlow;
}

export type VehicleModelDeleteModalFlow = "Delete" | null;
export type VehicleModelModalFlow = "Create" | "Update" | null;

export enum EVehicleType {
  Pump = "Pump",
  CityPump = "City Pump",
  MixoPump = "Mixo Pump",
  Stationary = "Stationary Pump",
}
