import i18n from "i18next";
import { initReactI18next } from "react-i18next";

import enCommon from "./locales/en/common.json";
import enManager from "./locales/en/manager.json";
import enDispatcher from "./locales/en/dispatcher.json";
import enOperator from "./locales/en/operator.json";

import frCommon from "./locales/fr/common.json";
import frManager from "./locales/fr/manager.json";
import frDispatcher from "./locales/fr/dispatcher.json";
import frOperator from "./locales/fr/operator.json";

import deCommon from "./locales/de/common.json";
import deManager from "./locales/de/manager.json";
import deDispatcher from "./locales/de/dispatcher.json";
import deOperator from "./locales/de/operator.json";

import nlCommon from "./locales/nl/common.json";
import nlManager from "./locales/nl/manager.json";
import nlDispatcher from "./locales/nl/dispatcher.json";
import nlOperator from "./locales/nl/operator.json";

export const calendarLocales: { [key: string]: string } = {
  en: "en-EN",
  nl: "nl-BE",
  fr: "fr-FR",
  de: "de-DE",
};

const resources = {
  en: {
    common: enCommon,
    manager: enManager,
    dispatcher: enDispatcher,
    operator: enOperator,
  },
  fr: {
    common: frCommon,
    manager: frManager,
    dispatcher: frDispatcher,
    operator: frOperator,
  },
  de: {
    common: deCommon,
    manager: deManager,
    dispatcher: deDispatcher,
    operator: deOperator,
  },
  nl: {
    common: nlCommon,
    manager: nlManager,
    dispatcher: nlDispatcher,
    operator: nlOperator,
  },
};

i18n
  .use(initReactI18next) // bind react-i18next to the instance
  .init({
    lng: "en",
    fallbackLng: "en",
    resources,
    supportedLngs: ["en", "de", "nl", "fr"],
    debug: true,

    interpolation: {
      escapeValue: false, // not needed for react!!
    },
  });

export default i18n;
