import { useEffect } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { isTrackAndTraceAuthenticated, rmTrackTraceTokenToStorage } from "src/common/api";
import { ApiLoadingIndicator } from "src/common/components";

export const RequireTrackAndTraceAuth: React.FC = ({ children }) => {
  const { isAuth, orderDetails } = isTrackAndTraceAuthenticated();
  const location = useLocation();

  useEffect(() => {
    if (!isAuth && orderDetails) {
      rmTrackTraceTokenToStorage()
    }
  }, [isAuth, orderDetails]);

  if (!isAuth) {
    return (
      <Navigate
        to={`/track-and-trace?orderNumber=${orderDetails?.orderNumber || ""}&clientEmail=${orderDetails?.clientEmail || ""}`}
        state={{ from: location }}
        replace
      />
    );
  }

  return <>
  <ApiLoadingIndicator marginTop={0} />
  {children}
  </>;
};
