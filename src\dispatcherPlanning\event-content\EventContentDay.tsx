import { EventContentArg } from "@fullcalendar/core";
import {
  Box,
  Chip,
  CircularProgress,
  CircularProgressProps,
  Tooltip,
  tooltipClasses,
  TooltipProps,
  Typography,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { useTranslation } from "react-i18next";
import { FC } from "react";
import { JOB_STEPS, STATUS_COLORS } from "src/common/constants";
import {
  CalendarEventTypography,
  CalendarEventWrapper,
} from "src/common/components/custom/CalendarEventWrapper";
import { HtmlTooltipResource } from "src/common/components/custom/HtmlTooltipResource";
import { TickDouble02Icon } from "@hugeicons/react";
import { JobStatus } from "src/common/types";
import { EventContentDayRow } from "./EventContentDayRow";

type Props = {
  arg: EventContentArg;
  eventsNumber?: number;
};

const EventContentDay: FC<Props> = (props) => {
  const extendedProps = props.arg.event.extendedProps;
  const address = extendedProps.address;
  const city = extendedProps.city || "-";
  const fullSchedule = extendedProps.schedule;
  const jobStatus = extendedProps.jobStatus;
  const quantity = extendedProps.quantity;
  const hasBeenRead = extendedProps.hasBeenRead;
  const ownReservation = extendedProps.ownReservation;
  const flowRate = extendedProps.flowRate || 0;
  const { t } = useTranslation(["manager", "dispatcher", "common"]);
  const displayAddress = ownReservation ? `${address || "-"}, ${city}` : city;

  const statusColors = ownReservation
    ? STATUS_COLORS[jobStatus]
    : STATUS_COLORS.disabled;

  const { backgroundColor, borderColor, color } = statusColors as {
    backgroundColor: string;
    borderColor: string;
    color: string;
  };

  const getStatusData = (
    jobStatus: string
  ): { statusText: string; statusPercentage: number } => {
    const step = JOB_STEPS.find((step) => step.name === jobStatus);
    let statusPercentage = 0;

    if (jobStatus === "UNAVAILABLE") {
      return { statusText: t("common:unavailable"), statusPercentage: 0 };
    } else if (step) {
      statusPercentage = step.progress ?? 0;
      return { statusText: t(`${step.label}`), statusPercentage };
    } else {
      return { statusText: t("common:unknown-status"), statusPercentage: 0 };
    }
  };

  const statusData = getStatusData(jobStatus);

  const HtmlTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: "#f5f5f9",
      color: "rgba(0, 0, 0, 0.87)",
      maxWidth: 220,
      fontSize: theme.typography.pxToRem(12),
      border: "1px solid #dadde9",
    },
  }));

  const CircularProgressWithLabel = (
    props: CircularProgressProps & { value: number; status: string }
  ) => {
    const status = props.status;
    if (
      [
        t("common:complete"),
        t("common:cancelled"),
        t("common:not-started"),
        t("common:unavailable"),
      ].includes(t(status))
    ) {
      return <></>;
    } else {
      return (
        <Box sx={{ position: "relative", display: "inline-flex" }}>
          <CircularProgress
            size={32}
            sx={{
              color: (theme) =>
                theme.palette.grey[theme.palette.mode === "light" ? 200 : 800],
            }}
            thickness={4}
            variant="determinate"
            {...props}
            value={100}
          />
          <CircularProgress
            size={32}
            variant="determinate"
            {...props}
            sx={{ position: "absolute", left: 0 }}
          />
          <Box
            sx={{
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              position: "absolute",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Typography
              color="primary"
              component="div"
              fontSize={10}
              variant="caption"
            >{`${Math.round(props.value)}%`}</Typography>
          </Box>
        </Box>
      );
    }
  };

  const StatusTooltipContent = (
    <Box alignItems="center" display="flex">
      <Box alignItems="center" display="flex">
        <Box alignItems="center" display="flex" mr={1}>
          <CircularProgressWithLabel
            color="secondary"
            status={statusData.statusText}
            value={statusData.statusPercentage}
          />
        </Box>
        <Chip label={statusData.statusText} />
      </Box>
    </Box>
  );

  const nonGenericTypographyStyle = {
    lineHeight: "14.3px",
    textOverflow: "ellipsis",
    overflow: "hidden",
    whiteSpace: "nowrap",
    width: "100%",
    flex: "1",
  };

  if (props.eventsNumber && props.eventsNumber > 1) {
    return (
      <EventContentDayRow
        arg={props.arg}
        eventsnumber={props.eventsNumber}
        bulletStyles={{ pt: "5.5px" }}
      />
    );
  }
  return (
    <HtmlTooltipResource
      placement="bottom-end"
      title={
        <Box>
          <Box
            alignItems="center"
            display="flex"
            justifyContent={"flex-start"}
            mt={0.5}
            gap="5px"
          >
            <Typography>Time:</Typography>
            <Typography>{props.arg.event.extendedProps.schedule}</Typography>
          </Box>
          <Box
            alignItems="center"
            display="flex"
            justifyContent={"flex-start"}
            mt={0.5}
            gap="5px"
          >
            <Typography>Address:</Typography>
            <Typography>
             {displayAddress}
            </Typography>
          </Box>
          <Box
            alignItems="center"
            display="flex"
            justifyContent={"flex-start"}
            mt={0.5}
            gap="5px"
          >
            <Typography>Amount:</Typography>
            <Typography>{`${
              props.arg.event.extendedProps.quantity
            }m\u00b3 ${""} (${flowRate} m\u00b3/h)`}</Typography>
          </Box>
        </Box>
      }
    >
      <Box
        sx={{
          opacity: ownReservation ? 1 : 0.6,
          cursor: ownReservation ? "pointer" : "not-allowed",
        }}
      >
        <CalendarEventWrapper
          borderColor={borderColor}
          backgroundColor={backgroundColor}
          color={color}
          eventsnumber={props.eventsNumber}
        >
          <Chip
            sx={{
              color: color,
              backgroundColor: "transparent",
              height: "auto",
              "& span": {
                paddingLeft: "0px !important",
              },
            }}
            label={
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {hasBeenRead && jobStatus === JobStatus.NOT_STARTED ? (
                  <TickDouble02Icon
                    size={14}
                    color="currentColor"
                    variant={"solid"}
                  />
                ) : null}
                <CalendarEventTypography
                  eventsnumber={props.eventsNumber}
                  sx={{
                    flex: "0.5",
                  }}
                >
                  {fullSchedule}
                </CalendarEventTypography>
                {![
                  t("common:complete"),
                  t("common:cancelled"),
                  t("common:not-started"),
                  t("common:unavailable"),
                ].includes(t(statusData.statusText)) &&
                  ownReservation && (
                    <HtmlTooltip title={<>{StatusTooltipContent}</>}>
                      <Box
                        border="solid white 4px"
                        borderRadius="50%"
                        ml={1}
                      ></Box>
                    </HtmlTooltip>
                  )}
              </Box>
            }
          />
          <CalendarEventTypography
            eventsnumber={props.eventsNumber}
            sx={nonGenericTypographyStyle}
          >
            {displayAddress}
          </CalendarEventTypography>
          <CalendarEventTypography
            eventsnumber={props.eventsNumber}
            sx={{ ...nonGenericTypographyStyle, textTransform: "lowercase" }}
          >
            {`${quantity} m\u00b3 ${""} (${flowRate} m\u00b3/h)`}
          </CalendarEventTypography>
        </CalendarEventWrapper>
      </Box>
    </HtmlTooltipResource>
  );
};

export default EventContentDay;
