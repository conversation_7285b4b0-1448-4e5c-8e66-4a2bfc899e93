import { JobEvent, JobStatus } from "src/common/types";
import { JOB_STEPS } from "src/common/constants";
import { format } from "date-fns";

export const formatDateWithoutTimezone = (date: Date) => {
  return format(date, "yyyy/MM/dd HH:mm");
};

export const getJobEventTimestamp = (
  jobEvents: JobEvent[],
  status: JobStatus
) => {
  const event = jobEvents.find((event) =>
    event.statusDescription.includes(status)
  );
  return event ? formatDateWithoutTimezone(new Date(event.updated_at)) : null;
};

export const getStepIconColor = (
  stepName: string,
  currentStatus: string,
  currentProgress: number,
  jobEvents: JobEvent[]
): string => {
  const stepProgress =
    JOB_STEPS.find((step) => step.name === stepName)?.progress ?? 0;

  const isCanceled = currentStatus === JobStatus.CANCELLED;

  const lastEventBeforeCancelation = jobEvents
    .filter((event) => event.statusDescription !== JobStatus.CANCELLED)
    .reduce(
      (prev, curr) =>
        new Date(prev.updated_at) > new Date(curr.updated_at) ? prev : curr,
      jobEvents[0]
    );

  const lastStepBeforeCancellation = lastEventBeforeCancelation
    ? JOB_STEPS.find(
        (step) =>
          step.name ===
          lastEventBeforeCancelation.statusDescription.split(" ")[2]
      )?.name
    : null;

  if (stepName === JobStatus.CANCELLED) {
    return isCanceled ? "#e53935" : "#e0e0e0";
  } else if (isCanceled && stepName === lastStepBeforeCancellation) {
    return "#e53935";
  } else if (stepProgress < currentProgress) {
    return "#7cb342";
  } else if (currentStatus === stepName) {
    return "#0057b2";
  } else {
    return "#e0e0e0";
  }
};

export const apiWarningMessage = (apiKey: string | undefined): void => {
  if (!apiKey) {
    console.warn("Google Maps API key is missing");
  }
};
