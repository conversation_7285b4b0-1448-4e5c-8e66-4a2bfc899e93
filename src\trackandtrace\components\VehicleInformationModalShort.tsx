import { MainModal } from "src/common/components";
import { Box, Tab } from "@mui/material";
import { Tab<PERSON>ontex<PERSON>, <PERSON>b<PERSON>ist, TabPanel } from "@mui/lab";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import VehicleImageStack from "src/vehicles/vehicleInfoModal/VehicleImageStack";
import VehicleInfoTab from "src/vehicles/vehicleInfoModal/VehicleInfoTab";
import { Vehicle } from "src/common/types";

interface VehicleInformationModalProps {
    vehicle: Vehicle;
    open: boolean;
    onClose: () => void;
}

export const VehicleInformationModalShort: React.FC<VehicleInformationModalProps> = ({ vehicle, open, onClose }) => {
    const { t } = useTranslation("common");

    return (
        <MainModal
            title={t("vehicle-details")}
            isOpen={open}
            handleClose={onClose}
            maxWidth={"xl"}
        >
            <Box>
                <VehicleImageStack vehicle={vehicle} />
                <Box mt={2} p={2}>
                    <VehicleInfoTab vehicle={vehicle} />
                </Box>
            </Box>
        </MainModal>
    );
};
