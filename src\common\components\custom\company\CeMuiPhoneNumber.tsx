import MuiPhoneNumber from "material-ui-phone-number";
import { styled } from "@mui/material";

export const CeMuiPhoneNumber = styled(MuiPhoneNumber)(({ theme }) => ({
  "& .MuiOutlinedInput-root": {
    borderRadius: "8px",
    "& fieldset, &:hover fieldset, &.Mui-focused fieldset": {
      borderWidth: "2px",
    },
    "&.Mui-disabled fieldset": {
      borderColor:
        theme.palette.mode === "light"
          ? theme.palette.info.light
          : theme.palette.info.main,
    },
  },

  "& .MuiFilledInput-root": {
    borderRadius: "8px",
    "&:before, &:after": {
      borderBottomWidth: "2px",
      borderColor: theme.palette.info.light,
    },
    "&.Mui-disabled:before": {
      borderBottomColor:
        theme.palette.mode === "light"
          ? theme.palette.info.light
          : theme.palette.info.main,
    },
  },

  "& .MuiInput-root": {
    "&:before, &:after": {
      borderBottomWidth: "2px",
      borderColor: theme.palette.info.light,
    },
    "&.Mui-disabled:before": {
      borderBottomColor:
        theme.palette.mode === "light"
          ? theme.palette.info.light
          : theme.palette.info.main,
    },
  },

  "& .MuiInputLabel-root": {
    "&.Mui-disabled": {
      color: theme.palette.info.main,
    },
  },
}));
