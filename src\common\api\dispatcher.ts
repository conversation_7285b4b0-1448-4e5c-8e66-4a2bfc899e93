import axios, { AxiosError } from "axios";
import { CreateDispatcherDto, DeleteDispatcher<PERSON>to, Dispatcher, UpdateDispatcherDto } from "../types";
import { useMutation, useQueryClient } from "react-query";
import { processApiError, processApiSuccess } from "../utils/errors";

const backendUrl = process.env.REACT_APP_API_URL;

export const createNewDispatcher = (attrs: CreateDispatcherDto) => {
  return axios
    .post(`${backendUrl}/users`, attrs, { withCredentials: true })
    .then((response) => response.data);
};

export const useCreateNewDispatcher = () => {
  const queryClient = useQueryClient();
  return useMutation<
    Dispatcher,
    AxiosError | Error,
    CreateDispatcherDto,
    () => void
  >((a: CreateDispatcherDto) => createNewDispatcher(a), {
    onSuccess: (newDispatcher) => {
      queryClient.invalidateQueries("user");
      queryClient.invalidateQueries("users");

      // maybe in the future we can include email invitations..
      processApiSuccess(`Invite email sent to ${newDispatcher?.email}`);
    },
    onError: (err) => processApiError("Unable to create dispatcher", err),
  });
};


export const handleUpdateDispatcher = (updateDispatcherArgs: UpdateDispatcherDto) => {
    const { dispatcherId, ...dispatcher } = updateDispatcherArgs;
    if (!dispatcherId) {
      throw new Error("the dispatcher ID was not provided");
    }
    return axios
      .patch(`${backendUrl}/users/${dispatcherId}`, dispatcher, {
        withCredentials: true,
      })
      .then((response) => response.data);
  };
  
  export const useUpdateDispatcher = () => {
    const queryClient = useQueryClient();
    return useMutation<Dispatcher, AxiosError | Error, UpdateDispatcherDto, () => void>(
      (updateDispatcherArgs: UpdateDispatcherDto) =>
        handleUpdateDispatcher(updateDispatcherArgs),
      {
        onSuccess: () => {
          queryClient.invalidateQueries("user");
          queryClient.invalidateQueries("users");
        },
        onError: (err) => {
          processApiError("Unable to update dispatcher", err);
        },
      }
    );
  };
  
  export const deleteDispatcher = (deleteDispatcherDto: DeleteDispatcherDto) => {
    const { dispatcherId } = deleteDispatcherDto;
    if (!dispatcherId) {
      throw new Error("the dispatcher ID was not provided");
    }
    return axios
      .delete(`${backendUrl}/users/${dispatcherId}`, { withCredentials: true })
      .then((response) => response.data);
  };
  
  export const useDeleteDispatcher = () => {
    const queryClient = useQueryClient();
    return useMutation<Dispatcher, AxiosError | Error, DeleteDispatcherDto, () => void>(
      (deleteDispatcherDto: DeleteDispatcherDto) => deleteDispatcher(deleteDispatcherDto),
      {
        onSuccess: () => {
          queryClient.invalidateQueries("user");
          queryClient.invalidateQueries("users");
        },
        onError: (err) => processApiError("Unable to delete dispatcher", err),
      }
    );
  };