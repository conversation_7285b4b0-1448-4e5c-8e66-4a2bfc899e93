import {
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography,
} from "@mui/material";
import { useState } from "react";
import { CeDialog, CeButton, CeTextField } from "src/common/components";

import { ContractResponse } from "src/common/types";

interface PreviewListModalProps {
  title: string;
  isLoading: boolean;
  handleRejectContract: (comment: string) => void;
  handleAcceptContract: (comment: string) => void;
  contractResponse: ContractResponse | null;
  closeContractDialog: () => void;
}

const ContractResponseModal = ({
  handleAcceptContract,
  handleRejectContract,
  closeContractDialog,
  contractResponse,
  title,
  isLoading,
}: PreviewListModalProps) => {
  const [inputValue, setInputValue] = useState("");

  const handleInputChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setInputValue(event.target.value);
  };
  const contractIsAccepted = contractResponse === "accept";
  return (
    <CeDialog
      open={Boolean(contractResponse)}
      onClose={closeContractDialog}
      fullWidth
    >
      <DialogTitle>
        {`${contractIsAccepted ? "Accept" : "Reject"} ${title}?`}
      </DialogTitle>
      <DialogContent dividers>
        <DialogContentText sx={{ marginBottom: 2, marginTop: 0 }}>
          <Typography
            sx={{
              fontSize: "16px",
              letterSpacing: "0.4px",
            }}
          >
            Please leave a comment
          </Typography>
          <CeTextField
            label=""
            value={inputValue}
            onChange={handleInputChange}
            multiline
            rows={3}
            maxRows={4}
            fullWidth
            margin="normal"
          />
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <CeButton
          variant="text"
          onClick={closeContractDialog}
          disabled={isLoading}
        >
          Cancel
        </CeButton>
        <CeButton
          variant="contained"
          color="primary"
          onClick={() => {
            if (contractIsAccepted) {
              handleAcceptContract(inputValue);
            } else {
              handleRejectContract(inputValue);
            }
            setInputValue("");
          }}
          disabled={isLoading}
        >
          {contractIsAccepted ? "Accept" : "Reject"}
        </CeButton>
      </DialogActions>
    </CeDialog>
  );
};

export default ContractResponseModal;
