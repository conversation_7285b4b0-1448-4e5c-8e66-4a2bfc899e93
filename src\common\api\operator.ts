import axios, { AxiosError } from "axios";
import { CreateOperatorDto, DeleteOperatorDto, Operator, UpdateOperatorDto } from "../types";
import { useMutation, useQueryClient } from "react-query";
import { processApiError, processApiSuccess } from "../utils/errors";

const backendUrl = process.env.REACT_APP_API_URL;

export const createNewOperator = (attrs: CreateOperatorDto) => {
  return axios
    .post(`${backendUrl}/users`, attrs, { withCredentials: true })
    .then((response) => response.data);
};

export const useCreateNewOperator = () => {
  const queryClient = useQueryClient();
  return useMutation<
    Operator,
    AxiosError | Error,
    CreateOperatorDto,
    () => void
  >((a: CreateOperatorDto) => createNewOperator(a), {
    onSuccess: (newOperator) => {
      queryClient.invalidateQueries("user");
      queryClient.invalidateQueries("users");

      // maybe in the future we can include email invitations..
      processApiSuccess(`Invite email sent to ${newOperator?.email}`);
    },
    onError: (err) => processApiError("Unable to create operator", err),
  });
};


export const handleUpdateOperator = (updateOperatorArgs: UpdateOperatorDto) => {
    const { operatorId, ...operator } = updateOperatorArgs;
    if (!operatorId) {
      throw new Error("the operator ID was not provided");
    }
    return axios
      .patch(`${backendUrl}/users/${operatorId}`, operator, {
        withCredentials: true,
      })
      .then((response) => response.data);
  };
  
  export const useUpdateOperator = () => {
    const queryClient = useQueryClient();
    return useMutation<Operator, AxiosError | Error, UpdateOperatorDto, () => void>(
      (updateOperatorArgs: UpdateOperatorDto) =>
        handleUpdateOperator(updateOperatorArgs),
      {
        onSuccess: () => {
          queryClient.invalidateQueries("user");
          queryClient.invalidateQueries("users");
        },
        onError: (err) => {
          processApiError("Unable to update operator", err);
        },
      }
    );
  };
  
  export const deleteOperator = (deleteOperatorDto: DeleteOperatorDto) => {
    const { operatorId } = deleteOperatorDto;
    if (!operatorId) {
      throw new Error("the operator ID was not provided");
    }
    return axios
      .delete(`${backendUrl}/users/${operatorId}`, { withCredentials: true })
      .then((response) => response.data);
  };
  
  export const useDeleteOperator = () => {
    const queryClient = useQueryClient();
    return useMutation<Operator, AxiosError | Error, DeleteOperatorDto, () => void>(
      (deleteOperatorDto: DeleteOperatorDto) => deleteOperator(deleteOperatorDto),
      {
        onSuccess: () => {
          queryClient.invalidateQueries("user");
          queryClient.invalidateQueries("users");
        },
        onError: (err) => processApiError("Unable to delete operator", err),
      }
    );
  };