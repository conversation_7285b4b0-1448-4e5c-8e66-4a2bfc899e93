import { <PERSON>b<PERSON><PERSON>x<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>ane<PERSON> } from "@mui/lab";
import { Box, Tab, Typography, useTheme } from "@mui/material";
import React, { useState } from "react";
import { getCurrentUser } from "src/common/api";
import { TaskActivity, TaskComment } from "src/common/types/tasks";
import Comments from "./Comments";
import { useTranslation } from "react-i18next";
import Activities from "./Activities";

interface CommentsAndActivitiesProps {
  onSubmitComment: (comment: TaskComment) => void;
  taskId: number | null;
}

const CommentsAndActivities = ({
  taskId,
  onSubmitComment,
}: CommentsAndActivitiesProps) => {
  const { t } = useTranslation(["common", "dispatcher"]);

  const currentUser = getCurrentUser();
  const [view, setView] = useState("comments");

  const handleViewChange = (event: React.SyntheticEvent, newValue: string) => {
    setView(newValue);
  };

  return (
    <TabContext value={view}>
      <TabList onChange={handleViewChange} sx={{ mb: 2 }}>
        <Tab
          sx={{ p: "9px 16px 0" }}
          value="comments"
          label={
            <Typography
              sx={{
                fontSize: "14px",
                letterSpacing: "0.4px",
                fontWeight: 700,
                textTransform: "initial",
              }}
            >
              {t("comments")}
            </Typography>
          }
        />
        <Tab
          sx={{ p: "9px 16px 0" }}
          value="activities"
          label={
            <Typography
              sx={{
                fontSize: "14px",
                letterSpacing: "0.4px",
                fontWeight: 700,
                textTransform: "initial",
              }}
            >
              {t("activities")}
            </Typography>
          }
        />
      </TabList>
      <TabPanel value="comments">
        <Comments taskId={taskId} onSubmitComment={onSubmitComment} />
      </TabPanel>
      <TabPanel value="activities">
        <Activities taskId={taskId} />
      </TabPanel>
    </TabContext>
  );
};

export default CommentsAndActivities;
