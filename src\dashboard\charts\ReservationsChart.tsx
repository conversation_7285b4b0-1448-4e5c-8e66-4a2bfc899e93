import * as React from "react";
import { useTheme } from "@mui/material/styles";
import { LineChart } from "@mui/x-charts/LineChart";
import { ReservationsPerMonth } from "src/common/types/analytics";
import { format } from "date-fns";
import AreaGradient from "./AreaGradient";
import { ChartsAxisContentProps } from "@mui/x-charts/ChartsTooltip";
import CustomTooltipChart from "./CustomTooltipChart";

interface ReservationsChartProps {
  totalReservationsPerMonth: ReservationsPerMonth[];
}

const ReservationsChart: React.FC<ReservationsChartProps> = ({
  totalReservationsPerMonth,
}) => {
  const theme = useTheme();

  const months = totalReservationsPerMonth.map((item) =>
    item?.month ? format(new Date(item.month), "MMM") : ""
  );
  const reservations = totalReservationsPerMonth.map(
    (item) => item.totalReservations
  );

  const colorPalette: string[] = [
    theme.palette.secondary.light,
    theme.palette.secondary.main,
    theme.palette.secondary.dark,
  ];

  const ChartsAxisTooltipContent = (item: ChartsAxisContentProps) => {
    const dataIndex = item.dataIndex!;
    const month = totalReservationsPerMonth[dataIndex]?.month || "";
    const progress = totalReservationsPerMonth[dataIndex]?.progress || 0;
    const formattedDate =
      month !== "" ? format(new Date(month), "MMM, yyyy") : "";
    return (
      <CustomTooltipChart
        title={Number(item.series[0].data[item.dataIndex!]) || 0}
        label={progress}
        status={progress! >= 0}
        date={formattedDate}
      />
    );
  };

  return (
    <LineChart
      colors={colorPalette}
      xAxis={[
        {
          scaleType: "point",
          data: months,
          tickInterval: (index, i) => (i + 1) % 1 === 0,
        },
      ]}
      series={[
        {
          id: "reservations",
          label: "Reservations",
          showMark: false,
          curve: "monotoneX",
          stack: "total",
          area: true,
          stackOrder: "ascending",
          data: reservations,
        },
      ]}
      height={380}
      margin={{ left: 50, right: 20, top: 20, bottom: 20 }}
      sx={{
        "& .MuiAreaElement-series-reservations": {
          fill: "url('#reservations')",
        },
      }}
      slotProps={{
        legend: {
          hidden: true,
        },
      }}
      tooltip={{ axisContent: ChartsAxisTooltipContent }}
    >
      <AreaGradient color={theme.palette.secondary.light} id="reservations" />
    </LineChart>
  );
};

export default ReservationsChart;
