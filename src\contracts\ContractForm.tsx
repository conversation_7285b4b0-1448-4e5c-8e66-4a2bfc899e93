import { Autocomplete, DialogActions, Stack } from "@mui/material";
import { useFormik } from "formik";
import * as yup from "yup";
import { ContractFormValues, CreateContractDto, User } from "src/common/types";
import { useTranslation } from "react-i18next";
import { PriceList } from "src/common/types/priceList";
import { usePriceLists } from "src/common/api/priceList";
import { getCurrentUser } from "src/common/api";
import { turnContractFormValuesIntoCreateDto } from "src/common/utils";
import { CeTextField, CeButton } from "src/common/components";
import { useCompanies } from "src/common/api/company";
import { Company } from "src/common/types/company";

interface ContractFormProps {
  isLoading: boolean;
  handleClose: () => void;
  handleCreateNewContract?: (args: CreateContractDto) => void;
  initialFormValues: ContractFormValues;
}

export const ContractForm: React.FC<ContractFormProps> = ({
  handleClose,
  isLoading,
  handleCreateNewContract,
  initialFormValues,
}) => {
  const { t } = useTranslation(["common", "dispatcher"]);
  const currentUser = getCurrentUser();
  const {
    data: allPriceLists,
    isLoading: isLoadingPriceLists,
    refetch: refetchPricelists,
  } = usePriceLists(
    { expressions: [], sortModel: [], excludeVariants: true },
    Boolean(currentUser?.id)
  );

  const { data: allCompanies, isLoading: isCompaniesLoading } = useCompanies(
    { expressions: [], sortModel: [], limit: 1000, offset: 0 },
    Boolean(currentUser?.id)
  );

  const companies = allCompanies?.data || [];
  const pricelists = (allPriceLists?.data || []).filter(
    (pricelist) => !pricelist.contracts || pricelist.contracts.length === 0
  );

  const isFormLoading = isLoading || isLoadingPriceLists || isCompaniesLoading;

  const formik = useFormik<ContractFormValues>({
    initialValues: initialFormValues,
    validationSchema: yup.object({
      company: yup.object().required("company is required").nullable(),
      pricelist: yup.object().required("pricelist is required").nullable(),
    }),
    onSubmit: (values) => {
      if (initialFormValues.flow === "Request" && handleCreateNewContract) {
        const payload: CreateContractDto =
          turnContractFormValuesIntoCreateDto(values);
        handleCreateNewContract(payload);
      }
    },
  });
  return (
    <Stack
      component="form"
      spacing={2}
      noValidate
      onSubmit={formik.handleSubmit}
      sx={{ width: "500px" }}
    >
      <Stack direction={"row"} spacing={2}>
        <Autocomplete
          id="pricelist"
          fullWidth
          value={null}
          onChange={(event: any, nextValues: PriceList | null) => {
            formik.setFieldValue("pricelist", nextValues);
          }}
          onBlur={() => formik.setFieldTouched("pricelist", true)}
          isOptionEqualToValue={(option: PriceList, value: PriceList) =>
            option.id === value.id
          }
          getOptionLabel={(pricelist: PriceList) => pricelist.title || ""}
          options={pricelists.map((op) => op)}
          filterSelectedOptions
          disabled={isFormLoading}
          renderInput={(params) => (
            <CeTextField
              {...params}
              InputLabelProps={{ shrink: true }}
              label={t("pricelist")}
              size="small"
            />
          )}
        />

        <Autocomplete
          id="company"
          fullWidth
          value={null}
          onChange={(event: any, nextValues: Company | null) => {
            formik.setFieldValue("company", nextValues);
          }}
          onBlur={() => formik.setFieldTouched("company", true)}
          isOptionEqualToValue={(option: Company, value: Company) =>
            option.id === value.id
          }
          getOptionLabel={(company: Company) => company.name || ""}
          options={companies.map((op) => op)}
          filterSelectedOptions
          disabled={isFormLoading}
          renderInput={(params) => (
            <CeTextField
              {...params}
              InputLabelProps={{ shrink: true }}
              label={t("company")}
              size="small"
            />
          )}
        />
      </Stack>

      <Stack>
        <CeTextField
          fullWidth
          id="comment"
          name="comment"
          label={t("common:comments")}
          size="small"
          sx={{ mt: 2 }}
          inputProps={{ style: { height: "50px" } }}
          value={formik.values.comment}
          onChange={formik.handleChange}
          InputLabelProps={{ shrink: true }}
          disabled={isFormLoading}
        />
      </Stack>

      <DialogActions>
        <CeButton variant="text" onClick={handleClose} disabled={isFormLoading}>
          {t("cancel")}
        </CeButton>
        <CeButton type="submit" disabled={isFormLoading}>
          {t("submit")}
        </CeButton>
      </DialogActions>
    </Stack>
  );
};
