import { useWindowSize } from "@react-hook/window-size";
import { OperatorsDatagrid } from "./datagrid/OperatorsDatagrid";
import { getCurrentUser, useUsers } from "src/common/api";
import { useState } from "react";
import { CePaper } from "src/common/components";
import { turnDatagridFilterIntoExpressions } from "src/common/utils";
import { Expression } from "src/common/types";
import { GridFilterModel, GridSortModel } from "@mui/x-data-grid";
import usePersistentGridState from "src/common/utils/gridState";
import { useLocation } from "react-router-dom";

export const Operators = () => {
  const [, height] = useWindowSize();
  const currentUser = getCurrentUser();

  const location = useLocation();
  const localStorageKey = location.pathname.slice(1);
  const [gridState, updateGridStatePart] = usePersistentGridState(
    localStorageKey,
    1,
    20,
    [{ field: "name", sort: "asc" }]
  );

  const expression = turnDatagridFilterIntoExpressions(gridState.filterModel);
  const [expressions, setExpressions] = useState<Expression[]>(expression);
  const [sortingModelOptions, setSortingModelOptions] = useState<GridSortModel>(
    gridState.sortModel
  );

  const excludeSelfExpression: Expression = {
    category: '"user"."id"',
    operator: "!=",
    value: currentUser?.id,
    conditionType: "AND",
  };

  const {
    data: allUsers,
    isLoading: isLoadingUsers,
    refetch: refetchUsers,
  } = useUsers(
    {
      expressions: [...expressions, excludeSelfExpression],
      sortModel: sortingModelOptions,
      limit: gridState.pageSize,
      offset: (gridState.page - 1) * gridState.pageSize,
    },
    Boolean(currentUser?.id)
  );

  const handlePageChange = (newPage: number) => {
    updateGridStatePart("page", newPage + 1);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    updateGridStatePart("pageSize", newPageSize);
  };

  const handleSortModelChange = (sortModel: GridSortModel) => {
    setSortingModelOptions(sortModel);
  };

  const onDatagridFiltersChange = (filterModel: GridFilterModel) => {
    const expressions = turnDatagridFilterIntoExpressions(filterModel);
    setExpressions(expressions);
  };

  return (
    <CePaper sx={{ height: `${height - 100}px`, p: 2 }}>
      <OperatorsDatagrid
        data={allUsers?.data || []}
        isFetchingOperators={isLoadingUsers}
        refetchOperators={refetchUsers}
        shouldRenderRefreshButton
        shouldRenderAddButton
        shouldRenderEditActionsColumn
        total={allUsers?.totalCount || 0}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        gridState={gridState}
        updateGridStatePart={updateGridStatePart}
        isServerDriven
        handleSortModelChange={handleSortModelChange}
        onDatagridFiltersChange={onDatagridFiltersChange}
      />
    </CePaper>
  );
};
