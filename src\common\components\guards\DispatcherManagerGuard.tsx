import type { FC, ReactNode } from "react";
import { isAuthenticated } from "src/common/api/auth";
import { Role } from "src/common/types";

interface DispatcherManagerGuardProps {
  children: ReactNode;
}
export const DispatcherManagerGuard: FC<DispatcherManagerGuardProps> = ({
  children,
}) => {
  const { role } = isAuthenticated();

  if (role === Role.DISPATCHER_MANAGER) {
    return <>{children}</>;
  }

  return null;
};
