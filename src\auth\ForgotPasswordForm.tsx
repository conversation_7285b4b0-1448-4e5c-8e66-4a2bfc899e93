import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  Typography
} from "@mui/material";
import { useFormik } from "formik";
import { useNavigate } from "react-router-dom";
import * as yup from "yup";

import { ForgotPasswordDto } from "src/common/types";
import { useTranslation } from "react-i18next";
import { buttonTextTransform } from "src/common/components/custom/customCss";
import { CeCard, CeTextField } from "src/common/components";

interface ForgotPasswordFormProps {
  handleSubmitForm: (attrs: ForgotPasswordDto) => void;
  isLoading: boolean;
}

export const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({
  handleSubmitForm,
  isLoading
}) => {
  const { t } = useTranslation("common");
  const navigate = useNavigate();

  const formik = useFormik({
    initialValues: {
      email: ""
    },
    validationSchema: yup.object({
      email: yup
        .string()
        .email("Enter a valid email")
        .required("Email is required")
    }),
    onSubmit: (values) => {
      handleSubmitForm(values);
    }
  });

  return (
    <>
      <CeCard sx={{ minWidth: 275, padding: 2 }}>
        <CardContent>
          <Stack
            component="form"
            sx={{ width: "30ch" }}
            spacing={2}
            noValidate
            onSubmit={formik.handleSubmit}
          >
            <Stack>
              <Typography
                variant="h5"
                textAlign="center"
                sx={{ marginTop: 0, marginBottom: 0 }}
              >
                {t("forgot-password")}
              </Typography>
              <Typography
                textAlign="center"
                color="text.secondary"
                sx={{ marginBottom: 2, fontSize: "14px", marginTop: "5px" }}
              >
                {t("enter-email")}
              </Typography>
            </Stack>

            <CeTextField
              fullWidth
              id="email"
              name="email"
              label={t("email")}
              type="email"
              size="small"
              value={formik.values.email}
              onChange={formik.handleChange}
              error={formik.touched.email && Boolean(formik.errors.email)}
              helperText={formik.touched.email && formik.errors.email}
              disabled={isLoading}
              required
            />
            <Button
              color="primary"
              variant="contained"
              fullWidth
              type="submit"
              disabled={isLoading}
              sx={buttonTextTransform}
            >
              {t("send-email")}
            </Button>
          </Stack>
        </CardContent>
        <CardActions sx={{ justifyContent: "space-between" }}>
          <Button
            sx={buttonTextTransform}
            size="small"
            onClick={() => navigate("/auth/signin")}
          >
            {t("sign-in")}
          </Button>
        </CardActions>
      </CeCard>
    </>
  );
};
