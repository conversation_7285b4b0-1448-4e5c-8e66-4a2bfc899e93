import { userRolesDisplay } from "../constants";
import { UpdateUserDto, User, UserSettingsFormValues } from "../types";
import countries from "./countries";
import { turnDateIntoStrWithoutTimezone } from "./formatDate";

export const getRoles = (role: number): string => {
  const activeRole = userRolesDisplay.find((r) => r.id === role);

  return activeRole?.title || "";
};

export const turnUserIntoFormValues = (user: User): UserSettingsFormValues => {
  const country = countries.find((c) => c.value === user.country);
  return {
    firstName: user.firstName || "",
    lastName: user.lastName || "",
    birthdate: user.birthdate ? new Date(user.birthdate) : null,
    address: user.address || "",
    secondaryAddress: user.secondaryAddress || "",
    zipCode: user.zipCode?.toString() || "",
    city: user.city || "",
    country: country || null,
    phoneNumber: user.phoneNumber || "",
    phoneNumberPersonal: user.phoneNumberPersonal || "",
    email: user.email || "",
  };
};

export const turnUserFormValuesIntoUpdateDto = (
  values: UserSettingsFormValues
): UpdateUserDto => {
  const {
    userId,
    firstName,
    lastName,
    birthdate,
    address,
    secondaryAddress,
    zipCode,
    city,
    country,
    phoneNumber,
    phoneNumberPersonal,
    email,
  } = values;
  return {
    userId: userId!,
    firstName,
    lastName,
    birthdate: birthdate ? turnDateIntoStrWithoutTimezone(birthdate) : "",
    address,
    secondaryAddress,
    zipCode,
    city,
    country: country?.value || "",
    phoneNumber,
    phoneNumberPersonal,
    email,
  };
};

export const getUserFullName = (
  firstName: string | null,
  lastName: string | null
) => `${firstName || ""} ${lastName || ""}`;
