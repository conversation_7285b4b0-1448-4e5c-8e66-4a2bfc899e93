import { DialogActions, FormControl, Stack, Typography } from "@mui/material";
import React, { useRef } from "react";
import { useTranslation } from "react-i18next";
import { CeButton, MainModal } from "src/common/components";
import SignatureCanvas from "react-signature-canvas";
import { useFormik } from "formik";
import * as yup from "yup";
import { SignatureFormValue } from "src/common/types";
import { useAddJobSignature } from "src/common/api";

interface SignatureModalProps {
    isOpen: boolean;
    handleClose: () => void;
    jobId: number
}

const SignatureModal: React.FC<SignatureModalProps> = ({ isOpen, handleClose, jobId }) => {
    const { t } = useTranslation(["common"]);
    const signatureCanvasRef = useRef<any>(null);

    const {
        mutateAsync: handleAddJobSignatureAsync,
        isLoading: isAddJobSignatureLoading,
      } = useAddJobSignature();

    const formik = useFormik<SignatureFormValue>({
        initialValues: {
            signature: "",
            jobId
        },
        validationSchema: yup.object({
            signature: yup.string().required(t("signature-required")),
        }),
        onSubmit: (values) => { 
            handleAddJobSignatureAsync(values)
            handleModalClose();
        },
    });

    const clearSignature = () => {
        signatureCanvasRef.current?.clear();
        formik.setFieldValue("signature", "");
    };

    const handleModalClose = () => {
        clearSignature();
        formik.setTouched({ signature: false });
        handleClose();
    };

    return (
        <MainModal
            title={t("signature")}
            isOpen={isOpen}
            handleClose={handleModalClose}
            maxWidth="md"
        >
            <Stack
                component="form"
                onSubmit={formik.handleSubmit}>
                    <Typography variant="subtitle1" sx={{ marginBottom: 1, textAlign:'center' }}>
                    {t("please-sign-here")}
                    </Typography>
                <FormControl>
                    <SignatureCanvas
                        ref={signatureCanvasRef}
                        canvasProps={{ width: 300, height: 200,  style: { borderRadius: "10px", border: "2px solid #ccc" }  }}
                        onEnd={() => {
                            formik.setFieldValue(
                                "signature",
                                signatureCanvasRef.current?.toDataURL()
                            );
                        }}
                    />
                    {formik.errors.signature && formik.touched.signature && (
                        <p style={{ color: "red", marginBottom:0 }}>{formik.errors.signature}</p>
                    )}
                    <DialogActions>
                        <CeButton 
                            onClick={formik.values.signature ? clearSignature : handleModalClose} 
                            variant="text"
                            disabled={isAddJobSignatureLoading}
                            >
                            {formik.values.signature ? t("clear") : t("cancel")}
                        </CeButton>
                        <CeButton 
                        type="submit"
                        disabled={isAddJobSignatureLoading}
                        >
                            {t("confirm")}
                        </CeButton>
                    </DialogActions>
                </FormControl>
            </Stack>
        </MainModal>
    );
};

export default SignatureModal;
