import * as React from "react";
import { useTheme } from "@mui/material/styles";
import { LineChart } from "@mui/x-charts/LineChart";
import { ExpensePerMonth } from "src/common/types/analytics";
import { format } from "date-fns";
import AreaGradient from "./AreaGradient";
import { ChartsAxisContentProps } from "@mui/x-charts/ChartsTooltip";
import CustomTooltipChart from "./CustomTooltipChart";

interface ExpensesChartProps {
  totalExpensesPerMonth: ExpensePerMonth[];
}

const ExpenseChart: React.FC<ExpensesChartProps> = ({
  totalExpensesPerMonth,
}) => {
  const theme = useTheme();

  const months = totalExpensesPerMonth.map((item) =>
    item?.month ? format(new Date(item.month), "MMM") : ""
  );

  const expenses = totalExpensesPerMonth.map((item) => item.totalExpenses);

  const colorPalette: string[] = [
    theme.palette.primary.light,
    theme.palette.primary.main,
    theme.palette.primary.dark,
  ];
  const ChartsAxisTooltipContent = (item: ChartsAxisContentProps) => {
    const dataIndex = item.dataIndex!;
    const month = totalExpensesPerMonth[dataIndex]?.month || "";
    const progress = totalExpensesPerMonth[dataIndex]?.progress || 0;
    const formattedDate =
      month !== "" ? format(new Date(month), "MMM, yyyy") : "";
    return (
      <CustomTooltipChart
        title={Number(item.series[0].data[item.dataIndex!]) || 0}
        unit="€"
        label={progress}
        status={progress! >= 0}
        date={formattedDate}
      />
    );
  };

  return (
    <LineChart
      colors={colorPalette}
      xAxis={[
        {
          scaleType: "point",
          data: months,
          tickInterval: (index, i) => (i + 1) % 1 === 0,
        },
      ]}
      series={[
        {
          id: "expense",
          label: "Expense",
          showMark: false,
          curve: "monotoneX",
          stack: "total",
          area: true,
          stackOrder: "ascending",
          data: expenses,
        },
      ]}
      height={380}
      margin={{ left: 50, right: 20, top: 20, bottom: 20 }}
      sx={{
        "& .MuiAreaElement-series-expense": {
          fill: "url('#expense')",
        },
      }}
      slotProps={{
        legend: {
          hidden: true,
        },
      }}
      tooltip={{ axisContent: ChartsAxisTooltipContent }}
    >
      <AreaGradient color={theme.palette.primary.light} id="expense" />
    </LineChart>
  );
};

export default ExpenseChart;
