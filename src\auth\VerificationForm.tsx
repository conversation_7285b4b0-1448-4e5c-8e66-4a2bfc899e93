import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  Typography,
} from "@mui/material";
import { useFormik } from "formik";
import { useNavigate } from "react-router-dom";
import { buttonTextTransform } from "src/common/components/custom/customCss";
import { <PERSON><PERSON>ard, CeTextField } from "src/common/components";
import * as yup from "yup";

interface VerificationFormProps {
  isLoading: boolean;
}

export const VerificationForm: React.FC<VerificationFormProps> = ({
  isLoading,
}) => {
  const navigate = useNavigate();

  const formik = useFormik({
    initialValues: {
      authCode: "",
    },
    validationSchema: yup.object({
      authCode: yup.string().required("Code is required"),
    }),
    onSubmit: () => {},
  });

  return (
    <>
      <CeCard sx={{ minWidth: 275, padding: 2 }}>
        <CardContent>
          <Stack
            component="form"
            sx={{ width: "30ch" }}
            spacing={2}
            noValidate
            onSubmit={formik.handleSubmit}
          >
            <Stack>
              <Typography
                variant="h5"
                textAlign="center"
                sx={{ marginTop: 0, marginBottom: 0 }}
              >
                Enter Code
              </Typography>
              <Typography
                textAlign="center"
                color="text.secondary"
                sx={{ marginBottom: 2, fontSize: "14px", marginTop: 1 }}
              >
                A verification code has been sent to your email. This code will
                be valid for 15 minutes.
              </Typography>
            </Stack>

            <CeTextField
              fullWidth
              id="authCode"
              name="authCode"
              label="Code"
              type="authCode"
              size="small"
              value={formik.values.authCode}
              onChange={formik.handleChange}
              error={formik.touched.authCode && Boolean(formik.errors.authCode)}
              helperText={formik.touched.authCode && formik.errors.authCode}
              disabled={isLoading}
              required
            />
            <Button
              color="primary"
              variant="contained"
              fullWidth
              type="submit"
              disabled={isLoading}
              sx={buttonTextTransform}
            >
              Submit
            </Button>
          </Stack>
        </CardContent>
        <CardActions sx={{ justifyContent: "space-between" }}>
          <Button
            sx={buttonTextTransform}
            size="small"
            onClick={() => navigate("/auth/signin")}
          >
            Back to login
          </Button>
        </CardActions>
      </CeCard>
    </>
  );
};
