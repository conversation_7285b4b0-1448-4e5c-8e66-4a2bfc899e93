import {
  CreateVehicleModelDto,
  EVehicleType,
  VehicleModelFormValues,
  VehicleModelModalFlow,
  VehicleModels,
  VehicleTypes,
} from "../types";
import * as yup from "yup";

export const turnVehicleModelIntoFormValues = (
  vehicleModel: VehicleModels,
  flow: VehicleModelModalFlow
): VehicleModelFormValues => {
  const payload: VehicleModelFormValues = {
    ...vehicleModel,
    flow,
    vehicleModelId: vehicleModel.id,
    type: vehicleModel.type || null,
    boomSize: vehicleModel.boomSize || null,
    operatorManagerId: vehicleModel.operatorManagerId || null,
  };

  return payload;
};

export const turnVehicleModelFormValuesIntoDto = (
  values: VehicleModelFormValues
): CreateVehicleModelDto => {
  const { type, ...attrs } = values;
  return {
    ...values,
    vehicleTypeId: type?.id!,
  };
};

export const getValidationSchemaByType = (
  type: VehicleTypes | undefined,
  vehicleValuesRanges: Record<string, { min: number; max: number }>
) => {
  switch (type) {
    case EVehicleType.CityPump:
      return yup
        .number()
        .min(vehicleValuesRanges["cityPump"].min, "common:out-of-range")
        .max(vehicleValuesRanges["cityPump"].max, "common:out-of-range")
        .required("required");
    case EVehicleType.Pump:
    case EVehicleType.MixoPump:
    case EVehicleType.Stationary:
    default:
      return yup
        .number()
        .min(vehicleValuesRanges["boomSize"].min, "common:out-of-range")
        .max(vehicleValuesRanges["boomSize"].max, "common:out-of-range")
        .required("required");
  }
};
