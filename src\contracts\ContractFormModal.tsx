import { AxiosError } from "axios";
import { UseMutateFunction } from "react-query";
import { MainModal } from "src/common/components";
import {
  Contract,
  ContractFormValues,
  CreateContractDto,
} from "src/common/types";
import { ContractForm } from "./ContractForm";

interface ContractModalProps {
  isLoading: boolean;
  initialFormValues: ContractFormValues;
  handleCloseContractModal: () => void;
  handleCreateNewContract?: UseMutateFunction<
    Contract,
    AxiosError<CreateContractDto, CreateContractDto> | Error,
    CreateContractDto,
    () => void
  >;
}
export const ContractFormModal: React.FC<ContractModalProps> = ({
  isLoading,
  initialFormValues,
  handleCloseContractModal,
  handleCreateNewContract,
}) => {
  return (
    <MainModal
      title={`${initialFormValues.flow || ""} Contract`}
      isOpen={!!initialFormValues.flow}
      handleClose={handleCloseContractModal}
    >
      <ContractForm
        initialFormValues={initialFormValues}
        isLoading={isLoading}
        handleCreateNewContract={handleCreateNewContract}
        handleClose={handleCloseContractModal}
      />
    </MainModal>
  );
};
