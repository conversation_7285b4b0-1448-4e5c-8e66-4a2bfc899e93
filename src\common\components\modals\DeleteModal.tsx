import { Breakpoint, DialogActions } from "@mui/material";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import { CeDialog, CeButton } from "..";

interface DeleteModalProps {
  isOpen: boolean;
  title: string;
  helperText?: string | JSX.Element;
  handleClose: () => void;
  handleSubmit: () => void;
  maxWidth?: false | Breakpoint | undefined;
  isLoading: boolean;
}
export const DeleteModal: React.FC<DeleteModalProps> = ({
  isOpen,
  title,
  helperText,
  handleClose,
  handleSubmit,
  maxWidth,
  isLoading
}) => {
  return (
    <CeDialog
      open={isOpen}
      onClose={handleClose}
      maxWidth={maxWidth || "md"}
      aria-labelledby={`main-dialog-${title}`}
    >
      <DialogTitle>{title}</DialogTitle>
      <DialogContent dividers>
        <DialogContentText
          sx={{ marginBottom: 2, marginTop: 0, width: "400px" }}
        >
          {helperText || "Are you sure?"}
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <CeButton onClick={handleClose} disabled={isLoading} variant="text">
          Cancel
        </CeButton>
        <CeButton
          onClick={handleSubmit}
          disabled={isLoading}
          variant="contained"
        >
          Submit
        </CeButton>
      </DialogActions>
    </CeDialog>
  );
};
