import {
  Avatar,
  AvatarGroup,
  DialogContent,
  Stack,
  Typography,
} from "@mui/material";
import { usePriceList } from "src/common/api/priceList";
import { MainModal } from "src/common/components";
import CustomizedAccordions, {
  AccordionItem,
} from "src/common/components/custom/CustomizedAccordion";
import { CeButton } from "src/common/components";
import { ContractResponse } from "src/common/types";
import PriceListView from "src/vehicles/vehicleInfoModal/PriceList";
import { stringAvatar } from "src/common/utils/avatar";
import ContractDropDownActionButtons from "./ContractDropDownActionButtons";

interface PreviewListModalProps {
  pricelistId: number | null;
  isOpen: boolean;
  handleClose: () => void;
  isLoading: boolean;
  onRespond: (values: ContractResponse) => void;
}
const PriceListReviewModal = ({
  pricelistId,
  isOpen,
  handleClose,
  isLoading,
  onRespond,
}: PreviewListModalProps) => {
  const { data: pricelist, isLoading: isLoadingPricelist } = usePriceList(
    pricelistId,
    Boolean(pricelistId)
  );
  const isAllLoading = isLoading || isLoadingPricelist;
  return (
    <MainModal
      title="Contract Proposal"
      isOpen={isOpen}
      handleClose={handleClose}
      shouldRenderDialogActions
      dialogActionStyling={{ display: "flex", gap: 1, px: 4.5, py: 2 }}
      actionsChildren={
        <>
          <CeButton
            onClick={handleClose}
            size="large"
            variant="text"
            disabled={isAllLoading}
          >
            Cancel
          </CeButton>
          <ContractDropDownActionButtons onRespond={onRespond} />
        </>
      }
    >
      <DialogContent sx={{ px: 0, py: 3 }}>
        {pricelist ? (
          <CustomizedAccordions>
            <AccordionItem
              key={String(pricelist.id)}
              id={String(pricelist.id)}
              title={
                <Stack
                  sx={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    gap: 1,
                  }}
                >
                  <Typography fontSize={2} variant="body1">
                    {pricelist?.title}
                  </Typography>
                  {/* {pricelist?.title? (
                    <AvatarGroup max={4}>
                      <Avatar
                        {...stringAvatar(
                          pricelist.vehicleSize?.boomSize?.toString() || ""
                        )}
                        sx={{
                          marginRight: 1,
                          width: 26,
                          height: 26,
                          fontSize: 14,
                        }}
                      />
                    </AvatarGroup>
                  ) : null} */}
                </Stack>
              }
            >
              <PriceListView priceList={pricelist} />
            </AccordionItem>
          </CustomizedAccordions>
        ) : (
          <Typography>Pricelist was not found!</Typography>
        )}
      </DialogContent>
    </MainModal>
  );
};

export default PriceListReviewModal;
