import {
  Breakpoint,
  DialogActions,
  Stack,
  SxProps,
  Theme
} from "@mui/material";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import { CeDialog } from "../../components";
import { ReactNode } from "react";

interface MainModalProps {
  isOpen: boolean;
  title: string;
  helperText?: string;
  handleClose: () => void;
  maxWidth?: false | Breakpoint | undefined;
  isAppointmentForm?: boolean;
  titlePosition?: string;
  helperTextProps?: SxProps<Theme>;
  PaperProps?: object;
  dialogActionStyling?: SxProps<Theme>;
  shouldRenderDialogActions?: boolean;
  actionsChildren?: ReactNode;
}
export const MainModal: React.FC<MainModalProps> = ({
  isOpen,
  title,
  helperText,
  handleClose,
  children,
  maxWidth,
  isAppointmentForm,
  titlePosition,
  helperTextProps,
  dialogActionStyling,
  shouldRenderDialogActions,
  actionsChildren
}) => {
  return (
    <CeDialog
      open={isOpen}
      onClose={handleClose}
      maxWidth={maxWidth || "md"}
      aria-labelledby={`main-dialog-${title}`}
    >
      <Stack
        direction="row"
        spacing={2}
        justifyContent={titlePosition ?? "center"}
        alignItems="center"
      >
        {isAppointmentForm && title?.includes("Update")}
        <DialogTitle>{title}</DialogTitle>
      </Stack>
      <DialogContent dividers>
        {helperText ? (
          <DialogContentText
            sx={{ marginBottom: 2, marginTop: 0, ...helperTextProps }}
          >
            {helperText}
          </DialogContentText>
        ) : null}
        {children}
      </DialogContent>
      {shouldRenderDialogActions && (
        <DialogActions sx={dialogActionStyling}>
          {actionsChildren}
        </DialogActions>
      )}
    </CeDialog>
  );
};
