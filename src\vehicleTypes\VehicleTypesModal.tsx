import { AxiosError } from "axios";
import { UseMutateFunction } from "react-query";
import { MainModal } from "src/common/components";
import { SetterOrUpdater } from "recoil";
import { CreateVehicleTypeDto, UpdateVehicleTypeDto, VehicleTypeFormValues, VehicleType } from "src/common/types";
import { VehicleTypesForm } from "./VehicleTypesForm";


interface VehicleTypeModalProps {
  isLoading: boolean;
  initialFormValues: VehicleTypeFormValues;
  setInitialFormValues:SetterOrUpdater<VehicleTypeFormValues>
  handleCloseVehicleModal: () => void;
  handleCreateNewVehicleType?: UseMutateFunction<
    VehicleType,
    AxiosError<CreateVehicleTypeDto, CreateVehicleTypeDto> | Error,
    CreateVehicleTypeDto,
    () => void
  >;
  handleUpdateVehicleType?: UseMutateFunction<
    VehicleType,
    AxiosError<UpdateVehicleTypeDto, UpdateVehicleTypeDto> | Error,
    UpdateVehicleTypeDto,
    () => void
  >;
  
}
export const VehicleTypeModal: React.FC<VehicleTypeModalProps> = ({
  isLoading,
  initialFormValues,
  setInitialFormValues,
  handleCloseVehicleModal,
  handleCreateNewVehicleType,
  handleUpdateVehicleType,
}) => {
  return (
    <MainModal
      title={`${initialFormValues.flow || ""} Vehicle Type`}
      isOpen={!!initialFormValues.flow}
      handleClose={handleCloseVehicleModal}
    >
      <VehicleTypesForm
        initialFormValues={initialFormValues}
        isLoading={isLoading}
        setInitialFormValues={setInitialFormValues}
        handleCreateNewVehicleType={handleCreateNewVehicleType}
        handleUpdateVehicleType={handleUpdateVehicleType}
        handleClose={handleCloseVehicleModal}
      />
    </MainModal>
  );
};
