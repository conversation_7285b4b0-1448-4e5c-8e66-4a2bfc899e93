import { useWindowSize } from "@react-hook/window-size";
import { getCurrentUser, useReservations } from "src/common/api";
import { ReservationsDatagrid } from "./datagrid/ReservationDatagrid";
import { Role } from "src/common/types";
import { getReservationsParamsByUserRoles, turnDatagridFilterIntoExpressions } from "src/common/utils";
import { useState } from "react";
import { CePaper } from "src/common/components";
import { Company } from "src/common/types/company";
import { Expression } from "src/common/types";
import { GridFilterModel, GridSortModel } from "@mui/x-data-grid";
import { useLocation } from "react-router-dom";
import usePersistentGridState from "src/common/utils/gridState";

export const Reservations = () => {
  const currentUser = getCurrentUser();

  const [, height] = useWindowSize();

  const role = currentUser?.role || null;
  const userId = currentUser?.id || null;

  const location = useLocation();
  const localStorageKey = location.pathname.slice(1); 

  const attrs = getReservationsParamsByUserRoles(role, userId);
  const [gridState, updateGridStatePart] = usePersistentGridState(
    localStorageKey,
    1,  
    20,
    [{ field: "name", sort: "asc" }]
  );

 const expression = turnDatagridFilterIntoExpressions(gridState.filterModel);
  const [expressions, setExpressions] = useState<Expression[]>(expression);
  const [sortingModelOptions, setSortingModelOptions] = useState<GridSortModel>(
    gridState.sortModel
  );

  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const {
    data: allReservations,
    isLoading: isLoadingReservations,
    refetch: refetchReservations,
  } = useReservations(
    {
      ...attrs,
      limit: gridState.pageSize,
      offset: (gridState.page - 1) * gridState.pageSize,
      dispatcherCompanyId: selectedCompany?.id,
      expressions,
      sortModel: sortingModelOptions,
    },
    Boolean(currentUser?.id)
  );

  const handlePageChange = (newPage: number) => {
    updateGridStatePart("page", newPage + 1);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    updateGridStatePart("pageSize", newPageSize);
  };
  
  const handleSortModelChange = (sortModel: GridSortModel) => {
    setSortingModelOptions(sortModel)
  }

  const onDatagridFiltersChange = (filterModel: GridFilterModel) => {
    const expressions = turnDatagridFilterIntoExpressions(filterModel)
    setExpressions(expressions)
  }

  return (
    <CePaper sx={{ height: `${height - 100}px`, p: 2 }}>
      <ReservationsDatagrid
        data={allReservations?.data || []}
        selectedCompany={selectedCompany}
        setSelectedCompany={setSelectedCompany}
        isFetchingReservations={isLoadingReservations}
        refetchReservations={refetchReservations}
        shouldRenderRefreshButton
        shouldRenderAddButton={role === Role.OEPRATOR_MANAGER}
        shouldRenderEditActionsColumn
        page={gridState.page}
        pageSize={gridState.pageSize}
        total={allReservations?.totalCount || 0}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        gridState={gridState}
        updateGridStatePart={updateGridStatePart}
        isServerDriven
        handleSortModelChange={handleSortModelChange}
        onDatagridFiltersChange={onDatagridFiltersChange}
      />
    </CePaper>
  );
};
