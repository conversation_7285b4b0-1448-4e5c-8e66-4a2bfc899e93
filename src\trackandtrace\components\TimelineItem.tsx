import { <PERSON>, Button, Typography } from "@mui/material";
import CheckIcon from "@mui/icons-material/Check";
import { Job, JobStatus } from "src/common/types";
import { JOB_STEPS } from "src/common/constants";
import { useTranslation } from "react-i18next";
import { getJobEventTimestamp, getStepIconColor } from "src/common/utils/reservationDetails";
import Timeline from "@mui/lab/Timeline";
import TimelineItem from "@mui/lab/TimelineItem";
import TimelineSeparator from "@mui/lab/TimelineSeparator";
import TimelineConnector from "@mui/lab/TimelineConnector";
import TimelineContent from "@mui/lab/TimelineContent";
import TimelineDot from "@mui/lab/TimelineDot";
import { CeButton } from "src/common/components";
import { useState } from "react";
import SignatureModal from "./SignatureModal";

interface TimelineItemsProps {
  jobDetails: Job | null;
}

const TimelineItems: React.FC<TimelineItemsProps> = ({ jobDetails }) => {
  const { t } = useTranslation(["common"]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  if (!jobDetails) return null;
  const { jobEvents, status, progress } = jobDetails;

  const reservationCancelled = status === JobStatus.CANCELLED;
  const isSignatureStep = status === JobStatus.SIGNATURE;

  const stepBeforeCancel = JOB_STEPS.find(
    (step) => step.name === jobEvents.slice(-2)[0]?.currentStatus
  );

  const handleOpenModal = () => setIsModalOpen(true);
  const handleCloseModal = () => setIsModalOpen(false);

  return (
    <>
      <Timeline
        sx={{
          [`& .MuiTimelineItem-root:before`]: { flex: 0, padding: 0 },
        }}
        position="right"
      >
        {JOB_STEPS.map((step, index) =>
          step.name !== JobStatus.CANCELLED ? (
            <TimelineItem key={step.name}>
              <TimelineSeparator>
                <TimelineDot
                  sx={{
                    bgcolor: getStepIconColor(
                      step.name,
                      reservationCancelled
                        ? stepBeforeCancel?.name!
                        : status!,
                      reservationCancelled
                        ? stepBeforeCancel?.progress!
                        : progress!,
                      jobEvents
                    ),

                  }}
                  variant="filled"
                >
                  <CheckIcon fontSize="small" />
                </TimelineDot>
                {index < JOB_STEPS.length - 1 && <TimelineConnector sx={{ bgcolor: "grey.400" }} />}
              </TimelineSeparator>
              <TimelineContent sx={{ py: "12px", px: 2 }}>
                <Typography variant="body1" fontWeight={700}>
                  {t(step.label)}
                </Typography>
                <Typography color="text.secondary" variant="caption">
                  {getJobEventTimestamp(jobEvents, step.name)}
                </Typography>
                {step.name === "SIGNATURE" && isSignatureStep && (
                  <Box>
                    <CeButton
                      variant="contained"
                      color="primary"
                      size="small"
                      sx={{ mt: 1 }}
                      onClick={handleOpenModal}
                    >
                      {t("add-signature")}
                    </CeButton>
                  </Box>
                )}
              </TimelineContent>
            </TimelineItem>
          ) : null
        )}
      </Timeline>
      <SignatureModal 
        isOpen={isModalOpen} 
        handleClose={handleCloseModal}
        jobId={jobDetails.id}
        />
    </>
  );
};

export default TimelineItems;
