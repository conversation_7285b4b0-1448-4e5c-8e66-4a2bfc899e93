import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  <PERSON>ack,
  Typography,
} from "@mui/material";
import * as yup from "yup";
import { useFormik } from "formik";
import { FC } from "react";
import { SetterOrUpdater } from "recoil";
import { useTranslation } from "react-i18next";

import { SecurityValidationFormValues } from "src/common/types";
import PreviewImage from "src/common/components/custom/PreviewImage";
import { CeTextField } from "src/common/components";
import { buttonTextTransform } from "src/common/components/custom/customCss";
import { useDeleteFile } from "src/common/api";

interface SecurityValidationFormProps {
  isLoading: boolean;
  initialFormValues: SecurityValidationFormValues;
  setInitialFormValues: SetterOrUpdater<SecurityValidationFormValues>;
}
export const SecurityValidationForm: FC<SecurityValidationFormProps> = ({
  isLoading,
  initialFormValues,
  setInitialFormValues,
}) => {
  const { t } = useTranslation(["common", "manager", "dispatcher", "operator"]);

  const {
    mutate: handleDeleteFile,
    isLoading: isDeletingFile,
    isSuccess: isDeleteFileSuccess,
  } = useDeleteFile();

  const formik = useFormik<SecurityValidationFormValues>({
    initialValues: initialFormValues,
    enableReinitialize: true,
    validationSchema: yup.object({}),
    onSubmit: () => {},
  });

  const handleRemoveImage = (
    fieldName: keyof SecurityValidationFormValues,
    pathField: keyof SecurityValidationFormValues,
    riskField: keyof SecurityValidationFormValues
  ) => {
    const fileKey = initialFormValues[pathField] as string;
    setInitialFormValues((prevValues) => {
      const newValues = {
        ...prevValues,
        [fieldName]: null,
        ...(pathField ? { [pathField]: "" } : {}),
        [riskField]: false,
      };
      return newValues;
    });
    if (fileKey) {
      handleDeleteFile(fileKey);
    }
  };

  return (
    <Stack spacing={5} mt={2}>
      <Box>
        <Stack
          direction="row"
          alignItems="center"
          justifyContent={"space-between"}
        >
          <FormControl>
            <FormControlLabel
              checked={formik.values.isElectricalRisk}
              control={<Checkbox />}
              label={t("electrical-risk")}
              name="isElectricalRisk"
              onChange={(event: any) =>
                setInitialFormValues((values: SecurityValidationFormValues) => {
                  return {
                    ...values,
                    isElectricalRisk: event.target.checked,
                  };
                })
              }
            />
          </FormControl>

          <Button
            variant="contained"
            component="label"
            size="small"
            sx={buttonTextTransform}
          >
            {t("upload")}
            <input
              type="file"
              hidden
              accept="image/*"
              onChange={(event: any) => {
                const file = event.target?.files[0];
                if (file) {
                  setInitialFormValues(
                    (values: SecurityValidationFormValues) => {
                      return {
                        ...values,
                        electricalRiskFile: file,
                        isElectricalRisk: true,
                      };
                    }
                  );
                }
              }}
            />
          </Button>
        </Stack>

        <CeTextField
          fullWidth
          id="electricalRiskComment"
          name="electricalRiskComment"
          label={t("common:comment")}
          size="small"
          value={formik.values.electricalRiskComment}
          onChange={(event) => {
            setInitialFormValues((values: SecurityValidationFormValues) => {
              return {
                ...values,
                electricalRiskComment: event.target.value,
              };
            });
          }}
          InputLabelProps={{ shrink: true }}
          disabled={isLoading}
        />

        <PreviewImage
          file={initialFormValues.electricalRiskFile}
          fallbackPath={initialFormValues.electricalRiskKey}
          onRemove={() =>
            handleRemoveImage(
              "electricalRiskFile",
              "electricalRiskKey",
              "isElectricalRisk"
            )
          }
        />
      </Box>

      <Box>
        <Stack
          direction="row"
          alignItems="center"
          justifyContent={"space-between"}
        >
          <FormControl>
            <FormControlLabel
              checked={formik.values.isAccessCompliance}
              control={<Checkbox />}
              label={t("access-compliance")}
              name="isAccessCompliance"
              onChange={(event: any) =>
                setInitialFormValues((values: SecurityValidationFormValues) => {
                  return {
                    ...values,
                    isAccessCompliance: event.target.checked,
                  };
                })
              }
            />
          </FormControl>
          <Button
            variant="contained"
            component="label"
            size="small"
            sx={buttonTextTransform}
          >
            {t("upload")}
            <input
              type="file"
              hidden
              accept="image/*"
              onChange={(event: any) => {
                const file = event.target?.files[0];
                if (file) {
                  setInitialFormValues(
                    (values: SecurityValidationFormValues) => {
                      return {
                        ...values,
                        accessComplianceFile: file,
                        isAccessCompliance: true,
                      };
                    }
                  );
                }
              }}
            />
          </Button>
        </Stack>
        <CeTextField
          fullWidth
          id="accessComplianceComment"
          name="accessComplianceComment"
          label={t("common:comment")}
          size="small"
          value={formik.values.accessComplianceComment}
          onChange={(event) => {
            setInitialFormValues((values: SecurityValidationFormValues) => {
              return {
                ...values,
                accessComplianceComment: event.target.value,
              };
            });
          }}
          InputLabelProps={{ shrink: true }}
          disabled={isLoading}
        />

        {initialFormValues.accessComplianceFile?.name ? (
          <Typography variant="caption" mb={1} color={"text.secondary"}>
            {initialFormValues.accessComplianceFile.name}
          </Typography>
        ) : null}

        <PreviewImage
          file={initialFormValues.accessComplianceFile}
          fallbackPath={initialFormValues.accessComplianceKey}
          onRemove={() =>
            handleRemoveImage(
              "accessComplianceFile",
              "accessComplianceKey",
              "isAccessCompliance"
            )
          }
        />
      </Box>

      <Box>
        <Stack
          direction="row"
          alignItems="center"
          justifyContent={"space-between"}
        >
          <FormControl>
            <FormControlLabel
              checked={formik.values.isParkingCompliance}
              control={<Checkbox />}
              label={t("parking-compliance")}
              name="isParkingCompliance"
              onChange={(event: any) =>
                setInitialFormValues((values: SecurityValidationFormValues) => {
                  return {
                    ...values,
                    isParkingCompliance: event.target.checked,
                  };
                })
              }
            />
          </FormControl>
          <Button
            variant="contained"
            component="label"
            size="small"
            sx={buttonTextTransform}
          >
            {t("upload")}
            <input
              type="file"
              hidden
              accept="image/*"
              onChange={(event: any) => {
                const file = event.target?.files[0];
                if (file) {
                  setInitialFormValues(
                    (values: SecurityValidationFormValues) => {
                      return {
                        ...values,
                        parkingComplianceFile: file,
                        isParkingCompliance: true,
                      };
                    }
                  );
                }
              }}
            />
          </Button>
        </Stack>
        <CeTextField
          fullWidth
          id="parkingComplianceComment"
          name="parkingComplianceComment"
          label={t("common:comment")}
          size="small"
          value={formik.values.parkingComplianceComment}
          onChange={(event) => {
            setInitialFormValues((values: SecurityValidationFormValues) => {
              return {
                ...values,
                parkingComplianceComment: event.target.value,
              };
            });
          }}
          InputLabelProps={{ shrink: true }}
          disabled={isLoading}
        />

        {initialFormValues.parkingComplianceFile?.name ? (
          <Typography variant="caption" mb={1} color={"text.secondary"}>
            {initialFormValues.parkingComplianceFile.name}
          </Typography>
        ) : null}

        <PreviewImage
          file={initialFormValues.parkingComplianceFile}
          fallbackPath={initialFormValues.parkingComplianceKey}
          onRemove={() =>
            handleRemoveImage(
              "parkingComplianceFile",
              "parkingComplianceKey",
              "isParkingCompliance"
            )
          }
        />
      </Box>

      <Box>
        <Stack
          direction="row"
          alignItems="center"
          justifyContent={"space-between"}
        >
          <FormControl>
            <FormControlLabel
              checked={formik.values.isTerrainStability}
              control={<Checkbox />}
              label={t("terrain-stability")}
              name="isTerrainStability"
              onChange={(event: any) =>
                setInitialFormValues((values: SecurityValidationFormValues) => {
                  return {
                    ...values,
                    isTerrainStability: event.target.checked,
                  };
                })
              }
            />
          </FormControl>
          <Button
            variant="contained"
            component="label"
            size="small"
            sx={buttonTextTransform}
          >
            {t("upload")}
            <input
              type="file"
              hidden
              accept="image/*"
              onChange={(event: any) => {
                const file = event.target?.files[0];
                if (file) {
                  setInitialFormValues(
                    (values: SecurityValidationFormValues) => {
                      return {
                        ...values,
                        terrainStabilityFile: file,
                        isTerrainStability: true,
                      };
                    }
                  );
                }
              }}
            />
          </Button>
        </Stack>

        <CeTextField
          fullWidth
          id="terrainStabilityComment"
          name="terrainStabilityComment"
          label={t("common:comment")}
          size="small"
          value={formik.values.terrainStabilityComment}
          onChange={(event) => {
            setInitialFormValues((values: SecurityValidationFormValues) => {
              return {
                ...values,
                terrainStabilityComment: event.target.value,
              };
            });
          }}
          InputLabelProps={{ shrink: true }}
          disabled={isLoading}
        />

        {initialFormValues.terrainStabilityFile?.name ? (
          <Typography variant="caption" mb={1} color={"text.secondary"}>
            {initialFormValues.terrainStabilityFile.name}
          </Typography>
        ) : null}

        <PreviewImage
          file={initialFormValues.terrainStabilityFile}
          fallbackPath={initialFormValues.terrainStabilityKey}
          onRemove={() =>
            handleRemoveImage(
              "terrainStabilityFile",
              "terrainStabilityKey",
              "isTerrainStability"
            )
          }
        />
      </Box>
    </Stack>
  );
};
