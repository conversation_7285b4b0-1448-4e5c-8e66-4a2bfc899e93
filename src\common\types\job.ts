import { ChipPropsColorOverrides } from "@mui/material";
import { CommonEntity, PossibleSortDir } from "./common";
import { Reservation } from "./reservation";
import { OverridableStringUnion } from "@mui/types";
import { TFunction } from "i18next";
import { GridSortModel } from "@mui/x-data-grid";
import { Expression } from "./filters";
import { differenceInSeconds, format } from "date-fns";

export enum JobState {
  RUNNING = "RUNNING",
  SUCCESS = "SUCCESS",
  FAILURE = "FAILURE",
  CANCELLED = "CANCELLED",
  WAITING = "WAITING",
}

export enum JobStatus {
  NOT_STARTED = "NOT_STARTED",
  DRIVING_TO_SITE = "DRIVING_TO_SITE",
  SITE_ARRIVAL = "SITE_ARRIVAL",
  SECURITY_VALIDATION = "SECURITY_VALIDATION",
  START_SETUP = "START_SETUP",
  END_SETUP = "END_SETUP",
  START_PUMPING = "START_PUMPING",
  END_PUMPING = "END_PUMPING",
  REPORT = "REPORT",
  SIGNATURE = "SIGNATURE",
  START_CLEANUP = "START_CLEANUP",
  END_CLEANUP = "END_CLEANUP",
  LEAVE_SITE = "LEAVE_SITE",
  COMPLETE = "COMPLETE",
  CANCELLED = "CANCELLED",
}

export interface Job extends CommonEntity {
  id: number;
  amountOfConcrete: number | null;
  balance: boolean;
  flowRate: number | null;
  flexiblePipeLength80Mm: number | null;
  flexiblePipeLength90Mm: number | null;
  frontOutriggersSpan: number | null;
  rearOutriggersSpan: number | null;
  rigidPipeLength100Mm: number | null;
  rigidPipeLength120Mm: number | null;
  extraCementBag: boolean;
  units: number | null;
  presenceOfPowerLines: boolean | null;
  voltage: number | null;
  pipeStartingFromBAC: boolean;
  comments: string | null;
  state: JobState | null;
  status: JobStatus | null;
  start: string | null;
  end: string | null;
  progress: number | null;
  reservation: Reservation | null;
  terrainStability: TerrainStability | null;
  tonnageRestriction: boolean;
  authorizedWeight?: number | null;
  heightRestriction: boolean;
  heightLimit: number | null;
  enlistSecondTechnician: boolean;
  backupPumpPackage: boolean;
  parkingOn: ParkingOn | null;
  cleaning: Cleaning | null;
  isElectricalRisk: boolean;
  electricalRiskKey: string | null;
  electricalRiskComment: string | null;
  isAccessCompliance: boolean;
  accessComplianceKey: string | null;
  accessComplianceComment: string | null;
  isParkingCompliance: boolean;
  parkingComplianceKey: string | null;
  parkingComplianceComment: string | null;
  isTerrainStability: boolean;
  terrainStabilityKey: string | null;
  terrainStabilityComment: string | null;
  jobEvents: JobEvent[];
  jobLocations: JobLocationEvent[];
  report: JobReport;
  barbotine: boolean;
  jobType: JobType | null;
  ciaw: string | null;
  supplyOfTheChemicalSlushie: boolean;
  reasonForDelay: string | null;
}

export interface JobReport {
  amountOfConcrete?: number | null;
  backupPackage: boolean;
  cementBags?: number | null;
  cleaningTime?: number | null;
  extraCementBags?: boolean;
  secondTechnician: boolean;
  flexiblePipeLength80Mm?: number | null;
  flexiblePipeLength90Mm?: number | null;
  rigidPipeLength100Mm?: number | null;
  rigidPipeLength120Mm?: number | null;
}

export interface JobEvent {
  statusDescription: string;
  jobId: number;
  created_at: string;
  updated_at: string;
  currentStatus?: string;
}

export interface JobReportFormValues {
  jobId?: number | null;
  cleaningTime?: number | null;
  amountOfConcrete: number | null;
  flexiblePipeLength80Mm?: number | null;
  flexiblePipeLength90Mm?: number | null;
  rigidPipeLength100Mm?: number | null;
  rigidPipeLength120Mm?: number | null;
  extraCementBags: boolean;
  cementBags?: number | null;
  secondTechnician: boolean;
  signature?: string;
  flow: JobReportModalFlow | null;
}

export interface SecurityValidationFormValues {
  jobId?: number;
  isElectricalRisk: boolean;
  electricalRiskFile: File | null;
  electricalRiskKey: string | null;
  electricalRiskComment: string | null;
  isAccessCompliance: boolean;
  accessComplianceFile: File | null;
  accessComplianceKey: string | null;
  accessComplianceComment: string | null;
  isParkingCompliance: boolean;
  parkingComplianceFile: File | null;
  parkingComplianceKey: string | null;
  parkingComplianceComment: string | null;
  isTerrainStability: boolean;
  terrainStabilityFile: File | null;
  terrainStabilityKey: string | null;
  terrainStabilityComment: string | null;
  flow: SecurityValidationModalFlow | null;
}

export interface SecurityValidationDto {
  jobId?: number;
  isElectricalRisk: boolean;
  electricalRiskFile: File | null;
  electricalRiskComment: string | null;
  electricalRiskKey: string | null;
  isAccessCompliance: boolean;
  accessComplianceFile: File | null;
  accessComplianceComment: string | null;
  accessComplianceKey: string | null;
  isParkingCompliance: boolean;
  parkingComplianceFile: File | null;
  parkingComplianceComment: string | null;
  parkingComplianceKey: string | null;
  isTerrainStability: boolean;
  terrainStabilityFile: File | null;
  terrainStabilityComment: string | null;
  terrainStabilityKey: string | null;
}

export interface UpdateReportDto {
  jobId: number;
  cleaningTime?: number | null;
  amountOfConcrete: number | null;
  flexiblePipeLength80Mm?: number | null;
  flexiblePipeLength90Mm?: number | null;
  rigidPipeLength100Mm?: number | null;
  rigidPipeLength120Mm?: number | null;
  extraCementBags: boolean;
  cementBags?: number | null;
  secondTechnician: boolean;
  signature?: string;
}

export interface JobFormValues {
  id?: number;
  amountOfConcrete: number | null;
  balance?: boolean;
  flowRate?: number | null;
  flexiblePipeLength80Mm?: number | null;
  flexiblePipeLength90Mm?: number | null;
  frontOutriggersSpan?: number | null;
  rearOutriggersSpan?: number | null;
  frontSideOpening?: number | null;
  rearSideOpening?: number | null;
  rigidPipeLength100Mm?: number | null;
  rigidPipeLength120Mm?: number | null;
  extraCementBag?: boolean;
  units?: number | null;
  presenceOfPowerLines?: boolean;
  voltage: number | null;
  pipeStartingFromBAC: boolean;
  comments?: string;
  status: JobStatus | null;
  terrainStability: TerrainStability | null;
  tonnageRestriction: boolean;
  authorizedWeight?: number | null;
  heightRestriction?: boolean;
  heightLimit: number | null;
  enlistSecondTechnician: boolean;
  barbotine: boolean;
  supplyOfTheChemicalSlushie: boolean;
  parkingOn: ParkingOn | null;
  cleaning: Cleaning | null;
  jobType: JobType | null;
  ciaw: string | null;
}

export interface CreateJobDto {
  amountOfConcrete?: number | null;
  balance?: boolean;
  flowRate?: number | null;
  flexiblePipeLength80Mm?: number | null;
  flexiblePipeLength90Mm?: number | null;
  frontOutriggersSpan?: number | null;
  rearOutriggersSpan?: number | null;
  frontSideOpening?: number | null;
  rearSideOpening?: number | null;
  rigidPipeLength100Mm?: number | null;
  rigidPipeLength120Mm?: number | null;
  extraCementBag?: boolean;
  units?: number | null;
  presenceOfPowerLines?: boolean;
  voltage?: number | null;
  pipeStartingFromBAC?: boolean;
  comments?: string | null;
  status?: string;
  terrainStability?: TerrainStability | null;
  tonnageRestriction?: boolean;
  authorizedWeight?: number | null;
  heightRestriction?: boolean;
  heightLimit?: number | null;
  enlistSecondTechnician?: boolean;
  backupPumpPackage?: boolean;
  barbotine?: boolean;
  parkingOn?: ParkingOn | null;
  cleaning?: Cleaning | null;
  jobType?: JobType | null;
  ciaw?: string | null;
  supplyOfTheChemicalSlushie?: boolean;
}

export interface UpdateJobDto extends CreateJobDto {
  jobId: number;
  reservationId?: number;
}

export interface CreateJobLocationDto {
  status: JobStatus;
  state: JobState;
  progress: number;
  coords: {
    latitude: number;
    longitude: number;
  };
  jobId: number;
}
export interface JobLocationEvent {
  id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  created_by: number;
  status: string;
  state: string;
  progress: number;
  coords: {
    longitude: number;
    latitude: number;
  };
  jobId: number;
}

export interface getJobLocationDto {
  sortModel?: GridSortModel;
  expressions?: Expression[];
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
  relations?: string[];
}
export interface JobLocationWithCount {
  data: JobLocationEvent[];
  totalCount: number;
}

export type ColorTypes = {
  color: string;
  backgroundColor: string;
  borderColor: string;
  chipColor: OverridableStringUnion<
    | "default"
    | "primary"
    | "secondary"
    | "error"
    | "info"
    | "success"
    | "warning",
    ChipPropsColorOverrides
  >;
};

export type StatusColors = {
  [key: string]: ColorTypes;
};

export type ParkingOn = "Public Road" | "Private Road" | "Worksite";

export const parkingOnList: ParkingOn[] = [
  "Public Road",
  "Private Road",
  "Worksite",
];

export type TerrainStability = "Natural soil" | "Backfill" | "Buried network";

export const TerrainStabilityList: TerrainStability[] = [
  "Natural soil",
  "Backfill",
  "Buried network",
];

export type Cleaning = "Worksite" | "Central";

export const cleaningList: Cleaning[] = ["Worksite", "Central"];

export type JobType =
  | "Cementation / Mounting"
  | "Cleanliness / Bottom of excavation"
  | "Concrete apron"
  | "Concrete blocs"
  | "Filling"
  | "Floor"
  | "Foundations"
  | "Longrine"
  | "Pole"
  | "Beam"
  | "Reinforced concrete wall / panel"
  | "Roads / Networks / Utilities"
  | "Screed"
  | "Slab"
  | "Unknown";
export const JobTypeList: JobType[] = [
  "Cementation / Mounting",
  "Cleanliness / Bottom of excavation",
  "Concrete apron",
  "Concrete blocs",
  "Filling",
  "Floor",
  "Foundations",
  "Longrine",
  "Pole",
  "Beam",
  "Reinforced concrete wall / panel",
  "Roads / Networks / Utilities",
  "Screed",
  "Slab",
  "Unknown",
];

export type JobReportModalFlow = "Open" | null;
export type SecurityValidationModalFlow = "Open" | null;

export const getTranslatedJobStatusOptions = (t: TFunction) => {
  return [
    { value: JobStatus.NOT_STARTED, label: t("common:not-started") },
    { value: JobStatus.DRIVING_TO_SITE, label: t("common:driving-to-site") },
    { value: JobStatus.SITE_ARRIVAL, label: t("common:site-arrival") },
    {
      value: JobStatus.SECURITY_VALIDATION,
      label: t("common:security-validation"),
    },
    { value: JobStatus.START_SETUP, label: t("common:start-setup") },
    { value: JobStatus.END_SETUP, label: t("common:end-setup") },
    { value: JobStatus.START_PUMPING, label: t("common:start-pumping") },
    { value: JobStatus.END_PUMPING, label: t("common:end-pumping") },
    { value: JobStatus.REPORT, label: t("common:report") },
    { value: JobStatus.SIGNATURE, label: t("common:signature") },
    { value: JobStatus.START_CLEANUP, label: t("common:start-cleanup") },
    { value: JobStatus.END_CLEANUP, label: t("common:end-cleanup") },
    { value: JobStatus.LEAVE_SITE, label: t("common:leave-site") },
    { value: JobStatus.COMPLETE, label: t("common:complete") },
    { value: JobStatus.CANCELLED, label: t("common:cancelled") },
  ];
};

export const calculateElapsedTime =(
  jobEvents: JobEvent[] | null,
  t: TFunction
): string => {
  if (!jobEvents || jobEvents.length === 0) {
    return '00:00';
  }
  const endSetupEvent = jobEvents.find(
    event => event.currentStatus === JobStatus.END_SETUP
  );
  const startPumpingEvent = jobEvents.find(
    event => event.currentStatus === JobStatus.START_PUMPING
  );
  if (!endSetupEvent || !startPumpingEvent) {
    return '00:00';
  }
  const endSetupTime = new Date(endSetupEvent.created_at!);
  const startPumpingTime = new Date(startPumpingEvent.created_at!);
  const totalSeconds = differenceInSeconds(startPumpingTime, endSetupTime);
  if (totalSeconds < 3600) {
    return `${format(new Date(totalSeconds * 1000), 'mm:ss')} ${t("common:second")}`;
  } else {
    return `${format(new Date(totalSeconds * 1000), 'HH:mm')} ${t("common:minutes")}`;
  }
}