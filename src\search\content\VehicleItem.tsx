import {
  Box,
  CardMedia,
  Chip,
  IconButton,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import { FC } from "react";

import { Manager, Operator, User, Vehicle } from "src/common/types";
import {
  getTruncatedCompany,
  getTruncatedName,
  getVehicleUniqueId,
} from "src/common/utils";
import { useTranslation } from "react-i18next";
import {
  ArrowRight03Icon,
  ArrowUp03Icon,
  Building06Icon,
  Calendar03Icon,
  Calendar04Icon,
  IdIcon,
  InformationCircleIcon,
} from "@hugeicons/react";
import { CeCard, CeButton } from "src/common/components";
import { Company } from "src/common/types/company";
import { HtmlTooltipResource } from "src/common/components/custom/HtmlTooltipResource";
import TooltipResource from "src/dispatcherPlanning/resource-label-content/TooltipResource";

export interface VehicleItemProps {
  vehicle: Vehicle;
  handleVehicleSelect: (vehicle: Vehicle) => void;
  handleVehicleModal: (vehicle: Vehicle) => void;
}

const vehicleImages: { [key: number]: string } = {
  15: "./images/vehicle-size-15.png",
  32: "./images/vehicle-size-32.png",
  21: "./images/vehicle-size-21.png",
  42: "./images/vehicle-size-42.png",
  46: "./images/vehicle-size-46.png",
  51: "./images/vehicle-size-51.png",
  52: "./images/vehicle-size-52.png",
  55: "./images/vehicle-size-55.png",
  63: "./images/vehicle-size-63.png",
  65: "./images/vehicle-size-65.png",
  56: "./images/vehicle-size-56.png",
  20: "./images/vehicle-size-20.png",
  24: "./images/vehicle-size-24.png",
  25: "./images/vehicle-size-25.png",
  28: "./images/vehicle-size-28.png",
  30: "./images/vehicle-size-30.png",
  31: "./images/vehicle-size-31.png",
  36: "./images/vehicle-size-36.png",
  38: "./images/vehicle-size-38.png",
  39: "./images/vehicle-size-39.png",
  47: "./images/vehicle-size-47.png",
  45: "./images/vehicle-size-45.png",
};

const defaultImage = "./images/pump-truck-2.png";

const getImagesForVehicles = (boomSize: number | null) => {
  if (boomSize === null) {
    return defaultImage;
  }
  return vehicleImages[boomSize] || defaultImage;
};

export const VehicleItem: FC<VehicleItemProps> = ({
  vehicle,
  handleVehicleModal,
  handleVehicleSelect,
}) => {
  const { t } = useTranslation("dispatcher");

  const vehicleManager = vehicle.manager as unknown as User;
  const operator = vehicle.operator as Operator;
  const vehicleName = getVehicleUniqueId(vehicle);
  const vehicleBoomSize = Number(vehicle.boomSize);
  const vehicleImage = getImagesForVehicles(vehicleBoomSize);
  const operatorFullName = vehicle.operator
    ? `${vehicle.operator?.firstName} ${vehicle.operator?.lastName}`
    : "None";

  return (
    <CeCard
      sx={{
        width: "100%", // Make width responsive to the Grid's layout
        borderRadius: "12px",
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
        height: "100%",
      }}
    >
      <Box sx={{ paddingLeft: 1, paddingRight: 1, paddingTop: 1 }}>
        <CardMedia
          component="img"
          sx={{ height: 150, objectFit: "scale-down" }}
          image={vehicleImage}
          alt={`vehicle size ${vehicleBoomSize}`}
        />
      </Box>
      <Box
        sx={{ padding: 2, gap: 1, flexDirection: "column", display: "flex" }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Tooltip title={vehicleName}>
            <Typography
              variant="body2"
              color="textSecondary"
              fontWeight="bold"
              textTransform="uppercase"
              noWrap={true}
            >
              {vehicleName}
            </Typography>
          </Tooltip>
          <IconButton
            color="primary"
            aria-label="vehicle information"
            onClick={() => handleVehicleModal(vehicle)}
            edge="end"
          >
            <InformationCircleIcon size={24} variant="stroke" />
          </IconButton>
        </Box>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-start",
            gap: 2,
          }}
        >
          <Typography variant="h6" fontWeight={"bold"}>
            {vehicle?.assignedPricelist?.packageFlatFee ??
              vehicle?.assignedPricelist?.dayContractFee ??
              "-"}{" "}
            €
          </Typography>

          <Typography variant="body1" color="textSecondary" fontWeight="bold">
            {vehicle?.assignedPricelist?.additionalHour ??
              vehicle?.assignedPricelist?.dayContractOvertimeRate ??
              "-"}{" "}
            €/h
          </Typography>
        </Box>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-start",
            gap: 1,
          }}
        >
          <Tooltip title={"Max. Vertical Reach"}>
            <Chip
              size="small"
              icon={
                <ArrowUp03Icon
                  size={16}
                  color="currentColor"
                  variant="stroke"
                />
              }
              label={vehicle.maxVerticalReach + " m"}
            />
          </Tooltip>
          <Tooltip title={"Max. Horizontal Reach"}>
            <Chip
              size="small"
              icon={
                <ArrowRight03Icon
                  size={16}
                  color="currentColor"
                  variant="stroke"
                />
              }
              label={vehicle.maxHorizontalReach + " m"}
            />
          </Tooltip>
          <Tooltip title={"Completed Jobs"}>
            <Chip
              size="small"
              icon={
                <Calendar04Icon
                  size={16}
                  color="currentColor"
                  variant="stroke"
                />
              }
              label={vehicle.completedJobs || 0}
            />
          </Tooltip>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-start",
            alignItems: "center",
            overflow: "hidden",
            gap: 1,
          }}
        >
          <HtmlTooltipResource
            title={
              <Stack>
                <Stack direction="row" gap={1}>
                  <Typography>{t("common:name")}:</Typography>
                  <Typography>{operatorFullName}</Typography>
                </Stack>
                <Box>
                  <Stack direction="row" gap={1}>
                    <Typography>{t("common:phone-number")}:</Typography>
                    <Typography>
                      {vehicle?.operator?.phoneNumber || ""}
                    </Typography>
                  </Stack>
                </Box>
              </Stack>
            }
          >
            <Chip
              sx={{ border: "none", maxWidth: "50%" }}
              size="small"
              icon={<IdIcon size={16} color="currentColor" variant="stroke" />}
              label={
                operator
                  ? `${operator.firstName || ""} ${
                      operator.lastName || ""
                    }`.trim()
                  : "None"
              }
              variant="outlined"
            />
          </HtmlTooltipResource>
          <HtmlTooltipResource
            title={
              <TooltipResource
                phoneNumber={vehicleManager.company?.phoneNumber || "-"}
                contactEmail={vehicleManager.company?.contactEmail || "-"}
                companyName={vehicleManager.company?.name || "-"}
              />
            }
          >
            <Chip
              sx={{
                display: "flex",
                alignItems: "center",
                border: "none",
                maxWidth: "50%",
              }}
              size="small"
              icon={
                <Building06Icon
                  size={16}
                  color="currentColor"
                  variant="stroke"
                />
              }
              label={vehicleManager.company.name}
              variant="outlined"
            />
          </HtmlTooltipResource>
        </Box>
        <Box sx={{ display: "flex", justifyContent: "center" }}>
          <CeButton
            fullWidth
            color="primary"
            variant="outlined"
            size="medium"
            onClick={() => handleVehicleSelect(vehicle)}
            disabled={!vehicle?.assignedPricelist}
          >
            {t("select")}
          </CeButton>
        </Box>
      </Box>
    </CeCard>
  );
};
