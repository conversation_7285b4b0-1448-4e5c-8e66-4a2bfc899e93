import { <PERSON>, <PERSON><PERSON>, Chip, Typography, useTheme } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import {
  getCurrentUser,
  useReservation,
  useUpdateReservation,
} from "src/common/api";
import {
  JOB_STEPS,
  RESERVATION_CANCEL_DEFAULT,
  RESERVATION_EDIT_DEFAULT,
  STATUS_COLORS,
} from "src/common/constants";
import {
  getJobLocationDto,
  JobEvent,
  JobStatus,
  ReservationFormValues,
  ReservationQueryFilters,
} from "src/common/types";
import GoogleMapComponent from "src/common/components/reservationDetails/GoogleMapComponent";
import { getStepIconColor } from "src/common/utils/reservationDetails";
import HistoryStepper from "src/common/components/reservationDetails/HistoryStepper";
import DetailsSection from "src/common/components/reservationDetails/DetailsSection";
import ClientInfo from "src/common/components/reservationDetails/ClientInfo";
import Media from "src/common/components/reservationDetails/Media";
import { getFormattedDatewithLocale } from "src/common/utils/formatDate";
import { useRecoilState } from "recoil";
import {
  reservationCancelValuesState,
  reservationEditValuesState,
  reservationFullFormValuesState,
} from "src/common/state";
import { useLocation, useUpdateJob } from "src/common/api/job";
import { ReservationModalCancel } from "./ReservationModalCancel";
import { ReservationEditModal } from "./ReservationEditModal";
import { useEffect, useRef } from "react";
import { useReactToPrint } from "react-to-print";
import {
  Cancel01Icon,
  Invoice01Icon,
  PencilEdit01Icon,
  PrinterIcon,
} from "@hugeicons/react";
import { useCreateReservationInvoice } from "src/common/api/invoices";
import LoadingButton from "@mui/lab/LoadingButton";
import { ReservationCancelWithCommentModal } from "./ReservationCancelWithCommentModal";
import { CePaper } from "src/common/components";
import queryString from "query-string";
import {
  determineCancelationFee,
  turnReservationIntoFormValues,
} from "src/common/utils";
import { useCompany } from "src/common/api/company";

export const ReservationDetails = () => {
  const { t, i18n } = useTranslation(["dispatcher", "common", "manager"]);
  const reservationContainerRef = useRef<HTMLDivElement>(null);
  const { id } = useParams<{ id: string }>();
  const reservationId = Number(id);
  const theme = useTheme();
  const navigate = useNavigate();
  const currentUser = getCurrentUser();
  const { data: reservation, isLoading } = useReservation(
    reservationId,
    Boolean(reservationId)
  );

  const {
    data: companyData,
    isLoading: isCompanyLoading,
    isSuccess: isCompanySuccess,
  } = useCompany(
    reservation?.operator?.companyId,
    Boolean(reservation?.operator?.companyId)
  );

  const jobLocationParams: getJobLocationDto = {
    limit: 50,
    offset: 0,
    expressions: [
      {
        category: `"job_location"."jobId"`,
        operator: "=",
        value: reservation?.job?.id,
      },
    ],
    sortModel: [
      {
        field: "job_location.id",
        sort: "asc",
      },
    ],
  };

  const { data: jobLocations } = useLocation(
    jobLocationParams,
    Boolean(reservation?.job?.id)
  );

  const [reservationCancelValues, setReservationCancelValues] = useRecoilState(
    reservationCancelValuesState
  );
  const [reservationFullFormValues, setReservationFullFormValues] =
    useRecoilState(reservationFullFormValuesState);

  const [reservationEditValues, setReservationEditValues] = useRecoilState(
    reservationEditValuesState
  );
  const { mutate: handleUpdateJob } = useUpdateJob();
  const {
    mutate: handleCreateReservationInvoice,
    isSuccess: isCreateReservationInvoiceSuccess,
    isLoading: isCreateReservationInvoice,
  } = useCreateReservationInvoice();

  const status = reservation?.job?.status;
  const isInvoiced = reservationEditValues?.invoiced || reservation?.invoiced;
  const findStatus = JOB_STEPS.find((step) => step.name === status);
  const statusStyles = findStatus?.name ? STATUS_COLORS[findStatus.name] : {};
  const statusLabel = findStatus?.label ? t(findStatus.label) : "";
  const selectedLanguage = i18n.language;
  const formattedDate = getFormattedDatewithLocale(
    selectedLanguage,
    reservation?.created_at!,
    "dd MMM yyyy hh:mm a"
  );

  const handleOpenReservationEditModal = () => {
    setReservationEditValues({
      reservationId,
      flow: "Edit",
      dateFrom: reservation?.dateFrom || "",
      dateTo: reservation?.dateTo || "",
      siteAddress: reservation?.siteAddress || "",
      city: reservation?.city || "",
      plz: reservation?.plz || null,
      vehicle: reservation?.vehicle || null,
    });
  };
  const handleCloseReservationEditModal = () => {
    setReservationEditValues(RESERVATION_EDIT_DEFAULT);
  };
  const handleOpenReservationModalCancel = () => {
    const cancelationFee = determineCancelationFee(
      reservation?.dateFrom!,
      companyData?.settings.cancellation?.cancellationWindow || "",
      companyData?.settings.cancellation?.reducedCancellationWindow || "",
      reservation?.pricelist?.cancellationFee || 0,
      reservation?.pricelist?.dayCancellationFee || 0
    );
    setReservationCancelValues({
      reservationId: reservation?.id || null,
      jobId: reservation?.job?.id || null,
      reservationTitle: reservation?.orderNumber || "",
      cancelationFee,
      flow: "Cancel",
    });
  };
  const handleCloseReservationModalCancel = () => {
    if (!isLoading) {
      setReservationCancelValues(RESERVATION_CANCEL_DEFAULT);
    }
  };
  const handleCancelReservation = (comment: string) => {
    handleUpdateJob({
      status: JobStatus.CANCELLED,
      jobId: Number(reservationCancelValues.jobId),
      reservationId: Number(reservationCancelValues.reservationId),
      comments: comment,
    });
    handleCloseReservationModalCancel();
  };

  const handleEditReservation = (values: ReservationFormValues) => {
    if (reservation) {
      const reservationQuery: ReservationQueryFilters = {
        dateFrom: values.dateFrom
          ? new Date(values.dateFrom).toISOString()
          : null,
        dateTo: values.dateTo ? new Date(values.dateTo).toISOString() : null,
        siteAddress: values.siteAddress || null,
        city: values.city || null,
        plz: values.plz || null,
        lat: values.location?.coordinates[0] || null,
        lng: values.location?.coordinates[1] || null,
        vehicleId: reservation.vehicle?.id.toString() || null,
        editMode: true,
      };
      const queryParams = queryString.stringify(reservationQuery);

      const reservationFormData = turnReservationIntoFormValues(reservation);
      setReservationFullFormValues(reservationFormData);
      navigate(`/reservation?${queryParams}`);
      handleCloseReservationEditModal();
    }
  };

  useEffect(() => {
    if (isCreateReservationInvoiceSuccess) {
      setReservationEditValues((prev) => ({
        ...prev,
        invoiced: true,
      }));
    }
  }, [isCreateReservationInvoiceSuccess, setReservationEditValues]);

  const handleInvoiced = () => {
    if (reservation?.id !== undefined) {
      handleCreateReservationInvoice({
        reservationIds: [reservation.id],
        dispatcherCompanyId: reservation?.dispatcher?.company?.id || 0
      });
    }
  };

  const handlePrint = useReactToPrint({
    content: () => reservationContainerRef.current,
    pageStyle: `
    @media print {
      body {
        zoom: 57.7%;
      }
    }
  `,
  });

  const canEditReservation =
    status !== JobStatus.NOT_STARTED &&
    reservation?.created_by === currentUser?.id;

  const getStepIcon = (
    stepName: string,
    currentStatus: string,
    currentProgress: number,
    jobEvents: JobEvent[]
  ) => {
    const backgroundColor = getStepIconColor(
      stepName,
      currentStatus,
      currentProgress,
      jobEvents
    );
    return (
      <Box
        sx={{
          width: 16,
          height: 16,
          borderRadius: "50%",
          backgroundColor: backgroundColor,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      />
    );
  };

  return (
    <Box
      ref={reservationContainerRef}
      sx={{
        padding: 2,
        display: "flex",
        flexDirection: "column",
        gap: 2,
      }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <Box
          display="flex"
          flexDirection="column"
          gap={0.5}
          className="first-page"
        >
          <Box display="flex" alignItems="center">
            <Typography
              variant="h5"
              sx={{ marginRight: 2, fontWeight: "bold" }}
            >
              {t("common:reservation-order-number")}
            </Typography>
            {statusLabel ? (
              <Chip
                size="medium"
                label={statusLabel}
                sx={{
                  ...statusStyles,
                  fontWeight: 700,
                }}
              />
            ) : null}
          </Box>
          <Typography color="text.secondary" variant="body2">
            {formattedDate}
          </Typography>
        </Box>
        <Box
          sx={{
            display: "flex",
            gap: 1.5,
            flexWrap: "wrap",
            alignItems: "center",
          }}
          className="no-print"
        >
          <Button
            size="small"
            variant="contained"
            color="error"
            sx={{
              display: "flex",
              alignItems: "center",
              padding: "6px 16px",
              borderRadius: "8px",
              textTransform: "none",
              fontWeight: "bold",
            }}
            onClick={handleOpenReservationModalCancel}
            disabled={
              status === JobStatus.CANCELLED || status === JobStatus.COMPLETE
            }
            startIcon={<Cancel01Icon size={24} color={"currentColor"} />}
          >
            <Typography sx={{ fontWeight: 600 }}>
              {t("common:cancel")}
            </Typography>
          </Button>
          <LoadingButton
            loading={isCreateReservationInvoice}
            loadingPosition="start"
            onClick={handleInvoiced}
            size="small"
            variant={isInvoiced ? "contained" : "outlined"}
            color={isInvoiced ? "secondary" : "primary"}
            sx={{
              display: "flex",
              alignItems: "center",
              padding: "6px 14px",
              borderWidth: 0,
              borderRadius: "8px",
              textTransform: "none",
              fontWeight: "bold",
              backgroundColor: isInvoiced
                ? theme.palette.secondary.main
                : theme.palette.background.paper,
              boxShadow:
                "0px 3px 5px rgba(0, 0, 0, 0.07), 0px 2px 2px rgba(0, 0, 0, 0.04), 0px 3px 1px -2px rgba(0, 0, 0, 0.02)",
              pointerEvents: isInvoiced ? "none" : "auto",
            }}
            startIcon={<Invoice01Icon size={24} color={"currentColor"} />}
            disabled={status !== JobStatus.COMPLETE}
          >
            <Typography sx={{ fontWeight: 600 }}>
              {isInvoiced ? t("common:invoiced") : t("common:invoice")}
            </Typography>
          </LoadingButton>
          <Button
            onClick={handlePrint}
            size="small"
            variant="outlined"
            sx={{
              display: "flex",
              alignItems: "center",
              padding: "6px 14px",
              borderWidth: 0,
              borderRadius: "8px",
              textTransform: "none",
              fontWeight: "bold",
              backgroundColor: theme.palette.background.paper,
              boxShadow:
                "0px 3px 5px rgba(0, 0, 0, 0.07), 0px 2px 2px rgba(0, 0, 0, 0.04), 0px 3px 1px -2px rgba(0, 0, 0, 0.02)",
            }}
            startIcon={<PrinterIcon size={24} color={"currentColor"} />}
          >
            <Typography sx={{ fontWeight: 600 }}>
              {t("common:print")}
            </Typography>
          </Button>
          <Button
            size="small"
            variant="outlined"
            color={status === JobStatus.CANCELLED ? "secondary" : "primary"}
            sx={{
              display: "flex",
              alignItems: "center",
              padding: "6px 14px",
              borderRadius: "8px",
              borderWidth: 0,
              textTransform: "none",
              fontWeight: "bold",
              backgroundColor: theme.palette.background.paper,
              boxShadow:
                "0px 3px 5px rgba(0, 0, 0, 0.07), 0px 2px 2px rgba(0, 0, 0, 0.04), 0px 3px 1px -2px rgba(0, 0, 0, 0.02)",
            }}
            onClick={handleOpenReservationEditModal}
            disabled={canEditReservation}
            startIcon={<PencilEdit01Icon size={24} color={"currentColor"} />}
          >
            <Typography sx={{ fontWeight: 600 }}>{t("common:edit")}</Typography>
          </Button>
        </Box>
      </Box>

      <Box display="flex" gap={2}>
        <Box flex={2}>
          <CePaper
            sx={{
              padding: 3,
              marginBottom: 2,
            }}
          >
            <GoogleMapComponent
              reservationLocation={reservation?.location!}
              events={jobLocations?.data!}
            />
          </CePaper>

          <HistoryStepper
            status={status || ""}
            progress={reservation?.job?.progress || 0}
            jobEvents={reservation?.job?.jobEvents || []}
            getStepIcon={getStepIcon}
            reservation={reservation}
          />
          <DetailsSection reservation={reservation} />
        </Box>

        <Box flex={1} display="flex" flexDirection="column">
          <ClientInfo reservation={reservation} />
          <CePaper
            sx={{
              padding: 3,
              marginTop: 2,
            }}
          >
            <Media job={reservation?.job!} />
          </CePaper>
        </Box>
      </Box>
      <ReservationCancelWithCommentModal
        onSubmit={handleCancelReservation}
        isLoading={isLoading}
        cancelationFee={reservationCancelValues.cancelationFee}
        flow={reservationCancelValues.flow}
        reservationTitle={reservationCancelValues.reservationTitle}
        handleCloseReservationModalCancel={handleCloseReservationModalCancel}
      />
      <ReservationEditModal
        initialFormValues={reservationEditValues}
        handleEditReservation={handleEditReservation}
        isLoading={isLoading}
        flow={reservationEditValues.flow}
        handleCloseReservationEditModal={handleCloseReservationEditModal}
        fullUpdate
      />
    </Box>
  );
};
