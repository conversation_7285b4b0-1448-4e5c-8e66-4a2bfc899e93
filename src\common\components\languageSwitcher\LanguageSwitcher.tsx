import { Box, MenuItem, SelectChangeEvent } from "@mui/material";
import { useTranslation } from "react-i18next";
import { CeSelect } from "..";

interface LanguageSwitcherProps {
  fullWidth?: boolean;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  fullWidth = false,
}) => {
  const { i18n } = useTranslation("common");
  const { t } = useTranslation(["common", "manager", "dispatcher"]);

  const changeLanguage = (event: SelectChangeEvent<string>) => {
    const language = event.target.value;
    i18n.changeLanguage(language);
  };

  const getLanguageDetails = (selected: string) => {
    switch (selected) {
      case "fr":
        return {
          imageSrc: "/images/france.png",
          altText: "language-french",
          label: "Français",
        };
      case "de":
        return {
          imageSrc: "/images/germany.png",
          altText: "language-german",
          label: "Deutsch",
        };
      case "en":
        return {
          imageSrc: "/images/united-kingdom.png",
          altText: "language-english",
          label: "English",
        };
      case "nl":
        return {
          imageSrc: "/images/netherlands.png",
          altText: "language-dutch",
          label: "Nederlands",
        };
      default:
        return { imageSrc: "", altText: "", label: "" };
    }
  };

  return (
    <CeSelect
      renderValue={(selected) => {
        const { imageSrc, altText, label } = getLanguageDetails(selected);
        return (
          <Box display="flex" alignItems="center">
            <img alt={altText} height={20} src={imageSrc} width={20} />
            <Box ml={1}>{label}</Box>
          </Box>
        );
      }}
      size="small"
      value={i18n.language}
      onChange={changeLanguage}
      fullWidth={fullWidth}
    >
      <MenuItem value="en">
        <Box alignItems="center" display="flex">
          <img
            alt="language-english"
            height={16}
            src="/images/united-kingdom.png"
            width={16}
          />
          <Box ml={1}>{t("English")}</Box>
        </Box>
      </MenuItem>
      <MenuItem value="fr">
        <Box alignItems="center" display="flex">
          <img
            alt="language-french"
            height={16}
            src="/images/france.png"
            width={16}
          />
          <Box ml={1}>{t("French")}</Box>
        </Box>
      </MenuItem>
      <MenuItem value="nl">
        <Box alignItems="center" display="flex">
          <img
            alt="language-dutch"
            height={16}
            src="/images/netherlands.png"
            width={16}
          />
          <Box ml={1}>{t("Nederlands")}</Box>
        </Box>
      </MenuItem>
      <MenuItem value="de">
        <Box alignItems="center" display="flex">
          <img
            alt="language-german"
            height={16}
            src="/images/germany.png"
            width={16}
          />
          <Box ml={1}>{t("Deutsch")}</Box>
        </Box>
      </MenuItem>
    </CeSelect>
  );
};

export default LanguageSwitcher;
