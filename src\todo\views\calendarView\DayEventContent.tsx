import { EventContentArg } from "@fullcalendar/core";
import { Box, Typography } from "@mui/material";
import { format, isSameDay } from "date-fns";

export const DayEventContent = (arg: EventContentArg) => {
  const { event, isStart, isEnd } = arg;
  const from = event.extendedProps.scheduleFrom;
  const to = event.extendedProps.scheduleTo;
  const timeFrom = format(new Date(from), "HH:mm");
  const timeTo = format(new Date(to), "HH:mm");
  const sameDay = isSameDay(new Date(from), new Date(to));

  return (
    <Box
      gap={1}
      sx={{
        backgroundColor: !sameDay ? event.extendedProps.labelColor : "inherit",
        borderTopLeftRadius: isStart ? 4 : null,
        borderBottomLeftRadius: isStart ? 4 : null,
        borderTopRightRadius: isEnd ? 4 : null,
        borderBottomRightRadius: isEnd ? 4 : null,
        p: 0.5,
        display: "flex",
        direction: "row",
        alignItems: "center",
        justifyContent: "flex-start",
        overflow: "hidden",
        textOverflow: "ellipsis",
        whiteSpace: "nowrap",
        "&:hover": {
          cursor: "pointer"
        }
      }}
    >
      <>
        {sameDay && (
          <Box
            borderRadius="50%"
            sx={{
              backgroundColor: event.extendedProps.labelColor,
              p: 1
            }}
          />
        )}
        <Typography>
          {timeFrom}-{timeTo}
        </Typography>
      </>
      <Typography overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap">
        {event.extendedProps.description}
      </Typography>
    </Box>
  );
};
