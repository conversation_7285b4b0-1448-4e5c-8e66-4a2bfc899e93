import { Stack, Typography } from "@mui/material";
import { Ce<PERSON><PERSON>on, CeTextField, MainModal } from "src/common/components";
import {
  CancelContractDto,
  CancelContractValues,
  RespondToContractPayload,
} from "src/common/types";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import enGb from "date-fns/locale/en-GB";
import * as yup from "yup";
import { useFormik } from "formik";
import { useTranslation } from "react-i18next";
import { noonTime } from "src/common/utils";
import toast from "react-hot-toast";
import { startOfToday } from "date-fns";

interface CancelOrSuspendContractModalProps {
  initialValues: CancelContractValues;
  handleClose: () => void;
  isLoading?: boolean;
  handleSuspendContract: (values: CancelContractDto) => void;
  handleCancelContract: ({
    id,
    payload,
  }: {
    id: number;
    payload: RespondToContractPayload;
  }) => void;
}

const CancelOrSuspendContractModal = ({
  initialValues,
  handleClose,
  isLoading,
  handleSuspendContract,
  handleCancelContract,
}: CancelOrSuspendContractModalProps) => {
  const { t } = useTranslation(["dispatcher", "common"]);
  const isCancel = initialValues.flow === "Cancel";
  const today = startOfToday();

  const formik = useFormik<CancelContractValues>({
    enableReinitialize: true,
    initialValues,
    validationSchema: yup.object({
      startDate: yup
        .date()
        .nullable()

        .when("$flow", {
          is: "Suspend",
          then: (schema) =>
            schema
              .required("Start date is required")
              .min(today, "Start date cannot be before today"),
        }),
      endDate: yup
        .date()
        .nullable()
        .when("$flow", {
          is: "Suspend",
          then: (schema) =>
            schema
              .required("End date is required")
              .min(
                yup.ref("startDate"),
                "End date must be on/after start date"
              ),
        }),
    }),
    onSubmit: (values) => {
      if (isCancel) {
        const id = values.contractId || null;
        const payload = { comment: values.comment || "" };
        if (!id) {
          toast.error("Contract ID is required for cancellation");
          return;
        }
        handleCancelContract({ id, payload });
      } else {
        const suspensionStart = new Date(values.startDate!).toISOString();
        const suspensionEnd = new Date(values.endDate!).toISOString();
        handleSuspendContract({
          suspensionStart,
          suspensionEnd,
          contractId: initialValues.contractId!,
        });
      }
    },
  });
  const title = isCancel ? "Cancel Contract" : "Suspend Contract";
  const helperText = isCancel
    ? "Are you sure u want to cancel this contract?"
    : "Please select suspension period";
  return (
    <MainModal
      title={title}
      isOpen={!!initialValues.flow}
      handleClose={handleClose}
      shouldRenderDialogActions
      dialogActionStyling={{ display: "flex", gap: 1, px: 4.5, py: 2 }}
      helperText={helperText}
    >
      <Stack
        component="form"
        spacing={2}
        noValidate
        onSubmit={formik.handleSubmit}
      >
        {!isCancel ? (
          <>
            <LocalizationProvider
              dateAdapter={AdapterDateFns}
              adapterLocale={enGb}
            >
              <DatePicker
                renderInput={(props: any) => (
                  <CeTextField
                    {...props}
                    size="small"
                    fullWidth
                    onBlur={() => formik.setFieldTouched("startDate", true)}
                    error={
                      !!formik.errors.startDate && formik.touched.startDate
                    }
                    helperText={
                      formik.touched.startDate && formik.errors.startDate
                    }
                    required
                  />
                )}
                label={t("date-from")}
                value={formik.values.startDate}
                onChange={(newValue) => {
                  formik.setFieldValue("startDate", newValue);
                }}
              />
            </LocalizationProvider>

            <LocalizationProvider
              dateAdapter={AdapterDateFns}
              adapterLocale={enGb}
            >
              <DatePicker
                renderInput={(props: any) => (
                  <CeTextField
                    {...props}
                    fullWidth
                    size="small"
                    error={!!formik.errors.endDate && formik.touched.endDate}
                    helperText={formik.touched.endDate && formik.errors.endDate}
                    onBlur={() => formik.setFieldTouched("endDate", true)}
                    required
                  />
                )}
                label={t("date-to")}
                value={formik.values.endDate}
                onChange={(newValue) => {
                  formik.setFieldValue("endDate", newValue);
                }}
              />
            </LocalizationProvider>
          </>
        ) : (
          <>
            <Typography
              sx={{
                fontSize: "16px",
                letterSpacing: "0.4px",
              }}
            >
              Please leave a comment
            </Typography>
            <CeTextField
              id="comment"
              name="comment"
              label={t("common:comment")}
              value={formik.values.comment || ""}
              onChange={formik.handleChange}
              multiline
              rows={3}
              fullWidth
            />
          </>
        )}
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="flex-end"
          width="100%"
        >
          <CeButton
            onClick={handleClose}
            size="large"
            variant="text"
            disabled={isLoading}
          >
            Cancel
          </CeButton>
          <CeButton
            type="submit"
            size="large"
            variant="contained"
            disabled={isLoading}
          >
            Submit
          </CeButton>
        </Stack>
      </Stack>
    </MainModal>
  );
};

export default CancelOrSuspendContractModal;
