import { atom } from "recoil";
import { DeleteVehicleModelModalValues, VehicleModelFormValues } from "../types/vehicleModels";
import { VEHICLE_MODEL_DELETE_DEFAULT, VEHICLE_MODEL_FORM_VALUES } from "../constants/vehicleModel";

export const vehicleModelFormValuesState = atom<VehicleModelFormValues>({
  key: "vehicleModelFormValuesState",
  default: VEHICLE_MODEL_FORM_VALUES,
});
  
export const vehicleModelDeleteValuesState = atom<DeleteVehicleModelModalValues>({
    key: "vehicleModelDeleteValuesState",
    default: VEHICLE_MODEL_DELETE_DEFAULT,
});