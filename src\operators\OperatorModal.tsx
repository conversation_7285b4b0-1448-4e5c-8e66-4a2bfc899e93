import { AxiosError } from "axios";
import { UseMutateFunction } from "react-query";
import { MainModal } from "src/common/components";
import {
  CreateOperatorDto,
  Operator,
  OperatorFormValues,
  UpdateOperatorDto,
  Vehicle,
  VehicleFormValues,
} from "src/common/types";
import { OperatorForm } from "./OperatorForm";

interface OperatorModalProps {
  isLoading: boolean;
  initialFormValues: OperatorFormValues;
  handleCloseOperatorModal: () => void;
  handleCreateNewOperator?: UseMutateFunction<
    Operator,
    AxiosError<CreateOperatorDto, CreateOperatorDto> | Error,
    CreateOperatorDto,
    () => void
  >;
  handleUpdateOperator?: UseMutateFunction<
    Operator,
    AxiosError<UpdateOperatorDto, UpdateOperatorDto> | Error,
    UpdateOperatorDto,
    () => void
  >;
}
export const OperatorModal: React.FC<OperatorModalProps> = ({
  isLoading,
  initialFormValues,
  handleCloseOperatorModal,
  handleCreateNewOperator,
  handleUpdateOperator,
}) => {
  const isFlowCreateOrUpdate =
  initialFormValues.flow === "Create" || initialFormValues.flow === "Update";
  
  return (
    <MainModal
      title={`${initialFormValues.flow || ""} Operator`}
      isOpen={isFlowCreateOrUpdate}
      handleClose={handleCloseOperatorModal}
    >
      <OperatorForm
        initialFormValues={initialFormValues}
        isLoading={isLoading}
        handleCreateNewOperator={handleCreateNewOperator}
        handleUpdateOperator={handleUpdateOperator}
        handleClose={handleCloseOperatorModal}
      />
    </MainModal>
  );
};
