import {
  Autocomplete,
  Box,
  DialogActions,
  Divider,
  InputAdornment,
  Stack,
  Typography,
} from "@mui/material";
import { FormikProvider, useFormik } from "formik";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import {
  CreateVehicleModelDto,
  UpdateVehicleModelDto,
  VehicleModelFormValues,
  VehicleType,
} from "src/common/types";
import { number } from "yup";
import { vehicleValuesRanges } from "src/common/constants";
import { CeButton, CeTextField } from "src/common/components";
import { getCurrentUser, useVehicleTypes } from "src/common/api";
import { getValidationSchemaByType } from "src/common/utils";

interface VehicleModalFormProps {
  initialFormValues: VehicleModelFormValues;
  title: string;
  handleClose: () => void;
  handleCreateNewVehicleModel?: (args: CreateVehicleModelDto) => void;
  handleUpdateVehicleModel?: (args: UpdateVehicleModelDto) => void;
  isLoading: boolean;
}

export const VehicleModalForm: React.FC<VehicleModalFormProps> = ({
  initialFormValues,
  title,
  isLoading,
  handleClose,
  handleCreateNewVehicleModel,
  handleUpdateVehicleModel,
}) => {
  const { t } = useTranslation("common");
  const currentUser = getCurrentUser();

  const formik = useFormik<VehicleModelFormValues>({
    initialValues: initialFormValues,
    enableReinitialize: true,
    validationSchema: yup.object({
      type: yup.object().required("required").nullable(),
      boomSize: number()
        .min(vehicleValuesRanges["boomSize"].min, t("out-of-range"))
        .max(vehicleValuesRanges["boomSize"].max, t("out-of-range"))
        .when("type", (type, schema) => {
          return getValidationSchemaByType(type.name, vehicleValuesRanges);
        })
        .required("required")
        .nullable(),
    }),
    onSubmit: (values) => {
      if (initialFormValues.flow === "Create" && handleCreateNewVehicleModel) {
        const payload: CreateVehicleModelDto = {
          ...initialFormValues,
          ...values,
          operatorManagerId: currentUser?.id!,
          vehicleTypeId: values.type?.id!,
        };

        handleCreateNewVehicleModel(payload);
      }

      if (initialFormValues.flow === "Update" && handleUpdateVehicleModel) {
        const payload: UpdateVehicleModelDto = {
          ...initialFormValues,
          ...values,
          vehicleModelId: values.vehicleModelId!,
          vehicleTypeId: values.type?.id!,
          operatorManagerId: currentUser?.id!,
        };
        handleUpdateVehicleModel(payload);
      }
    },
  });

  const { data: allVehicleTypes, isLoading: isLoadingVehicleTypes } =
    useVehicleTypes(
      { operatorManagerId: currentUser?.id },
      Boolean(currentUser?.id)
    );

  return (
    <FormikProvider value={formik}>
      <Stack
        component="form"
        spacing={2}
        noValidate
        width={300}
        onSubmit={formik.handleSubmit}
      >
        <Stack spacing={2}>
          <Autocomplete
            id="type"
            fullWidth
            value={formik.values.type}
            onChange={(event: any, nextValue: VehicleType | null) => {
              console.log(" Vehicle Type:", nextValue);
              formik.setFieldValue("type", nextValue);
            }}
            onBlur={() => formik.setFieldTouched("type", true)}
            options={allVehicleTypes?.data || []}
            getOptionLabel={(option: VehicleType) => option.name || ""}
            disabled={isLoadingVehicleTypes}
            filterSelectedOptions
            renderInput={(params) => (
              <CeTextField
                {...params}
                InputLabelProps={{ shrink: true }}
                error={formik.touched.type && Boolean(formik.errors.type)}
                helperText={formik.touched.type && formik.errors.type}
                required
                label={t("manager:type")}
                size="small"
              />
            )}
          />

          <CeTextField
            fullWidth
            id="boomSize"
            name="boomSize"
            label={t("manager:vehicle-boom-size")}
            size="small"
            type="number"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  {t("meter-symbol")}
                </InputAdornment>
              ),
            }}
            InputLabelProps={{ shrink: true }}
            value={formik.values.boomSize || ""}
            onChange={formik.handleChange}
            error={formik.touched.boomSize && Boolean(formik.errors.boomSize)}
            helperText={formik.touched.boomSize && formik.errors.boomSize}
            disabled={isLoading}
            required
          />
        </Stack>

        <DialogActions
          sx={{
            display: "flex",
            flexDirection: "column",
            padding: "0",
            gap: 1.5,
          }}
        >
          <CeButton
            type="submit"
            disabled={isLoading}
            variant="contained"
            color="primary"
            fullWidth
          >
            <Typography
              sx={{
                fontSize: "14px",
                letterSpacing: "0.4px",
              }}
            >
              {t(
                `common:${
                  formik.values.flow === "Create" ? "create" : "update"
                }-vehicle-model`
              )}
            </Typography>
          </CeButton>
          <CeButton
            variant="text"
            onClick={handleClose}
            disabled={isLoading}
            fullWidth
          >
            {t("common:cancel")}
          </CeButton>
        </DialogActions>
      </Stack>
    </FormikProvider>
  );
};
