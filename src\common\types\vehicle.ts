import { User } from "./user";
import { CommonEntity, Location, PossibleSortDir } from "./common";
import { PriceList } from "./priceList";
import { ContractedStatus } from "./contract";
import { VehicleModels } from "./vehicleModels";
import { VehicleType } from "./vehicleType";
import { JobStatus } from "./job";
import { Operator } from "./operator";
import { UnavailablePeriodPlanning } from "./unavailablePeriod";

export interface Vehicle extends CommonEntity {
  id: number;
  name?: string | null;
  operator?: Operator | null;
  manager?: User | null;
  type: VehicleTypes | null;
  vehicleBrand: string | null;
  brandModel: string | null;
  typeOfMotorization: MotorisationType | null;
  licensePlateNumber: string | null;
  weight: number | null;
  height: number | null;
  length: number | null;
  width?: number | null;
  maxVerticalReach: number | null;
  maxHorizontalReach: number | null;
  hasStrangler: boolean;
  invoicingPipesFrom: string | null;
  pipeLengthForSecondTechnician: string | null;
  endHoseLength?: number | null;
  maxFlowRate: number | null;
  maxConcretePressure?: number | null;
  availableFlexiblePipeLength80Mm: number | null;
  availableFlexiblePipeLength90Mm: number | null;
  availableFlexiblePipeLength100Mm: number | null;
  availableFlexiblePipeLength120Mm: number | null;
  availableRigidPipeLength: number | null;
  maxDownwardReach: number | null;
  numberOfBoomSections?: number | null;
  minUnfoldingHeight?: number | null;
  boomRotation?: number | null;
  frontOutriggerSpan?: number | null;
  rearOutriggerSpan?: number | null;
  frontPressureOnOutrigger?: number | null;
  rearPressureOnOutrigger?: number | null;
  frontSideOpening: number | null;
  rearSideOpening: number | null;
  boomUnfoldingSystem: BoomUnfoldingSystemType | null;
  bacExit: boolean | null;
  bacExitReverse: boolean | null;
  siteAddress: string | null;
  city?: string | null;
  plz?: number | null;
  country: string | null;
  location: Location | null;
  uniqueIdentificationNumber: string | null;
  completedJobs: number | null;
  boomSize: number | null;
  assignedPricelist: PriceList | null;
}

export interface VehicleFormValues {
  vehicleId: number | null;
  operator: Operator | null;
  type: VehicleTypes | null;
  boomSize: number | null;
  typeOfMotorization: MotorisationType | null;
  vehicleBrand: string | null;
  brandModel: string | null;
  licensePlateNumber: string;
  weight: number | null;
  height: number | null;
  length: number | null;
  width: number | null;
  maxVerticalReach: number | null;
  maxHorizontalReach: number | null;
  hasStrangler: boolean;
  invoicingPipesFrom: string | null;
  pipeLengthForSecondTechnician: string | null;
  endHoseLength: number | null;
  maxFlowRate: number | null;
  maxConcretePressure: number | null;
  availableFlexiblePipeLength80Mm: number | null;
  availableFlexiblePipeLength90Mm: number | null;
  availableFlexiblePipeLength100Mm: number | null;
  availableFlexiblePipeLength120Mm: number | null;
  availableRigidPipeLength: number | null;
  maxDownwardReach: number | null;
  numberOfBoomSections: number | null;
  minUnfoldingHeight: number | null;
  boomRotation: number | null;
  frontOutriggerSpan: number | null;
  rearOutriggerSpan: number | null;
  frontSideOpening: number | null;
  rearSideOpening: number | null;
  frontPressureOnOutrigger?: number | null;
  rearPressureOnOutrigger?: number | null;
  boomUnfoldingSystem: BoomUnfoldingSystemType | null;
  bacExit: boolean;
  bacExitReverse: boolean;
  siteAddress: string;
  city?: string;
  plz?: number;
  country: string;
  location: Location;
  uniqueIdentificationNumber: string | null;
  completedJobs: number | null;
  flow: VehicleModalFlow;
}

export interface VehiclesWithCount {
  totalCount: number;
  data: Vehicle[];
}

export interface getVehiclesDto {
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
  operatorManagerId?: number | null;
  type?: VehicleTypes | null;
  boomSize?: number | null;
  siteAddress?: string | null;
  city?: string | null;
  plz?: number | null;
  location?: Location;
  availableFlexiblePipeLength80Mm?: number | null;
  availableFlexiblePipeLength90Mm?: number | null;
  availableFlexiblePipeLength100Mm?: number | null;
  availableFlexiblePipeLength120Mm?: number | null;
  frontOutriggerSpan?: number | null;
  rearOutriggerSpan?: number | null;
  dateFrom?: Date | null;
  dateTo?: Date | null;
  coordinates?: number[] | null;
  radius?: number;
  vehicleIds?: number[];
  searchType?: SearchVehicleType;
}

export interface VehicleReservationFilterDto {
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
  operatorManagerId?: number | null;
  type?: VehicleTypes | null;
  boomSize?: number | null;
  siteAddress?: string | null;
  availableFlexiblePipeLength80Mm?: number | null;
  availableFlexiblePipeLength90Mm?: number | null;
  availableFlexiblePipeLength100Mm?: number | null;
  availableFlexiblePipeLength120Mm?: number | null;
  frontOutriggerSpan?: number | null;
  rearOutriggerSpan?: number | null;
  dateFrom?: string;
  dateTo?: string;
  searchType: SearchVehicleType;
  dispatcherCompanyId?: number | null;
  operatorCompanyIds?: number[] | null;
  vehicleTypeId?: number | null;
}

export interface GetSearchedVehicleDto extends getVehiclesDto {
  status?: ContractedStatus | ContractedStatus[] | null;
  dispatcherManagerId?: number | null;
  operatorManagerIds?: number[];
  dispatcherCompanyId?: number | null;
  operatorCompanyIds?: number[] | null;
  searchType: SearchVehicleType;
}

export interface CreateVehicleDto
  extends Omit<Vehicle, "manager" | "operator" | "id" | "assignedPricelist"> {
  operatorId: number | null;
}

export interface UpdateVehicleDto extends CreateVehicleDto {
  vehicleId: number;
  operatorId: number | null;
}

export interface DeleteVehicleDto {
  vehicleId: number;
}

export interface DeleteVehicleModalValues {
  vehicleId?: number;
  vehicleTitle?: string;
  flow: VehicleModalDeleteFlow;
}

export interface OpenVehicleInformationValues {
  vehicleId: number | null;
  flow: VehicleInformationFlow;
}

export interface FilterVehiclesFormValues {
  type: VehicleTypes | null;
  boomSize: number | null;
  pipeDiameter: PipeDiameter | null;
  requiredPipes: number | null;
  frontOutriggerSpan?: number | null;
  rearOutriggerSpan?: number | null;
  flow?: VehicleFilterModalFlow | null;
  dateFrom?: string | null;
  dateTo?: string | null;
  coordinates?: number[] | null;
  radius: number;
  siteAddress?: string | null;
}

export interface FilterVehiclesDto {
  type: VehicleTypes | null;
  boomSize: number | null;
  availableFlexiblePipeLength80Mm: number | null;
  availableFlexiblePipeLength90Mm: number | null;
  availableFlexiblePipeLength100Mm: number | null;
  availableFlexiblePipeLength120Mm: number | null;
  frontOutriggerSpan: number | null;
  rearOutriggerSpan: number | null;
  dateFrom: Date | null;
  dateTo: Date | null;
  coordinates: number[] | null;
  radius: number;
  siteAddress?: string | null;
}

export interface CheckVehicleAvailabilityDto {
  id: number;
  dateFrom: string;
  dateTo: string;
  currentReservationId: number;
}

export type VehicleModalFlow = "Create" | "Update" | "Details" | null;
export type VehicleInformationFlow = "Vehicle Details" | null;
export type VehicleFilterModalFlow = "Filter" | null;
export type VehicleModalDeleteFlow = "Delete" | null;
export type VehicleTypes =
  | "Pump"
  | "City Pump"
  | "Mixo Pump"
  | "Stationary Pump"
  | "Mixer";
export type PipeDiameter = "80mm" | "90mm" | "100mm" | "120mm";

export enum VehicleTypeEnum {
  PUMP = "Pump",
  CITY_PUMP = "City Pump",
  MIXO_PUMP = "Mixo Pump",
  STATIONARY_PUMP = "Stationary Pump",
  MIXER = "Mixer",
}

export enum VehicleTypeCode {
  PUMP = "P",
  CITY_PUMP = "CP",
  MIXO_PUMP = "MX",
  STATIONARY_PUMP = "SP",
  MIXER = "M",
}

export type MotorisationType =
  | "Diesel (red diesel)"
  | "Petrol/Gasoline"
  | "Diesel"
  | "Electric"
  | "Hybrid";

export type BoomUnfoldingSystemType =
  | "R"
  | "RZ"
  | "Z"
  | "ZR"
  | "ZZ"
  | "M"
  | "OTHER";

export const vehicleTypes: VehicleTypes[] = [
  "Pump",
  "City Pump",
  "Mixo Pump",
  "Stationary Pump",
  "Mixer",
];

export const boomUnfoldingSystemTypes: BoomUnfoldingSystemType[] = [
  "R",
  "RZ",
  "Z",
  "ZR",
  "ZZ",
  "M",
  "OTHER",
];

export const VehicleMotorisationTypes: MotorisationType[] = [
  "Diesel (red diesel)",
  "Petrol/Gasoline",
  "Diesel",
  "Electric",
  "Hybrid",
];

export const PipeDiameterTypes: PipeDiameter[] = [
  "80mm",
  "90mm",
  "100mm",
  "120mm",
];

export const searchVehicleTypes: SearchVehicleType[] = [
  "All",
  "Contracted",
  "Favorited",
  "Partnered",
];

export type SearchVehicleType =
  | "All"
  | "Favorited"
  | "Contracted"
  | "Partnered";

export interface ReservationVehicleDetails {
  id: number;
  dateFrom: string;
  dateTo: string;
  operatorName: string;
  siteAddress: string;
  city: string;
  pricelistId: number;
  amount: number;
  flowRate: number;
  jobStatus: JobStatus;
  ownReservation: boolean;
  hasBeenRead: boolean;
  dispatcherId: number;
}

export interface VehicleReservation {
  id: number;
  vehicleUniqueId: string;
  operatorName: string;
  companyName: string;
  companyEmail: string;
  companyPhoneNumber: string;
  boomSize: number;
  type: string;
  managerId: number;
  operatorId: number;
  operatorPhoneNumber: string;
  hasAssignedPricelist: boolean;
  reservations: ReservationVehicleDetails[];
  unavailablePeriods: UnavailablePeriodPlanning[];
  operatorStatus: number;
}

export interface VehicleReservationsResponse {
  data: VehicleReservation[];
  totalCount: number;
}
