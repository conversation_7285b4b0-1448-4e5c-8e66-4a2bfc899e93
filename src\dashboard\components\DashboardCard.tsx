import { ArrowDownRight01Icon, ArrowUpRight01Icon } from '@hugeicons/react';
import { Box, Chip, styled, Typography } from '@mui/material';
import React from 'react';
import { CeChip, CePaper } from 'src/common/components';

interface DashboardCardProps {
    title?: string;
    value?: number;
    unit?: string;
    trendData: number;
}

const DashboardCard: React.FC<DashboardCardProps> = ({ title, value, unit, trendData }) => {
    const isPositive = trendData >= 0;
    const icon = isPositive ? (
        <ArrowUpRight01Icon size={14} variant={"stroke"} />
    ) : (
        <ArrowDownRight01Icon size={16} variant={"stroke"} />
    );
    const label = trendData || 0;

    return(
    <CePaper
        elevation={1}
        sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}
    >
        <Typography
            variant="body2"
            color="text.secondary"
            sx={{ fontSize: '14px', fontWeight: 700, mb: 1 }}
        >
            {title}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography
                variant="h5"
                component="div"
                sx={{ fontWeight: 700, fontSize: '20px', mr: 1 }}
            >
                {value || 0}
                {unit && (
                    <Typography
                        component="span"
                        sx={{ ml: 0.5, fontSize: '20px', fontWeight: 700 }}
                    >
                        {unit}
                    </Typography>
                )}
            </Typography>
            <CeChip
                label={`${label}%`}
                deleteIcon={icon}
                onDelete={() => null}
                size="small"
                status={isPositive ? 'positive' : 'negative'}
            />
        </Box>
    </CePaper>
)}    

export default DashboardCard;