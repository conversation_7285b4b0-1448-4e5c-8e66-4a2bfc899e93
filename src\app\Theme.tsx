import "@fontsource/roboto/300.css";
import "@fontsource/roboto/400.css";
import "@fontsource/roboto/500.css";
import "@fontsource/roboto/700.css";
import { useEffect } from "react";
import { enUS, nlNL, deDE, frFR } from "@mui/x-data-grid/locales";
import {
  createTheme,
  CssBaseline,
  ThemeOptions,
  ThemeProvider,
} from "@mui/material";
import { useRecoilValue } from "recoil";

import { themeState } from "../common/state";
import { useTranslation } from "react-i18next";

export const darkThemeOptions: ThemeOptions = {
  typography: {
    fontFamily: "Rethink Sans, Roboto, sans-serif",
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          fontFamily: "Rethink Sans, Roboto, sans-serif",
        },
      },
    },
  },
  palette: {
    mode: "dark",
    primary: {
      main: "#42a5f5",
      light: "#64b5f6",
      dark: "#2196f3",
      contrastText: "#fff",
    },
    secondary: {
      main: "#29b6f6",
      light: "#4fc3f7",
      dark: "#03a9f4",
      contrastText: "#fff",
    },
    error: {
      main: "#f44336",
      light: "#e57373",
      dark: "#d32f2f",
      contrastText: "#fff",
    },
    warning: {
      main: "#ffa726",
      light: "#ffe0b2",
      dark: "#ff9800",
      contrastText: "#fff",
    },
    info: {
      main: "#29b6f6",
      light: "#4fc3f7",
      dark: "#0288d1",
      contrastText: "#fff",
    },
    success: {
      main: "#8bc34a",
      light: "#aed581",
      dark: "#689f38",
      contrastText: "#fff",
    },
  },
};

export const lightThemeOptions: ThemeOptions = {
  typography: {
    fontFamily: "Rethink Sans, Roboto, sans-serif",
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          backgroundColor: "#f3f3f3 !important", // not a good way to write css using important!
          fontFamily: "Rethink Sans, Roboto, sans-serif",
        },
      },
    },
  },
  palette: {
    mode: "light",
    primary: {
      main: "#0057b2",
      light: "#1e88e5",
      dark: "#1565c0",
      contrastText: "#fff",
    },
    secondary: {
      main: "#0288d1",
      light: "#039be5",
      dark: "#0277bd",
      contrastText: "#fff",
    },
    error: {
      main: "#e53935",
      light: "#ef5350",
      dark: "#c62828",
      contrastText: "#fff",
    },
    warning: {
      main: "#fb8c00",
      light: "#ffb74d",
      dark: "#f57c00",
      contrastText: "#fff",
    },
    info: {
      main: "#0288d1",
      light: "#03a9f4",
      dark: "#01579b",
      contrastText: "#fff",
    },
    success: {
      main: "#7cb342",
      light: "#8bc34a",
      dark: "#689f38",
      contrastText: "#fff",
    },
  },
};

export const Theme: React.FC = ({ children }) => {
  const theme = useRecoilValue(themeState);
  const themeOptions = theme === "light" ? lightThemeOptions : darkThemeOptions;
  const { i18n } = useTranslation(["common", "dispatcher"]);
  const selectedLanguage = i18n.language;

  const localeText: { [key: string]: any } = {
    en: enUS,
    fr: frFR,
    nl: nlNL,
    de: deDE,
  };

  const concreteEasyTheme = createTheme(
    themeOptions,
    selectedLanguage ? localeText[selectedLanguage] : enUS
  );

  useEffect(() => {
    document.body.style.backgroundColor =
      concreteEasyTheme.palette.background.default;
  }, [concreteEasyTheme.palette.background.default]);

  return (
    <>
      <ThemeProvider theme={concreteEasyTheme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </>
  );
};
