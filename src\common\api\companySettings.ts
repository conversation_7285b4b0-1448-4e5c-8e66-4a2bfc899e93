import { useMutation, useQuery, useQueryClient } from "react-query";
import axios, { AxiosError } from "axios";

import { processApiError, processApiSuccess } from "../utils/errors";
import {
  CompanySettings,
  CompanySettingsWithCount,
  CreateCompanySettingsDto,
  GetCompanySettingsDto,
  UpdateCompanySettingsDto,
} from "../types/companySettings";

const backendUrl = process.env.REACT_APP_API_URL;

// GET ONE Company
export const getCompanySetting = async (companyId?: number) => {
  if (!companyId) {
    throw new Error("The company ID was not provided");
  }
  return axios
    .get(`${backendUrl}/company-settings/${companyId}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useCompanySetting = (
  companyId?: number,
  enabled: boolean = true
) => {
  return useQuery<CompanySettings, AxiosError | Error>(
    ["companySettings", companyId],
    () => getCompanySetting(companyId),
    {
      onError: (err) => processApiError("Unable to fetch company", err),
      enabled,
    }
  );
};

// GET MANY CompanySettings
export const getCompanySettings = async (params: GetCompanySettingsDto) => {
  return axios
    .get(`${backendUrl}/company-settings`, {
      withCredentials: true,
      params,
    })
    .then((response) => response.data);
};

export const useCompanySettings = (
  params: GetCompanySettingsDto,
  enabled: boolean = true
) => {
  return useQuery<CompanySettingsWithCount, AxiosError | Error>(
    ["companySettings", params],
    () => getCompanySettings(params),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch companies", err),
      enabled,
    }
  );
};

// CREATE NEW Company
export const createNewCompanySetting = (attrs: CreateCompanySettingsDto) => {
  return axios
    .post(`${backendUrl}/company-settings`, attrs, { withCredentials: true })
    .then((response) => response.data);
};

export const useCreateNewCompanySettings = () => {
  const queryClient = useQueryClient();
  return useMutation<
    CompanySettings,
    AxiosError | Error,
    CreateCompanySettingsDto,
    () => void
  >((attrs: CreateCompanySettingsDto) => createNewCompanySetting(attrs), {
    onSuccess: (newCompany) => {
      queryClient.invalidateQueries("companySettings");
      processApiSuccess(
        `CompanySetting created successfully: ${newCompany.companyId}`
      );
    },
    onError: (err) => processApiError("Unable to create companySetting", err),
  });
};

// UPDATE Company BY ID
export const handleUpdateCompanySetting = (
  updateCompanySettingsArgs: UpdateCompanySettingsDto
) => {
  const { companySettingsId, ...companySettings } = updateCompanySettingsArgs;
  if (!companySettingsId) {
    throw new Error("The company ID was not provided");
  }
  return axios
    .patch(
      `${backendUrl}/company-settings/${companySettingsId}`,
      companySettings,
      {
        withCredentials: true,
      }
    )
    .then((response) => response.data);
};

export const useUpdateCompanySetting = () => {
  const queryClient = useQueryClient();
  return useMutation<
    CompanySettings,
    AxiosError | Error,
    UpdateCompanySettingsDto
  >(
    (updateCompanySettingsArgs: UpdateCompanySettingsDto) =>
      handleUpdateCompanySetting(updateCompanySettingsArgs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("companySetting");
        queryClient.invalidateQueries("companySettings");
        processApiSuccess("CompanySetting updated successfully");
      },
      onError: (err) => processApiError("Unable to update companySetting", err),
    }
  );
};
