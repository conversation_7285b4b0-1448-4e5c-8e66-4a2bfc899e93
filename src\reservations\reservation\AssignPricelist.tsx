import {
  <PERSON>complete,
  Card<PERSON>ontent,
  Grid,
  Stack,
  SxProps,
  Theme,
  Typography,
} from "@mui/material";
import React from "react";
import { useTranslation } from "react-i18next";
import { Ce<PERSON>ard, CeTextField } from "src/common/components";
import { GetContractedPriceList } from "src/common/types";
import { PriceList } from "src/common/types/priceList";

interface AssignPricelistProps {
  attrs: GetContractedPriceList;
  assignedPricelistId: number | null;
  onPricelistChange: (pricelistId: PriceList | null) => void;
  pricelists: PriceList[] | [];
}

const AssignPricelist: React.FC<AssignPricelistProps> = ({
  attrs,
  assignedPricelistId,
  pricelists,
  onPricelistChange,
}) => {
  const { t } = useTranslation(["common"]);
  const cardStyle: SxProps<Theme> = {
    mt: 2.5,
    padding: "10px 5px",
    borderRadius: "12px",
    "& > *": { padding: "16px" },
  };

  return (
    <CeCard sx={cardStyle}>
      <CardContent>
        <Grid container columnSpacing={0} rowSpacing={2}>
          <Grid item xs={12}>
            <Typography
              fontSize={20}
              fontWeight="400"
              paddingBottom={1}
              textAlign="center"
            >
              {t("select-pricelist")}
            </Typography>
          </Grid>
        </Grid>
        <Stack direction={"row"} spacing={2}>
          <Autocomplete
            id="pricelist"
            fullWidth
            options={pricelists}
            value={pricelists.find((v) => v.id === assignedPricelistId) || null}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            filterOptions={(options) =>
              options.filter((option) => option.id !== assignedPricelistId)
            }
            getOptionLabel={(variant) => variant.title || ""}
            onChange={(_, selected) => onPricelistChange?.(selected)}
            renderInput={(params) => (
              <CeTextField
                {...params}
                InputLabelProps={{ shrink: true }}
                label={t("pricelist")}
                size="small"
              />
            )}
          />
        </Stack>
      </CardContent>
    </CeCard>
  );
};

export default AssignPricelist;
