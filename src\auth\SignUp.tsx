import { Grid } from "@mui/material";
// import { useEffect } from 'react'
// import { useNavigate } from 'react-router-dom'
// import { useSignUpWithEmailAndPassword } from 'src/common/api'
// import { SignUpWithEmailForm } from './SignUpWithEmailForm'

export const SignUp: React.FC = () => {
  // const navigate = useNavigate()
  // const { mutate: handleSignUp, isLoading, isSuccess } = useSignUpWithEmailAndPassword()

  // useEffect(() => {
  //   if (!isLoading && isSuccess) {
  //     const navigateTo = '/locations/practices'
  //     navigate(navigateTo, { replace: true })
  //   }
  // }, [isLoading, isSuccess, navigate])

  return (
    <Grid container direction="row" justifyContent="center" alignItems="center">
      {/* <SignUpWithEmailForm handleSignUp={handleSignUp} isLoading={isLoading} /> */}
    </Grid>
  );
};
