import { AxiosError } from "axios";
import { UseMutateFunction } from "react-query";
import {
  Contract,
  ContractFormValues,
  CreateContractDto,
  CreateUnavailabilityVehicleDto,
  Vehicle,
} from "src/common/types";
import { Drawer } from "@mui/material";
import { CreateContractForm } from "./CreateContractForm";
import { Partner } from "src/common/types/partners";

interface ContractModalProps {
  isLoading: boolean;
  initialFormValues: ContractFormValues;
  handleCloseContractModal: () => void;
  title?: string;
  handleCreateContract: (args: CreateContractDto) => void;
  vehicles: Vehicle[];
  partners: Partner[];
}
export const ContractFormDrawer: React.FC<ContractModalProps> = ({
  isLoading,
  initialFormValues,
  handleCloseContractModal,
  handleCreateContract,
  vehicles,
  partners,
  title,
}) => {
  return (
    <Drawer
      anchor="right"
      open={!!initialFormValues.flow}
      onClose={handleCloseContractModal}
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 2,
        display: "flex",
        flexDirection: "column",
        gap: 2,
        "& .MuiBackdrop-root": {
          backgroundColor: "transparent",
        },
      }}
    >
      <CreateContractForm
        handleClose={handleCloseContractModal}
        handleCreateContract={handleCreateContract}
        initialFormValues={initialFormValues}
        isLoading={isLoading}
        partners={partners}
        vehicles={vehicles}
        title={title}
      />
    </Drawer>
  );
};
