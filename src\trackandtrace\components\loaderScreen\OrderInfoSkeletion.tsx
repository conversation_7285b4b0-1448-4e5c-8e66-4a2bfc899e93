import React from 'react';
import {
    Box,
    Grid,
    Skeleton,
    useMediaQuery,
    useTheme,
} from '@mui/material';

const OrderInformationSkeleton: React.FC = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

    return (
        <Box sx={{ padding: isMobile ? 2 : 3 }}>
            <Skeleton variant="text" width="40%" height={32} sx={{ mb: 2 }} />
            <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                    <Skeleton variant="text" width="40%" sx={{ mb: 1 }} />
                    <Skeleton variant="text" width="80%" />
                </Grid>
                <Grid item xs={12} sm={6}>
                    <Skeleton variant="text" width="40%" sx={{ mb: 1 }} />
                    <Skeleton variant="text" width="80%" />
                </Grid>
                <Grid item xs={12} sm={6}>
                    <Skeleton variant="text" width="40%" sx={{ mb: 1 }} />
                    <Skeleton variant="text" width="80%" />
                </Grid>
                <Grid item xs={12} sm={6}>
                    <Skeleton variant="text" width="40%" sx={{ mb: 1 }} />
                    <Skeleton variant="rectangular" width="80%" height={40} />
                </Grid>
                <Grid item xs={12} sm={6}>
                    <Skeleton variant="text" width="40%" sx={{ mb: 1 }} />
                    <Skeleton variant="text" width="80%" />
                </Grid>
                <Grid item xs={12} sm={6}>
                    <Skeleton variant="text" width="40%" sx={{ mb: 1 }} />
                    <Skeleton variant="text" width="80%" />
                </Grid>
            </Grid>
        </Box>
    );
};
export default OrderInformationSkeleton