import { GridSortModel } from "@mui/x-data-grid";
import {
  CommonEntity,
  PossibleSortDir,
  Expression,
  TaskLabel,
  Company,
  User,
} from ".";

export enum TaskViews {
  CALENDAR = "calendar",
  LIST = "list",
  BOARD = "board",
}

export enum TaskFormFlow {
  CREATE = "Create",
  UPDATE = "Update",
  DELETE = "Delete",
}
export enum Priority {
  LOW = "Low",
  MEDIUM = "Medium",
  HIGH = "High",
}

export enum TaskStatus {
  TODO = "To Do",
  DOING = "Doing",
  DONE = "Done",
}

export enum ActionType {
  STATUS_UPDATE = "StatusUpdate",
  ASSIGNEE_CHANGE = "AssigneeChange",
  PRIORITY_UPDATE = "PriorityUpdate",
  LABEL_CHANGE = "LabelChange",
}

export interface TaskComment extends CommonEntity {
  content: string;
  taskId?: number;
  task?: Task;
}

export interface CreateTaskCommentDto {
  content: string;
  taskId: number;
}

export interface TaskActivity extends CommonEntity {
  actionType: ActionType;
  oldValue: string;
  taskId: number;
  task?: Task;
}

export interface Task extends CommonEntity {
  id: number;
  startDate: Date;
  dueDate: Date;
  labelId?: number;
  label: TaskLabel;
  priority: Priority;
  status: TaskStatus;
  description: string;
  assigneeId: number;
  assignee: User;
  companyId: number;
  company?: Company;
  comments?: TaskComment[];
  activities?: TaskActivity[];
}
export interface TaskFormValues {
  taskId: number | null;
  startDate: Date | null;
  dueDate: Date | null;
  label: TaskLabel | null;
  priority: Priority | null;
  status: TaskStatus | null;
  description: string | null;
  assignee: User | null;
  flow: TaskFormFlow | null;
  comments: TaskComment[] | [];
  activities: TaskActivity[] | [];
  assigneeBeingSet: boolean;
  labelBeingSet: boolean;
  priorityBeingSet: boolean;
  descriptionBeingSet: boolean;
  dueDateBeingSet: boolean;
  statusBeingSet: boolean;
}
export interface TaskWithCount {
  totalCount: number;
  data: Task[];
}
export interface GetTaskDto {
  expressions: Expression[];
  sortModel: GridSortModel;
  relations?: string[];
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  priority?: Priority;
  status?: TaskStatus;
  dueDateFrom?: Date;
  dueDateTo?: Date;
}

export interface CreateTaskDto {
  startDate?: Date | null;
  labelId?: number;
  priority: Priority;
  status: TaskStatus;
  assigneeId: number;
  dueDate?: Date;
  description: string;
}

export type UpdateTaskDto = Partial<CreateTaskDto> & { taskId: number };

export interface DeleteTaskDto {
  taskId: number;
}
