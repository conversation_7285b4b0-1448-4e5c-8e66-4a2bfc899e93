import { CommonEntity } from "./common";
import { Company } from "./company";
import { Job, JobEvent, JobReport } from "./job";
import { Reservation } from "./reservation";

export interface TrackAndTraceArgs{
    orderNumber: string; 
    clientEmail: string 
  }
  
  export interface DecodedToken {
    orderNumber: string;
    clientEmail: string;
    iat: number;
    exp: number;
  }
  export interface TrackAndTrace extends CommonEntity {
    company: Company,
    job: Job,
    reservation: Reservation
  }

  export type SignatureFormValue = {
    signature: string;
    jobId: number;
  };
  