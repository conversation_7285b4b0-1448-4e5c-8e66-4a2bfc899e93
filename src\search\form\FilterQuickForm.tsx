import { Autocomplete, InputAdornment, Stack } from "@mui/material";
import enGb from "date-fns/locale/en-GB";
import { FormikProvider, useFormik } from "formik";
import { LocalizationProvider, DateTimePicker } from "@mui/x-date-pickers";

import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import {
  FilterVehiclesDto,
  FilterVehiclesFormValues,
  VehicleTypes,
  vehicleTypes,
} from "src/common/types";
import { turnFilterVehicleFormValuesIntoDto } from "src/common/utils";
import { isBefore } from "date-fns";
import { Calendar04Icon } from "@hugeicons/react";
import { CeTextField, CeButton } from "src/common/components";
import AutocompleteInput from "src/common/components/custom/AutocompleteGoogleInput";

export interface ExistingFilterFormValues extends FilterVehiclesFormValues {
  siteAddress?: string | null;
}
interface FilterQuickFormProps {
  isLoading: boolean;
  handleClose: () => void;
  handleFilterVehicle?: (args: FilterVehiclesDto) => void;
  initialFormValues: ExistingFilterFormValues;
  enableDateRange?: boolean;
}

export const FilterQuickForm: React.FC<FilterQuickFormProps> = ({
  isLoading,
  handleFilterVehicle,
  initialFormValues,
  enableDateRange = true,
}) => {
  const { t } = useTranslation(["common", "manager", "dispatcher"]);

  const formik = useFormik<ExistingFilterFormValues>({
    initialValues: initialFormValues,
    enableReinitialize: true,
    validationSchema: yup.object({
      coordinates: yup.array().length(2).nullable(),
      radius: yup.number(),
      dateFrom: yup.date().when([], {
        is: () => enableDateRange,
        then: (schema) =>
          schema
            .required("required")
            .typeError("invalid-date")
            .test(
              "isDateFromInPast",
              t("common:cannot-select-past-date-from")!,
              function (value) {
                if (!value) return false;
                const currentTime = new Date();
                const fiveMinutesAgo = new Date(
                  currentTime.getTime() - 5 * 60 * 1000
                );
                return !isBefore(value, fiveMinutesAgo);
              }
            )
            .test("dateFrom", t("common:date-from-error")!, function (value) {
              if (!value) return false;
              const { dateTo } = this.parent;
              return value < dateTo;
            }),
        otherwise: (schema) => schema.nullable(),
      }),
      dateTo: yup.date().when([], {
        is: () => enableDateRange,
        then: (schema) =>
          schema
            .required("required")
            .typeError("invalid-date")
            .test(
              "isDateToInPast",
              t("common:cannot-select-past-date-to")!,
              function (value) {
                if (!value) return false;
                const currentTime = new Date();
                const fiveMinutesAgo = new Date(
                  currentTime.getTime() - 5 * 60 * 1000
                );
                return !isBefore(value, fiveMinutesAgo);
              }
            )
            .test("dateTo", t("common:date-to-error")!, function (value) {
              if (!value) return false;
              const { dateFrom } = this.parent;
              return value > dateFrom;
            }),
        otherwise: (schema) => schema.nullable(),
      }),
    }),
    onSubmit: (values) => {
      if (handleFilterVehicle) {
        const payload: FilterVehiclesDto =
          turnFilterVehicleFormValuesIntoDto(values);

        handleFilterVehicle(payload);
      }
    },
  });

  return (
    <FormikProvider value={formik}>
      <Stack
        component="form"
        direction={"row"}
        spacing={2}
        justifyContent={"space-between"}
        noValidate
        onSubmit={formik.handleSubmit}
        sx={{ width: "100%" }}
      >
        <Stack direction={"row"} spacing={2}>
          <Autocomplete
            id="type"
            fullWidth
            value={formik.values.type || null}
            onChange={(event: any, nextValues: VehicleTypes | null) => {
              formik.setFieldValue("type", nextValues);
            }}
            onBlur={() => formik.setFieldTouched("type", true)}
            options={vehicleTypes}
            filterSelectedOptions
            renderInput={(params) => (
              <CeTextField
                {...params}
                InputLabelProps={{ shrink: true }}
                error={formik.touched.type && Boolean(formik.errors.type)}
                helperText={formik.touched.type && formik.errors.type}
                required
                label={t("common:type")}
                size="small"
              />
            )}
          />

          <CeTextField
            fullWidth
            id="boomSize"
            name="boomSize"
            label={t("common:vehicle-boom-size")}
            size="small"
            type="number"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  {t("meter-symbol")}
                </InputAdornment>
              ),
            }}
            InputLabelProps={{ shrink: true }}
            value={formik.values.boomSize || ""}
            onChange={formik.handleChange}
            error={formik.touched.boomSize && Boolean(formik.errors.boomSize)}
            helperText={formik.touched.boomSize && formik.errors.boomSize}
            disabled={isLoading}
          />

          <AutocompleteInput
            id="siteAddress"
            name="siteAddress"
            existingInputValue={formik.values.siteAddress}
            label={t("common:site-address")}
            variant="outlined"
            onAddressChange={({ lng, lat, siteAddress, city, countryCode }) => {
              if (lng || lat) {
                formik.setFieldValue("coordinates", [lng, lat]);
                formik.setFieldValue(
                  "siteAddress",
                  siteAddress ? `${siteAddress}, ${city}, ${countryCode}` : ""
                );
              } else {
                formik.setFieldValue("coordinates", null);
                formik.setFieldValue("siteAddress", "");
              }
            }}
            disabled={isLoading}
          />

          {enableDateRange && (
            <>
              <LocalizationProvider
                dateAdapter={AdapterDateFns}
                adapterLocale={enGb}
              >
                <DateTimePicker
                  minutesStep={5}
                  renderInput={(props: any) => (
                    <CeTextField
                      {...props}
                      fullWidth
                      size="small"
                      onBlur={() => formik.setFieldTouched("dateFrom", true)}
                      error={
                        formik.errors.dateFrom &&
                        Boolean(formik.touched.dateFrom)
                      }
                      helperText={
                        formik.touched.dateFrom && formik.errors.dateFrom
                      }
                      required={enableDateRange}
                    />
                  )}
                  label={t("date-from")}
                  value={formik.values.dateFrom}
                  onChange={(newValue) => {
                    formik.setFieldValue("dateFrom", newValue);
                  }}
                  components={{
                    OpenPickerIcon: Calendar04Icon,
                  }}
                />
              </LocalizationProvider>
              <LocalizationProvider
                dateAdapter={AdapterDateFns}
                adapterLocale={enGb}
              >
                <DateTimePicker
                  minutesStep={5}
                  renderInput={(props: any) => (
                    <CeTextField
                      {...props}
                      size="small"
                      fullWidth
                      error={
                        formik.errors.dateTo && Boolean(formik.touched.dateTo)
                      }
                      helperText={formik.touched.dateTo && formik.errors.dateTo}
                      onBlur={() => formik.setFieldTouched("dateTo", true)}
                      required={enableDateRange}
                    />
                  )}
                  label={t("date-to")}
                  value={formik.values.dateTo}
                  onChange={(newValue) => {
                    formik.setFieldValue("dateTo", newValue);
                  }}
                  components={{
                    OpenPickerIcon: Calendar04Icon,
                  }}
                />
              </LocalizationProvider>
            </>
          )}
        </Stack>
        <CeButton
          type="submit"
          disabled={isLoading}
          variant="outlined"
          sx={{ width: "220px" }}
        >
          {t("search")}
        </CeButton>
      </Stack>
    </FormikProvider>
  );
};
