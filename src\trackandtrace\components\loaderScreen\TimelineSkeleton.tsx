// TimelineSkeleton.tsx
import React from "react";
import { Skeleton, Box } from "@mui/material";
import Timeline from "@mui/lab/Timeline";
import TimelineItem from "@mui/lab/TimelineItem";
import TimelineSeparator from "@mui/lab/TimelineSeparator";
import TimelineConnector from "@mui/lab/TimelineConnector";
import TimelineContent from "@mui/lab/TimelineContent";
import { JOB_STEPS } from "src/common/constants";

const TimelineSkeleton: React.FC = () => {
  return (
    <Timeline
      sx={{
        [`& .MuiTimelineItem-root:before`]: { flex: 0, padding: 0 },
      }}
      position="right"
    >
      {JOB_STEPS.map((step, index) => (
        <TimelineItem key={step.name}>
          <TimelineSeparator>
            <Skeleton variant="circular" width={40} height={40} />
            {index < JOB_STEPS.length - 1 && (
              <TimelineConnector sx={{ bgcolor: "grey.400" }} />
            )}
          </TimelineSeparator>
          <TimelineContent sx={{ py: "12px", px: 2 }}>
            <Skeleton variant="text" width="60%" />
            <Skeleton variant="text" width="40%" />
          </TimelineContent>
        </TimelineItem>
      ))}
    </Timeline>
  );
};

export default TimelineSkeleton;
