import { FC, lazy, Suspense } from "react";
import { Toaster } from "react-hot-toast";
import { Routes, Route, BrowserRouter } from "react-router-dom";
import { QueryParamProvider } from "use-query-params";
import { ReactRouter6Adapter } from "use-query-params/adapters/react-router-6";
import { RequireAuth } from "src/auth/RequireAuth";
import { PageLoading } from "src/common/components";
import { DispatcherManagerGuard } from "src/common/components/guards/DispatcherManagerGuard";
import { SuperAdminGuard } from "src/common/components/guards/SuperAdminGuard";
import { OperatorManagerGuard } from "src/common/components/guards/OperatorManagerGuard";
import { OperatorGuard } from "src/common/components/guards/OperatorGuard";
import { OperatorManagerAndDispatcherGuard } from "src/common/components/guards/OperatorManagerAndDispatcherGuard";
import { OperatorManagerAndDispatcherManagerGuard } from "src/common/components/guards/OperatorManagerAndDispatcherManagerGuard";
import { DispatcherManagerAndDispatcherGuard } from "src/common/components/guards/DispatcherManagerAndDispatcherGuard";
import { RequireTrackAndTraceAuth } from "src/trackandtrace/RequireTrackAndTraceAuth";

const MainLayout = lazy(() =>
  import("src/app/MainLayout").then((module) => ({
    default: module.MainLayout,
  }))
);
// AUTH
const AuthLayout = lazy(() =>
  import("src/auth/AuthLayout").then((module) => ({
    default: module.AuthLayout,
  }))
);
const SignIn = lazy(() =>
  import("src/auth/SignIn").then((module) => ({ default: module.SignIn }))
);
const SignUp = lazy(() =>
  import("src/auth/SignUp").then((module) => ({ default: module.SignUp }))
);

const ResetPassword = lazy(() =>
  import("src/auth/ResetPassword").then((module) => ({
    default: module.ResetPassword,
  }))
);
const ForgotPassword = lazy(() =>
  import("src/auth/ForgotPassword").then((module) => ({
    default: module.ForgotPassword,
  }))
);

const EmailSent = lazy(() =>
  import("src/auth/EmailSent").then((module) => ({ default: module.EmailSent }))
);

const TrackAndTrace = lazy(() =>
  import("src/trackandtrace/TrackAndTrace").then((module) => ({
    default: module.TrackAndTrace,
  }))
);
const TrackAndTraceDetails = lazy(() =>
  import("src/trackandtrace/TrackAndTraceDetails").then((module) => ({
    default: module.TrackAndTraceDetails,
  }))
);

const Planning = lazy(() =>
  import("src/planning/Planning").then((module) => ({
    default: module.Planning,
  }))
);

const Dashboard = lazy(() =>
  import("src/dashboard/Dashboard").then((module) => ({
    default: module.Dashboard,
  }))
);

const Todo = lazy(() =>
  import("src/todo/Tasks").then((module) => ({
    default: module.Tasks,
  }))
);

const Settings = lazy(() =>
  import("src/settings/Settings").then((module) => ({
    default: module.Settings,
  }))
);

const Managers = lazy(() =>
  import("src/managers/Managers").then((module) => ({
    default: module.Managers,
  }))
);

const Vehicles = lazy(() =>
  import("src/vehicles/Vehicles").then((module) => ({
    default: module.Vehicles,
  }))
);

const Operators = lazy(() =>
  import("src/operators/Operators").then((module) => ({
    default: module.Operators,
  }))
);

const Dispatchers = lazy(() =>
  import("src/dispatchers/Dispatchers").then((module) => ({
    default: module.Dispatchers,
  }))
);

const PriceLists = lazy(() =>
  import("src/priceList/PriceLists").then((module) => ({
    default: module.PriceLists,
  }))
);

const PriceListsDetails = lazy(() =>
  import("src/priceList/priceListDetails/PriceListDetails").then((module) => ({
    default: module.PriceListDetails,
  }))
);

const Invoices = lazy(() =>
  import("src/invoices/Invoices").then((module) => ({
    default: module.Invoices,
  }))
);

const Contracts = lazy(() =>
  import("src/contracts/Contracts").then((module) => ({
    default: module.Contracts,
  }))
);

const Reservations = lazy(() =>
  import("src/reservations/Reservations").then((module) => ({
    default: module.Reservations,
  }))
);

const Reservation = lazy(() =>
  import("src/reservations/reservation/Reservation").then((module) => ({
    default: module.Reservation,
  }))
);

const ReservationDetails = lazy(() =>
  import("src/reservations/ReservationDetails").then((module) => ({
    default: module.ReservationDetails,
  }))
);

const Verification = lazy(() =>
  import("src/auth/Verification").then((module) => ({
    default: module.Verification,
  }))
);

const OperatorPlanning = lazy(() =>
  import("src/jobs/OperatorPlanning").then((module) => ({
    default: module.OperatorPlanning,
  }))
);

const UpcomingJob = lazy(() =>
  import("src/jobs/UpcomingJob").then((module) => ({
    default: module.UpcomingJob,
  }))
);

const JobDetails = lazy(() =>
  import("src/jobs/JobDetails").then((module) => ({
    default: module.JobDetails,
  }))
);

const JobTracker = lazy(() =>
  import("src/jobs/JobTracker").then((module) => ({
    default: module.JobTracker,
  }))
);

const Search = lazy(() =>
  import("src/search/Search").then((module) => ({
    default: module.Search,
  }))
);

const DispatcherPlanning = lazy(() =>
  import("src/dispatcherPlanning/Planning").then((module) => ({
    default: module.DispatcherPlanning,
  }))
);

const Clients = lazy(() =>
  import("src/clients/Clients").then((module) => ({
    default: module.Clients,
  }))
);
const Partners = lazy(() =>
  import("src/partners/Partners").then((module) => ({
    default: module.Partners,
  }))
);

export const Router: FC = () => {
  return (
    <BrowserRouter>
      <Toaster position="top-center" />
      <QueryParamProvider adapter={ReactRouter6Adapter}>
        <Routes>
          <Route
            path="/"
            element={
              <RequireAuth>
                <Suspense fallback={<PageLoading />}>
                  <MainLayout />
                </Suspense>
              </RequireAuth>
            }
          >
            <Route
              path="managers"
              element={
                <Suspense fallback={<PageLoading />}>
                  <SuperAdminGuard>
                    <Managers />
                  </SuperAdminGuard>
                </Suspense>
              }
            />
            <Route
              path="clients"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorManagerGuard>
                    <Clients />
                  </OperatorManagerGuard>
                </Suspense>
              }
            />
            <Route
              path="partners"
              element={
                <Suspense fallback={<PageLoading />}>
                  <DispatcherManagerAndDispatcherGuard>
                    <Partners />
                  </DispatcherManagerAndDispatcherGuard>
                </Suspense>
              }
            />
            <Route
              path="planning"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorManagerGuard>
                    <Planning />
                  </OperatorManagerGuard>
                </Suspense>
              }
            />

            <Route
              path="dashboard"
              element={
                <Suspense fallback={<PageLoading />}>
                  <DispatcherManagerGuard>
                    <Dashboard />
                  </DispatcherManagerGuard>
                </Suspense>
              }
            />

            <Route
              path="todo"
              element={
                <Suspense fallback={<PageLoading />}>
                  <DispatcherManagerAndDispatcherGuard>
                    <Todo />
                  </DispatcherManagerAndDispatcherGuard>
                </Suspense>
              }
            />

            <Route
              path="settings"
              element={
                <Suspense fallback={<PageLoading />}>
                  <Settings />
                </Suspense>
              }
            />

            <Route
              path="vehicles"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorManagerGuard>
                    <Vehicles />
                  </OperatorManagerGuard>
                </Suspense>
              }
            />

            <Route
              path="reservations"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorManagerAndDispatcherGuard>
                    <Reservations />
                  </OperatorManagerAndDispatcherGuard>
                </Suspense>
              }
            />

            <Route
              path="reservation"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorManagerAndDispatcherGuard>
                    <Reservation />
                  </OperatorManagerAndDispatcherGuard>
                </Suspense>
              }
            />

            <Route
              path="reservation/:id"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorManagerAndDispatcherGuard>
                    <ReservationDetails />
                  </OperatorManagerAndDispatcherGuard>
                </Suspense>
              }
            />

            <Route
              path="dispatcher-planning"
              element={
                <Suspense fallback={<PageLoading />}>
                  <DispatcherManagerAndDispatcherGuard>
                    <DispatcherPlanning />
                  </DispatcherManagerAndDispatcherGuard>
                </Suspense>
              }
            />

            <Route
              path="operators"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorManagerGuard>
                    <Operators />
                  </OperatorManagerGuard>
                </Suspense>
              }
            />

            <Route
              path="dispatchers"
              element={
                <Suspense fallback={<PageLoading />}>
                  <DispatcherManagerGuard>
                    <Dispatchers />
                  </DispatcherManagerGuard>
                </Suspense>
              }
            />

            <Route
              path="search"
              element={
                <Suspense fallback={<PageLoading />}>
                  <DispatcherManagerAndDispatcherGuard>
                    <Search />
                  </DispatcherManagerAndDispatcherGuard>
                </Suspense>
              }
            />
            <Route
              path="finance/invoices"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorManagerGuard>
                    <Invoices />
                  </OperatorManagerGuard>
                </Suspense>
              }
            />
            <Route
              path="finance/pricelist"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorManagerGuard>
                    <PriceLists />
                  </OperatorManagerGuard>
                </Suspense>
              }
            />
            <Route
              path="finance/pricelist/:id"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorManagerGuard>
                    <PriceListsDetails />
                  </OperatorManagerGuard>
                </Suspense>
              }
            />
            <Route
              path="finance/contracts"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorManagerAndDispatcherManagerGuard>
                    <Contracts />
                  </OperatorManagerAndDispatcherManagerGuard>
                </Suspense>
              }
            />

            <Route
              path="operator-planning"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorGuard>
                    <OperatorPlanning />
                  </OperatorGuard>
                </Suspense>
              }
            />

            <Route
              path="upcoming-jobs"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorGuard>
                    <UpcomingJob />
                  </OperatorGuard>
                </Suspense>
              }
            />

            <Route
              path="job-details/:id"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorGuard>
                    <JobDetails />
                  </OperatorGuard>
                </Suspense>
              }
            />

            <Route
              path="job-tracker/:id"
              element={
                <Suspense fallback={<PageLoading />}>
                  <OperatorGuard>
                    <JobTracker />
                  </OperatorGuard>
                </Suspense>
              }
            />
          </Route>
          <Route
            path="track-and-trace"
            element={
              <Suspense fallback={<PageLoading />}>
                <TrackAndTrace />
              </Suspense>
            }
          />
          <Route
            path="track-and-trace/details/:orderNumber"
            element={
              <RequireTrackAndTraceAuth>
                <Suspense fallback={<PageLoading />}>
                  <TrackAndTraceDetails />
                </Suspense>
              </RequireTrackAndTraceAuth>
            }
          />
          <Route
            path="auth"
            element={
              <Suspense fallback={<PageLoading />}>
                <AuthLayout />
              </Suspense>
            }
          >
            <Route
              path="signin"
              element={
                <Suspense fallback={<PageLoading />}>
                  <SignIn />
                </Suspense>
              }
            />
            <Route
              path="verification"
              element={
                <Suspense fallback={<PageLoading />}>
                  <Verification />
                </Suspense>
              }
            />
            <Route
              path="signup"
              element={
                <Suspense fallback={<PageLoading />}>
                  <SignUp />
                </Suspense>
              }
            />
            <Route
              path="reset"
              element={
                <Suspense fallback={<PageLoading />}>
                  <ResetPassword />
                </Suspense>
              }
            />
            <Route
              path="emailsent"
              element={
                <Suspense fallback={<PageLoading />}>
                  <EmailSent />
                </Suspense>
              }
            />
            <Route path="password">
              <Route
                path="forgot"
                element={
                  <Suspense fallback={<PageLoading />}>
                    <ForgotPassword />
                  </Suspense>
                }
              />
              <Route
                path="reset"
                element={
                  <Suspense fallback={<PageLoading />}>
                    <ResetPassword />
                  </Suspense>
                }
              />
            </Route>
          </Route>
          <Route
            path="*"
            element={
              <div style={{ textAlign: "center" }}>404 - Page not found!</div>
            }
          />
        </Routes>
      </QueryParamProvider>
    </BrowserRouter>
  );
};
