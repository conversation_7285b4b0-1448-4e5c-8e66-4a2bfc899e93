import { EventContentArg } from '@fullcalendar/core';
import { Box, Typography } from '@mui/material';
import { FC } from 'react';

type Props = {
    arg: EventContentArg;
};

const EventContentWeek: FC<Props> = (props) => {
    const { title } = props.arg.event;
    const { dateFrom } = props.arg.event.extendedProps;

    return (
      <Box
        overflow={"hidden"}
        sx={{ background: (theme) => theme.palette.primary.dark,height: "100%"  }}
      >
        <Typography variant="caption">{dateFrom}</Typography>
        <Typography variant="body2">{title}</Typography>
      </Box>
    );
};

export default EventContentWeek;
