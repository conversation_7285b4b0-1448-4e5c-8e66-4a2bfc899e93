name: Deployment To TST Environment

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main

permissions:
  id-token: write
  contents: read
  actions: read

env:
  AWS_REGION: us-east-1
  ENVIRONMENT: tst
  BUCKET_NAME: concreteeasy-web-artifact-tst
  DISTRIBUTION_ID: E3HS0I5JMTY39E

jobs:
  slackNotificationPipelineStarted:
    name: Pipeline Started
    if: github.event_name == 'push'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_USERNAME: WebBot
          SLACK_TITLE: "Pipeline Execution Started"
          SLACK_MESSAGE: "Pipeline Execution in Web Repository Started"

  lint:
    name: Lint
    if: github.event_name == 'pull_request' || github.event_name == 'push'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
      - name: Use Node.js 16
        uses: actions/setup-node@v4
        with:
          node-version: 16
      - name: Install Dependencies
        run: npm ci    
      - name: Run Lint
        run: npm run lint

  audit:
    name: Audit
    if: github.event_name == 'pull_request' || github.event_name == 'push'
    needs: lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
      - name: Use Node.js 16
        uses: actions/setup-node@v4
        with:
          node-version: 16
      - name: Run Audit
        run: echo "npm audit --omit=dev --audit-level=high"

  unitTest:
    name: Unit Test
    if: github.event_name == 'pull_request' || github.event_name == 'push'
    needs: audit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
      - name: Use Node.js 16
        uses: actions/setup-node@v4
        with:
          node-version: 16
      - name: Install Dependencies
        run: npm ci
      - name: Run Unit Test
        run: echo "npm run test"
      - name: List Coverage Directory
        run: |
          ls -la
          find coverage -print | sed -e "s;[^/]*/;|____;g;s;____|; |;g"
      - name: Upload Test Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: unit_test_artifact
          retention-days: 1
          path: |
            coverage

  build:
    name: Build
    if: github.event_name == 'push'
    needs:
      - slackNotificationPipelineStarted
      - unitTest
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
      - name: Create env file
        run: |
          cat >> .env <<'EOF'
           REACT_APP_API_URL=https://api.stg.app-concreteasy.be
           REACT_APP_GM_API_KEY=${{ secrets.REACT_APP_GM_API_KEY }}
          EOF
      - name: Use Node.js 16
        uses: actions/setup-node@v4
        with:
          node-version: 16
      - name: Install Dependencies
        run: npm ci
      - name: List Node Modules Directory
        run: |
          ls -la
          find node_modules -print | sed -e "s;[^/]*/;|____;g;s;____|; |;g"
      - name: Build Artifacts
        run: npm run build
        env:
          CI: false
      - name: List Build Directory
        run: |
          ls -la
          find build -print | sed -e "s;[^/]*/;|____;g;s;____|; |;g"
      - name: Upload Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build_artifact
          retention-days: 1
          path: |
            build

  deploy:
    name: Deployment
    if: github.event_name == 'push'
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Create build directory
        run: mkdir build
      - name: Download Artifacts
        uses: actions/download-artifact@v4
        with:
          name: build_artifact
          path: build
      - name: List Directory
        run: |
          ls -la
          find build -print | sed -e "s;[^/]*/;|____;g;s;____|; |;g"
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.STAGING_GITHUB_ROLE }}
          aws-region: ${{ env.AWS_REGION }}
      - name: Check If S3 Bucket Exists
        run: aws s3api head-bucket --bucket ${{env.BUCKET_NAME}}
      - name: Upload Artifacts To S3
        run: aws s3 sync build/ s3://${{env.BUCKET_NAME}}
      - name: Invalidate Cloudfront Cache
        run: aws cloudfront create-invalidation --distribution-id ${{env.DISTRIBUTION_ID}} --paths "/*"

  slackNotificationPipelineFinished:
    name: Pipeline Finished
    if: github.event_name == 'push' && always()
    needs: deploy
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: martialonline/workflow-status@v4.1
        id: check
      - name: Notification Succeded
        if: steps.check.outputs.status == 'success'
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_USERNAME: WebBot
          SLACK_TITLE: "Pipeline Execution Succeeded"
          SLACK_MESSAGE: "Pipeline Execution in Web Repository Succeeded"
      - name: Notification Failed
        if: steps.check.outputs.status == 'failure'
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_USERNAME: WebBot
          SLACK_TITLE: "Pipeline Execution Failed"
          SLACK_MESSAGE: "Pipeline Execution in Web Repository Failed"
      - name: Notification Cancelled
        if: steps.check.outputs.status == 'cancelled'
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_USERNAME: WebBot
          SLACK_TITLE: "Pipeline Execution Cancelled"
          SLACK_MESSAGE: "Pipeline Execution in Web Repository Cancelled"
