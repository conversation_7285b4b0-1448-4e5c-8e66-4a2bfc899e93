import { EventContentArg } from '@fullcalendar/core';
import { Box, Typography } from '@mui/material';
import { FC } from 'react';

type Props = {
    arg: EventContentArg;
};
const style = {
    display: 'inline-block',
    left: 0,
    maxWidth: 100,
    right: 0,
    verticalAlign: 'top'
};

const EventContentMonth: FC<Props> = (props) => {
    const { title } = props.arg.event;

    return (
      <Box
        overflow={"hidden"}
        sx={{
          background: (theme) => theme.palette.primary.dark,
          height: "100%" 
    
        }}
      >
        <Typography sx={style} variant="body2">
          {title}
        </Typography>
      </Box>
    );
};

export default EventContentMonth;
