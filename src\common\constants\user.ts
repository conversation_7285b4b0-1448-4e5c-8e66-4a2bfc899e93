import { UserEnumDisplay, UserSettingsFormValues } from "../types";

export const SIGN_IN_DEFAULT = {
  email: "",
  password: "",
};

export const SETTINGS_USER_DEFAULT: UserSettingsFormValues = {
  firstName: "",
  lastName: "",
  birthdate: null,
  address: "",
  secondaryAddress: "",
  zipCode: "",
  city: "",
  country: null,
  phoneNumber: "",
  phoneNumberPersonal: "",
  email: "",
};

export const userRolesDisplay: UserEnumDisplay[] = [
  {
    id: 1,
    title: "Manager",
    visible: false,
  },
  {
    id: 2,
    title: "Dispatcher Manager",
    visible: true,
  },
  {
    id: 3,
    title: "Operator Manager",
    visible: true,
  },
  {
    id: 4,
    title: "Dispatcher",
    visible: true,
  },
  {
    id: 5,
    title: "Operator",
    visible: true,
  },
];

export const userStatusDisplay: UserEnumDisplay[] = [
  {
    id: 1,
    title: "Pending",
    visible: false,
  },
  {
    id: 2,
    title: "Active",
    visible: true,
  },
  {
    id: 3,
    title: "Inactive",
    visible: true,
  },
];
