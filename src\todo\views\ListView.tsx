import { useState } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Chip,
  IconButton,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { format } from "date-fns";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import {
  ArrowUp01Icon,
  Comment02Icon,
  DragDropHorizontalIcon,
} from "@hugeicons/react";
import { CeAvatar } from "src/common/components";
import { Task, TaskStatus, UpdateTaskDto } from "src/common/types/tasks";
import { stringAvatar } from "src/common/utils/avatar";
import { useTranslation } from "react-i18next";

interface TaskViewProps {
  taskData: Task[];
  onTaskDrag: (itemData: UpdateTaskDto) => void;
  onListTaskClick: (task: Task) => void;
}

const TaskBoard = ({
  taskData,
  onTaskDrag,
  onListTaskClick,
}: TaskViewProps) => {
  const [groupedTasks, setGroupedTasks] = useState(() =>
    taskData.reduce((acc, task) => {
      acc[task.status] = acc[task.status] || [];
      acc[task.status].push(task);
      return acc;
    }, {} as Record<TaskStatus, Task[]>)
  );

  const { t } = useTranslation(["common", "dispatcher"]);

  const moveTask = (task: Task, newStatus: TaskStatus, targetIndex: number) => {
    setGroupedTasks((prev) => {
      const updated = { ...prev };

      for (const status in updated) {
        updated[status as TaskStatus] = updated[status as TaskStatus].filter(
          (t) => t.id !== task.id
        );
      }

      const updatedTask = { ...task, status: newStatus };
      updated[newStatus] = updated[newStatus] || [];

      if (task.status === newStatus) {
        const tasksInStatus = updated[newStatus];
        tasksInStatus.splice(targetIndex, 0, updatedTask);
      } else {
        updated[newStatus].splice(targetIndex, 0, updatedTask);
      }

      onTaskDrag({ taskId: updatedTask.id, status: updatedTask.status });

      return updated;
    });
  };

  const DraggableTask = ({ task, index }: { task: Task; index: number }) => {
    const [, drag] = useDrag(() => ({
      type: "task",
      item: { task, index },
    }));

    const formattedDate = format(new Date(task.dueDate), "dd MMM, yy");

    return (
      <TableRow
        className="task-card"
        onClick={() => onListTaskClick(task)}
        sx={{
          borderColor: "divider",
          cursor: "pointer",
          "&:hover .drag-indicator": {
            visibility: "visible !important",
          },
        }}
      >
        <TableCell align="left">
          <Stack direction="row" gap={1} alignItems="center">
            <IconButton
              ref={drag}
              className="drag-indicator"
              sx={{ cursor: "grab" }}
            >
              <DragDropHorizontalIcon size={18} />
            </IconButton>

            <Typography variant="body2" sx={{ whiteSpace: "nowrap" }}>
              Task {task.id}
            </Typography>
          </Stack>
        </TableCell>
        <TableCell width={250} align="right" sx={{ pl: 1 }}>
          <Stack direction="row" alignItems="center" gap={2}>
            <CeAvatar
              size="small"
              {...stringAvatar(
                `${task.assignee?.firstName} ${task.assignee?.lastName}`
              )}
            />
            <Typography variant="body2" sx={{ whiteSpace: "nowrap" }}>
              {task.assignee?.firstName} {task.assignee?.lastName}
            </Typography>
          </Stack>
        </TableCell>
        <TableCell width={250} align="left" sx={{ pl: 2 }}>
          <Typography variant="body2" sx={{ whiteSpace: "nowrap" }}>
            {formattedDate}
          </Typography>
        </TableCell>
        <TableCell width={250} align="left" sx={{ pl: 2 }}>
          {task.label && (
            <Chip
              label={task.label?.name}
              style={{
                backgroundColor: task.label?.color,
                color: "background",
              }}
            />
          )}
        </TableCell>
        <TableCell width={250} align="left" sx={{ pl: 2 }}>
          <Chip
            label={task.priority}
            color={
              task.priority === "High"
                ? "error"
                : task.priority === "Medium"
                ? "warning"
                : "secondary"
            }
          />
        </TableCell>
        <TableCell width={250} align="left" sx={{ pl: 2, maxWidth: 250 }}>
          <Typography
            variant="body2"
            textOverflow="ellipsis"
            overflow="hidden"
            whiteSpace="nowrap"
          >
            {task.description}
          </Typography>
        </TableCell>
        <TableCell width={250} align="left">
          {!!task.comments?.length ? (
            <Box
              display="flex"
              alignItems="center"
              justifyContent="center"
              gap={0.5}
            >
              <Typography variant="body2">{task.comments?.length}</Typography>
              <Comment02Icon size={20} />
            </Box>
          ) : (
            <Box display="flex" alignItems="center" justifyContent="center">
              -
            </Box>
          )}
        </TableCell>
      </TableRow>
    );
  };

  const DroppableAccordion = ({
    status,
    tasks,
  }: {
    status: TaskStatus;
    tasks: Task[];
  }) => {
    const [, drop] = useDrop(() => ({
      accept: "task",
      hover: (item: { task: Task; targetIndex?: number }, monitor) => {
        const clientOffset = monitor.getClientOffset();

        if (!clientOffset) return;

        const taskElements = document.querySelectorAll(
          `[data-status="${status}"] .task-card`
        );

        let targetIndex = tasks.length;
        taskElements.forEach((taskElement, index) => {
          const { top, bottom } = taskElement.getBoundingClientRect();
          if (clientOffset.y >= top && clientOffset.y <= bottom) {
            targetIndex = index;
          }
        });

        item.targetIndex = targetIndex;
      },
      drop: (item: { task: Task; targetIndex?: number }) => {
        const targetIndex = item.targetIndex ?? tasks.length;
        moveTask(item.task, status, targetIndex);
      },
    }));

    return (
      <Accordion ref={drop} defaultExpanded={true} elevation={0}>
        <AccordionSummary
          expandIcon={<ArrowUp01Icon size={24} />}
          id={`${status}-header`}
          sx={{
            display: "flex",
            alignItems: "center",
            flexDirection: "row-reverse",
            gap: 2,
            px: 0,
          }}
        >
          <Typography variant="h6" sx={{ whiteSpace: "nowrap" }}>
            {status}
          </Typography>
        </AccordionSummary>
        <AccordionDetails sx={{ px: 0 }}>
          <TableContainer>
            <Table>
              <TableBody data-status={status}>
                {tasks.map((task, index) => (
                  <DraggableTask key={task.id} task={task} index={index} />
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </AccordionDetails>
      </Accordion>
    );
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <Box>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell align="left">Task</TableCell>
                <TableCell width={250} align="left">
                  {t("assignee")}
                </TableCell>
                <TableCell width={250} align="left">
                  {t("due-date")}
                </TableCell>
                <TableCell width={250} align="left">
                  {t("label")}
                </TableCell>
                <TableCell width={250} align="left">
                  {t("priority")}
                </TableCell>
                <TableCell width={250} align="left">
                  {t("description")}
                </TableCell>
                <TableCell width={250} align="center">
                  {t("comments")}
                </TableCell>
              </TableRow>
            </TableHead>
          </Table>
        </TableContainer>
        {(
          [TaskStatus.TODO, TaskStatus.DOING, TaskStatus.DONE] as TaskStatus[]
        ).map((status) => (
          <DroppableAccordion
            key={status}
            status={status}
            tasks={groupedTasks[status] || []}
          />
        ))}
      </Box>
    </DndProvider>
  );
};

export default TaskBoard;
