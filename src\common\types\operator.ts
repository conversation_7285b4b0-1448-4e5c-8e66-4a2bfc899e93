import { Role, Status, User, UserEnumDisplay } from "./user";
import { Vehicle } from "./vehicle";

export interface Operator extends User {
  id: number;
  name: string | null;
  surname: string | null;
  email: string | null;
  phoneNumber: string | null;
  status: Status | null;
  role: Role | null;
  remainingAttempts?: number;
  lockoutEndTime?: string | null;
  resetPasswordToken?: string | null;
  resetPasswordTokenExpiresAt?: string | null;
  vehicles?: Vehicle[];
}

export interface OperatorFormValues {
  operatorId?: number;
  firstName: string;
  lastName: string;
  country: string;
  email: string;
  phoneNumber: string;
  status: UserEnumDisplay | null;
  role: UserEnumDisplay | null;
  flow: OperatorModalFlow;
}

export interface DeleteOperatorModalValues {
  operatorId?: number;
  operatorTitle?: string;
  flow: OperatorModalDeleteFlow;
}

export interface CreateOperatorDto {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  country: string;
  status: number;
  role: number;
}

export interface UpdateOperatorDto extends Omit<CreateOperatorDto, "password"> {
  operatorId: number;
}

export interface DeleteOperatorDto {
  operatorId: number;
}

export interface DeleteOperatorModalValues {
  operatorId?: number;
  operatorTitle?: string;
  flow: OperatorModalDeleteFlow;
}

export type OperatorModalFlow = "Create" | "Update" | "Details" | null;
export type OperatorModalDeleteFlow = "Delete" | null;
