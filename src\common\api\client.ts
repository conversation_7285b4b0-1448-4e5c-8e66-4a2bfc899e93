import axios, { AxiosError } from "axios";
import {
  Client,
  ClientsWithCount,
  CreateClientDto,
  GetClientDto,
  UpdateClientDto,
} from "../types/client";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { processApiError } from "../utils/errors";
import { wrapEachFieldIntoDoubleQuotes } from "../utils";

const backendUrl = process.env.REACT_APP_API_URL;

// GET MANY clients
export const getClients = async (params: GetClientDto) => {
  return axios
    .post(`${backendUrl}/operator-client/get`, params, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useClients = (params: GetClientDto, enabled: boolean = true) => {
  const formattedSortModel = wrapEachFieldIntoDoubleQuotes(params.sortModel);
  const formattedAttrs = { ...params, sortModel: formattedSortModel };
  return useQuery<ClientsWithCount, AxiosError | Error>(
    ["clients", formattedAttrs],
    () => getClients(formattedAttrs),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch clients", err),
      enabled,
    }
  );
};

// Create Client
export const createNewClient = async (attrs: CreateClientDto) => {
  const response = await axios.post(`${backendUrl}/operator-client/`, attrs, {
    withCredentials: true,
  });
  return response.data;
};

export const useCreateNewClient = () => {
  const queryClient = useQueryClient();
  return useMutation<Client, AxiosError | Error, CreateClientDto, () => void>(
    (a: CreateClientDto) => createNewClient(a),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("clients");
      },
      onError: (err) => processApiError("Unable to create client", err),
    }
  );
};

export const updateClient = async (attrs: UpdateClientDto) => {
  const { id, ...rest } = attrs;
  const response = await axios.patch(
    `${backendUrl}/operator-client/${id}`,
    rest,
    {
      withCredentials: true,
    }
  );
  return response.data;
};

export const useUpdateClient = () => {
  const queryClient = useQueryClient();
  return useMutation<Client, AxiosError | Error, UpdateClientDto, () => void>(
    (attrs: UpdateClientDto) => updateClient(attrs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("clients");
      },
      onError: (err) => {
        processApiError("Unable to update client", err);
      },
    }
  );
};

export const deleteClient = async (id: string) => {
  const response = await axios.delete(`${backendUrl}/operator-client/${id}`, {
    withCredentials: true,
  });
  return response.data;
};

export const useDeleteClient = () => {
  const queryClient = useQueryClient();
  return useMutation<string, AxiosError | Error, string, () => void>(
    (id: string) => deleteClient(id),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("clients");
      },
      onError: (err) => {
        processApiError("Unable to update client", err);
      },
    }
  );
};
