import { AxiosError } from "axios";
import { UseMutateAsyncFunction } from "react-query";
import { SetterOrUpdater } from "recoil";
import { MainModal } from "src/common/components";

import {
  Client,
  ClientFormValues,
  CreateClientDto,
  UpdateClientDto,
} from "src/common/types/client";

import { DEFAULT_CLIENT_FORM_VALUES } from "src/common/constants/client";
import { ClientForm } from "./ClientForm";
import { useTranslation } from "react-i18next";

export interface ClientProps {
  initialFormValues: ClientFormValues;
  setClientFormValues: SetterOrUpdater<ClientFormValues>;
  handleCreateClient: UseMutateAsyncFunction<
    Client,
    Error | AxiosError<any, any>,
    CreateClientDto,
    () => void
  >;
  handleUpdateClient: UseMutateAsyncFunction<
    Client,
    AxiosError<any, any> | Error,
    UpdateClientDto,
    () => void
  >;
  isLoading: boolean;
}

export const ClientActionModal = ({
  initialFormValues,
  setClientFormValues,
  handleCreateClient,
  handleUpdateClient,
  isLoading,
}: ClientProps) => {
  const { t } = useTranslation(["manager"]);
  const handleCloseClientActionModal = () => {
    if (!isLoading) setClientFormValues(DEFAULT_CLIENT_FORM_VALUES);
  };

  return (
    <MainModal
      title={t(
        `common:${
          initialFormValues.flow === "Create" ? "create" : "update"
        }-client`
      )}
      isOpen={Boolean(initialFormValues.flow)}
      handleClose={handleCloseClientActionModal}
      maxWidth="sm"
    >
      <ClientForm
        handleCloseClientActionModal={handleCloseClientActionModal}
        handleCreateClient={handleCreateClient}
        setClientFormValues={setClientFormValues}
        handleUpdateClient={handleUpdateClient}
        initialFormValues={initialFormValues}
        isLoading={isLoading}
      />
    </MainModal>
  );
};
