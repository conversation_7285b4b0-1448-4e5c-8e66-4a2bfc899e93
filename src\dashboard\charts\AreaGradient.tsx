import React from 'react';

interface AreaGradientProps {
  color: string;
  id: string;
}

const AreaGradient: React.FC<AreaGradientProps> = ({ color, id }) => {
  return (
    <defs>
      <linearGradient id={id} x1="50%" y1="0%" x2="50%" y2="100%">
        <stop offset="0%" stopColor={color} stopOpacity={0.5} />
        <stop offset="100%" stopColor={color} stopOpacity={0} />
      </linearGradient>
    </defs>
  );
};

export default AreaGradient;
