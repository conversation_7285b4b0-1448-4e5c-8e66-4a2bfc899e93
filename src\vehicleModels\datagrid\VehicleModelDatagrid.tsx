import { Box, IconButton, MenuItem, Select, Stack } from "@mui/material";
import {
  DataGrid,
  GridColDef,
  GridFilterModel,
  GridRenderCellParams,
  GridSortModel,
} from "@mui/x-data-grid";
import { useTranslation } from "react-i18next";
import { FeatureFlag } from "src/common/components";
import { useCallback, useEffect, useState } from "react";
import { VehicleModelDeleteModal } from "../VehicleModelDeleteModal";
import {
  UpdateVehicleModelDto,
  VehicleModelFormValues,
  VehicleModels,
  doesNumberOrEmptyOperator,
} from "src/common/types";
import {
  useCreateNewVehicleModel,
  useDeleteVehicleModel,
  useUpdateVehicleModel,
  useVehicleTypes,
} from "src/common/api";
import {
  vehicleModelDeleteValuesState,
  vehicleModelFormValuesState,
} from "src/common/state";
import {
  VEHICLE_MODEL_DELETE_DEFAULT,
  VEHICLE_MODEL_FORM_VALUES,
} from "src/common/constants";
import { turnVehicleModelIntoFormValues } from "src/common/utils";
import { VehicleModelModal } from "../VehicleModelModal";
import { useRecoilState } from "recoil";
import { Delete01Icon, Edit02Icon } from "@hugeicons/react";
import { GridColumnTypography } from "src/common/components/custom/company/GridColumnTypography";
import { CeDataGridToolbar } from "src/common/components/custom/company/CeDataGridToolbar";
import {
  GridStateSnapshot,
  UpdateGridStatePart,
} from "src/common/utils/gridState";
import { useLocation } from "react-router-dom";

interface VehicleModelsDatagridProps {
  isServerDriven: boolean;
  data: VehicleModels[];
  isFetchingVehicleModels: boolean;
  refetchVehicleModels: () => void;
  shouldRenderRefreshButton: boolean;
  shouldRenderAddButton: boolean;
  total: number;
  gridState: GridStateSnapshot;
  updateGridStatePart: UpdateGridStatePart;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
  handleSortModelChange?: (sortModel: GridSortModel) => void;
  onDatagridFiltersChange?: (datagridFilter: GridFilterModel) => void;
}
export const VehicleModelDatagrid: React.FC<VehicleModelsDatagridProps> = ({
  data,
  shouldRenderRefreshButton,
  isFetchingVehicleModels,
  refetchVehicleModels,
  shouldRenderAddButton,
  gridState,
  updateGridStatePart,
  total,
  onPageChange,
  onPageSizeChange,
  isServerDriven,
  handleSortModelChange,
  onDatagridFiltersChange,
}) => {
  const { t } = useTranslation(["manager", "common", "dispatcher"]);
  const [editMode, setEditMode] = useState<boolean>(false);

  const {
    mutateAsync: handleUpdateVehicleModel,
    isSuccess: isUpdateVehicleModelSuccess,
    isLoading: isUpdatingVehicleModel,
  } = useUpdateVehicleModel();
  const {
    mutate: handleCreateNewVehicleModel,
    isSuccess: isCreateVehicleModelSuccess,
    isLoading: isCreatingVehicleModel,
  } = useCreateNewVehicleModel();
  const {
    mutate: handleDeleteVehicleModel,
    isLoading: isDeletingVehicleModel,
    isSuccess: isDeleteVehicleModelSuccess,
  } = useDeleteVehicleModel();
  const {
    data: vehicleTypesData,
    isSuccess: isVehicleTypesSuccess,
    isLoading: isVehicleTypeLoading,
  } = useVehicleTypes({}, editMode);
  const [vehicleModelFormValues, setVehicleModelFormValues] = useRecoilState(
    vehicleModelFormValuesState
  );

  const [vehicleModelDeleteFormValues, setVehicleModelDeleteFormValues] =
    useRecoilState(vehicleModelDeleteValuesState);

  const processRowUpdate = async (
    newRow: VehicleModels,
    oldRow: VehicleModels
  ) => {
    try {
      const payload: UpdateVehicleModelDto = {
        ...newRow,
        vehicleTypeId: newRow.type?.id!,
        vehicleModelId: newRow.id,
      };
      const updatedRow = handleUpdateVehicleModel(payload);
      return updatedRow;
    } catch (error: any) {
      console.error(error.message);
      return oldRow;
    }
  };

  const handleProcessRowUpdateError = useCallback((error: Error) => {
    console.error(error.message);
  }, []);

  const isLoading =
    isFetchingVehicleModels ||
    isCreatingVehicleModel ||
    isDeletingVehicleModel ||
    isUpdatingVehicleModel;

  useEffect(() => {
    if (isCreateVehicleModelSuccess) {
      setVehicleModelFormValues(VEHICLE_MODEL_FORM_VALUES);
    }
  }, [isCreateVehicleModelSuccess, setVehicleModelFormValues]);

  useEffect(() => {
    if (isUpdateVehicleModelSuccess) {
      setVehicleModelFormValues(VEHICLE_MODEL_FORM_VALUES);
    }
  }, [isUpdateVehicleModelSuccess, setVehicleModelFormValues]);

  useEffect(() => {
    if (isDeleteVehicleModelSuccess) {
      setVehicleModelDeleteFormValues(VEHICLE_MODEL_DELETE_DEFAULT);
    }
  }, [isDeleteVehicleModelSuccess, setVehicleModelDeleteFormValues]);

  const handleCloseVehicleModalDelete = () => {
    if (!isLoading) {
      setVehicleModelDeleteFormValues(VEHICLE_MODEL_DELETE_DEFAULT);
    }
  };
  const handleCloseVehicleModal = () => {
    if (!isLoading) {
      setVehicleModelFormValues(VEHICLE_MODEL_FORM_VALUES);
    }
  };
  const vehicleTypes = vehicleTypesData?.data || [];

  const renderEditActions = (params: GridRenderCellParams) => {
    const vehicleModel: VehicleModels = params.row;

    return (
      <Stack
        direction="row"
        justifyContent="center"
        alignItems="center"
        spacing={1}
      >
        <IconButton
          aria-label="update vehicle model"
          disabled={isUpdatingVehicleModel}
          size="small"
          onClick={() => {
            const formValues: VehicleModelFormValues =
              turnVehicleModelIntoFormValues(vehicleModel, "Update");

            setVehicleModelFormValues(formValues);
          }}
        >
          <Edit02Icon size={16} variant={"stroke"} />
        </IconButton>
        <FeatureFlag flags={["operator manager"]}>
          <IconButton
            aria-label="delete vehicle size"
            disabled={isDeletingVehicleModel}
            color="error"
            size="small"
            onClick={() =>
              setVehicleModelDeleteFormValues({
                vehicleModelId: vehicleModel.id,
                vehicleModelTitle: `${vehicleModel.boomSize || "unknown size"}`,
                flow: "Delete",
              })
            }
          >
            <Delete01Icon size={16} variant={"stroke"} />
          </IconButton>
        </FeatureFlag>
      </Stack>
    );
  };

  const columns: GridColDef[] = [
    {
      field: "editActions",
      headerName: `${t("common:edit")}`,
      sortable: false,
      renderCell: (params: GridRenderCellParams<string>) =>
        renderEditActions(params),
      filterable: false,
    },
    {
      field: "vehicle_size.id",
      headerName: `id`,
      headerAlign: "left",
      align: "left",
      filterOperators: doesNumberOrEmptyOperator,
      width: 100,
      renderCell: (params) => <GridColumnTypography value={params.row.id} />,
    },
    {
      field: "type.name",
      headerName: `${t("common:type")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      sortable: false,
      filterable: false,
      editable: true,
      type: "singleSelect",
      valueOptions: () => {
        return vehicleTypes.map((type) => ({
          value: type.id || "",
          label: type.name || "-",
        }));
      },
      valueGetter: (params) => {
        if (!vehicleTypes || !vehicleTypes.length) return "";
        return params.row.type?.id;
      },
      valueSetter: (params) => {
        const selectedType = vehicleTypes.find(
          (type) => type.id === params.value
        );
        if (!selectedType) {
          return params.row;
        }
        return { ...params.row, type: selectedType || [] };
      },
      renderCell: (params) => (
        <GridColumnTypography value={params.row.type?.name || "-"} />
      ),
    },
    {
      field: "vehicle_size.boomSize",
      headerName: `${t("common:vehicle-boom-size")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      type: "number",
      filterOperators: doesNumberOrEmptyOperator,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.boomSize} />
      ),
    },
  ];

  const onFilterChange = useCallback(
    (filterModel: GridFilterModel) => {
      if (onDatagridFiltersChange) {
        onDatagridFiltersChange(filterModel);
        updateGridStatePart("filterModel", filterModel);
      }
    },
    [onDatagridFiltersChange]
  );

  const onSortChange = useCallback(
    (sortModel: GridSortModel) => {
      if (handleSortModelChange) {
        handleSortModelChange(sortModel);
        updateGridStatePart("sortModel", sortModel);
      }
    },
    [handleSortModelChange]
  );

  return (
    <>
      <DataGrid
        sx={{
          border: "none",
          paddingTop: 0.5,
          background: (theme) => theme.palette.background.paper,
        }}
        pagination
        page={gridState.page - 1}
        pageSize={gridState.pageSize}
        rowsPerPageOptions={[20, 40, 60, 80, 100]}
        rowCount={total}
        sortingMode={isServerDriven ? "server" : "client"}
        onSortModelChange={isServerDriven ? onSortChange : undefined}
        filterMode={isServerDriven ? "server" : "client"}
        onFilterModelChange={isServerDriven ? onFilterChange : undefined}
        onPageChange={(newPage) => {
          onPageChange(newPage);
        }}
        onPageSizeChange={(newPageSize) => {
          onPageSizeChange(newPageSize);
        }}
        onColumnVisibilityModelChange={(model) =>
          updateGridStatePart("columnVisibilityModel", model)
        }
        filterModel={gridState.filterModel}
        sortModel={gridState.sortModel}
        paginationMode="server"
        components={{
          Toolbar: () => (
            <CeDataGridToolbar
              shouldRenderRefreshButton={shouldRenderRefreshButton}
              shouldRenderAddButton={shouldRenderAddButton}
              onRefreshButtonClick={refetchVehicleModels}
              addButtonClickHandler={() => {
                setVehicleModelFormValues({
                  ...vehicleModelFormValues,
                  flow: "Create",
                });
              }}
              addButtonDescription={t("common:add-pump-model")}
            />
          ),
        }}
        columns={columns}
        rows={data}
        disableSelectionOnClick
        initialState={{
          columns: {
            columnVisibilityModel: {
              ...gridState.columnVisibilityModel,
            },
          },
        }}
        editMode="row"
        processRowUpdate={processRowUpdate}
        onProcessRowUpdateError={handleProcessRowUpdateError}
        experimentalFeatures={{ newEditingApi: true }}
        onRowEditStart={(params) => {
          setEditMode(!!params);
        }}
      />
      <VehicleModelModal
        initialFormValues={vehicleModelFormValues}
        isLoading={isLoading}
        handleCreateNewVehicleModel={handleCreateNewVehicleModel}
        handleUpdateVehicleModel={handleUpdateVehicleModel}
        handleCloseVehicleModal={handleCloseVehicleModal}
      />
      <VehicleModelDeleteModal
        flow={vehicleModelDeleteFormValues.flow}
        isLoading={isLoading}
        vehicleModelTitle={vehicleModelDeleteFormValues.vehicleModelTitle}
        vehicleModelId={vehicleModelDeleteFormValues.vehicleModelId}
        handleCloseVehicleModalDelete={handleCloseVehicleModalDelete}
        handleDeleteVehicleModel={handleDeleteVehicleModel}
      />
    </>
  );
};
