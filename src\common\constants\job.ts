import { JobReportFormValues, SecurityValidationFormValues } from "../types";

export const JOB_REPORT_FORM_VALUES: JobReportFormValues = {
  amountOfConcrete: 0,
  cleaningTime: 0,
  rigidPipeLength100Mm: 0,
  rigidPipeLength120Mm: 0,
  flexiblePipeLength80Mm: 0,
  flexiblePipeLength90Mm: 0,
  extraCementBags: false,
  secondTechnician: false,
  cementBags: 0,
  signature: "",
  flow: null,
};

export const JOB_SECURITY_VALIDATION_FORM_VALUES: SecurityValidationFormValues =
  {
    isElectricalRisk: false,
    electricalRiskFile: null,
    electricalRiskComment: "",
    electricalRiskKey: null,
    isAccessCompliance: false,
    accessComplianceFile: null,
    accessComplianceComment: "",
    accessComplianceKey: null,
    isParkingCompliance: false,
    parkingComplianceFile: null,
    parkingComplianceComment: "",
    parkingComplianceKey: null,
    isTerrainStability: false,
    terrainStabilityFile: null,
    terrainStabilityComment: "",
    terrainStabilityKey: null,
    flow: null,
  };
