export interface GetAnalyticstDto {
    dispatcherCompanyId?: number | null;
    startDate: string; 
    endDate: string; 
    limit?: number;
    offset?: number;
}
export interface ReservationsPerMonth {
    month: string; 
    totalReservations: number;
    progress: number | null;
} 
export interface TotalReservationsPerMonth {
    totalReservationsPerMonth: ReservationsPerMonth[];
}

export interface AverageM3PerJob {
    averageM3PerJob: number;
    progress: number | null;
}
export interface AverageM3PerHourCompany {
    averageM3PerHourCompany: number;
    progress: number | null;
}

export interface AverageM3PerHour {
    averageM3PerHour: number;
    progress: number | null;
}

export interface AverageJobCompletionTime {
    averageCompletionTimeInHours: number;
    progress: number | null;
}

export interface AverageReservationsPerDispatcher {
    averageReservationsPerDispatcher: number;
    progress: number | null;
}

export interface ExpensePerMonth {
    month: string; 
    totalExpenses: number;
    progress: number | null;
}  

export interface TotalExpensesPerMonth {
    totalExpensesPerMonth: ExpensePerMonth[];
}

export interface MostActiveContractor {
    operatorCompanyId?: number;
    operatorCompanyName: string;
    reservationCount: number;
    totalSum: number | null;
  }
  
export interface MostActiveContractorsResponse {
    mostActiveContractors: MostActiveContractor[];
    total: number
}
export interface UsedVehicle {
    vehicleId: number;
    vehicleName: string;
    companyName: string;
    reservationCount: number;
    totalSum: number;
  }
  
  export interface FrequentlyUsedVehicles {
    frequentlyUsedVehicles: UsedVehicle[];
    total: number
  }

export interface HighestExpensesPerPumpingCompany {
    highestExpensesPerPumpingCompany: number;
}

