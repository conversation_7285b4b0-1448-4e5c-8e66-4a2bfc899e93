import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  InputAdornment,
  Radio,
  RadioGroup,
  Stack,
  Typography,
} from "@mui/material";
import { FormikErrors, FormikProvider, useFormik } from "formik";
import * as yup from "yup";
import {
  CreatePriceListDto,
  PriceList,
  PriceListFormValues,
  PricelistTypeEnum,
  PumpTier,
  TransportRate,
  UpdatePriceListDto,
} from "src/common/types/priceList";
import { useTranslation } from "react-i18next";
import {
  getInitialPumpFeeOption,
  getPackageFlatHelperText,
  getTierHelperText,
  isPumpOrCityPump,
  turnPriceListFormValuesIntoCreateDto,
  turnPriceListFormValuesIntoUpdateDto,
  updateCurrentMaxAndNextMin,
  updateCurrentMinAndPrevMax,
} from "src/common/utils/priceList";
import { getCurrentUser, useVehicles } from "src/common/api";
import { Add01Icon, Delete01Icon } from "@hugeicons/react";
import { CeButton, CeTextField } from "src/common/components";
import React from "react";
import { Vehicle } from "src/common/types";

interface PriceListFormProps {
  title: string;
  isLoading: boolean;
  handleClose: () => void;
  handleCreateNewPriceList?: (args: CreatePriceListDto) => void;
  handleUpdatePriceList?: (args: UpdatePriceListDto) => void;
  initialFormValues: PriceListFormValues;
  vehiclesWithExistingPricelists: number[];
  pricelists: PriceList[];
  inheritFromExistingPricelist: (pricelist: PriceList) => void;
}

export const PriceListForm: React.FC<PriceListFormProps> = ({
  title,
  handleClose,
  isLoading,
  handleCreateNewPriceList,
  handleUpdatePriceList,
  initialFormValues,
  vehiclesWithExistingPricelists,
  pricelists,
  inheritFromExistingPricelist,
}) => {
  const currentUser = getCurrentUser();

  const { data: AllVehicles } = useVehicles(
    { operatorManagerId: currentUser?.id },
    Boolean(currentUser?.id)
  );

  const vehicles = AllVehicles?.data || [];

  const { t } = useTranslation(["dispatcher", "common"]);

  const initialPumpFeeOptions = getInitialPumpFeeOption(
    initialFormValues.flow,
    initialFormValues
  );
  const isCreateFlow =
    initialFormValues.flow === PricelistTypeEnum.CREATE_VARIANT;

  const isUpdateFlow =
    initialFormValues.flow === PricelistTypeEnum.UDPATE_VARIANT;

  const formik = useFormik<PriceListFormValues>({
    initialValues: {
      ...initialFormValues,
      pumpingFeeOptions: initialPumpFeeOptions,
    },
    enableReinitialize: true,
    validationSchema: yup.object({
      title: yup.string().required("Title is required"),
      packageFlatFee: yup
        .number()
        .required("Package FlatFee is required")
        .nullable(),
      packageFlatFeeDuration: yup
        .number()
        .required("Package FlatFee Duration is required")
        .nullable(),
      additionalHour: yup
        .number()
        .required("Additional hour is required")
        .nullable(),
      cleaningFee: yup
        .number()
        .required("Cleaning fee  is required")
        .nullable(),
      secondTechnicianHourFee: yup
        .number()
        .required("Second Technician Fee is required")
        .nullable(),
      pricePerMeterOfFlexiblePipeLength80Mm: yup
        .number()
        .required("Pipe length 80 is required")
        .nullable(),
      pricePerMeterOfFlexiblePipeLength90Mm: yup
        .number()
        .required("Pipe length 90 is required")
        .nullable(),
      pricePerMeterOfFlexiblePipeLength100Mm: yup
        .number()
        .required("Pipe length 100 is required")
        .nullable(),
      pricePerMeterOfRigidPipeLength120Mm: yup
        .number()
        .required("Pipe length 120 is required")
        .nullable(),
      pipeInvoicingStartsFrom: yup
        .number()
        .required("Pipe Invoicing Starts From is required")
        .nullable(),
      supplyOfTheChemicalSlushie: yup
        .number()
        .required("Supply of the chemical slushie required")
        .nullable(),
      barbotine: yup.number().required("barbotine required").nullable(),
      extraCementBagPrice: yup
        .number()
        .required("Extra cement bag price  is required")
        .nullable(),

      transportRates: yup.array().of(
        yup.object({
          tariff: yup.number().required("Tariff is required").nullable(),
          to: yup
            .number()
            .nullable()
            .required("To value is required")
            .moreThan(
              yup.ref("from"),
              "To value must be greater than From value"
            ),
        })
      ),
      pumpingFeeOptions: yup.object({
        tierPricing: yup.boolean(),
        pricePerMeter: yup.boolean(),
      }),
      pricePerMeterPumped: yup
        .number()
        .when("pumpingFeeOptions.pricePerMeter", {
          is: true,
          then: yup
            .number()
            .nullable()
            .required("Price Per Meter Pumped is required"),
          otherwise: yup.number().nullable(),
        }),
      pumpTiers: yup
        .array()
        .of(yup.object({}))
        .when("pumpingFeeOptions.tierPricing", {
          is: true,
          then: (schema) =>
            schema.of(
              yup.object({
                price: yup.number().nullable().required("Price is required"),
                minimum: yup
                  .number()
                  .nullable()
                  .required("Minimum value is required"),
                maximum: yup
                  .number()
                  .nullable()
                  .required("Maximum value is required")
                  .moreThan(
                    yup.ref("minimum"),
                    "Maximum must be greater than minimum"
                  ),
              })
            ),
          otherwise: (schema) =>
            schema.of(
              yup.object({
                price: yup.number().nullable(),
                minimum: yup.number().nullable(),
                maximum: yup.number().nullable(),
              })
            ),
        }),
      vehicleId: yup.number().nullable().required("Vehicle  is required"),

      isWeekendContract: yup.boolean(),
      isNightContract: yup.boolean(),
      packageFlatFeeWeekend: yup
        .number()
        .nullable()
        .when("isWeekendContract", {
          is: true,
          then: yup
            .number()
            .nullable()
            .required("Package flat fee weekend  is required"),
          otherwise: yup.number().nullable(),
        }),
      additionalHourWeekend: yup
        .number()
        .nullable()
        .when("isWeekendContract", {
          is: true,
          then: yup
            .number()
            .nullable()
            .required("Additional hour for weekend is required"),
          otherwise: yup.number().nullable(),
        }),
      packageFlatFeeNight: yup
        .number()
        .nullable()
        .when("isNightContract", {
          is: true,
          then: yup
            .number()
            .nullable()
            .required("Package flat fee for night is required"),
          otherwise: yup.number().nullable(),
        }),
      additionalHourNight: yup
        .number()
        .nullable()
        .when("isNightContract", {
          is: true,
          then: yup
            .number()
            .nullable()
            .required("Additional hour for night is required"),
          otherwise: yup.number().nullable(),
        }),
      minimumChargeFlatFee: yup.number().nullable(),
      minimumM3Charged: yup.number().nullable(),
      dayCancellationFee: yup.number().nullable(),
      cancellationFee: yup.number().nullable(),
      secondTechnicianHourFeeWeekend: yup.number().nullable(),
      secondTechnicianHourFeeNight: yup.number().nullable(),
    }),
    onSubmit: (values) => {
      if (isCreateFlow && handleCreateNewPriceList) {
        const payload: CreatePriceListDto =
          turnPriceListFormValuesIntoCreateDto(values);

        handleCreateNewPriceList(payload);
      }
      if (isUpdateFlow && handleUpdatePriceList) {
        const payload: UpdatePriceListDto =
          turnPriceListFormValuesIntoUpdateDto(values);

        handleUpdatePriceList(payload);
      }
    },
  });

  const handleAddTransportRate = () => {
    formik.setFieldValue("transportRates", [
      ...formik.values.transportRates,
      {
        name: "",
        tariff: null,
        from:
          (formik.values.transportRates[formik.values.transportRates.length - 1]
            ?.to ?? 0) + 1,
        to: null,
      },
    ]);
  };

  const handleRemoveTransportRate = (index: number) => {
    const updatedRates = formik.values.transportRates.filter(
      (_, i) => i !== index
    );
    formik.setFieldValue("transportRates", updatedRates);
  };

  const handleAddTier = () => {
    formik.setFieldValue("pumpTiers", [
      ...formik.values.pumpTiers,
      {
        name: "",
        price: null,
        minimum:
          (formik.values.pumpTiers[formik.values.pumpTiers.length - 1]
            ?.maximum ?? 0) + 1,
        maximum: null,
      },
    ]);
  };

  const handleRemoveTier = (index: number) => {
    const updatedTiers = formik.values.pumpTiers.filter((_, i) => i !== index);
    formik.setFieldValue("pumpTiers", updatedTiers);
  };

  return (
    <FormikProvider value={formik}>
      <Stack noValidate component="form" onSubmit={formik.handleSubmit}>
        <Box
          sx={{
            width: "100%",
            height: "70px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            position: "sticky",
            top: 0,
            zIndex: 10,
            borderBottom: "1px solid",
            borderColor: (theme) => theme.palette.divider,
            backgroundColor: "inherit",
          }}
        >
          <Typography variant="h6" letterSpacing={0.15} fontSize={20}>
            {title}
          </Typography>
        </Box>

        <Stack
          spacing={2}
          sx={{
            height: "calc(100vh - 140px)",
            overflowY: "scroll",
            width: "35vw",
          }}
        >
          <Stack sx={{ mt: 2, px: 4.5, gap: 3 }}>
            <Autocomplete
              id="vehicle"
              fullWidth
              value={
                vehicles.find((v) => v.id === formik.values.vehicleId) || null
              }
              onChange={(event: any, newValue: Vehicle | null) => {
                formik.setFieldValue(
                  "vehicleId",
                  newValue ? newValue.id : null
                );
                formik.setFieldValue(
                  "title",
                  newValue?.uniqueIdentificationNumber
                );
              }}
              onBlur={() => formik.setFieldTouched("vehicleId", true)}
              isOptionEqualToValue={(option: Vehicle, value: Vehicle) =>
                option.id === value.id
              }
              getOptionLabel={(vehicle: Vehicle) =>
                `${vehicle.uniqueIdentificationNumber}` || ""
              }
              options={vehicles}
              filterOptions={(options) =>
                options.filter(
                  (option) =>
                    !vehiclesWithExistingPricelists.includes(option.id)
                )
              }
              renderInput={(params) => (
                <CeTextField
                  {...params}
                  InputLabelProps={{ shrink: true }}
                  label={t("common:vehicles")}
                  size="small"
                  error={
                    formik.touched.vehicleId && Boolean(formik.errors.vehicleId)
                  }
                  helperText={
                    formik.touched.vehicleId && formik.errors.vehicleId
                  }
                />
              )}
            />
            {isCreateFlow ? (
              <Autocomplete
                id="pricelist"
                fullWidth
                value={
                  pricelists.find((p) => p.title === initialFormValues.title) ||
                  null
                }
                onChange={(_, newValue: PriceList | null) => {
                  if (newValue) {
                    inheritFromExistingPricelist(newValue);
                  }
                }}
                isOptionEqualToValue={(option: PriceList, value: PriceList) =>
                  option.id === value.id
                }
                getOptionLabel={(pricelist: PriceList) =>
                  `${pricelist.title}` || ""
                }
                options={pricelists}
                renderInput={(params) => (
                  <CeTextField
                    {...params}
                    InputLabelProps={{ shrink: true }}
                    label={t("common:copy-from")}
                    size="small"
                  />
                )}
              />
            ) : null}
          </Stack>

          <Stack id="packageAndHourlyFees" sx={{ px: 1.5, py: 2, gap: 3 }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                Package & Hourly fees
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>
            <Stack gap={2.5} px={3}>
              <Box>
                <Box sx={{ display: "flex", gap: 2, height: "63px", mb: 2 }}>
                  <CeTextField
                    fullWidth
                    id="packageFlatFee"
                    name="packageFlatFee"
                    label={t("package-flat-fee")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">€</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={formik.values.packageFlatFee || ""}
                    onChange={formik.handleChange}
                    error={
                      formik.touched.packageFlatFee &&
                      Boolean(formik.errors.packageFlatFee)
                    }
                    helperText={
                      (formik.touched.packageFlatFee &&
                        formik.errors.packageFlatFee) ||
                      getPackageFlatHelperText(
                        formik.values.packageFlatFeeDuration,
                        true
                      )
                    }
                    disabled={isLoading}
                  />
                  <Divider
                    orientation="vertical"
                    flexItem
                    variant="fullWidth"
                  />
                  <CeTextField
                    fullWidth
                    id="packageFlatFeeDuration"
                    name="packageFlatFeeDuration"
                    label={t("packageFlatFeeDuration")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">h</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={formik.values.packageFlatFeeDuration || ""}
                    onChange={formik.handleChange}
                    error={
                      formik.touched.packageFlatFeeDuration &&
                      Boolean(formik.errors.packageFlatFeeDuration)
                    }
                    helperText={
                      formik.touched.packageFlatFeeDuration &&
                      formik.errors.packageFlatFeeDuration
                    }
                    disabled={isLoading}
                  />
                </Box>
                <Box height={64}>
                  <CeTextField
                    fullWidth
                    id="additionalHour"
                    name="additionalHour"
                    label={t("additional-hour")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">€/h</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={formik.values.additionalHour || ""}
                    onChange={formik.handleChange}
                    error={
                      formik.touched.additionalHour &&
                      Boolean(formik.errors.additionalHour)
                    }
                    helperText={
                      (formik.touched.additionalHour &&
                        formik.errors.additionalHour) ||
                      getPackageFlatHelperText(
                        formik.values.packageFlatFeeDuration,
                        false
                      )
                    }
                    disabled={isLoading}
                  />
                </Box>
              </Box>
              {isPumpOrCityPump(vehicles, formik.values.vehicleId) ? (
                <>
                  <Box>
                    <FormControl
                      fullWidth
                      sx={{
                        mx: 1.25,
                        mt: 0,
                        mb: 3,
                      }}
                    >
                      <FormControlLabel
                        label={t("weekend-pricing")}
                        control={
                          <Checkbox
                            size="small"
                            sx={{ py: 0 }}
                            checked={formik.values.isWeekendContract}
                            name="isWeekendContract"
                            onChange={(event) => {
                              const { checked } =
                                event.target as HTMLInputElement;

                              formik.setFieldValue(
                                "isWeekendContract",
                                checked
                              );

                              if (!checked) {
                                formik.setFieldValue(
                                  "packageFlatFeeWeekend",
                                  null
                                );
                                formik.setFieldValue(
                                  "additionalHourWeekend",
                                  null
                                );
                              }
                            }}
                          />
                        }
                      />
                    </FormControl>
                    {formik.values.isWeekendContract ? (
                      <Box sx={{ display: "flex", gap: 2, height: "63px" }}>
                        <CeTextField
                          fullWidth
                          id="packageFlatFeeWeekend"
                          name="packageFlatFeeWeekend"
                          label={t("weekend-flat-fee")}
                          size="small"
                          type="number"
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">€</InputAdornment>
                            ),
                          }}
                          InputLabelProps={{ shrink: true }}
                          value={formik.values.packageFlatFeeWeekend || ""}
                          onChange={formik.handleChange}
                          disabled={isLoading}
                          helperText={
                            formik.touched.packageFlatFeeWeekend &&
                            formik.errors.packageFlatFeeWeekend
                          }
                          error={
                            formik.touched.packageFlatFeeWeekend &&
                            Boolean(formik.errors.packageFlatFeeWeekend)
                          }
                        />
                        <Divider
                          orientation="vertical"
                          flexItem
                          variant="fullWidth"
                        />
                        <CeTextField
                          fullWidth
                          id="additionalHourWeekend"
                          name="additionalHourWeekend"
                          label={t("weekend-additional-hour")}
                          size="small"
                          type="number"
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                €/h
                              </InputAdornment>
                            ),
                          }}
                          InputLabelProps={{ shrink: true }}
                          value={formik.values.additionalHourWeekend || ""}
                          onChange={formik.handleChange}
                          error={
                            formik.touched.additionalHourWeekend &&
                            Boolean(formik.errors.additionalHourWeekend)
                          }
                          helperText={
                            formik.touched.additionalHourWeekend &&
                            formik.errors.additionalHourWeekend
                          }
                          disabled={isLoading}
                        />
                      </Box>
                    ) : null}
                  </Box>
                  <Box>
                    <FormControl
                      fullWidth
                      sx={{
                        mx: 1.25,
                        mt: 0,
                        mb: 3,
                      }}
                    >
                      <FormControlLabel
                        label={t("night-pricing")}
                        control={
                          <Checkbox
                            size="small"
                            sx={{ py: 0 }}
                            checked={formik.values.isNightContract}
                            name="isNightContract"
                            onChange={(event) => {
                              formik.handleChange(event);

                              const { checked } =
                                event.target as HTMLInputElement;

                              if (!checked) {
                                formik.setFieldValue(
                                  "packageFlatFeeNight",
                                  null
                                );
                                formik.setFieldValue(
                                  "additionalHourNight",
                                  null
                                );
                              }
                            }}
                          />
                        }
                      />
                    </FormControl>
                    {formik.values.isNightContract ? (
                      <Box sx={{ display: "flex", gap: 2, height: "63px" }}>
                        <CeTextField
                          fullWidth
                          id="packageFlatFeeNight"
                          name="packageFlatFeeNight"
                          label={t("night-flat-fee")}
                          size="small"
                          type="number"
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">€</InputAdornment>
                            ),
                          }}
                          InputLabelProps={{ shrink: true }}
                          value={formik.values.packageFlatFeeNight || ""}
                          onChange={formik.handleChange}
                          disabled={isLoading}
                          error={
                            formik.touched.packageFlatFeeNight &&
                            Boolean(formik.errors.packageFlatFeeNight)
                          }
                          helperText={
                            formik.touched.packageFlatFeeNight &&
                            formik.errors.packageFlatFeeNight
                          }
                        />
                        <Divider
                          orientation="vertical"
                          flexItem
                          variant="fullWidth"
                        />
                        <CeTextField
                          fullWidth
                          id="additionalHourNight"
                          name="additionalHourNight"
                          label={t("night-hour")}
                          size="small"
                          type="number"
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                €/h
                              </InputAdornment>
                            ),
                          }}
                          InputLabelProps={{ shrink: true }}
                          value={formik.values.additionalHourNight || ""}
                          onChange={formik.handleChange}
                          disabled={isLoading}
                          error={
                            formik.touched.additionalHourNight &&
                            Boolean(formik.errors.additionalHourNight)
                          }
                          helperText={
                            formik.touched.additionalHourNight &&
                            formik.errors.additionalHourNight
                          }
                        />
                      </Box>
                    ) : null}
                  </Box>
                </>
              ) : null}
            </Stack>
          </Stack>

          <Stack id="pumpingFees" sx={{ px: 1.5, py: 2, gap: 3 }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                Pumping fees
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>

            <Stack sx={{ px: 3, gap: 3 }}>
              <Box sx={{ display: "flex", gap: 2 }}>
                <CeTextField
                  fullWidth
                  id="minimumChargeFlatFee"
                  name="minimumChargeFlatFee"
                  label={t("minimum-charge")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={formik.values.minimumChargeFlatFee || ""}
                  onChange={formik.handleChange}
                  disabled={isLoading}
                  helperText={
                    formik.touched.minimumChargeFlatFee &&
                    formik.errors.minimumChargeFlatFee
                  }
                  error={
                    formik.touched.minimumChargeFlatFee &&
                    Boolean(formik.errors.minimumChargeFlatFee)
                  }
                />
                <Divider orientation="vertical" flexItem variant="fullWidth" />
                <CeTextField
                  fullWidth
                  id="minimumM3Charged"
                  name="minimumM3Charged"
                  label={t("minimum-m3-charged")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€/h</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={formik.values.minimumM3Charged || ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.minimumM3Charged &&
                    Boolean(formik.errors.minimumM3Charged)
                  }
                  helperText={
                    formik.touched.minimumM3Charged &&
                    formik.errors.minimumM3Charged
                  }
                  disabled={isLoading}
                />
              </Box>
              <FormControl sx={{ width: "100%" }}>
                <RadioGroup
                  sx={{ width: "100%" }}
                  row
                  name="pumpingFeeOptions"
                  value={
                    formik.values.pumpingFeeOptions?.pricePerMeter
                      ? "pricePerMeter"
                      : "tierPricing"
                  }
                  onChange={(event) => {
                    const selectedValue = event.target.value;
                    formik.setFieldValue(
                      "pumpingFeeOptions.tierPricing",
                      selectedValue === "tierPricing"
                    );
                    formik.setFieldValue(
                      "pumpingFeeOptions.pricePerMeter",
                      selectedValue === "pricePerMeter"
                    );
                    if (selectedValue === "pricePerMeter") {
                      formik.setFieldValue("pumpTiers", []);
                      formik.setFieldValue("pricePerMeterPumped", "");
                      return;
                    }
                    formik.setFieldValue("pumpTiers", [
                      { name: "", price: null, minimum: 0, maximum: null },
                    ]);
                    formik.setFieldValue("pricePerMeterPumped", null);
                  }}
                >
                  <FormControlLabel
                    value="pricePerMeter"
                    sx={{ padding: 1.5 }}
                    control={<Radio size="medium" />}
                    label={
                      <Typography
                        variant="body1"
                        lineHeight={1.5}
                        letterSpacing={0.15}
                      >
                        {t("price-per-meter-pumped")}
                      </Typography>
                    }
                  />
                  <FormControlLabel
                    value="tierPricing"
                    control={<Radio />}
                    label={
                      <Typography variant="body1">
                        {t("tier-pricing")}
                      </Typography>
                    }
                  />
                </RadioGroup>
              </FormControl>
              {formik.values.pumpingFeeOptions?.tierPricing ? (
                <Stack gap={2.5} alignItems="flex-start">
                  {formik.values.pumpTiers.map((tier, index) => {
                    const tierErrors = formik.errors.pumpTiers?.[index] as
                      | FormikErrors<PumpTier>
                      | undefined;

                    return (
                      <React.Fragment key={index}>
                        <Stack
                          direction="row"
                          spacing={2}
                          alignItems="flex-start"
                        >
                          <Typography
                            sx={{ fontSize: 14 }}
                            fontWeight={700}
                            variant="overline"
                            fontSize={12}
                            lineHeight="32.92px"
                            whiteSpace="nowrap"
                          >
                            {`Tier ${index + 1}`}
                          </Typography>

                          <CeTextField
                            sx={{ flex: "50%" }}
                            id={`pumpTiers[${index}].price`}
                            name={`pumpTiers[${index}].price`}
                            label={`Price for tier ${index + 1}`}
                            size="small"
                            type="number"
                            value={tier.price || ""}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            InputLabelProps={{ shrink: true }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  €/h
                                </InputAdornment>
                              ),
                            }}
                            error={
                              formik.touched.pumpTiers?.[index]?.price &&
                              Boolean(tierErrors?.price)
                            }
                            helperText={
                              (formik.touched.pumpTiers?.[index]?.price &&
                                tierErrors?.price) ||
                              getTierHelperText(
                                index,
                                formik.values.pumpTiers?.[index]?.minimum,
                                formik.values.pumpTiers?.[index]?.maximum
                              )
                            }
                          />
                          <Divider
                            orientation="vertical"
                            flexItem
                            variant="fullWidth"
                          />

                          <CeTextField
                            sx={{ flex: "25%" }}
                            id={`pumpTiers[${index}].minimum`}
                            name={`pumpTiers[${index}].minimum`}
                            label={t("min-m3")}
                            size="small"
                            type="number"
                            value={
                              index === 0
                                ? tier.minimum ?? 0
                                : tier.minimum || ""
                            }
                            onChange={(event) =>
                              updateCurrentMinAndPrevMax(
                                event,
                                `pumpTiers[${index}].minimum`,
                                `pumpTiers[${index - 1}].maximum`,
                                index,
                                formik.values.pumpTiers,
                                formik.setFieldValue
                              )
                            }
                            onBlur={formik.handleBlur}
                            InputLabelProps={{ shrink: true }}
                            disabled={index === 0}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  m³
                                </InputAdornment>
                              ),
                            }}
                            error={
                              formik.touched.pumpTiers?.[index]?.minimum &&
                              Boolean(tierErrors?.minimum)
                            }
                            helperText={
                              formik.touched.pumpTiers?.[index]?.minimum &&
                              tierErrors?.minimum
                            }
                          />
                          <CeTextField
                            sx={{ flex: "25%" }}
                            id={`pumpTiers[${index}].maximum`}
                            name={`pumpTiers[${index}].maximum`}
                            label={t("max-m3")}
                            size="small"
                            type="number"
                            value={tier.maximum || ""}
                            onChange={(event) => {
                              updateCurrentMaxAndNextMin(
                                event,
                                `pumpTiers[${index}].maximum`,
                                `pumpTiers[${index + 1}].minimum`,
                                index,
                                formik.values.pumpTiers,
                                formik.setFieldValue
                              );
                            }}
                            onBlur={formik.handleBlur}
                            InputLabelProps={{ shrink: true }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  m³
                                </InputAdornment>
                              ),
                            }}
                            error={
                              formik.touched.pumpTiers?.[index]?.maximum &&
                              Boolean(tierErrors?.maximum)
                            }
                            helperText={
                              formik.touched.pumpTiers?.[index]?.maximum &&
                              tierErrors?.maximum
                            }
                          />
                          {formik.values.pumpTiers.length > 1 && (
                            <Button
                              type="button"
                              onClick={() => handleRemoveTier(index)}
                              color="error"
                              variant="text"
                              size="small"
                              sx={{ minWidth: 34 }}
                              disabled={
                                index !== formik.values.pumpTiers.length - 1
                              }
                            >
                              <Delete01Icon />
                            </Button>
                          )}
                        </Stack>
                      </React.Fragment>
                    );
                  })}
                  <CeButton
                    type="button"
                    startIcon={<Add01Icon />}
                    onClick={handleAddTier}
                    variant="text"
                    size="large"
                  >
                    {t("add-additional-tier")}
                  </CeButton>
                </Stack>
              ) : (
                <Box sx={{ height: 64 }}>
                  <CeTextField
                    fullWidth
                    id="pricePerMeterPumped"
                    name="pricePerMeterPumped"
                    label={t("price-per-meter-pumped")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">€/m3</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={formik.values.pricePerMeterPumped || ""}
                    onChange={formik.handleChange}
                    error={
                      formik.touched.pricePerMeterPumped &&
                      Boolean(formik.errors.pricePerMeterPumped)
                    }
                    helperText={
                      formik.touched.pricePerMeterPumped &&
                      formik.errors.pricePerMeterPumped
                    }
                    disabled={isLoading}
                  />
                </Box>
              )}
            </Stack>
          </Stack>

          <Stack id="pipes" sx={{ px: 1.5, py: 2, gap: 3 }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                Pipes
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>
            <Box sx={{ px: 3, pb: 2 }}>
              <Stack direction="column" gap={3}>
                <Box sx={{ display: "flex", gap: "12px" }}>
                  <CeTextField
                    sx={{ flex: "50%" }}
                    fullWidth
                    id="pricePerMeterOfFlexiblePipeLength80Mm"
                    name="pricePerMeterOfFlexiblePipeLength80Mm"
                    label={t("price-per-meter-of-flexible-pipe-length-80Mm")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">€/m</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={
                      formik.values.pricePerMeterOfFlexiblePipeLength80Mm || ""
                    }
                    onChange={formik.handleChange}
                    error={
                      formik.touched.pricePerMeterOfFlexiblePipeLength80Mm &&
                      Boolean(
                        formik.errors.pricePerMeterOfFlexiblePipeLength80Mm
                      )
                    }
                    helperText={
                      formik.touched.pricePerMeterOfFlexiblePipeLength80Mm &&
                      formik.errors.pricePerMeterOfFlexiblePipeLength80Mm
                    }
                    disabled={isLoading}
                  />
                  <CeTextField
                    sx={{ flex: "50%" }}
                    fullWidth
                    id="pricePerMeterOfFlexiblePipeLength90Mm"
                    name="pricePerMeterOfFlexiblePipeLength90Mm"
                    label={t("price-per-meter-of-flexible-pipe-length-90Mm")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">€/m</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={
                      formik.values.pricePerMeterOfFlexiblePipeLength90Mm || ""
                    }
                    onChange={formik.handleChange}
                    error={
                      formik.touched.pricePerMeterOfFlexiblePipeLength90Mm &&
                      Boolean(
                        formik.errors.pricePerMeterOfFlexiblePipeLength90Mm
                      )
                    }
                    helperText={
                      formik.touched.pricePerMeterOfFlexiblePipeLength90Mm &&
                      formik.errors.pricePerMeterOfFlexiblePipeLength90Mm
                    }
                    disabled={isLoading}
                  />
                </Box>
                <Box sx={{ display: "flex", gap: "12px" }}>
                  <CeTextField
                    sx={{ flex: "50%" }}
                    fullWidth
                    id="pricePerMeterOfFlexiblePipeLength100Mm"
                    name="pricePerMeterOfFlexiblePipeLength100Mm"
                    label={t("price-per-meter-of-flexible-pipe-length-100Mm")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">€/m</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={
                      formik.values.pricePerMeterOfFlexiblePipeLength100Mm || ""
                    }
                    onChange={formik.handleChange}
                    error={
                      formik.touched.pricePerMeterOfFlexiblePipeLength100Mm &&
                      Boolean(
                        formik.errors.pricePerMeterOfFlexiblePipeLength100Mm
                      )
                    }
                    helperText={
                      formik.touched.pricePerMeterOfFlexiblePipeLength100Mm &&
                      formik.errors.pricePerMeterOfFlexiblePipeLength100Mm
                    }
                    disabled={isLoading}
                  />
                  <CeTextField
                    sx={{ flex: "50%" }}
                    fullWidth
                    id="pricePerMeterOfRigidPipeLength120Mm"
                    name="pricePerMeterOfRigidPipeLength120Mm"
                    label={t("price-per-meter-of-rigid-pipe-length-120Mm")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">€/m</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={
                      formik.values.pricePerMeterOfRigidPipeLength120Mm || ""
                    }
                    onChange={formik.handleChange}
                    error={
                      formik.touched.pricePerMeterOfRigidPipeLength120Mm &&
                      Boolean(formik.errors.pricePerMeterOfRigidPipeLength120Mm)
                    }
                    helperText={
                      formik.touched.pricePerMeterOfRigidPipeLength120Mm &&
                      formik.errors.pricePerMeterOfRigidPipeLength120Mm
                    }
                    disabled={isLoading}
                  />
                </Box>
                <CeTextField
                  fullWidth
                  id="pipeInvoicingStartsFrom"
                  name="pipeInvoicingStartsFrom"
                  label={t("pipeInvoicingStartsFrom")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">m</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={formik.values.pipeInvoicingStartsFrom || ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.pipeInvoicingStartsFrom &&
                    Boolean(formik.errors.pipeInvoicingStartsFrom)
                  }
                  helperText={
                    formik.touched.pipeInvoicingStartsFrom &&
                    formik.errors.pipeInvoicingStartsFrom
                  }
                  disabled={isLoading}
                />
              </Stack>
            </Box>
          </Stack>

          <Stack id="additionalServices" sx={{ px: 1.5, py: 2, gap: 3 }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                Additional Services & Products
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>
            <Box sx={{ px: 3 }}>
              <Stack direction="column" gap={3}>
                <CeTextField
                  fullWidth
                  id="cleaningFee"
                  name="cleaningFee"
                  label={t("cleaning-fee")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={formik.values.cleaningFee || ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.cleaningFee &&
                    Boolean(formik.errors.cleaningFee)
                  }
                  helperText={
                    formik.touched.cleaningFee && formik.errors.cleaningFee
                  }
                  disabled={isLoading}
                />
                <CeTextField
                  fullWidth
                  id="extraCementBagPrice"
                  name="extraCementBagPrice"
                  label={t("extra-cement-bag-price")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€/bag</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={formik.values.extraCementBagPrice || ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.extraCementBagPrice &&
                    Boolean(formik.errors.extraCementBagPrice)
                  }
                  helperText={
                    formik.touched.extraCementBagPrice &&
                    formik.errors.extraCementBagPrice
                  }
                  disabled={isLoading}
                />
                <CeTextField
                  fullWidth
                  id="supplyOfTheChemicalSlushie"
                  name="supplyOfTheChemicalSlushie"
                  label={t("supply-of-the-chemical-slushie")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={formik.values.supplyOfTheChemicalSlushie || ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.supplyOfTheChemicalSlushie &&
                    Boolean(formik.errors.supplyOfTheChemicalSlushie)
                  }
                  helperText={
                    formik.touched.supplyOfTheChemicalSlushie &&
                    formik.errors.supplyOfTheChemicalSlushie
                  }
                  disabled={isLoading}
                />
                <CeTextField
                  fullWidth
                  id="barbotine"
                  name="barbotine"
                  label={t("barbotine")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={formik.values.barbotine || ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.barbotine && Boolean(formik.errors.barbotine)
                  }
                  helperText={
                    formik.touched.barbotine && formik.errors.barbotine
                  }
                  disabled={isLoading}
                />
              </Stack>
            </Box>
          </Stack>

          <Stack id="transportRates" sx={{ px: 1.5, py: 2, gap: 3 }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                Transport rates
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>
            <Box sx={{ px: 3 }}>
              <Stack direction="column" gap={2.5} alignItems="flex-start">
                {formik.values.transportRates.map((rate, index) => {
                  const transportRateErrors = formik.errors.transportRates?.[
                    index
                  ] as FormikErrors<TransportRate> | undefined;
                  return (
                    <Stack direction="row" spacing={2} alignItems="flex-start">
                      <CeTextField
                        sx={{ flex: "50%" }}
                        fullWidth
                        id={`transportRates[${index}].tariff`}
                        name={`transportRates[${index}].tariff`}
                        label={`Tariff ${index + 1}`}
                        size="small"
                        type="number"
                        value={rate.tariff || ""}
                        onChange={formik.handleChange}
                        InputLabelProps={{ shrink: true }}
                        onBlur={formik.handleBlur}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">€</InputAdornment>
                          ),
                        }}
                        error={
                          formik.touched.transportRates?.[index]?.tariff &&
                          Boolean(transportRateErrors?.tariff)
                        }
                        helperText={
                          formik.touched.transportRates?.[index]?.tariff &&
                          transportRateErrors?.tariff
                        }
                      />
                      <Divider
                        orientation="vertical"
                        flexItem
                        variant="fullWidth"
                      />
                      <CeTextField
                        sx={{ flex: "25%" }}
                        fullWidth
                        id={`transportRates[${index}].from`}
                        name={`transportRates[${index}].from`}
                        label={t("date-from")}
                        size="small"
                        type="number"
                        value={index === 0 ? rate.from ?? 0 : rate.from || ""}
                        onChange={(event) =>
                          updateCurrentMinAndPrevMax(
                            event,
                            `transportRates[${index}].from`,
                            `transportRates[${index - 1}].to`,
                            index,
                            formik.values.transportRates,
                            formik.setFieldValue
                          )
                        }
                        onBlur={formik.handleBlur}
                        InputLabelProps={{ shrink: true }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">km</InputAdornment>
                          ),
                        }}
                        error={
                          formik.touched.transportRates?.[index]?.from &&
                          Boolean(transportRateErrors?.from)
                        }
                        helperText={
                          formik.touched.transportRates?.[index]?.from &&
                          transportRateErrors?.from
                        }
                      />
                      <CeTextField
                        sx={{ flex: "25%" }}
                        fullWidth
                        id={`transportRates[${index}].to`}
                        name={`transportRates[${index}].to`}
                        label={t("date-to")}
                        size="small"
                        type="number"
                        value={rate.to || ""}
                        onBlur={formik.handleBlur}
                        onChange={(event) =>
                          updateCurrentMaxAndNextMin(
                            event,
                            `transportRates[${index}].to`,
                            `transportRates[${index + 1}].from`,
                            index,
                            formik.values.transportRates,
                            formik.setFieldValue
                          )
                        }
                        InputLabelProps={{ shrink: true }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">km</InputAdornment>
                          ),
                        }}
                        error={
                          formik.touched.transportRates?.[index]?.to &&
                          Boolean(transportRateErrors?.to)
                        }
                        helperText={
                          formik.touched.transportRates?.[index]?.to &&
                          transportRateErrors?.to
                        }
                      />
                      {formik.values.transportRates.length > 1 && (
                        <Button
                          type="button"
                          onClick={() => handleRemoveTransportRate(index)}
                          color="error"
                          variant="text"
                          size="small"
                          sx={{ minWidth: 34 }}
                          disabled={
                            index !== formik.values.transportRates.length - 1
                          }
                        >
                          <Delete01Icon />
                        </Button>
                      )}
                    </Stack>
                  );
                })}
                <CeButton
                  type="button"
                  startIcon={<Add01Icon />}
                  onClick={handleAddTransportRate}
                  variant="text"
                  size="large"
                >
                  {t("add-tariff")}
                </CeButton>
              </Stack>
            </Box>
          </Stack>

          <Stack id="contingencies" sx={{ px: 1.5, pt: 2, gap: 3, pb: 8 }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                Contingencies
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>
            <Box sx={{ px: 3 }}>
              <Stack direction="column" gap={3}>
                <>
                  <CeTextField
                    fullWidth
                    id="cancellationFee"
                    name="cancellationFee"
                    label={t("standard-cancellation-fee")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">€</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={formik.values.cancellationFee || ""}
                    onChange={formik.handleChange}
                    error={
                      formik.touched.cancellationFee &&
                      Boolean(formik.errors.cancellationFee)
                    }
                    helperText={
                      formik.touched.cancellationFee &&
                      formik.errors.cancellationFee
                    }
                    disabled={isLoading}
                  />
                  <CeTextField
                    fullWidth
                    id="dayCancellationFee"
                    name="dayCancellationFee"
                    label={t("late-cancellation-fee")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">€</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={formik.values.dayCancellationFee || ""}
                    onChange={formik.handleChange}
                    error={
                      formik.touched.dayCancellationFee &&
                      Boolean(formik.errors.dayCancellationFee)
                    }
                    helperText={
                      formik.touched.dayCancellationFee &&
                      formik.errors.dayCancellationFee
                    }
                    disabled={isLoading}
                  />
                </>

                <CeTextField
                  fullWidth
                  id="secondTechnicianHourFee"
                  name="secondTechnicianHourFee"
                  label={t("secondTechnicianFee")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€/h</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={formik.values.secondTechnicianHourFee || ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.secondTechnicianHourFee &&
                    Boolean(formik.errors.secondTechnicianHourFee)
                  }
                  helperText={
                    formik.touched.secondTechnicianHourFee &&
                    formik.errors.secondTechnicianHourFee
                  }
                  disabled={isLoading}
                />
                <CeTextField
                  fullWidth
                  id="secondTechnicianHourFeeNight"
                  name="secondTechnicianHourFeeNight"
                  label={t("common:second-technician-fee-night")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€/h</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={formik.values.secondTechnicianHourFeeNight || ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.secondTechnicianHourFeeNight &&
                    Boolean(formik.errors.secondTechnicianHourFeeNight)
                  }
                  helperText={
                    formik.touched.secondTechnicianHourFeeNight &&
                    formik.errors.secondTechnicianHourFeeNight
                  }
                  disabled={isLoading}
                />

                <CeTextField
                  fullWidth
                  id="secondTechnicianHourFeeWeekend"
                  name="secondTechnicianHourFeeWeekend"
                  label={t("second-technician-fee-weekend")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={formik.values.secondTechnicianHourFeeWeekend || ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.secondTechnicianHourFeeWeekend &&
                    Boolean(formik.errors.secondTechnicianHourFeeWeekend)
                  }
                  helperText={
                    formik.touched.secondTechnicianHourFeeWeekend &&
                    formik.errors.secondTechnicianHourFeeWeekend
                  }
                  disabled={isLoading}
                />
              </Stack>
            </Box>
          </Stack>
        </Stack>
        {/* <Typography color={"text.secondary"} variant="caption">
          {t("vat-info")}
        </Typography> */}
        <Box
          sx={{
            width: "100%",
            height: "70px",
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-end",
            position: "sticky",
            bottom: 0,
            pr: 4,
            zIndex: 10,
            gap: 2,
            borderTop: "1px solid",
            borderColor: (theme) => theme.palette.divider,
            backgroundColor: "inherit",
          }}
        >
          <CeButton
            variant="text"
            size="large"
            onClick={handleClose}
            disabled={isLoading}
          >
            {t("common:cancel")}
          </CeButton>
          <CeButton type="submit" size="large" disabled={isLoading}>
            {t("common:submit")}
          </CeButton>
        </Box>
      </Stack>
    </FormikProvider>
  );
};
