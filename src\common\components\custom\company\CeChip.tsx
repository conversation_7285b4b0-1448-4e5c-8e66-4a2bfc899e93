import React from 'react';
import styled from '@emotion/styled';
import Chip, { ChipProps } from '@mui/material/Chip';

interface CeChipProps extends ChipProps {
    status?: 'positive' | 'negative';
}

const StyledChip = styled(Chip)<CeChipProps>(({ theme, status }) => ({
    backgroundColor: status === 'positive' ? '#05C16833' : '#DA262633',
    color: status === 'positive' ? '#05C168' : '#DA2626',
    border: `1px solid ${status === 'positive' ? '#05C16833' : '#DA262633'}`,
    borderRadius: '8px',
    fontSize: '12px',
    fontWeight: '400',
    '& .MuiChip-deleteIcon': {
        color: 'inherit',
        '&:hover': {
            color: 'inherit', 
        },
    },
}));

export const CeChip: React.FC<CeChipProps> = (props) => {
    return <StyledChip {...props} />;
};
