import { GridSortModel } from "@mui/x-data-grid";
import { CommonEntity, PossibleSortDir, Expression, Company, Task } from ".";

type LabelFlow = "Create" | "Update" | null;

export enum LabelColors {
  GREY = "#78909C",
  PURPLE = "#7E57C2",
  BLUE = "#42A5F5",
  RED = "#EF5350",
  ORANGE = "#FF7043",
  GREEN = "#66BB6A",
}

export interface TaskLabel extends CommonEntity {
  id: number;
  name: string;
  color: LabelColors;
  companyId: number;
  company?: Company;
  tasks?: Task[];
}
export interface TaskLabelState {
  labelId: number | null;
  name: string | null;
  color: LabelColors | null;
  flow: LabelFlow;
}

export interface CreateTaskLabelDto {
  name: string;
  color: LabelColors;
}

export type UpdateTaskLabelDto = Partial<CreateTaskLabelDto> & {
  labelId: number;
};

export interface GetLabelTaskDto {
  expressions: Expression[];
  sortModel: GridSortModel;
  relations?: string[];
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
}

export interface TaskLabelWithCount {
  totalCount: number;
  data: TaskLabel[];
}
