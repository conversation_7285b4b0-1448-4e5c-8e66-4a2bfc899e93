import { Box, Grid, Typography } from '@mui/material';
import { useWindowSize } from '@react-hook/window-size';
import { endOfYear, format, startOfYear } from 'date-fns';
import React, { useState } from 'react'
import { getCurrentUser } from 'src/common/api';
import { useFrequentlyUsedVehicles, useMostActiveContractors } from 'src/common/api/analytics';
import { CePaper } from 'src/common/components';
import { GetAnalyticstDto } from 'src/common/types/analytics';
import ActiveContractorsDatagrid from '../datagrid/ActiveContractorsDatagrid';
import UsedVehiclesDatagrid from '../datagrid/UsedVehiclesDatagrid';
import { useTranslation } from 'react-i18next';

const DashboardTables = () => {
    const { t } = useTranslation(["dispatcher"]);
    const [, height] = useWindowSize();
    const currentUser = getCurrentUser();
    const currentDate = new Date();

    const [pageState, setPageState] = useState({
        activeContractor: { page: 1, pageSize: 20 },
        usedVehicles: { page: 1, pageSize: 20 },
    });

    const commonAttrs = {
        dispatcherCompanyId: currentUser?.companyId,
        startDate: format(startOfYear(currentDate), 'yyyy-MM-dd'),
        endDate: format(endOfYear(currentDate), 'yyyy-MM-dd'),
    };

    const attrsActiveContractors: GetAnalyticstDto = {
        ...commonAttrs,
        limit: pageState.activeContractor.pageSize,
        offset: (pageState.activeContractor.page - 1) * pageState.activeContractor.pageSize,
    };

    const attrsUsedVehicles: GetAnalyticstDto = {
        ...commonAttrs,
        limit: pageState.usedVehicles.pageSize,
        offset: (pageState.usedVehicles.page - 1) * pageState.usedVehicles.pageSize,
    };

    const {
        data: mostActiveContractors,
        isLoading: isLoadingActiveContractors,
        refetch: refetchActiveContractors,
    } = useMostActiveContractors(attrsActiveContractors, Boolean(currentUser?.id));

    const {
        data: usedVehicles,
        isLoading: isLoadingUsedVehicles,
        refetch: refetchUsedVehicles,
    } = useFrequentlyUsedVehicles(attrsUsedVehicles, Boolean(currentUser?.id));

    const handlePageChange = (type: 'activeContractor' | 'usedVehicles') => (newPage: number) => {
        setPageState((prevState) => ({
            ...prevState,
            [type]: { ...prevState[type], page: newPage + 1 },
        }));
    };

    const handlePageSizeChange = (type: 'activeContractor' | 'usedVehicles') => (newPageSize: number) => {
        setPageState((prevState) => ({
            ...prevState,
            [type]: { ...prevState[type], pageSize: newPageSize },
        }));
    };
    return (
        <CePaper sx={{ height: `${height - 148 - 48}px`, p: 2, my: 2 }}>
            <Grid container spacing={4}>
                <Grid item xs={12} sm={6}>
                    <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ fontSize: '16px', fontWeight: 700, mb: 1 }}
                    >
                    {t("most-active-contractors")}
                    </Typography>
                    <Box sx={{ height: `${height - 200 - 48}px` }}>
                    <ActiveContractorsDatagrid
                     data={mostActiveContractors?.mostActiveContractors || []}
                     isFetchingActiveContractors={isLoadingActiveContractors}
                     refetchActiveContractors={refetchActiveContractors}
                     shouldRenderRefreshButton
                     page={pageState.activeContractor.page}
                     pageSize={pageState.activeContractor.pageSize}
                     total={mostActiveContractors?.total || 0}
                     onPageChange={handlePageChange('activeContractor')}
                     onPageSizeChange={handlePageSizeChange('activeContractor')}
                     />
                     </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                    <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ fontSize: '16px', fontWeight: 700, mb: 1 }}
                    >
                    {t("frequently-used-vehicles")}
                    </Typography>
                    <Box sx={{ height: `${height - 200 - 48}px` }}>
                    <UsedVehiclesDatagrid
                     data={usedVehicles?.frequentlyUsedVehicles || []}
                     isFetchingUsedVehicles={isLoadingUsedVehicles}
                     refetchUsedVehicles={refetchUsedVehicles}
                     shouldRenderRefreshButton
                     page={pageState.usedVehicles.page}
                     pageSize={pageState.usedVehicles.pageSize}
                     total={usedVehicles?.total || 0}
                     onPageChange={handlePageChange('usedVehicles')}
                     onPageSizeChange={handlePageSizeChange('usedVehicles')}
                     />
                     </Box>
                </Grid>
            </Grid>
        </CePaper>
    )
}
export default DashboardTables;