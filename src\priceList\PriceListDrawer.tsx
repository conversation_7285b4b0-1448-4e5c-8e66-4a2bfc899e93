import { Drawer } from "@mui/material";
import { AxiosError } from "axios";
import React from "react";
import { UseMutateFunction } from "react-query";
import {
  CreatePriceListDto,
  PriceList,
  PriceListFormValues,
  PricelistTypeEnum,
  UpdatePriceListDto,
} from "src/common/types/priceList";
import { PriceListForm } from "./PriceListForm";
import { useTranslation } from "react-i18next";
interface PriceListDrawerProps {
  isLoading: boolean;
  initialFormValues: PriceListFormValues;
  handleClosePriceListDrawer: () => void;
  handleCreateNewPriceList?: UseMutateFunction<
    PriceList,
    AxiosError<CreatePriceListDto, CreatePriceListDto> | Error,
    CreatePriceListDto,
    () => void
  >;
  handleUpdatePriceList?: UseMutateFunction<
    PriceList,
    AxiosError<UpdatePriceListDto, UpdatePriceListDto> | Error,
    UpdatePriceListDto,
    () => void
  >;
  vehiclesWithExistingPricelists: number[];
  pricelists: PriceList[];
  inheritFromExistingPricelist: (pricelist: PriceList) => void;
}
const PriceListDrawer = ({
  isLoading,
  initialFormValues,
  handleClosePriceListDrawer,
  handleCreateNewPriceList,
  handleUpdatePriceList,
  vehiclesWithExistingPricelists,
  pricelists,
  inheritFromExistingPricelist,
}: PriceListDrawerProps) => {
  const { t } = useTranslation("common");

  const renderTitle = () => {
    switch (initialFormValues.flow) {
      case PricelistTypeEnum.CREATE_VARIANT:
        return t("add-vehicle-to-pricelist");
      case PricelistTypeEnum.UDPATE_VARIANT:
        return t("update-pricelist-for-vehicle", {
          vehicleName: initialFormValues.title,
        });
      default:
        return "";
    }
  };

  return (
    <Drawer
      anchor="right"
      open={!!initialFormValues.flow}
      onClose={handleClosePriceListDrawer}
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 2,
        display: "flex",
        flexDirection: "column",
        gap: 2,
        "& .MuiBackdrop-root": {
          backgroundColor: "transparent",
        },
      }}
    >
      <PriceListForm
        title={renderTitle()}
        initialFormValues={initialFormValues}
        isLoading={isLoading}
        handleCreateNewPriceList={handleCreateNewPriceList}
        handleUpdatePriceList={handleUpdatePriceList}
        handleClose={handleClosePriceListDrawer}
        vehiclesWithExistingPricelists={vehiclesWithExistingPricelists}
        pricelists={pricelists}
        inheritFromExistingPricelist={inheritFromExistingPricelist}
      />
    </Drawer>
  );
};

export default PriceListDrawer;
