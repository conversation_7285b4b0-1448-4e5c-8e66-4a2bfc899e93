import { Avatar, AvatarProps, styled } from "@mui/material";

type AvatarSize = "large" | "medium" | "small";

interface CustomAvatarProps extends AvatarProps {
  size: AvatarSize;
}

const CustomAvatar = styled(Avatar)<CustomAvatarProps>(({ size }) => {
  const fontSize = size === "large" || "medium" ? "16px" : "10px";
  const dimension = size === "large" ? 40 : size === "medium" ? 32 : 24;

  return {
    fontSize,
    width: dimension,
    height: dimension
  };
});

export const CeAvatar: React.FC<CustomAvatarProps> = ({
  size,
  sx,
  ...props
}) => {
  return <CustomAvatar size={size} sx={sx} {...props} />;
};
