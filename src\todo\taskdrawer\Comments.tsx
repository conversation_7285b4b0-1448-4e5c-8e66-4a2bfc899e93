import {
  Box,
  InputAdornment,
  Stack,
  Typography,
  useTheme,
} from "@mui/material";
import { useState } from "react";
import { getCurrentUser, useUser } from "src/common/api";
import { useTaskComments } from "src/common/api/tasks";
import { CeAvatar, CeButton, CeTextField } from "src/common/components";
import { formatDateToDayMonthYear } from "src/common/components/custom/CalendarEventWrapper";
import { TaskComment } from "src/common/types/tasks";
import { stringAvatar } from "src/common/utils/avatar";

interface CommentsProps {
  onSubmitComment: (comment: TaskComment) => void;
  taskId: number | null;
}

const SingleComment = ({ comment }: { comment: TaskComment }) => {
  const {
    data: author,
    isSuccess: isAuthorSuccess,
    error: isAuthorError,
  } = useUser(comment.created_by!);
  const fullName = `${author?.firstName} ${author?.lastName}`;

  const dataFormated = comment?.created_at
    ? formatDateToDayMonthYear(comment?.created_at)
    : "Just now";

  return (
    <Box display="flex" alignItems="flex-start" sx={{ width: "100%", mb: 2 }}>
      <Box>
        <CeAvatar
          size="large"
          {...stringAvatar(fullName)}
          sx={{
            mr: 2,
            backGroundColor: "grey.50",
          }}
        />
      </Box>

      <Box>
        <Stack direction="row" gap={1} alignItems="center">
          <Typography
            variant="body1"
            fontWeight={700}
            fontSize={16}
            letterSpacing={0.15}
          >
            {fullName}
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            fontWeight={400}
            fontSize={14}
            letterSpacing={0.17}
          >
            {dataFormated}
          </Typography>
        </Stack>
        <Typography variant="body2" color="text.primary">
          {comment.content}
        </Typography>
      </Box>
    </Box>
  );
};

const Comments = ({ taskId, onSubmitComment }: CommentsProps) => {
  const theme = useTheme();
  const currentUser = getCurrentUser();

  const { data: allComments } = useTaskComments(taskId!, Boolean(taskId));
  const [comment, setComment] = useState<TaskComment>({
    content: "",
    created_by: currentUser?.id,
  });
  const coments = allComments || [];

  return (
    <Box sx={{ p: 0, display: "flex", flexDirection: "column", gap: 2 }}>
      <Box
        display="flex"
        alignItems="flex-start"
        sx={{
          width: "100%",
          height: "100%",
        }}
      >
        <Box>
          <CeAvatar
            size="large"
            {...stringAvatar(
              `${currentUser?.firstName} ${currentUser?.lastName}`
            )}
            sx={{
              mr: 2,
              backGroundColor: theme.palette.grey[300],
            }}
          />
        </Box>
        <CeTextField
          rows={6}
          placeholder="Add a comment"
          fullWidth
          multiline
          value={comment.content}
          onChange={(e) => setComment({ ...comment, content: e.target.value })}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <CeButton
                  variant="outlined"
                  sx={{
                    position: "absolute",
                    bottom: 8,
                    right: 16,
                    padding: "4px 10px",
                    fontSize: 13,
                    fontWeight: 700,
                    letterSpacing: 0.48,
                    textTransform: "none",
                  }}
                  onClick={() => {
                    onSubmitComment(comment);
                    setComment({ content: "" });
                  }}
                >
                  Comment
                </CeButton>
              </InputAdornment>
            ),
          }}
          sx={{
            "& .MuiInputBase-root": {
              px: 2,
              py: 1,
              backgroundColor: theme.palette.background.default,
            },
          }}
        />
      </Box>
      <Box>
        {coments?.map((comment, index) => {
          return <SingleComment comment={comment} key={index} />;
        })}
      </Box>
    </Box>
  );
};

export default Comments;
