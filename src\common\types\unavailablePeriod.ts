import { GridSortModel } from "@mui/x-data-grid";
import { Expression } from "./filters";
import { CommonEntity, PossibleSortDir } from "./common";
import { Contract } from "./contract";

interface Vehicle {
  id: number;
  managerId: number;
  operatorId: number;
  uniqueIdentificationNumber: string;
}
export interface UnavailablePeriod extends CommonEntity {
  id: number;
  operatorManagerId: number | null;
  operatorId: number | null;
  from: string;
  to: string;
  vehicles: Vehicle[] | null;
}

export interface UnavailablePeriodsWithCount {
  totalCount: number;
  data: UnavailablePeriod[];
}

export interface UnavailablePeriodPlanning {
  contract: Contract;
  id: number;
  from: string;
  to: string;
  operatorId: string;
}

export interface getUnavailablePeriodDto {
  sortModel?: GridSortModel;
  expressions?: Expression[];
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  relations?: string[];
  searchText?: string;
}

export interface CreateUnavailablePeriodDto {
  from: string;
  to: string;
  operatorId: number;
}

export interface CreateUnavailabilityVehicleDto {
  from: string;
  to: string;
  vehicleIds: number[];
}

export interface DeleteUnavailablePeriodDto {
  id: number;
}
export interface UnavailablePeriodFormValues {
  id: number | null;
  from: string;
  to: string;
  operatorId?: number | null;
  vehicleIds?: number[] | null;
  flow: UnavailablePeriodFlow;
}
export interface DeleteUnavailablePeriodValues {
  id?: number;
  timeOffTitle?: string;
  flow: UnavailablePeriodDeleteFlow;
}
export interface UpdateUnavailabilityVehicleDto
  extends CreateUnavailabilityVehicleDto {
  id: number | null;
}
export interface UpdateUnavailablePeriodDto extends CreateUnavailablePeriodDto {
  id: number | null;
}

export type UnavailablePeriodFlow =
  | "Create"
  | "Update"
  | "CreateVehicle"
  | "UpdateVehicle"
  | null;
export type UnavailablePeriodStatus = "current" | "upcoming" | "used";
export type UnavailablePeriodDeleteFlow = "Delete" | null;
