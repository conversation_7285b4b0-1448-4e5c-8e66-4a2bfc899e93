import {
  Box,
  Chip,
  CircularProgress,
  Dialog,
  DialogContent,
  Divider,
  Grid,
  IconButton,
  Stack,
  Typography,
  useTheme,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { FC, useMemo } from "react";
import {
  ArrowRight01Icon,
  Call02Icon,
  Cancel01Icon,
  Clock01Icon,
  Location01Icon,
  UnavailableIcon,
  UserIcon,
  Edit02Icon,
} from "@hugeicons/react";
import {
  formatDateWithoutTimezoneForDisplaying,
  getTimeScheduled,
} from "src/common/utils/formatDate";
import { calculateElapsedTime, getJobLocationDto, JobStatus, Reservation } from "src/common/types";
import { CeButton } from "src/common/components";
import { JOB_STEPS } from "src/common/constants";
import { getCurrentUser } from "src/common/api";
import GoogleMapComponent from "src/common/components/reservationDetails/GoogleMapComponent";
import { useLocation } from "src/common/api/job";

type EventContentModalProps = {
  open: boolean;
  reservation: Reservation | undefined;
  onClose: () => void;
  jobCancelled: () => boolean;
  handleOpenReservationModalCancel?: () => void;
  onEditReservationClick?: () => void;
  onNavigateToReservationDetailsClick?: () => void;
};

const EventContentModal: FC<EventContentModalProps> = ({
  open,
  onClose,
  jobCancelled,
  handleOpenReservationModalCancel,
  reservation,
  onEditReservationClick,
  onNavigateToReservationDetailsClick,
}) => {
  const theme = useTheme();
  const currentUser = getCurrentUser();
  const { t } = useTranslation(["common", "dispatcher"]);
  const jobStatus = reservation?.job?.status || null;
  const jobIsCancelled = jobCancelled();
  const currentStep = JOB_STEPS.find((step) => step.name === jobStatus);
  const progress = currentStep?.progress || 0;
  const stepLabel = currentStep ? t(currentStep.label) : "";

  const canEditReservation =
    reservation?.job?.status !== JobStatus.NOT_STARTED &&
    reservation?.created_by === currentUser?.id;

    const jobLocationParams: getJobLocationDto = {
        limit: 50,
        offset: 0,
        expressions: [
          {
            category: `"job_location"."jobId"`,
            operator: "=",
            value: reservation?.job?.id
          }
        ],
        sortModel: [
          {
            field: "job_location.id",
            sort: "asc",
          }
        ]
      };
    
      const { data: jobLocations } = useLocation(
        jobLocationParams, 
        Boolean(reservation?.job?.id)
      );

    const elapsedTime = useMemo(() => {
      return calculateElapsedTime(reservation?.job?.jobEvents!, t);
    }, [reservation?.job?.jobEvents, t]);
    
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="xs"
      fullWidth
      PaperProps={{
        sx: { borderRadius: "12px" },
      }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        p={2}
      >
        <Typography variant="h6" fontWeight="bold">
          {t("job-details")}
        </Typography>
        <Box display="flex" alignItems="center">
          {jobStatus !== "COMPLETE" && (
            <Box>
              {jobIsCancelled ? (
                <Chip
                  label="Cancelled"
                  sx={{
                    backgroundColor: theme.palette.error.light,
                    color: "white",
                  }}
                />
              ) : (
                <CeButton
                  size="small"
                  variant="outlined"
                  color="error"
                  onClick={handleOpenReservationModalCancel}
                  sx={{ borderColor: "red" }}
                  startIcon={
                    <UnavailableIcon
                      size={20}
                      color={"currentColor"}
                      variant={"stroke"}
                    />
                  }
                >
                  {t("cancel")}
                </CeButton>
              )}
            </Box>
          )}
          <IconButton
            onClick={onEditReservationClick}
            disabled={canEditReservation}
          >
            <Edit02Icon size={20} color={"currentColor"} variant={"stroke"} />
          </IconButton>
          <IconButton onClick={onClose}>
            <Cancel01Icon size={20} color={"currentColor"} variant={"stroke"} />
          </IconButton>
        </Box>
      </Box>

      <Divider sx={{ marginBottom: 2 }} />
      <Stack
        spacing={2}
        display="flex"
        alignItems="flex-start"
        marginLeft={2}
        marginRight={2}
        gap={1}
      >
        <CeButton
          variant="text"
          onClick={onNavigateToReservationDetailsClick}
          size="small"
          sx={{
            width: "100%",
            textTransform: "none",
            backgroundColor: theme.palette.common.white,
            color: theme.palette.text.primary,
            border: `1px solid ${theme.palette.grey[300]}`,
            borderRadius: "12px",
            padding: "4px 8px",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.05)",
            "&:hover": {
              backgroundColor: theme.palette.grey[100],
            },
          }}
        >
          <Typography variant="body1">
            {t("go-to-reservation-details")}
          </Typography>
          <ArrowRight01Icon
            size={24}
            color={"currentColor"}
            variant={"stroke"}
          />
        </CeButton>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Box
            sx={{
              position: "relative",
              display: "inline-flex",
            }}
          >
            <CircularProgress
              variant="determinate"
              value={progress}
              size={32}
              thickness={4}
              sx={{ color: theme.palette.primary.main }}
            />
            <Box
              sx={{
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                position: "absolute",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Typography
                variant="caption"
                component="div"
                color="text.primary"
                fontWeight="bold"
                fontSize="10px"
              >
                {`${Math.round(progress)}%`}
              </Typography>
            </Box>
          </Box>
          <Box>
            <Typography variant="body1">{stepLabel}</Typography>
          </Box>
        </Stack>
      </Stack>
      <DialogContent>
        <Stack spacing={2}>
          {/* Client Information */}
          <Stack spacing={1}>
            <Box display="flex" alignItems="center" gap={1}>
              <Typography variant="h6" fontWeight="bold">
                {reservation?.clientDetails?.companyName || ""} (
                {reservation?.job?.amountOfConcrete} m³)
              </Typography>
              <Chip
                size="small"
                variant="outlined"
                color="default"
                icon={
                  <Clock01Icon
                    size={20}
                    color={"currentColor"}
                    variant={"stroke"}
                  />
                }
                label={getTimeScheduled(
                  reservation?.dateFrom,
                  reservation?.dateTo
                )}
              />
            </Box>
            <Stack spacing={1} direction={"row"} alignItems={"center"}>
              <UserIcon size={16} color={"currentColor"} variant={"stroke"} />
              <Typography color={theme.palette.text.secondary}>
                {reservation?.clientDetails?.name || ""}
              </Typography>
            </Stack>
            <Stack spacing={1} direction={"row"} alignItems={"center"}>
              <Location01Icon
                size={16}
                color={"currentColor"}
                variant={"stroke"}
              />
              <Typography color={theme.palette.text.secondary}>
                {reservation?.siteAddress || ""}, {reservation?.city || ""}
              </Typography>
            </Stack>
            <Stack
              spacing={1}
              direction={"row"}
              alignItems={"center"}
              sx={{ color: theme.palette.primary.main }}
            >
              <Call02Icon size={16} color={"currentColor"} variant={"stroke"} />
              <Typography>
                {t("common:operator")}:{" "}
                {reservation?.operator?.phoneNumber || ""}
              </Typography>
            </Stack>
            <Stack
              spacing={1}
              direction={"row"}
              alignItems={"center"}
              sx={{ color: theme.palette.primary.main }}
            >
              <Call02Icon size={16} color={"currentColor"} variant={"stroke"} />
              <Typography>
                {t("common:client")}:{" "}
                {reservation?.clientDetails?.phoneNumber || ""}
              </Typography>
            </Stack>
          </Stack>

          <Divider />
          <Box>
          <GoogleMapComponent reservationLocation={reservation?.location!} events={jobLocations?.data!}/>                
          </Box>
          <Divider />
          {/* Job Details Section */}
          <Box>
            <Typography
              variant="h6"
              sx={{ fontWeight: "bold", marginBottom: 2 }}
            >
              {t("common:job")}
            </Typography>
            <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
              <Grid container alignItems="center">
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.disabled">
                    {t("common:start-date")}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    {reservation?.job?.start ? (
                      <Chip
                        label={formatDateWithoutTimezoneForDisplaying(
                          reservation.job.start
                        )}
                        color="primary"
                        size="small"
                        sx={{ fontSize: "12px", fontWeight: "bold" }}
                      />
                    ) : (
                      "-"
                    )}
                  </Box>
                </Grid>
              </Grid>
              <Grid container alignItems="center">
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.disabled">
                    {t("common:end-date")}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    {reservation?.job?.end ? (
                      <Chip
                        label={formatDateWithoutTimezoneForDisplaying(
                          reservation.job.end
                        )}
                        color="primary"
                        size="small"
                        sx={{ fontSize: "12px", fontWeight: "bold" }}
                      />
                    ) : (
                      "-"
                    )}
                  </Box>
                </Grid>
              </Grid>
              <Grid container alignItems="center">
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.disabled">
                    {t("common:pumped-volume")}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Chip
                      label={
                        reservation?.job?.amountOfConcrete
                          ? reservation.job.amountOfConcrete + " m³"
                          : "-"
                      }
                      color="primary"
                      size="small"
                      sx={{ fontSize: "12px", fontWeight: "bold" }}
                    />
                    {reservation?.job?.report?.amountOfConcrete ? (
                      <Chip
                        label={reservation.job?.report.amountOfConcrete + " m³"}
                        color="success"
                        size="small"
                        sx={{ fontSize: "12px", fontWeight: "bold" }}
                      />
                    ) : null}
                  </Box>
                </Grid>
              </Grid>
              <Grid container alignItems="center">
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.disabled">
                    {t("common:flow-rate")}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Chip
                      label={
                        reservation?.job?.flowRate
                          ? reservation.job.flowRate + " m³/h"
                          : "-"
                      }
                      color="primary"
                      size="small"
                      sx={{ fontSize: "12px", fontWeight: "bold" }}
                    />
                  </Box>
                </Grid>
              </Grid>
              <Grid container alignItems="center">
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.disabled">
                    {t("flexible-pipe-80")}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Chip
                      label={
                        reservation?.job?.flexiblePipeLength80Mm
                          ? reservation.job.flexiblePipeLength80Mm + " m"
                          : "-"
                      }
                      color="primary"
                      size="small"
                      sx={{ fontSize: "12px", fontWeight: "bold" }}
                    />
                    {reservation?.job?.report?.flexiblePipeLength80Mm ? (
                      <Chip
                        label={
                          reservation.job.report.flexiblePipeLength80Mm + " m"
                        }
                        color="success"
                        size="small"
                        sx={{ fontSize: "12px", fontWeight: "bold" }}
                      />
                    ) : null}
                  </Box>
                </Grid>
              </Grid>
              <Grid container alignItems="center">
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.disabled">
                    {t("flexible-pipe-90")}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Chip
                      label={
                        reservation?.job?.flexiblePipeLength90Mm
                          ? reservation.job.flexiblePipeLength90Mm + " m"
                          : "-"
                      }
                      color="primary"
                      size="small"
                      sx={{ fontSize: "12px", fontWeight: "bold" }}
                    />
                    {reservation?.job?.report?.flexiblePipeLength90Mm ? (
                      <Chip
                        label={
                          reservation.job.report.flexiblePipeLength90Mm + " m"
                        }
                        color="success"
                        size="small"
                        sx={{ fontSize: "12px", fontWeight: "bold" }}
                      />
                    ) : null}
                  </Box>
                </Grid>
              </Grid>
              <Grid container alignItems="center">
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.disabled">
                    {t("rigid-pipe-100")}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Chip
                      label={
                        reservation?.job?.rigidPipeLength100Mm
                          ? reservation.job.rigidPipeLength100Mm + " m"
                          : "-"
                      }
                      color="primary"
                      size="small"
                      sx={{ fontSize: "12px", fontWeight: "bold" }}
                    />
                    {reservation?.job?.report?.rigidPipeLength100Mm ? (
                      <Chip
                        label={
                          reservation.job.report.rigidPipeLength100Mm + " m"
                        }
                        color="success"
                        size="small"
                        sx={{ fontSize: "12px", fontWeight: "bold" }}
                      />
                    ) : null}
                  </Box>
                </Grid>
              </Grid>
              <Grid container alignItems="center">
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.disabled">
                    {t("rigid-pipe-120")}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Chip
                      label={
                        reservation?.job?.rigidPipeLength120Mm
                          ? reservation.job.rigidPipeLength120Mm + " m"
                          : "-"
                      }
                      color="primary"
                      size="small"
                      sx={{ fontSize: "12px", fontWeight: "bold" }}
                    />
                    {reservation?.job?.report?.rigidPipeLength120Mm ? (
                      <Chip
                        label={
                          reservation.job.report.rigidPipeLength120Mm + " m"
                        }
                        color="success"
                        size="small"
                        sx={{ fontSize: "12px", fontWeight: "bold" }}
                      />
                    ) : null}
                  </Box>
                </Grid>
              </Grid>
              <Grid container alignItems="center">
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.disabled">
                    {t("common:order-number")}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Typography variant="body2">{reservation?.id}</Typography>
                  </Box>
                </Grid>
              </Grid>
              <Grid container alignItems="center">
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.disabled">
                    {t("common:cleanup-in-concrete-plant")}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  {reservation?.job?.cleaning ? (
                    <Chip
                      label={t("common:yes")}
                      color="success"
                      size="small"
                      sx={{ fontSize: "12px", fontWeight: "bold" }}
                    />
                  ) : (
                    <Chip
                      label={t("common:no")}
                      color="error"
                      size="small"
                      sx={{ fontSize: "12px", fontWeight: "bold" }}
                    />
                  )}
                </Grid>
              </Grid>
              <Grid container>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.disabled">
                    {t("delay")}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  {reservation?.job?.reasonForDelay ? (
                    <>
                      <Chip
                        label={elapsedTime}
                        color="error"
                        size="small"
                        sx={{ fontSize: "12px", fontWeight: "bold" }}
                      />
                      <Typography variant="body2">{reservation.job.reasonForDelay}</Typography>
                    </>
                  ) : (
                    <Chip
                      label={t("common:no")}
                      color="success"
                      size="small"
                      sx={{ fontSize: "12px", fontWeight: "bold" }}
                    />
                  )}
                </Grid>
              </Grid>
            </Box>
          </Box>

          <Divider />

          {/* Comment Section */}
          <Typography fontWeight="bold">{t("common:comment")}:</Typography>
          <Typography
            color={theme.palette.text.secondary}
            sx={{
              wordWrap: "break-word",
              whiteSpace: "pre-wrap",
            }}
          >
            {reservation?.job?.comments || "-"}
          </Typography>
        </Stack>
      </DialogContent>
    </Dialog>
  );
};

export default EventContentModal;
