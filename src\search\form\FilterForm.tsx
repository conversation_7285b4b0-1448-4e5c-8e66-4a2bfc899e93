import { DialogActions, Stack } from "@mui/material";
import { useFormik } from "formik";
import * as yup from "yup";
import {
  FilterVehiclesFormValues,
  ManagerFormValues,
  UpdateManagerDto
} from "src/common/types";

import { CeButton } from "src/common/components";

interface FilterFormProps {
  isLoading: boolean;
  handleClose: () => void;
  handleFilterVehicle?: (args: any) => void;
  initialFormValues: FilterVehiclesFormValues;
}

export const FilterForm: React.FC<FilterFormProps> = ({
  handleClose,
  isLoading,
  handleFilterVehicle,
  initialFormValues
}) => {
  const formik = useFormik<FilterVehiclesFormValues>({
    initialValues: initialFormValues,
    validationSchema: yup.object({
      // name: yup.string().required("First name is required"),
      // surname: yup.string().required("Last name is required"),
      // email: yup
      //   .string()
      //   .email("Enter a valid email")
      //   .required("Email is required"),
      // // TODO: password validation not working yet
      // password: yup.string().when([], {
      //   is: () => initialFormValues.flow === "Create",
      //   then: yup.string().required("Password is required"),
      //   otherwise: yup.string(),
      // }),
      // status: yup.object().required("Status is required").nullable(),
      // role: yup.object().required("Role is required").nullable(),
    }),
    onSubmit: (values) => {
      // if (initialFormValues.flow === "Create" && handleCreateNewManager) {
      //   const payload: CreateManagerDto =
      //     turnManagerFormValuesIntoCreateDto(values);
      //   handleCreateNewManager(payload);
      // }
      // if (initialFormValues.flow === "Update" && handleUpdateManager) {
      //   const payload: UpdateManagerDto =
      //     turnManagerFormValuesIntoUpdateDto(values);
      //   handleUpdateManager(payload);
      // }
    }
  });

  return (
    <Stack
      component="form"
      spacing={2}
      noValidate
      onSubmit={formik.handleSubmit}
      sx={{ width: "500px" }}
    >
      <DialogActions>
        <CeButton variant="text" onClick={handleClose} disabled={isLoading}>
          Cancel
        </CeButton>
        <CeButton type="submit" disabled={isLoading}>
          Submit
        </CeButton>
      </DialogActions>
    </Stack>
  );
};
