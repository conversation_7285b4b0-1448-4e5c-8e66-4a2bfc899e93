{"action": "Action", "add-articulation": "Add articulation", "add-new-operator": "Add new operator", "add-new-vehicle": "Add new vehicle", "add-reservation": "Add reservation", "add-vehicle": "Add Vehicle", "add-unavailable-period": "Add unavailable period", "available-flexible-pipe-length-80": "Available flexible pipe (80 mm)", "available-flexible-pipe-length-90": "Available flexible pipe (90 mm)", "available-flexible-pipe-length-100": "Available rigid pipe (100 mm)", "available-flexible-pipe-length-120": "Available rigid pipe (120 mm)", "linked-operator": "Linked operator", "available-rigid-pipe-length": "Available rigid pipe length", "bar": "bar", "boom-unfolding-system": "Boom unfolding system", "cannot-update-delete-operator": "You cannot update or delete this operator because he/she is already selected for a reservation.", "cannot-update-delete-vehicle": "You cannot update or delete this vehicle because it is already reserved.", "cement-bag-price": "Cement bag price", "clients": "Clients", "comment": "Comment", "company-address": "Company address", "confirm-delete-operator": "Are you sure you want to delete this operator?", "confirm-delete-unavailable-period": "Are you sure you want to delete this unavailable period?", "confirm-delete-vehicle": "Are you sure you want to delete this vehicle?", "confirm-action-needed": "Confirmation needed", "company-name-exceeds": "The company name length must not exceed 100 characters in length", "create-client": "Create Client", "delete-client": "Delete client", "create-vehicle-type": "Create Vehicle Type", "update-vehicle-type": "Update Vehicle Type", "delete-client-confirm": "Are you sure you want to delete this customer?", "delete-unavailable-period": "Delete unavailable period", "diesel": "Diesel", "edit": "Edit", "electric": "Electric", "email-sent-to-the-operator": "An invitation has been sent to the operator at the following address: ", "endHoseLength": "End hose length", "error-notification": "This service is currently experiencing some issues, an error notification has been sent. Please try again later.", "exceeds-available-stock": "Exceeds available stock", "below-span-limit": "Span too small", "exceeds-weight-limit": "Exceeds weight limit", "exceeds-height-limit": "Exceeds height limit", "flat-weekdays-fee": "Flat weekdays fee", "flat-weekend-fee": "Flat weekend fee", "flexible-pipe-length-80": "Flexible pipe (80 mm)", "flexible-pipe-length-90": "Flexible pipe (90 mm)", "gasoline": "Petrol/Gasoline", "hybrid": "Hybrid", "invalid-step": "Invalid Step", "linked-vehicle": "Linked vehicle", "max-concrete-pressure": "Max. concrete pressure", "modifications-saved": "Modifications saved", "modify-operator-details": "Modify operator details", "must-be-greater-than-or-equal-to-20": "Vehicle type must be greater than or equal to 20", "must-be-less-than-or-equal-to-80": "Vehicle type must be less than or equal to 80", "no-linked-operator": "No linked operator", "no-linked-vehicle": "No linked vehicle", "no-vehicle-registered": "You currently have no vehicles registered", "operator-details": "Operator details", "operator-successfully-deleted": "Operator successfully deleted", "out-coverage-100Km-additional-fee": "Out coverage 100 km additional fee", "out-coverage-50Km-additional-fee": "Out coverage 50 km additional fee", "out-of-range": "out of range", "overwrite-job-hours": "Based on the entered Flow rate, the job duration will be adjusted to a duration of {{duration}} and is estimated to finish by {{time}}. Do you want to continue with this adjustment?", "please-contact-client": "Please ensure you have contacted the client before deleting this reservation.", "price-per-m³-pumped": "Price per m³ pumped", "price-per-meter-of-flexible-pipe-placed": "Price per meter of flexible pipe placed", "price-per-meter-of-rigid-pipe-placed": "Price per meter of rigid pipe placed", "profile-operator-registered": "Operator successfully added", "red-diesel": "Diesel (red diesel)", "contracts": "Contracts", "register": "Register", "register-a-vehicle": "Register a vehicle", "register-vehicle-step-1": "Vehicle details (1)", "register-vehicle-step-2": "Vehicle details (2)", "register-vehicle-step-3": "Job details", "register-vehicle-step-4": "Confirmation", "register-your-first-vehicle": "Register your first vehicle here", "reservation-confirmation-email": "Confirmation emails have been sent to the client and your inbox.", "return-to-planning": "Return to planning", "reset": "Reset", "save": "Save", "steps-completed": "All steps completed - you're finished", "site-address": "Site address", "status": "Job Status", "success-registered": "Your profile has been successfully created. You can register your vehicles here", "search-clients": "Search Clients", "title": "Title", "type": "Type", "vehicle-boom-size": "Boom size", "type-of-motorisation": "Type of motorisation", "update-client": "Update client", "unavailable-period-successfully-deleted": "Unavailable period succesfully deleted", "unique-identification-number": "Unique ID", "vehicle": "Vehicle", "vehicle-registered": "Vehicle registered", "vehicle-succesfully-registered": "Vehicle succesfully registered", "vehicle-successfully-deleted": "Vehicle successfully deleted", "vehicle-details-one": "Vehicle details (1)", "vehicle-details-two": "Vehicle details (2)", "vehicle-model-details-one": "Vehicle Model details (1)", "vehicle-model-details-two": "Vehicle Model details (2)", "view-operators": "View all the operators", "view-vehicles": "View all the vehicles", "vat-number-exceeds": "The VAT number length must not exceed 100 characters in length", "width": "<PERSON><PERSON><PERSON>"}