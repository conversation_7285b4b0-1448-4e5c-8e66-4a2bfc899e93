import React, { useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import { Box } from "@mui/material";
import { useFile } from "src/common/api";

interface PreviewImageProps {
  file: File | null;
  fallbackPath: string | null;
  onRemove: () => void;
}

const PreviewImage: React.FC<PreviewImageProps> = ({
  file,
  fallbackPath,
  onRemove
}) => {

  const { data: fallbackFilePath } = useFile(
    fallbackPath,
    Boolean(fallbackPath)
  );

  const imageUrl = useObjectURL(file) || fallbackFilePath || "";

  if (!imageUrl) {
    return null;
  }

  return (
    <Box
      sx={{
        position: "relative",
        width: "75px",
        height: "75px",
        display: "flex",
        marginTop: "10px"
      }}
    >
      <img
        src={imageUrl}
        alt=""
        style={{
          width: "100%",
          height: "auto",
          borderRadius: "10px"
        }}
      />
      <Box
        onClick={onRemove}
        sx={{
          position: "absolute",
          top: "-4px",
          right: "-8px",
          width: "16px",
          height: "16px",
          backgroundColor: (theme) => theme.palette.error.dark,
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          borderRadius: "50%",
          cursor: "pointer",
          color: (theme) => theme.palette.error.contrastText
        }}
      >
        <CloseIcon style={{ fontSize: 16 }} />
      </Box>
    </Box>
  );
};

export default PreviewImage;

function useObjectURL(file: File | null): string | null {
  const [url, setUrl] = useState<string | null>(null);

  useEffect(() => {
    if (file) {
      const objectUrl = URL.createObjectURL(file);
      setUrl(objectUrl);

      return () => {
        URL.revokeObjectURL(objectUrl);
      };
    } else {
      setUrl(null);
    }
  }, [file]);

  return url;
}
