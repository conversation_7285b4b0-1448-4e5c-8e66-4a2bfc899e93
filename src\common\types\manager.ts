import { CommonEntity } from "./common";
import { Company } from "./company";
import { Role, Status, UserEnumDisplay } from "./user";

export interface Manager extends CommonEntity {
  id: number;
  firstName: string | null;
  lastName: string | null;
  companyName: string | null;
  companyAddress: string | null;
  email: string | null;
  phoneNumber: string | null;
  vatNumber: string | null;
  companyType: CompanyType | null;
  status: Status | null;
  role: Role | null;
  country: string | null;
  company: Company;
}

export interface ManagerFormValues {
  managerId?: number;
  firstName: string;
  lastName: string;
  companyName: string;
  companyAddress: string;
  email: string;
  phoneNumber: string;
  vatNumber: string;
  companyType: CompanyType | null;
  status: UserEnumDisplay | null;
  role: UserEnumDisplay | null;
  country: string | null;
  flow: ManagerModalFlow;
}

export interface CreateManagerDto {
  firstName: string;
  lastName: string;
  companyName: string;
  companyAddress: string;
  email: string;
  phoneNumber: string;
  companyType?: string;
  vatNumber: string;
  status: number;
  role: number;
  country: string;
}

export interface UpdateManagerDto extends Omit<CreateManagerDto, "password"> {
  managerId: number;
}

export interface DeleteManagerDto {
  managerId: number;
}

export interface DeleteManagerModalValues {
  managerId?: number;
  managerTitle?: string;
  flow: ManagerModalDeleteFlow;
}

export enum CompanyType {
  SA = "SA",
  SRL = "SRL",
  SC = "SC",
  SNC = "SNC",
  SComm = "SComm",
  Unknown = "Unknown",
}

export type ManagerModalFlow = "Create" | "Update" | null;
export type ManagerModalDeleteFlow = "Delete" | null;
