import { MainModal } from "src/common/components";
import { Box, Tab, Typography } from "@mui/material";
import { Tab<PERSON>ontex<PERSON>, TabList, TabPanel } from "@mui/lab";
import { useState } from "react";
import VehicleInfoTab from "./VehicleInfoTab";
import VehicleImageStack from "./VehicleImageStack";
import InfoIcon from "@mui/icons-material/Info";
import PriceList from "./PriceList";
import { getCurrentUser, useVehicle } from "src/common/api";
import { vehicleInformationFlowState } from "src/common/state";
import { useRecoilState } from "recoil";
import { VEHICLE_INFORMATION_VALUES_DEFAULT } from "src/common/constants";
import { Role } from "src/common/types";

interface VehicleInformationModalProps {
  vehicleId: number | null;
}
export const VehicleInformationModal: React.FC<
  VehicleInformationModalProps
> = ({ vehicleId }) => {
  const { data: vehicle, isLoading: isVehicleLoading } = useVehicle(
    vehicleId,
    Boolean(vehicleId)
  );
  const [tabValue, setTabValue] = useState("infoTab");
  const [vehicleInformationValues, setVehicleInformationValues] =
    useRecoilState(vehicleInformationFlowState);
  const currentUser = getCurrentUser();
  const handleChangeTab = (event: React.SyntheticEvent, newValue: string) => {
    setTabValue(newValue);
  };
  const handleCloseVehicleInformationModal = () => {
    if (!isVehicleLoading) {
      setVehicleInformationValues(VEHICLE_INFORMATION_VALUES_DEFAULT);
    }
  };

  return (
    <MainModal
      title={vehicleInformationValues.flow || ""}
      isOpen={Boolean(vehicleInformationValues.flow)}
      handleClose={handleCloseVehicleInformationModal}
      maxWidth={"xl"}
    >
      <Box>
        <VehicleImageStack vehicle={vehicle} />
        <Box>
          <TabContext value={tabValue}>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <TabList
                onChange={handleChangeTab}
                aria-label="vehicle info modal tab"
                sx={{ marginInline: "auto" }}
              >
                <Tab
                  sx={{ textTransform: "none" }}
                  label="Vehicle information"
                  value="infoTab"
                />
                {currentUser?.role === Role.DISPATCHER_MANAGER && (
                  <Tab
                    sx={{ textTransform: "none" }}
                    label="Price list"
                    value="priceTab"
                  />
                )}
              </TabList>
            </Box>
            <TabPanel value="infoTab">
              {vehicle && <VehicleInfoTab vehicle={vehicle} />}
            </TabPanel>
            <TabPanel value="priceTab">
              {vehicle?.assignedPricelist ? (
                <PriceList priceList={vehicle.assignedPricelist} />
              ) : (
                <Box textAlign={"center"} mt={2}>
                  <InfoIcon fontSize="large" color="warning" />
                  <Typography variant="h5" color={"text.secondary"}>
                    No Pricelist Configured!
                  </Typography>
                </Box>
              )}
            </TabPanel>
          </TabContext>
        </Box>
      </Box>
    </MainModal>
  );
};
