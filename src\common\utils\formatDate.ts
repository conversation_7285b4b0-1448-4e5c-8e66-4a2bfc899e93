import { zonedTimeToUtc } from "date-fns-tz";

import {
  addDays,
  differenceInYears,
  endOfDay,
  isAfter,
  isBefore,
  format,
  parseISO
} from "date-fns";
import { DateRange } from "@mui/lab";
import { processApiWarning } from "src/common/utils/errors";
import { enGB, nlBE, deAT, frCA } from "date-fns/locale";

// format date without timezone into correct date object
export const formatDateWithoutTimezone = (
  dateValue?: Date | string | null
): "" | Date => {
  if (!dateValue) {
    return "";
  }
  try {
    const localTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const utcDate = zonedTimeToUtc(dateValue, localTimezone);
    const endOfDayUtcDate = endOfDay(utcDate);
    return endOfDayUtcDate;
  } catch (e: any) {
    console.log("ERROR - unable to format date");
    console.error(e);
    console.error(e.message);
    return "";
  }
};
export const turnDateIntoStrWithoutTimezone = (
  date?: Date | "" | null
): string => {
  if (!date) {
    return "";
  }
  try {
    const userTimezoneOffset = date.getTimezoneOffset() * 60000;
    const dateObj = new Date(date.getTime() - userTimezoneOffset);
    return dateObj.toISOString().split("T")[0];
  } catch (e: any) {
    console.log("ERROR - unable to format date");
    console.log('input value: "${date}"');
    console.error(e);
    console.error(e.message);
    return "";
  }
};
export const formatDateWithoutTimezoneForDisplaying = (
  dateValue?: Date | string | null
): string => {
  const dateObj = formatDateWithoutTimezone(dateValue);
  if (dateObj) {
    return format(new Date(dateObj), "M/d/yyyy");
  }
  return "";
};
export const formatDateAndTimeForDisplaying = (
  dateAndTimeValue?: string | null
) => {
  if (!dateAndTimeValue) {
    return "";
  }
  try {
    const formattedDate = format(
      new Date(dateAndTimeValue),
      "M/d/yyyy - hh:mm a"
    );
    return formattedDate;
  } catch (e: any) {
    console.log("ERROR - unable to format date and time");
    console.log('input value: "${dateAndTimeValue}"');
    console.error(e);
    console.error(e.message);
    return dateAndTimeValue;
  }
};
export const formatDateTimeAndDayOfWeekForDisplaying = (
  dateAndTimeValue?: string | null
) => {
  if (!dateAndTimeValue) {
    return "";
  }
  try {
    const formattedDate = format(new Date(dateAndTimeValue), "PPPP - hh:mm a");
    return formattedDate;
  } catch (e: any) {
    console.log("ERROR - unable to format date and time");
    console.log('input value: "${dateAndTimeValue}"');
    console.error(e);
    console.error(e.message);
    return dateAndTimeValue;
  }
};
export function getAge(dateStr: string) {
  if (!dateStr) {
    return "";
  }
  const localTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const utcDate = zonedTimeToUtc(dateStr, localTimezone);
  const age = differenceInYears(new Date(), utcDate);
  return age;
}
export const getTimeOfDay = (dateStr: string) => {
  const date = new Date(dateStr);
  const hrs = date.getHours();
  if (hrs < 12) {
    return "Morning";
  } else if (hrs >= 12 && hrs < 17) {
    return "Afternoon";
  } else if (hrs >= 17 && hrs <= 24) {
    return "Evening";
  }
  return "Unknown";
};
export const formatDateRangeForCharts = (newValues: DateRange<Date>) => {
  const startsOn = formatDateWithoutTimezone(newValues[0]) || null;
  const endsOnRaw = newValues[1] ? addDays(newValues[1], 1) : null;
  const endsOn = formatDateWithoutTimezone(endsOnRaw) || null;
  return [startsOn, endsOn];
};
// example: 15 to 00:00:15
export const formatCallDuration = (seconds: number) => {
  return new Date(Number(seconds) * 1000).toISOString().slice(11, 19);
};
export const getTimeBetween = (date?: string | null) => {
  if (!date) {
    return "";
  }
  const createdDate = new Date(date);
  const currentDate = new Date();
  const hourInMls = 3600000;
  const minuteInMls = 60000;
  const secondsInMls = 1000;
  const hours = Math.round(
    Math.abs(currentDate.getTime() - createdDate.getTime()) / hourInMls
  );
  const minutes = Math.round(
    Math.abs(currentDate.getTime() - createdDate.getTime()) / minuteInMls
  );
  const seconds = Math.round(
    Math.abs(currentDate.getTime() - createdDate.getTime()) / secondsInMls
  );
  if (hours < 1) {
    if (minutes < 1) {
      return `${seconds} ${seconds === 1 ? "second" : "seconds"}`;
    } else {
      return `${minutes} ${minutes === 1 ? "minute" : "minutes"}`;
    }
  } else {
    if (hours > 48) {
      return formatDateAndTimeForDisplaying(date);
    }
    return `${hours} ${hours === 1 ? "hour" : "hours"}`;
  }
};

export const formatToSeconds = (time?: number) => {
  if (!time) {
    return "";
  }

  const minutes = Math.floor(time / 60);

  const seconds = time % 60;

  function padTo2Digits(num: number) {
    return num.toString().padStart(2, "0");
  }

  return `${padTo2Digits(minutes)}:${padTo2Digits(seconds)}`;
};

export const checkIfDateIsInThePast = (
  targetDate: string | Date,
  propertyName?: string | Date
) => {
  const currentDate = new Date();
  const isTargetDateAfterCurrentDate = isAfter(
    new Date(targetDate),
    currentDate
  );
  if (isTargetDateAfterCurrentDate) {
    processApiWarning(`Last ${propertyName} date needs to be in the past`);
  } else {
    return true;
  }
};

export const checkIfDateIsInTheFuture = (
  targetDate: Date | "",
  propertyName?: string
) => {
  const currentDate = new Date();
  const isTargetDateBeforeCurrentDate = isBefore(
    new Date(targetDate),
    currentDate
  );
  if (isTargetDateBeforeCurrentDate) {
    processApiWarning(`Next ${propertyName} date needs to be in the future`);
  } else {
    return true;
  }
};
export const convertDecimalHoursToTime = (decimalHours: number | undefined) => {
  if (!decimalHours) return;
  const hours = Math.floor(decimalHours);
  const minutesFraction = (decimalHours - hours) * 60;
  const minutes = Math.floor(minutesFraction);

  return `${hours}:${minutes < 10 ? "0" + minutes : minutes}`;
};

const getLanguageLocale = (selectedLanguage: string) => {
  if (!selectedLanguage) {
    return enGB;
  }

  switch (selectedLanguage) {
    case "en":
      return enGB;
    case "de":
      return deAT;
    case "nl":
      return nlBE;
    case "fr":
      return frCA;

    default:
      return enGB;
  }
};
export const getFormattedDatewithLocale = (
  selectedLanguage: string | null,
  date: string | null,
  formatType: string | null
) => {
  if (date && formatType && selectedLanguage) {
    const formattedDateWithLocale = format(parseISO(date), formatType, {
      locale: getLanguageLocale(selectedLanguage)
    });
    return formattedDateWithLocale;
  }
};

export const formatCalendarDate = (
  slotDate: Date,
  selectedLanguage: string
) => {
  const startDate = slotDate;

  const day = new Intl.DateTimeFormat(selectedLanguage || "en-EN", {
    timeZone: "UTC",
    day: "2-digit"
  }).format(startDate);

  const weekday = new Intl.DateTimeFormat(selectedLanguage || "en-EN", {
    timeZone: "UTC",
    weekday: "short"
  }).format(startDate);

  const month = new Intl.DateTimeFormat(selectedLanguage || "en-EN", {
    timeZone: "UTC",
    month: "2-digit"
  }).format(startDate);

  const formattedDate = `${weekday}, ${day}/${month}`;
  return formattedDate;
};

export const getTimeScheduled = (
  dateFrom: string | null | undefined,
  dateTo: string | null | undefined
) => {
  if (dateFrom && dateTo) {
    return `${format(new Date(dateFrom), "HH:mm")} - ${format(
      new Date(dateTo),
      "HH:mm"
    )}`;
  }
  return "";
};

export const parseTime = (time: string): number => {
  const [hours, minutes] = time.split(":").map(Number);
  return hours * 60 + minutes;
};
