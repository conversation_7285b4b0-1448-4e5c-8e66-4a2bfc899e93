import { FC, useCallback, useEffect } from "react";
import {
  GridColDef,
  GridFilterModel,
  GridRenderCellParams,
  GridRowParams,
  GridSortModel,
} from "@mui/x-data-grid";

import { Link as MuiLink, Stack, IconButton, Chip } from "@mui/material";

import { useRecoilState } from "recoil";
import { useTranslation } from "react-i18next";

import { DataGrid } from "@mui/x-data-grid";
import {
  OPERATOR_DELETE_DEFAULT,
  OPERATOR_FORM_VALUES_DEFAULT,
  userRolesDisplay,
} from "src/common/constants";
import {
  Operator,
  OperatorFormValues,
  Status,
  User,
  userStatuses,
} from "src/common/types";
import {
  useCreateNewOperator,
  useDeleteOperator,
  useUpdateOperator,
} from "src/common/api";
import {
  operatorDeleteValuesState,
  operatorFormValuesState,
} from "src/common/state";
import { OperatorModal } from "../OperatorModal";
import { OperatorModalDelete } from "../OperatorModalDelete";
import { turnOperatorIntoFormValues } from "src/common/utils";
import { Delete01Icon, Edit02Icon } from "@hugeicons/react";
import { CeDataGridToolbar } from "src/common/components/custom/company/CeDataGridToolbar";
import { GridColumnTypography } from "src/common/components/custom/company/GridColumnTypography";
import {
  GridStateSnapshot,
  UpdateGridStatePart,
} from "src/common/utils/gridState";
import OperatorDetailsModal from "../OperatorDetailsModal";

interface OperatorsDatagridProps {
  data: User[];
  isFetchingOperators: boolean;
  refetchOperators: () => void;
  shouldRenderRefreshButton: boolean;
  shouldRenderAddButton: boolean;
  shouldRenderEditActionsColumn: boolean;

  total: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
  gridState: GridStateSnapshot;
  updateGridStatePart: UpdateGridStatePart;
  handleSortModelChange?: (sortModel: GridSortModel) => void;
  onDatagridFiltersChange?: (datagridFilter: GridFilterModel) => void;
  isServerDriven: boolean;
}

export const OperatorsDatagrid: FC<OperatorsDatagridProps> = ({
  data,
  shouldRenderRefreshButton,
  shouldRenderAddButton,
  shouldRenderEditActionsColumn,
  isFetchingOperators,
  refetchOperators,
  total,
  onPageChange,
  onPageSizeChange,
  gridState,
  updateGridStatePart,
  handleSortModelChange,
  onDatagridFiltersChange,
  isServerDriven,
}) => {
  const { t } = useTranslation(["common", "manager", "dispatcher"]);

  const {
    mutate: handleUpdateOperator,
    isSuccess: isUpdateOperatorSuccess,
    isLoading: isUpdatingOperator,
    mutateAsync: handleUpdateOperatorAsync,
  } = useUpdateOperator();
  const {
    mutate: handleCreateNewOperator,
    isSuccess: isCreateOperatorSuccess,
    isLoading: isCreatingOperator,
  } = useCreateNewOperator();
  const {
    mutate: handleDeleteOperator,
    isLoading: isDeletingOperator,
    isSuccess: isDeleteOperatorSuccess,
  } = useDeleteOperator();

  const [operatorFormValues, setOperatorFormValues] = useRecoilState(
    operatorFormValuesState
  );

  const [operatorDeleteValues, setOperatorDeleteValues] = useRecoilState(
    operatorDeleteValuesState
  );

  const isLoading =
    isCreatingOperator ||
    isUpdatingOperator ||
    isDeletingOperator ||
    isFetchingOperators;

  const handleCloseOperatorModal = () => {
    if (!isLoading) {
      setOperatorFormValues(OPERATOR_FORM_VALUES_DEFAULT);
    }
  };
  
  const handleCloseOperatorDetailsModal = () => {
    if (!isLoading) {
      setOperatorFormValues(OPERATOR_FORM_VALUES_DEFAULT);
    }
  }

  const handleCloseOperatorModalDelete = () => {
    if (!isLoading) {
      setOperatorDeleteValues(OPERATOR_DELETE_DEFAULT);
    }
  };

  useEffect(() => {
    if (isCreateOperatorSuccess) {
      setOperatorFormValues(OPERATOR_FORM_VALUES_DEFAULT);
    }
  }, [isCreateOperatorSuccess, setOperatorFormValues]);

  useEffect(() => {
    if (isUpdateOperatorSuccess) {
      setOperatorFormValues(OPERATOR_FORM_VALUES_DEFAULT);
    }
  }, [isUpdateOperatorSuccess, setOperatorFormValues]);

  useEffect(() => {
    if (isDeleteOperatorSuccess) {
      setOperatorDeleteValues(OPERATOR_DELETE_DEFAULT);
    }
  }, [isDeleteOperatorSuccess, setOperatorDeleteValues]);

  const columns: GridColDef[] = [
    {
      field: "editActions",
      headerName: t("common:edit"),
      sortable: false,
      renderCell: (params: GridRenderCellParams<string>) =>
        renderEditActions(params),
      filterable: false,
    },
    {
      field: "user.id",
      headerName: "Id",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 50,
      renderCell: (params) => <GridColumnTypography value={params.row.id} />,
    },
    {
      field: "user.firstName",
      headerName: t("first-name"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.firstName} />
      ),
    },
    {
      field: "user.lastName",
      headerName: t("last-name"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.lastName} />
      ),
    },
    {
      field: "user.status",
      headerName: t("common:status"),
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => {
        const status: number = params.row.status - 1;

        const statusText = userStatuses[status] || "";
        const statusColor =
          params.row.status === Status.ACTIVE ? "success" : "default";

        return (
          <Chip
            sx={{ m: 0.5, ml: 0, fontSize: "13px" }}
            color={statusColor}
            component="span"
            size="small"
            label={statusText}
          />
        );
      },
    },
    {
      field: "user.role",
      headerName: t("role"),
      headerAlign: "left",
      align: "left",
      type: "number",
      width: 125,
      renderCell: (params) => {
        const role: number = params.row.role;

        const roleText =
          userRolesDisplay.find((r) => r.id === role)?.title || "";

        return (
          <Chip
            sx={{ m: 0.5, ml: 0, fontSize: "13px" }}
            color={"error"}
            component="span"
            size="small"
            label={roleText}
          />
        );
      },
    },
    {
      field: "country",
      headerName: t("country"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.country} />
      ),
    },
    {
      field: "user.email",
      headerName: t("email"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 250,
      renderCell: (params) => <GridColumnTypography value={params.row.email} />,
    },
    {
      field: "phoneNumber",
      headerName: t("phone-number"),
      headerAlign: "left",
      type: "string",
      align: "left",
      width: 150,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.phoneNumber} />
      ),
    },
    {
      field: "company.name",
      headerName: t("common:company-name"),
      headerAlign: "left",
      type: "string",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.company.name} />
      ),
    },
    {
      field: "user.created_by",
      headerName: "Created By",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.created_by} />
      ),
    },
    {
      field: "created_at",
      headerName: "Created At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.created_at} />
      ),
    },
    {
      field: "updated_at",
      headerName: "Updated At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.updated_at} />
      ),
    },
    {
      field: "deleted_at",
      headerName: "Deleted At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.deleted_at} />
      ),
    },
  ];

  const renderEditActions = (params: GridRenderCellParams) => {
    const operator: Operator = params.row;

    return (
      <Stack
        direction="row"
        justifyContent="center"
        alignItems="center"
        spacing={1}
      >
        <IconButton
          aria-label="update operator"
          disabled={isLoading}
          size="small"
          onClick={(event) => {
             event.stopPropagation();
            const formValues: OperatorFormValues = turnOperatorIntoFormValues(
              operator,
              "Update"
            );

            setOperatorFormValues(formValues);
          }}
        >
          <Edit02Icon size={16} variant={"stroke"} />
        </IconButton>

        <IconButton
          aria-label="delete operator"
          disabled={isLoading}
          color="error"
          size="small"
          onClick={(event) => {
            event.stopPropagation();
            setOperatorDeleteValues({
              operatorId: operator.id,
              operatorTitle: `${operator.name || ""} ${" "} ${
                operator.surname || ""
              }`,
              flow: "Delete",
            })
          }
          }
        >
          <Delete01Icon size={16} variant={"stroke"} />
        </IconButton>
      </Stack>
    );
  };

  const onFilterChange = useCallback(
    (filterModel: GridFilterModel) => {
      if (onDatagridFiltersChange) {
        onDatagridFiltersChange(filterModel);
        updateGridStatePart("filterModel", filterModel);
      }
    },
    [onDatagridFiltersChange]
  );

  const onSortChange = useCallback(
    (sortModel: GridSortModel) => {
      if (handleSortModelChange) {
        handleSortModelChange(sortModel);
        updateGridStatePart("sortModel", sortModel);
      }
    },
    [handleSortModelChange]
  );
  const handleRowClick  = (params: GridRowParams) =>{
    const operatorDetails = params.row;
    const formValues: OperatorFormValues = turnOperatorIntoFormValues(
      operatorDetails,
      "Details"
    );
    setOperatorFormValues(formValues);
    
  }
  
  return (
    <>
      <DataGrid
        onRowClick={handleRowClick}
        sx={{
          border: "none",
          paddingTop: 0.5,
        }}
        pagination
        rowsPerPageOptions={[20, 40, 60, 80, 100]}
        rowCount={total}
        page={gridState.page - 1}
        pageSize={gridState.pageSize}
        columnVisibilityModel={gridState.columnVisibilityModel}
        onColumnVisibilityModelChange={(model) =>
          updateGridStatePart("columnVisibilityModel", model)
        }
        filterModel={gridState.filterModel}
        sortModel={gridState.sortModel}
        paginationMode="server"
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        sortingMode={isServerDriven ? "server" : "client"}
        onSortModelChange={isServerDriven ? onSortChange : undefined}
        filterMode={isServerDriven ? "server" : "client"}
        onFilterModelChange={isServerDriven ? onFilterChange : undefined}
        components={{
          Toolbar: () => (
            <CeDataGridToolbar
              shouldRenderAddButton={shouldRenderAddButton}
              shouldRenderRefreshButton={shouldRenderRefreshButton}
              onRefreshButtonClick={refetchOperators}
              addButtonClickHandler={() => {
                setOperatorFormValues({
                  ...operatorFormValues,
                  flow: "Create",
                });
              }}
              addButtonDescription={t("add-operator")}
            />
          ),
        }}
        columns={columns}
        rows={data}
        disableSelectionOnClick
      />
      <OperatorDetailsModal
       initialFormValues={operatorFormValues}
       isLoading={isLoading}
       handleCloseOperatorDetailsModal={handleCloseOperatorDetailsModal}
       />
      <OperatorModal
        initialFormValues={operatorFormValues}
        isLoading={isLoading}
        handleCreateNewOperator={handleCreateNewOperator}
        handleUpdateOperator={handleUpdateOperator}
        handleCloseOperatorModal={handleCloseOperatorModal}
      />

      <OperatorModalDelete
        flow={operatorDeleteValues.flow}
        isLoading={isLoading}
        operatorTitle={operatorDeleteValues.operatorTitle}
        operatorId={operatorDeleteValues.operatorId}
        handleCloseOperatorModalDelete={handleCloseOperatorModalDelete}
        handleDeleteOperator={handleDeleteOperator}
      />
    </>
  );
};
