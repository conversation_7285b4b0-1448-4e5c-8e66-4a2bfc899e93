import { userRolesDisplay, userStatusDisplay } from "../constants";
import {
  CreateOperatorDto,
  Operator,
  OperatorFormValues,
  OperatorModalFlow,
  UpdateOperatorDto,
} from "../types";

export const turnOperatorFormValuesIntoCreateDto = (
  values: OperatorFormValues
): CreateOperatorDto => {
  const payload = {
    firstName: values.firstName,
    country: values.country,
    lastName: values.lastName,
    email: values.email,
    phoneNumber: values.phoneNumber,
    status: values.status?.id!,
    role: values.role?.id!,
  };

  return payload;
};

export const turnOperatorFormValuesIntoUpdateDto = (
  values: OperatorFormValues
): UpdateOperatorDto => {
  const payload: UpdateOperatorDto = {
    operatorId: values.operatorId!,
    firstName: values.firstName,
    country: values.country,
    lastName: values.lastName,
    email: values.email,
    phoneNumber: values.phoneNumber,
    status: values.status?.id!,
    role: values.role?.id!,
  };

  return payload;
};

export const turnOperatorIntoFormValues = (
  operator: Operator,
  flow: OperatorModalFlow
): OperatorFormValues => {
  const status =
    userStatusDisplay.find((status) => status.id === operator.status) || null;
  const role =
    userRolesDisplay.find((role) => role.id === operator.role) || null;

  const payload = {
    operatorId: operator.id,
    firstName: operator.firstName || "",
    lastName: operator.lastName || "",
    country: operator.country || "",
    email: operator.email || "",
    phoneNumber: operator.phoneNumber || "",
    status: status,
    role: role,
    flow,
  };

  return payload;
};

export const getTruncatedName = (name: string, surname?: string) => {
  const maxLength = 6;
  const truncatedName =
    name.length > maxLength ? `${name.substring(0, maxLength)}...` : name;

  if (!surname) {
    return truncatedName;
  }
  const truncatedSurname =
    surname.length > maxLength
      ? `${surname.substring(0, maxLength)}...`
      : surname;
  return `${truncatedName} ${truncatedSurname}`;
};

export const getTruncatedCompany = (name: string) => {
  const maxLength = 6;

  const truncatedName =
    name.length > maxLength ? `${name.substring(0, maxLength)}...` : name;
  return `${truncatedName}`;
};
