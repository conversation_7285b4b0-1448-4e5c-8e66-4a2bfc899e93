@import url("https://fonts.googleapis.com/css2?family=Rethink+Sans:ital,wght@0,400..800;1,400..800&display=swap");

.recharts-wrapper svg {
  overflow: inherit !important;
}

.highlighted-row {
  background-color: yellow;
  /* Change this to whatever highlighting style you prefer */
}

.fc-bg-event {
  background-color: red;
  border-color: red;
}

.fc-event:not(.fc-bg-event) {
  background: none;
  border: none;
}

.fc-day-sun,
.fc-day-sat {
  background-color: #c4c4c438;
}

.fc-col-header-cell-cushion,
.fc-toolbar-title,
.fc-col-header-cell {
  color: black;
}

.fc-header-toolbar .fc-toolbar-chunk .fc-button-group {
  box-shadow: 0px 1px 3px 0px #0000001f;
  border-radius: 8px;
  overflow: hidden;
}

.fc-header-toolbar .fc-toolbar-chunk .fc-button-group .fc-button {
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
  text-transform: capitalize;
  border: none;
  overflow: hidden;
  z-index: 1;
}

.fc-header-toolbar .fc-toolbar-chunk .fc-button-group .fc-button:active {
  z-index: 0;
}

.fc-header-toolbar
  .fc-toolbar-chunk
  .fc-button-group
  .fc-button:not(.fc-button-active):hover,
.dark-mode
  .fc-header-toolbar
  .fc-toolbar-chunk
  .fc-button-group
  .fc-button:not(.fc-button-active):hover {
  color: #1565c0 !important;
}

.fc-header-toolbar
  .fc-toolbar-chunk
  .fc-button-group
  .fc-button.fc-button-primary {
  background-color: white;
  color: #000;
  z-index: 1;
}
.fc-header-toolbar
  .fc-toolbar-chunk
  .fc-button-group
  .fc-button.fc-button-active,
.dark-mode
  .fc-header-toolbar
  .fc-toolbar-chunk
  .fc-button-group
  .fc-button.fc-button-active {
  background-color: #1565c0 !important;
  color: #fff !important;
  border: none;
  z-index: 0;
}

.fc-header-toolbar
  .fc-toolbar-chunk
  .fc-button-group
  .fc-button.fc-button-primary:hover {
  color: #1565c0;
}
.fc-header-toolbar
  .fc-toolbar-chunk
  .fc-button-group
  .fc-button.fc-button-primary:focus {
  z-index: 0;
}

.fc-list-table .fc-list-event .fc-list-event-time {
  padding-top: 11px;
}
.fc-list-table .fc-list-event .fc-list-event-graphic {
  position: relative;
}
.fc-list-table .fc-list-event .fc-list-event-graphic span {
  margin: auto;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  padding: 3px;
  background-color: #3788d8;
  border-radius: 50%;
}
.dark-mode .fc-toolbar-title,
.dark-mode .fc-datagrid-cell-main,
.dark-mode .fc-timeline-slot-cushion,
.dark-mode .fc-col-header-cell,
.dark-mode .fc-list-table .fc-list-event .fc-list-event-time,
.dark-mode
  .fc-header-toolbar
  .fc-toolbar-chunk
  .fc-button-group
  .fc-button.fc-button-primary,
.dark-mode .fc-list-table tbody > tr > th {
  color: white !important;
}

.dark-mode .fc-datagrid-cell-main .MuiPaper-root,
.dark-mode
  .fc-header-toolbar
  .fc-toolbar-chunk
  .fc-button-group
  .fc-button.fc-button-primary,
.dark-mode .fc-list-table tbody > tr > th {
  background-color: #424242 !important;
  background-image: none !important;
  box-shadow: none !important;
}

.fc .fc-scrollgrid-sync-table tbody > tr > td {
  height: 82px;
}

.fc-scrollgrid,
.fc-listMonth-view.fc-view.fc-list {
  border-radius: 12px;
  overflow: hidden;
}
td:last-of-type {
  border-radius: 0 0 12px 0;
}
.fc-scrollgrid-section.fc-scrollgrid-section-header:nth-of-type(3) {
  border-radius: 12px;
}
.taskCalendarView {
  .fc .fc-scrollgrid-sync-table tbody > tr > td {
    height: 180px !important;
  }
  .fc-day-today {
    background-color: inherit !important;
  }
  .fc-day-sun,
  .fc-day-sat {
    background-color: #c4c4c438 !important;
    border-radius: 0 !important;
  }
  .fc .fc-daygrid-day-events {
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 2px;
  }
}

@media print {
  .no-print {
    display: none;
  }
}
.hidden {
  display: none !important;
}

.drag-indicator {
  visibility: hidden;
  transition: all 0.1s ease-in-out;
}
