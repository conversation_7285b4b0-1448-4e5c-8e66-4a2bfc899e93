import { FC, useCallback, useEffect } from "react";
import {
  GridColDef,
  GridFilterModel,
  GridRenderCellParams,
  GridSortModel,
} from "@mui/x-data-grid";

import { Link as MuiLink, Stack, IconButton, Chip } from "@mui/material";

import { useRecoilState } from "recoil";

import { DataGrid } from "@mui/x-data-grid";
import { useTranslation } from "react-i18next";

import {
  DISPATCHER_DELETE_DEFAULT,
  DISPATCHER_FORM_VALUES_DEFAULT,
} from "src/common/constants/dispatcher";
import { userRolesDisplay } from "src/common/constants";

import {
  Dispatcher,
  DispatcherFormValues,
  Status,
  User,
  userStatuses,
} from "src/common/types";

import {
  useCreateNewDispatcher,
  useDeleteDispatcher,
  useUpdateDispatcher,
} from "src/common/api";

import {
  dispatcherDeleteValuesState,
  dispatcherFormValuesState,
} from "src/common/state/dispatcher";
import { DispatcherModal } from "../DispatcherModal";
import { DispatcherModalDelete } from "../DispatcherModalDelete";
import { turnDispatcherIntoFormValues } from "src/common/utils";
import { Delete01Icon, Edit02Icon } from "@hugeicons/react";
import { CeDataGridToolbar } from "src/common/components/custom/company/CeDataGridToolbar";
import { GridColumnTypography } from "src/common/components/custom/company/GridColumnTypography";
import {
  GridStateSnapshot,
  UpdateGridStatePart,
} from "src/common/utils/gridState";

interface DispatchersDatagridProps {
  data: User[];
  isFetchingDispatchers: boolean;
  refetchDispatchers: () => void;
  shouldRenderRefreshButton: boolean;
  shouldRenderAddButton: boolean;
  shouldRenderEditActionsColumn: boolean;
  total: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
  gridState: GridStateSnapshot;
  updateGridStatePart: UpdateGridStatePart;
  handleSortModelChange?: (sortModel: GridSortModel) => void;
  onDatagridFiltersChange?: (datagridFilter: GridFilterModel) => void;
  isServerDriven: boolean;
}

export const DispatchersDatagrid: FC<DispatchersDatagridProps> = ({
  data,
  isFetchingDispatchers,
  refetchDispatchers,
  total,
  onPageChange,
  onPageSizeChange,
  shouldRenderRefreshButton,
  shouldRenderAddButton,
  gridState,
  updateGridStatePart,
  handleSortModelChange,
  onDatagridFiltersChange,
  isServerDriven,
}) => {
  const { t } = useTranslation(["common", "dispatcher"]);

  const {
    mutate: handleUpdateDispatcher,
    isSuccess: isUpdateDispatcherSuccess,
    isLoading: isUpdatingDispatcher,
    mutateAsync: handleUpdateDispatcherAsync,
  } = useUpdateDispatcher();
  const {
    mutate: handleCreateNewDispatcher,
    isSuccess: isCreateDispatcherSuccess,
    isLoading: isCreatingDispatcher,
  } = useCreateNewDispatcher();
  const {
    mutate: handleDeleteDispatcher,
    isLoading: isDeletingDispatcher,
    isSuccess: isDeleteDispatcherSuccess,
  } = useDeleteDispatcher();

  const [dispatcherFormValues, setDispatcherFormValues] = useRecoilState(
    dispatcherFormValuesState
  );

  const [dispatcherDeleteValues, setDispatcherDeleteValues] = useRecoilState(
    dispatcherDeleteValuesState
  );

  const isLoading =
    isCreatingDispatcher ||
    isUpdatingDispatcher ||
    isDeletingDispatcher ||
    isFetchingDispatchers;

  const handleCloseDispatcherModal = () => {
    if (!isLoading) {
      setDispatcherFormValues(DISPATCHER_FORM_VALUES_DEFAULT);
    }
  };

  const handleCloseDispatcherModalDelete = () => {
    if (!isLoading) {
      setDispatcherDeleteValues(DISPATCHER_DELETE_DEFAULT);
    }
  };

  useEffect(() => {
    if (isCreateDispatcherSuccess) {
      setDispatcherFormValues(DISPATCHER_FORM_VALUES_DEFAULT);
    }
  }, [isCreateDispatcherSuccess, setDispatcherFormValues]);

  useEffect(() => {
    if (isUpdateDispatcherSuccess) {
      setDispatcherFormValues(DISPATCHER_FORM_VALUES_DEFAULT);
    }
  }, [isUpdateDispatcherSuccess, setDispatcherFormValues]);

  useEffect(() => {
    if (isDeleteDispatcherSuccess) {
      setDispatcherDeleteValues(DISPATCHER_DELETE_DEFAULT);
    }
  }, [isDeleteDispatcherSuccess, setDispatcherDeleteValues]);

  const columns: GridColDef[] = [
    {
      field: "editActions",
      headerName: t("common:edit"),
      sortable: false,
      renderCell: (params: GridRenderCellParams<string>) =>
        renderEditActions(params),
      filterable: false,
    },
    {
      field: "user.id",
      headerName: "Id",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 50,
      renderCell: (params) => <GridColumnTypography value={params.row.id} />,
    },
    {
      field: "user.firstName",
      headerName: t("first-name"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.firstName} />
      ),
    },
    {
      field: "user.lastName",
      headerName: t("last-name"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.lastName} />
      ),
    },
    {
      field: "user.status",
      headerName: t("common:status"),
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => {
        const status: number = params.row.status - 1;

        const statusText = userStatuses[status] || "";
        const statusColor =
          params.row.status === Status.ACTIVE ? "success" : "default";
        return (
          <Chip
            sx={{ m: 0.5, ml: 0, fontSize: "13px" }}
            color={statusColor}
            component="span"
            size="small"
            label={statusText}
          />
        );
      },
    },
    {
      field: "user.role",
      headerName: t("role"),
      headerAlign: "left",
      align: "left",
      type: "number",
      width: 125,
      renderCell: (params) => {
        const role: number = params.row.role;

        const roleText =
          userRolesDisplay.find((r) => r.id === role)?.title || "";

        return (
          <Chip
            sx={{ m: 0.5, ml: 0, fontSize: "13px" }}
            color={"warning"}
            component="span"
            size="small"
            label={roleText}
          />
        );
      },
    },
    {
      field: "user.email",
      headerName: t("email"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 250,
      renderCell: (params) => <GridColumnTypography value={params.row.email} />,
    },
    {
      field: "phoneNumber",
      headerName: t("phone-number"),
      headerAlign: "left",
      type: "string",
      align: "left",
      width: 150,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.phoneNumber} />
      ),
    },
    {
      field: "company.name",
      headerName: t("common:company-name"),
      headerAlign: "left",
      type: "string",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.company.name} />
      ),
    },
    {
      field: "user.created_by",
      headerName: "Created By",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.created_by} />
      ),
    },
    {
      field: "created_at",
      headerName: "Created At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.created_at} />
      ),
    },
    {
      field: "updated_at",
      headerName: "Updated At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.updated_at} />
      ),
    },
    {
      field: "deleted_at",
      headerName: "Deleted At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.deleted_at} />
      ),
    },
  ];

  const renderEditActions = (params: GridRenderCellParams) => {
    const dispatcher: Dispatcher = params.row;

    return (
      <Stack
        direction="row"
        justifyContent="center"
        alignItems="center"
        spacing={1}
      >
        <IconButton
          aria-label="update dispatcher"
          disabled={isLoading}
          size="small"
          onClick={() => {
            const formValues: DispatcherFormValues =
              turnDispatcherIntoFormValues(dispatcher, "Update");
            setDispatcherFormValues(formValues);
          }}
        >
          <Edit02Icon size={16} variant={"stroke"} />
        </IconButton>

        <IconButton
          aria-label="delete dispatcher"
          disabled={isLoading}
          color="error"
          size="small"
          onClick={() =>
            setDispatcherDeleteValues({
              dispatcherId: dispatcher.id,
              dispatcherTitle: `${dispatcher.firstName || ""} ${" "} ${
                dispatcher.lastName || ""
              }`,
              flow: "Delete",
            })
          }
        >
          <Delete01Icon size={16} variant={"stroke"} />
        </IconButton>
      </Stack>
    );
  };

  const onFilterChange = useCallback(
    (filterModel: GridFilterModel) => {
      if (onDatagridFiltersChange) {
        onDatagridFiltersChange(filterModel);
        updateGridStatePart("filterModel", filterModel);
      }
    },
    [onDatagridFiltersChange]
  );

  const onSortChange = useCallback(
    (sortModel: GridSortModel) => {
      if (handleSortModelChange) {
        handleSortModelChange(sortModel);
        updateGridStatePart("sortModel", sortModel);
      }
    },
    [handleSortModelChange]
  );

  return (
    <>
      <DataGrid
        sx={{
          border: "none",
          paddingTop: 0.5,
        }}
        pagination
        page={gridState.page - 1}
        pageSize={gridState.pageSize}
        rowsPerPageOptions={[20, 40, 60, 80, 100]}
        rowCount={total}
        onPageChange={(newPage) => {
          onPageChange(newPage);
        }}
        onPageSizeChange={(newPageSize) => {
          onPageSizeChange(newPageSize);
        }}
        columnVisibilityModel={gridState.columnVisibilityModel}
        onColumnVisibilityModelChange={(model) =>
          updateGridStatePart("columnVisibilityModel", model)
        }
        filterModel={gridState.filterModel}
        sortModel={gridState.sortModel}
        sortingMode={isServerDriven ? "server" : "client"}
        onSortModelChange={isServerDriven ? onSortChange : undefined}
        filterMode={isServerDriven ? "server" : "client"}
        onFilterModelChange={isServerDriven ? onFilterChange : undefined}
        paginationMode="server"
        components={{
          Toolbar: () => (
            <CeDataGridToolbar
              shouldRenderAddButton={shouldRenderAddButton}
              shouldRenderRefreshButton={shouldRenderRefreshButton}
              onRefreshButtonClick={refetchDispatchers}
              addButtonClickHandler={() => {
                setDispatcherFormValues({
                  ...dispatcherFormValues,
                  flow: "Create",
                });
              }}
              addButtonDescription={t("common:add-dispatcher")}
            />
          ),
        }}
        columns={columns}
        rows={data}
        disableSelectionOnClick
      />

      <DispatcherModal
        initialFormValues={dispatcherFormValues}
        isLoading={isLoading}
        handleCreateNewDispatcher={handleCreateNewDispatcher}
        handleUpdateDispatcher={handleUpdateDispatcher}
        handleCloseDispatcherModal={handleCloseDispatcherModal}
      />

      <DispatcherModalDelete
        flow={dispatcherDeleteValues.flow}
        isLoading={isLoading}
        dispatcherTitle={dispatcherDeleteValues.dispatcherTitle}
        dispatcherId={dispatcherDeleteValues.dispatcherId}
        handleCloseDispatcherModalDelete={handleCloseDispatcherModalDelete}
        handleDeleteDispatcher={handleDeleteDispatcher}
      />
    </>
  );
};
