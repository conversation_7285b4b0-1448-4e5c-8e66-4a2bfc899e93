import { GridSortModel } from "@mui/x-data-grid";
import { Country } from "../utils/countries";
import { CommonEntity, PossibleSortDir } from "./common";
import { Company } from "./company";
import { CompanyType } from "./manager";
import { Task } from "./tasks";
import { Expression } from "./filters";

export interface User extends CommonEntity {
  id: number;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  phoneNumber: string | null;
  status: Status | null;
  role: Role | null;
  companyName?: string | null;
  companyAddress?: string | null;
  country?: string | null;
  vatNumber?: string | null;
  city?: string | null;
  zipCode: number | null;
  birthdate: string | null;
  address: string | null;
  secondaryAddress: string | null;
  phoneNumberPersonal: string | null;
  companyId: number;
  companyType?: CompanyType;
  company: Company;
  tasks?: Task[];
}

export interface UserWithToken extends User {
  token: string;
}

export interface UsersWithCount {
  totalCount: number;
  data: User[];
}

export interface CreateUserDto {
  firstName: string;
  lastName: string;
  phoneNumberPersonal: string;
  birthdate: string;
  email: string;
  phoneNumber: string;
  address: string;
  zipCode: string;
  city: string;
  secondaryAddress: string;
  country: string;
}

export interface UpdateUserDto extends CreateUserDto {
  userId: number;
}

export interface UserSettingsFormValues {
  userId?: number;
  firstName: string;
  lastName: string;
  birthdate: Date | null;
  address: string;
  secondaryAddress: string;
  zipCode: string;
  city: string;
  country: Country | null;
  phoneNumber: string;
  phoneNumberPersonal: string;
  email: string;
}

export interface GetUsersDto {
  sortModel: GridSortModel;
  expressions: Expression[];
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
  type?: string;
  role?: number;
  userId?: number;
}

export interface UserProfile {
  sub: number;
  email: string;
  roleId: number;
  role: Role;
}

export interface SignInUserWithEmailDto {
  email: string;
  password: string;
}

export interface ResetPasswordDto {
  token: string;
  newPassword: string;
  newPasswordConfirmation: string;
}

export interface ForgotPasswordDto {
  email: string;
}

export enum Status {
  PENDING = 1,
  ACTIVE = 2,
  INACTIVE = 3,
  UNAVAILABLE = 4,
}

export enum Role {
  MANAGER = 1,
  DISPATCHER_MANAGER = 2,
  OEPRATOR_MANAGER = 3,
  DISPATCHER = 4,
  OPERATOR = 5,
}

export interface UserEnumDisplay {
  id: number;
  title: string;
  visible: boolean;
}

export type UserRole =
  | "manager"
  | "dispatcher manager"
  | "operator manager"
  | "dispatcher"
  | "operator";

export type UserStatus = "pending" | "active" | "inactive" | "unavailable";

export const userTypeHierarchy: UserRole[] = [
  "manager",
  "dispatcher manager",
  "operator manager",
  "dispatcher",
  "operator",
];

export const userStatuses: UserStatus[] = ["pending", "active", "inactive", "unavailable"];
