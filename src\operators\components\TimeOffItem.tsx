import React, { ReactElement } from 'react'
import { Box, Chip, Paper, Typography, useTheme } from '@mui/material'
import { ChipProps } from '@mui/material/Chip'
import { useTranslation } from 'react-i18next';

interface TimeOffItemProps {
  dateRange:string;
  days: number;
  chipLabel: string;
  chipIcon?: ReactElement
  chipColor?: ChipProps['color'];
  chipSx?: ChipProps['sx']
}

const TimeOffItem: React.FC<TimeOffItemProps> = ({
  dateRange,
  days,
  chipLabel,
  chipIcon,
  chipColor = 'default',
  chipSx
}) => {
    const theme = useTheme();
    const { t } = useTranslation("common");
  return (
    <Paper
      sx={{
        padding: 2,
        marginBottom: 2,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: theme.palette.grey[50],
        borderRadius: theme.shape.borderRadius,
        boxShadow: 'none',
        width:'100%'
      }}
    >
      <Box>
        <Typography variant="subtitle1" fontWeight={700}>
          {dateRange}
        </Typography>
        <Typography variant="subtitle2" color="text.secondary">
          {days} {days > 1 ? t("days"): t("day")}
        </Typography>
      </Box>
      <Chip
        icon={chipIcon}
        label={chipLabel}
        color={chipColor}
        sx={chipSx}
        size="medium"
      />
    </Paper>
  )
}

export default TimeOffItem
