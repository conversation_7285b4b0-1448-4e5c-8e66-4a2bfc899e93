import { Typography } from '@mui/material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import React, { useCallback } from 'react'
import { useTranslation } from 'react-i18next';
import { CeDataGridToolbar } from 'src/common/components/custom/company/CeDataGridToolbar';
import { GridColumnTypography } from 'src/common/components/custom/company/GridColumnTypography';
import { UsedVehicle } from 'src/common/types/analytics';

interface UsedVehiclesDatagridProps {
    data: UsedVehicle[];
    isFetchingUsedVehicles: boolean;
    refetchUsedVehicles: () => void;
    shouldRenderRefreshButton: boolean;
    page: number;
    pageSize: number;
    total: number;
    onPageChange: (newPage: number) => void;
    onPageSizeChange: (newPageSize: number) => void;
  }

const UsedVehiclesDatagrid: React.FC<UsedVehiclesDatagridProps> = ({
    data,
    shouldRenderRefreshButton,
    isFetchingUsedVehicles,
    refetchUsedVehicles,
    page,
    pageSize,
    total,
    onPageChange,
    onPageSizeChange
}) => {
    const { t } = useTranslation(["dispatcher"]);
    const columns: GridColDef[] = [
        {
            field: "vehicleName",
            headerName: t("vehicle"),
            type: "string",
            headerAlign: "left",
            align: "left",
            width: 150,
            renderCell: (params) => <GridColumnTypography value={params.row.vehicleName}/>,
            editable: true
        },
        {
            field: "companyName",
            headerName: t("company"),
            type: "string",
            headerAlign: "right",
            align: "right",
            width: 150,
            renderCell: (params) => (
              <Typography sx={{ textAlign: 'right', fontSize:'14px' }}>
                {params.row.companyName}
              </Typography>
            ),
            editable: true
        },
        {
            field: "reservationCount",
            headerName: t("no-of-reservations"),
            type: "string",
            headerAlign: "right",
            align: "right",
            width: 150,
            renderCell: (params) => (
              <Typography sx={{ textAlign: 'right', fontSize:'14px' }}>
                {params.row.reservationCount || 0}
              </Typography>
            ),
            editable: true
        },
        {
            field: "totalSum",
            headerName: t("total-invoiced"),
            type: "string",
            headerAlign: "right",
            align: "right",
            width: 150,
            renderCell: (params) => (
              <Typography sx={{ textAlign: 'right', fontSize:'14px' }}>
                {params.row.totalSum ? `${params.row.totalSum} €` : "0 €"}
              </Typography>
            ),
            editable: true
        },
    ]
    const handleProcessRowUpdateError = useCallback((error: Error) => {
        console.error(error.message);
      }, []);
    

  
  return (
    <>
    <DataGrid
      sx={{
        border: "none",
        paddingTop: 0.5
      }}
      pagination
      page={page - 1}
      pageSize={pageSize}
      rowsPerPageOptions={[20, 40, 60, 80, 100]}
      rowCount={total}
      onPageChange={onPageChange}
      onPageSizeChange={onPageSizeChange}
      paginationMode="server"
      editMode="row"
      experimentalFeatures={{ newEditingApi: true }}
      onProcessRowUpdateError={handleProcessRowUpdateError}
      components={{
        Toolbar: () => (
          <CeDataGridToolbar
            shouldRenderRefreshButton={shouldRenderRefreshButton}
            onRefreshButtonClick={refetchUsedVehicles}
          />
        )
      }}
      columns={columns}
      rows={data}
      getRowId={(row) => row.vehicleId}
      disableSelectionOnClick
      initialState={{
        columns: {
          columnVisibilityModel: {
            id:false,
            vehicleId: false,
          }
        },
        sorting: {
          sortModel: [{ field: "name", sort: "asc" }]
        }
      }}
    />
  </>
  )
}
export default UsedVehiclesDatagrid;