import React, { useState } from "react";
import { DirectionsRenderer, DirectionsService, GoogleMap, LoadScript, Marker, TrafficLayer, useLoadScript } from "@react-google-maps/api";
import { apiWarningMessage } from "src/common/utils/reservationDetails";
import { JobLocationEvent, Location } from "src/common/types";
import { Typography, Box, CircularProgress } from "@mui/material";

const apiKey = process.env.REACT_APP_GM_API_KEY;
apiWarningMessage(apiKey);

const containerStyle = {
  width: "100%",
  height: "400px",
  borderRadius: "12px",
  borderColor: "2px solid white",
};

const mapStyles = [
  {
    featureType: "poi",
    stylers: [{ visibility: "off" }],
  },
  {
    featureType: "poi.business",
    stylers: [{ visibility: "off" }],
  },
  {
    featureType: "transit",
    stylers: [{ visibility: "off" }],
  },
];

interface GoogleMapComponentProps {
  reservationLocation: Location;
  events: JobLocationEvent[];
}

export const GoogleMapComponent: React.FC<GoogleMapComponentProps> = ({
  reservationLocation,
  events,
}) => {
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: apiKey!,
  });

  const [directions, setDirections] =
    useState<google.maps.DirectionsResult | null>(null);
  // const [distance, setDistance] = useState<string | null>(null);
  // const [duration, setDuration] = useState<string | null>(null);

  if (loadError)
    return (
      <Typography color="error" textAlign="center">
        Error loading maps
      </Typography>
    );

  if (!isLoaded)
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
      </Box>
    );

    if (
      !reservationLocation ||
      !Array.isArray(reservationLocation.coordinates) ||
      reservationLocation.coordinates.length !== 2
    ) {
      return (
        <Typography color="error" textAlign="center">
          Invalid location data
        </Typography>
      );
    }
    const [siteLat, siteLng] = reservationLocation.coordinates;
    const siteLocation = { lat: siteLat, lng: siteLng };

  const lastEvent = events && events.length ? events[events.length - 1] : null;
  let vehicleLocation: google.maps.LatLngLiteral | null = null;
  if (lastEvent && lastEvent.coords) {
    vehicleLocation = {
      lat: lastEvent.coords.latitude,
      lng: lastEvent.coords.longitude,
    };
  }

  const vehicleIcon = {
    url: "/images/mixer-truck.svg",
    scaledSize: new window.google.maps.Size(40, 40),
    anchor: new google.maps.Point(20, 40),
  };

  const siteIcon = {
      url: "/images/place.svg",
      scaledSize: new window.google.maps.Size(40, 40), 
      anchor: new window.google.maps.Point(20, 40), 
   };

  const directionsCallback = (
    response: google.maps.DirectionsResult | null,
    status: google.maps.DirectionsStatus
  ) => {
    if (status === google.maps.DirectionsStatus.OK && response) {
      setDirections(response);
      // const leg = response.routes?.[0]?.legs?.[0];
      // if (leg) {
      //   const distanceText = leg.distance?.text ?? "";
      //   const durationText = leg.duration?.text ?? "";
      //   setDistance(distanceText);
      //   setDuration(durationText);
      // } tregon distancen dhe kohen
    } else {
      console.error("Directions request failed due to " + status);
    }
  };

  return (
    <GoogleMap
      mapContainerStyle={containerStyle}
      center={siteLocation}
      zoom={15}
      options={{ styles: mapStyles }}
    >
      <TrafficLayer />
      <Marker position={siteLocation} icon={siteIcon} />

      {vehicleLocation && <Marker position={vehicleLocation} icon={vehicleIcon} />}

      {vehicleLocation && !directions && (
        <DirectionsService
          options={{
            origin: vehicleLocation,
            destination: siteLocation,
            travelMode: google.maps.TravelMode.DRIVING,
          }}
          callback={(response, status) => directionsCallback(response, status)}
        />
      )}
      {directions &&
        <DirectionsRenderer
          directions={directions}
          options={{
            suppressMarkers: true,
            polylineOptions: {
              strokeColor: "#4285F4",
              strokeWeight: 10,
            },
          }}
        />
      }
    </GoogleMap>
  );
};

export default GoogleMapComponent;