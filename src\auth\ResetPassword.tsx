import { Grid } from "@mui/material";
import { useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useResetPassword } from "src/common/api";
import { ResetPasswordForm } from "./ResetPasswordForm";
import { useRecoilState } from "recoil";
import { passwordResetTokenState } from "src/common/state";

export const ResetPassword: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  const [passwordResetToken, setPasswordResetToken] = useRecoilState(
    passwordResetTokenState
  );
  const {
    mutate: handleResetPassword,
    isLoading,
    isSuccess,
  } = useResetPassword();

  const resetPasswordToken = searchParams.get("token") || "";

  useEffect(() => {
    if (!isLoading && isSuccess) {
      const navigateTo = "/auth/signin";
      navigate(navigateTo, { replace: true });
    }
  }, [isLoading, isSuccess, navigate]);

  useEffect(() => {
    if (resetPasswordToken) {
      setPasswordResetToken(resetPasswordToken);
      setSearchParams({}, { replace: true });
    }
  }, [resetPasswordToken, setSearchParams, setPasswordResetToken]);

  const tokenString = passwordResetToken || "";

  return (
    <Grid
      container
      direction="column"
      justifyContent="center"
      alignItems="center"
    >
      <ResetPasswordForm
        handleResetPassword={handleResetPassword}
        isLoading={isLoading}
        passwordResetToken={tokenString}
      />
    </Grid>
  );
};
