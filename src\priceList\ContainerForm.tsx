import React from "react";
import {
  ContainerFormFlow,
  ContainerModalFormValues,
  ContainerPricelists,
  CreateContainerDto,
  UpdateContainerDto,
} from "src/common/types/priceList";
import { FormikErrors, FormikProvider, useFormik } from "formik";
import * as yup from "yup";
import {
  Autocomplete,
  Checkbox,
  FormControl,
  FormControlLabel,
  Stack,
} from "@mui/material";
import { CeButton, CeTextField } from "src/common/components";
import { useTranslation } from "react-i18next";
import { usePartners } from "src/common/api/partner";
import { Partner } from "src/common/types/partners";
interface ContainerFormProps {
  initialFormValues: ContainerModalFormValues;
  onUpdateContainer: (values: UpdateContainerDto) => void;
  onCreateContainer: (values: CreateContainerDto) => void;
  handleCloseModal: () => void;
  partners: Partner[];
  existingContainers: ContainerPricelists[];
}

const ContainerForm = ({
  initialFormValues,
  onUpdateContainer,
  onCreateContainer,
  handleCloseModal,
  partners,
  existingContainers,
}: ContainerFormProps) => {
  const { t } = useTranslation(["common"]);

  const formik = useFormik<ContainerModalFormValues>({
    initialValues: initialFormValues,
    validationSchema: yup.object({
      title: yup.string().required("Title is required"),
      copyFromContainer: yup.object().nullable(),
    }),
    onSubmit: (values) => {
      if (values.flow === ContainerFormFlow.CREATE) {
        onCreateContainer({
          title: values.title!,
          isPublic: values.isPublic,
          isDefault: values.isDefault,
          partnerIds: values.partnerIds,
          copyFromContainerId: values.copyFromContainer?.id,
        });
      }
      if (values.flow === ContainerFormFlow.UPDATE) {
        onUpdateContainer({
          title: values.title!,
          isPublic: values.isPublic,
          isDefault: values.isDefault,
          partnerIds: values.partnerIds,
          id: values.id!,
        });
      }
    },
  });

  const partnerIds: number[] =
    formik.values.partners.map((partner) => partner.id) || [];
  return (
    <FormikProvider value={formik}>
      <Stack noValidate component="form" onSubmit={formik.handleSubmit} gap={2}>
        <Stack gap={2}>
          <CeTextField
            fullWidth
            id="title"
            name="title"
            label={t("title")}
            size="small"
            value={formik.values.title}
            onChange={formik.handleChange}
            InputLabelProps={{ shrink: true }}
            error={formik.touched.title && Boolean(formik.errors.title)}
            helperText={formik.touched.title && formik.errors.title}
          />

          <Autocomplete
            multiple
            limitTags={2}
            id="partner"
            sx={{ width: 400, mb: 2 }}
            value={partners.filter((partner) =>
              partnerIds.includes(partner.id)
            )}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            onChange={(event, newValue) => {
              if (!newValue.length) {
                formik.setFieldValue("partnerIds", null);
              } else {
                formik.setFieldValue(
                  "partnerIds",
                  newValue.map((p) => p.id)
                );
              }
              formik.setFieldValue("partners", newValue);
            }}
            options={partners}
            getOptionLabel={(option) => option.dispatcherCompany.name || ""}
            filterSelectedOptions
            renderInput={(params) => (
              <CeTextField
                {...params}
                InputLabelProps={{ shrink: true }}
                label="Assign partner"
                size="small"
              />
            )}
          />
          {ContainerFormFlow.CREATE ? (
            <Autocomplete
              id="container"
              sx={{ width: 400, mb: 2 }}
              value={formik.values.copyFromContainer}
              isOptionEqualToValue={(option, value) => option.id === value.id}
              onChange={(event, newValue) => {
                formik.setFieldValue("copyFromContainer", newValue);
              }}
              options={existingContainers}
              getOptionLabel={(option) => option.title || ""}
              renderInput={(params) => (
                <CeTextField
                  {...params}
                  InputLabelProps={{ shrink: true }}
                  label={t("copy-from")}
                  size="small"
                />
              )}
            />
          ) : null}

          <FormControl fullWidth sx={{ ml: 0.8 }}>
            <FormControlLabel
              checked={formik.values.isPublic}
              control={<Checkbox size="small" />}
              label="Public"
              name="isPublic"
              onChange={() =>
                formik.setFieldValue("isPublic", !formik.values.isPublic)
              }
            />
          </FormControl>
        </Stack>
        <Stack
          justifyContent="flex-end"
          direction="row"
          gap={2}
          alignItems={"center"}
        >
          <CeButton
            onClick={handleCloseModal}
            color="primary"
            variant="text"
            size="medium"
          >
            Cancel
          </CeButton>
          <CeButton
            type="submit"
            color="primary"
            variant="contained"
            size="medium"
          >
            Submit
          </CeButton>
        </Stack>
      </Stack>
    </FormikProvider>
  );
};

export default ContainerForm;
