import { useMutation, useQuery, useQueryClient } from "react-query";
import axios, { AxiosError } from "axios";

import { processApiError, processApiSuccess } from "../utils/errors";
import {
  Company,
  CompanyWithCount,
  CreateCompanyDto,
  GetCompanyDto,
  UpdateCompanyDto,
} from "../types/company";
import { wrapEachFieldIntoDoubleQuotes } from "../utils";

const backendUrl = process.env.REACT_APP_API_URL;

// GET ONE Company
export const getCompany = async (companyId?: number) => {
  if (!companyId) {
    throw new Error("The company ID was not provided");
  }
  return axios
    .get(`${backendUrl}/companies/${companyId}`, { withCredentials: true })
    .then((response) => response.data);
};

export const useCompany = (companyId?: number, enabled: boolean = true) => {
  return useQuery<Company, AxiosError | Error>(
    ["company", companyId],
    () => getCompany(companyId),
    {
      onError: (err) => processApiError("Unable to fetch company", err),
      enabled,
    }
  );
};

// GET MANY Companies
export const getCompanies = async (params: GetCompanyDto) => {
  return axios
    .post(`${backendUrl}/companies/get`, params, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useCompanies = (
  params: GetCompanyDto,
  enabled: boolean = true
) => {
  const formattedSortModel = wrapEachFieldIntoDoubleQuotes(params.sortModel);
  const formattedAttrs = { ...params, sortModel: formattedSortModel };
  return useQuery<CompanyWithCount, AxiosError | Error>(
    ["companies", formattedAttrs],
    () => getCompanies(formattedAttrs),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch companies", err),
      enabled,
    }
  );
};

// CREATE NEW Company
export const createNewCompany = (attrs: CreateCompanyDto) => {
  return axios
    .post(`${backendUrl}/companies`, attrs, { withCredentials: true })
    .then((response) => response.data);
};

export const useCreateNewCompany = () => {
  const queryClient = useQueryClient();
  return useMutation<Company, AxiosError | Error, CreateCompanyDto, () => void>(
    (attrs: CreateCompanyDto) => createNewCompany(attrs),
    {
      onSuccess: (newCompany) => {
        queryClient.invalidateQueries("companies");
        processApiSuccess(`Company created successfully: ${newCompany.name}`);
      },
      onError: (err) => processApiError("Unable to create company", err),
    }
  );
};

// UPDATE Company BY ID
export const handleUpdateCompany = (updateCompanyArgs: UpdateCompanyDto) => {
  const { companyId, ...company } = updateCompanyArgs;
  if (!companyId) {
    throw new Error("The company ID was not provided");
  }
  return axios
    .patch(`${backendUrl}/companies/${companyId}`, company, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useUpdateCompany = () => {
  const queryClient = useQueryClient();
  return useMutation<Company, AxiosError | Error, UpdateCompanyDto>(
    (updateCompanyArgs: UpdateCompanyDto) =>
      handleUpdateCompany(updateCompanyArgs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("company");
        queryClient.invalidateQueries("companies");
        processApiSuccess("Company updated successfully");
      },
      onError: (err) => processApiError("Unable to update company", err),
    }
  );
};
