import { Grid, Box, Typography } from "@mui/material";
import { Vehicle } from "src/common/types";
import { useTranslation } from "react-i18next";

interface VehicleInfoProps {
  vehicle?: Vehicle;
}
const VehicleInfoTab = ({ vehicle }: VehicleInfoProps) => {
  const { t } = useTranslation(["manager", "common"]);

  return (
    <Grid container spacing={4}>
      <Grid item xs={6}>
        <Grid container spacing={1}>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>{t("type")}</Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.type || "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>{t("vehicle-boom-size")}</Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.boomSize || "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>{t("common:vehicle-brand")}</Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.vehicleBrand || "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>{t("common:brand-model")}</Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.brandModel || "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>{t("common:operator")}</Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.operator?.firstName && vehicle.operator.lastName
                  ? `${vehicle.operator.firstName} ${vehicle.operator.lastName}`
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>{t("type-of-motorisation")}</Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.typeOfMotorization || "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("common:license-plate-number")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.licensePlateNumber || "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>{t("common:weight")}</Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.weight ? vehicle.weight + "T" : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Box display="flex" alignItems="flex-end">
                <Typography fontWeight="bold" mr={1}>
                  {"(C)"}
                </Typography>
                <Typography fontSize={14}>{t("common:height")}</Typography>
              </Box>
              <Box>
                <Typography fontSize={14} fontWeight="bold">
                  {vehicle?.height ? vehicle.height + "m" : "-"}
                </Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Box display="flex" alignItems="flex-end">
                <Typography fontWeight="bold" mr={1}>
                  {"(B)"}
                </Typography>
                <Typography fontSize={14}>{t("common:length")}</Typography>
              </Box>
              <Box>
                <Typography fontSize={14} fontWeight="bold">
                  {vehicle?.length ? vehicle.length + "m" : "-"}
                </Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>{t("width")}</Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.width ? vehicle.width + "m" : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("common:vertical-reach")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.maxVerticalReach
                  ? vehicle.maxVerticalReach + "m"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("common:horizontal-reach")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.maxHorizontalReach
                  ? vehicle.maxHorizontalReach + "m"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>{t("endHoseLength")}</Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.endHoseLength ? vehicle.endHoseLength + "m" : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>{t("common:max-flow-rate")}</Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.maxFlowRate ? vehicle.maxFlowRate + "m" : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("max-concrete-pressure")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.maxConcretePressure
                  ? vehicle.maxConcretePressure + "bar"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>{t("site-address")}</Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.siteAddress || "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>{t("common:strangler")}</Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.hasStrangler ? t("common:yes") : t("common:no")}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("available-flexible-pipe-length-80")}
              </Typography>
              <Box>
                <Typography fontSize={14} fontWeight="bold">
                  {vehicle?.availableFlexiblePipeLength80Mm
                    ? vehicle.availableFlexiblePipeLength80Mm + "m"
                    : "-"}
                </Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("available-flexible-pipe-length-90")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.availableFlexiblePipeLength90Mm
                  ? vehicle.availableFlexiblePipeLength90Mm + "m"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("available-flexible-pipe-length-100")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.availableFlexiblePipeLength100Mm
                  ? vehicle.availableFlexiblePipeLength100Mm + "m"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Grid>

      <Grid item xs={6}>
        <Grid container spacing={1}>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("available-flexible-pipe-length-120")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.availableFlexiblePipeLength120Mm
                  ? vehicle.availableFlexiblePipeLength120Mm + "m"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("available-rigid-pipe-length")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.availableRigidPipeLength
                  ? vehicle.availableRigidPipeLength + "m"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("common:max-downward-reach")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.maxDownwardReach
                  ? vehicle.maxDownwardReach + "m"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("common:number-of-boom-sections")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.numberOfBoomSections
                  ? vehicle.numberOfBoomSections
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Box display="flex" alignItems="flex-end">
                <Typography fontWeight="bold" mr={1}>
                  {"(A)"}
                </Typography>
                <Typography fontSize={14}>
                  {t("common:min-unfolding-height")}
                </Typography>
              </Box>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.minUnfoldingHeight
                  ? vehicle.minUnfoldingHeight + "m"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>{t("common:boom-rotation")}</Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.boomRotation ? vehicle.boomRotation + "°" : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Box display="flex" alignItems="flex-end">
                <Typography fontWeight="bold" mr={1}>
                  {"(D)"}
                </Typography>
                <Typography fontSize={14}>
                  {t("common:front-outriggers-span")}
                </Typography>
              </Box>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.frontOutriggerSpan
                  ? vehicle.frontOutriggerSpan + "m"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Box display="flex" alignItems="flex-end">
                <Typography fontWeight="bold" mr={1}>
                  {"(E)"}
                </Typography>
                <Typography fontSize={14}>
                  {t("common:rear-outriggers-span")}
                </Typography>
              </Box>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.rearOutriggerSpan
                  ? vehicle.rearOutriggerSpan + "m"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Box display="flex" alignItems="flex-end">
                <Typography fontWeight="bold" mr={1}>
                  {"(F)"}
                </Typography>
                <Typography fontSize={14}>
                  {t("common:front-side-opening")}
                </Typography>
              </Box>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.frontSideOpening
                  ? vehicle.frontSideOpening + "m"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Box display="flex" alignItems="flex-end">
                <Typography fontWeight="bold" mr={1}>
                  {"(G)"}
                </Typography>
                <Typography fontSize={14}>
                  {t("common:rear-side-opening")}
                </Typography>
              </Box>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.rearSideOpening ? vehicle.rearSideOpening + "m" : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("common:front-pressure-on-outrigger")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.frontPressureOnOutrigger
                  ? vehicle.frontPressureOnOutrigger + "kN"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("common:rear-pressure-on-outrigger")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.rearPressureOnOutrigger
                  ? vehicle.rearPressureOnOutrigger + "kN"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("common:invoicing-pipes-from")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.invoicingPipesFrom
                  ? vehicle.invoicingPipesFrom + "m"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("common:pipe-length-for-second-technician")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.pipeLengthForSecondTechnician
                  ? vehicle.pipeLengthForSecondTechnician + "m"
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("common:boom-unfolding-system")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.boomUnfoldingSystem
                  ? vehicle.boomUnfoldingSystem
                  : "-"}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>{t("common:bac-exit")}</Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.bacExit ? t("common:yes") : t("common:no")}
              </Typography>
            </Box>
          </Grid>
          <Grid item sx={{ borderBottom: "solid lightgrey 1px" }} xs={12}>
            <Box display="flex" justifyContent="space-between">
              <Typography fontSize={14}>
                {t("common:bac-exit-reverse")}
              </Typography>
              <Typography fontSize={14} fontWeight="bold">
                {vehicle?.bacExitReverse ? t("common:yes") : t("common:no")}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

export default VehicleInfoTab;
