import {
  getCurrentUser,
  useGetContrac<PERSON>,
  useGetFavorites,
  useSearchedVehicles,
} from "src/common/api";
import { useWindowSize } from "@react-hook/window-size";
import { Autocomplete, Stack } from "@mui/material";
import { useRecoilState } from "recoil";
import {
  vehicleFilterFormValuesState,
  vehicleInformationFlowState,
} from "src/common/state";
import { VehicleCards } from "./content/VehicleList";
import { FilterModal } from "./form/FilterModal";
import { VEHICLE_FILTER_VALUES_DEFAULT } from "src/common/constants";
import { FilterQuickForm } from "./form/FilterQuickForm";
import {
  ContractedStatus,
  FilterVehiclesDto,
  ReservationQueryFilters,
  SearchVehicleType,
  Vehicle,
  searchVehicleTypes,
} from "src/common/types";
import { useEffect, useState } from "react";
import { addMinutes } from "date-fns";
import queryString from "query-string";
import { useLocation, useNavigate } from "react-router-dom";
import { VehicleInformationModal } from "src/vehicles/vehicleInfoModal/VehicleInformationModal";
import { collectOperatorCompanyIdsAttr } from "src/common/utils";
import { CePaper, CeTextField } from "src/common/components";
import { useTranslation } from "react-i18next";
import { usePartners } from "src/common/api/partner";
import { usePersistentVehicleState } from "src/common/utils/vehicleFilterState";

export const Search = () => {
  const navigate = useNavigate();
  const [, height] = useWindowSize();
  const currentUser = getCurrentUser();
  const { t } = useTranslation(["common"]);

  const location = useLocation();
  const localStorageKey = location.pathname.slice(1);

  const [queryFilterState, setQueryFilterState] = usePersistentVehicleState(
    localStorageKey,
    "All"
  );
  const {
    searchType,
    selectedContractedCompanies,
    selectedFavoritedCompanies,
    selectedPartneredCompanies,
    filters,
  } = queryFilterState;

  const { siteAddress, ...quickFormFilters } = filters;

  const [vehicleInformationValues, setVehicleInformationValues] =
    useRecoilState(vehicleInformationFlowState);

  const [dateRange, setDateRange] = useState({
    dateFrom: new Date(),
    dateTo: addMinutes(new Date(), 180),
  });
  const [page, setPage] = useState(1);
  const vehiclesPerPage = 10;

  const [vehicleFilterFormValues, setVehicleFilterFormValues] = useRecoilState(
    vehicleFilterFormValuesState
  );

  const shouldEnableVehicleApi = Boolean(
    dateRange.dateFrom && dateRange.dateTo
  );

  const collectOperatorCompanyIds = collectOperatorCompanyIdsAttr(
    searchType,
    selectedContractedCompanies,
    selectedFavoritedCompanies,
    selectedPartneredCompanies
  );

  const { data: allVehicles, isLoading: isFetchingVehicles } =
    useSearchedVehicles(
      {
        ...quickFormFilters,
        dateFrom: dateRange.dateFrom,
        dateTo: dateRange.dateTo,
        limit: vehiclesPerPage,
        offset: (page - 1) * vehiclesPerPage,
        sortBy: "vehicle.boomSize",
        sortDir: "ASC",
        searchType: searchType,
        operatorCompanyIds: collectOperatorCompanyIds,
        dispatcherCompanyId: currentUser?.companyId || null,
      },
      shouldEnableVehicleApi
    );

  const { data: allFavorites, isLoading: isFavoritesLoading } = useGetFavorites(
    { dispatcherCompanyId: currentUser?.companyId },
    Boolean(currentUser?.companyId && searchType === "Favorited")
  );

  const favorites = allFavorites?.data || [];
  const companies = favorites.map((favorite) => favorite.operatorCompany) || [];

  const { data: allContracted, isLoading: isContractedLoading } =
    useGetContracts(
      {
        expressions: [],
        sortModel: [],
        dispatcherCompanyId: currentUser?.companyId,
        status: ContractedStatus.APPROVED,
        relations: ["operatorCompany"],
      },
      Boolean(currentUser?.companyId && searchType === "Contracted")
    );

  const contracts = allContracted?.data || [];
  const contractCompanies =
    contracts.map((contract) => contract.operatorCompany) || [];

  const { data: allPartners, isLoading: isLoadingPartners } = usePartners(
    { expressions: [], sortModel: [] },
    Boolean(currentUser?.companyId && searchType === "Partnered")
  );

  const partners = allPartners?.data || [];
  const partneredCompanies =
    partners.map((partner) => partner.operatorCompany) || [];

  const isLoading =
    isFetchingVehicles || isFavoritesLoading || isContractedLoading;

  const shouldShowSimilarResultsButton = Boolean(quickFormFilters.boomSize);

  const handleChangePage = (
    event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPage(value);
  };

  useEffect(() => {
    if (
      searchType === "Favorited" &&
      selectedFavoritedCompanies.length === 0 &&
      companies.length
    ) {
      setQueryFilterState((previousState) => ({
        ...previousState,
        selectedFavoritedCompanies: companies,
      }));
    }
  }, [searchType, selectedFavoritedCompanies, companies]);

  useEffect(() => {
    if (
      searchType === "Contracted" &&
      selectedContractedCompanies.length === 0 &&
      contractCompanies.length
    ) {
      setQueryFilterState((previousState) => ({
        ...previousState,
        selectedContractedCompanies: contractCompanies,
      }));
    }
  }, [searchType, selectedContractedCompanies, contractCompanies]);

  useEffect(() => {
    if (
      searchType === "Partnered" &&
      selectedPartneredCompanies.length === 0 &&
      partneredCompanies.length
    ) {
      setQueryFilterState((previousState) => ({
        ...previousState,
        selectedPartneredCompanies: partneredCompanies,
      }));
    }
  }, [searchType, selectedPartneredCompanies, allPartners?.data]);

  const handleCloseFilterModal = () => {
    if (!isLoading) {
      setVehicleFilterFormValues(VEHICLE_FILTER_VALUES_DEFAULT);
    }
  };

  const handleFilterVehicle = (newFilters: FilterVehiclesDto) => {
    setPage(1);
    setQueryFilterState((previousState) => ({
      ...previousState,
      filters: { ...previousState.filters, ...newFilters },
    }));
    setDateRange({
      dateFrom: newFilters.dateFrom!,
      dateTo: newFilters.dateTo!,
    });
  };

  const handleShowSimilarResults = () => {
    setQueryFilterState((previousState) => ({
      ...previousState,
      filters: { ...previousState.filters, boomSize: null },
    }));
  };

  const handleVehicleSelect = (vehicle: Vehicle) => {
    const { siteAddress, city, plz, location } = filters;
    const { dateFrom, dateTo } = dateRange;
    const { id } = vehicle;

    const reservationQuery: ReservationQueryFilters = {
      dateFrom: dateFrom ? new Date(dateFrom).toISOString() : null,
      dateTo: dateTo ? new Date(dateTo).toISOString() : null,
      siteAddress: siteAddress || vehicle.siteAddress,
      city: city || vehicle.city,
      plz: plz || vehicle.plz,
      lat: location?.coordinates[0] || vehicle.location?.coordinates[0],
      lng: location?.coordinates[1] || vehicle.location?.coordinates[1],
      vehicleId: id.toString() || null,
      dispatcherId: currentUser?.id.toString() || null,
    };

    const filteredParams = Object.fromEntries(
      Object.entries(reservationQuery).filter(([key, value]) => value)
    );

    const queryParams = queryString.stringify(filteredParams);

    navigate(`/reservation?${queryParams}`);
  };

  const handleVehicleInformationModal = (vehicle: Vehicle) => {
    setVehicleInformationValues({
      vehicleId: vehicle.id,
      flow: "Vehicle Details",
    });
  };

  return (
    <>
      <CePaper variant="outlined" sx={{ mb: 2, p: 1.5 }}>
        <Stack direction={"row"} spacing={2}>
          <Autocomplete
            id="searchCategory"
            sx={{ width: 200 }}
            value={searchType}
            onChange={(event: any, nextValues: SearchVehicleType | null) => {
              if (nextValues) {
                setQueryFilterState((state) => ({
                  ...state,
                  searchType: nextValues,
                }));
              }
            }}
            options={searchVehicleTypes}
            filterSelectedOptions
            renderInput={(params) => (
              <CeTextField
                {...params}
                InputLabelProps={{ shrink: true }}
                label={t("search-category")}
                size="small"
              />
            )}
          />

          {searchType === "Favorited" ? (
            <Autocomplete
              multiple
              id="tags-outlined"
              options={companies}
              value={selectedFavoritedCompanies}
              limitTags={4}
              getOptionLabel={(option) => option?.name || ""}
              filterSelectedOptions
              sx={{ width: 405 }}
              size="small"
              onChange={(event: any, nextValues) =>
                setQueryFilterState((previousState) => ({
                  ...previousState,
                  selectedFavoritedCompanies: nextValues,
                }))
              }
              renderInput={(params) => (
                <CeTextField
                  {...params}
                  label={t("common:companies")}
                  placeholder={t("common:search-companies")}
                  size="small"
                />
              )}
            />
          ) : null}

          {searchType === "Contracted" ? (
            <Autocomplete
              multiple
              id="tags-outlined"
              options={contractCompanies}
              value={selectedContractedCompanies}
              limitTags={4}
              getOptionLabel={(option) => option?.name || ""}
              filterSelectedOptions
              sx={{ width: 405 }}
              size="small"
              onChange={(event: any, nextValues) =>
                setQueryFilterState((previousState) => ({
                  ...previousState,
                  selectedContractedCompanies: nextValues,
                }))
              }
              renderInput={(params) => (
                <CeTextField
                  {...params}
                  label={t("common:companies")}
                  placeholder={t("common:search-companies")}
                  size="small"
                />
              )}
            />
          ) : null}

          {searchType === "Partnered" ? (
            <Autocomplete
              multiple
              id="tags-outlined"
              options={partneredCompanies}
              value={selectedPartneredCompanies}
              limitTags={4}
              getOptionLabel={(option) => option?.name || ""}
              filterSelectedOptions
              sx={{ width: 405 }}
              size="small"
              onChange={(event: any, nextValues) => {
                setQueryFilterState((previousState) => ({
                  ...previousState,
                  selectedPartneredCompanies: nextValues,
                }));
              }}
              renderInput={(params) => (
                <CeTextField
                  {...params}
                  label={t("common:companies")}
                  placeholder={t("common:search-companies")}
                  size="small"
                />
              )}
            />
          ) : null}
        </Stack>
      </CePaper>
      <CePaper variant="outlined" sx={{ mb: 2, p: 1.5 }}>
        <FilterQuickForm
          isLoading={isLoading}
          initialFormValues={{
            ...vehicleFilterFormValues,
            dateFrom: dateRange.dateFrom?.toISOString(),
            dateTo: dateRange.dateTo?.toISOString(),
            siteAddress: siteAddress || null,
            coordinates:
              filters.coordinates && filters?.coordinates.length > 0
                ? filters.coordinates
                : null,
            boomSize: quickFormFilters.boomSize
              ? quickFormFilters.boomSize
              : null,
            type: quickFormFilters.type ? quickFormFilters.type : null,
          }}
          handleFilterVehicle={handleFilterVehicle}
          handleClose={handleCloseFilterModal}
        />

        {/* later for more advanced filter */}
        {/* <Button
          variant="outlined"
          color="primary"
          onClick={() => {
            setVehicleFilterFormValues({
              ...vehicleFilterFormValues,
              flow: "Filter",
            });
          }}
        >
          Filter
        </Button> */}
      </CePaper>

      <VehicleCards
        vehicles={allVehicles?.data || []}
        handleVehicleSelect={handleVehicleSelect}
        handleVehicleModal={handleVehicleInformationModal}
        page={page}
        total={Math.ceil((allVehicles?.totalCount || 0) / vehiclesPerPage)}
        handleChangePage={handleChangePage}
        handleShowSimilarResults={handleShowSimilarResults}
        shouldShowSimilarResultsButton={shouldShowSimilarResultsButton}
      />

      <FilterModal
        isLoading={isLoading}
        initialFormValues={vehicleFilterFormValues}
        handleFilterVehicle={() => {}}
        handleCloseFilterModal={handleCloseFilterModal}
      />

      <VehicleInformationModal vehicleId={vehicleInformationValues.vehicleId} />
    </>
  );
};
