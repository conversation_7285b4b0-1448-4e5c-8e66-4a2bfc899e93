import { FC, useCallback, useEffect } from "react";
import {
  GridColDef,
  GridFilterModel,
  GridRenderCellParams,
  GridSortModel,
} from "@mui/x-data-grid";

import { Link as MuiLink, Stack, IconButton, Chip } from "@mui/material";
import { useRecoilState } from "recoil";

import { DataGrid } from "@mui/x-data-grid";
import {
  MANAGER_DELETE_DEFAULT,
  MANAGER_FORM_VALUES_DEFAULT,
  userRolesDisplay,
} from "src/common/constants";
import { ManagerModal } from "../ManagerModal";
import {
  ManagerFormValuesState,
  managerDeleteValuesState,
} from "src/common/state";
import {
  Role,
  Status,
  User,
  ManagerFormValues,
  userStatuses,
  Manager,
} from "src/common/types";
import {
  useCreateNewManager,
  useDeleteManager,
  useUpdateManager,
} from "src/common/api";
import { ManagerModalDelete } from "../ManagerModalDelete";
import { turnManagerIntoFormValues } from "src/common/utils";
import { Delete01Icon, Edit02Icon } from "@hugeicons/react";
import { GridColumnTypography } from "src/common/components/custom/company/GridColumnTypography";
import { CeDataGridToolbar } from "src/common/components/custom/company/CeDataGridToolbar";
import {
  GridStateSnapshot,
  UpdateGridStatePart,
} from "src/common/utils/gridState";

interface ManagersDatagridProps {
  data: User[];
  isFetchingManagers: boolean;
  refetchManagers: () => void;
  shouldRenderRefreshButton: boolean;
  shouldRenderAddButton: boolean;
  shouldRenderEditActionsColumn: boolean;

  total: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
  gridState: GridStateSnapshot;
  updateGridStatePart: UpdateGridStatePart;
  handleSortModelChange?: (sortModel: GridSortModel) => void;
  onDatagridFiltersChange?: (datagridFilter: GridFilterModel) => void;
  isServerDriven: boolean;
}

export const ManagersDatagrid: FC<ManagersDatagridProps> = ({
  data,
  shouldRenderRefreshButton,
  shouldRenderAddButton,
  shouldRenderEditActionsColumn,
  isFetchingManagers,
  refetchManagers,
  total,
  onPageChange,
  onPageSizeChange,
  gridState,
  updateGridStatePart,
  handleSortModelChange,
  onDatagridFiltersChange,
  isServerDriven,
}) => {
  const {
    mutate: handleUpdateManager,
    isSuccess: isUpdateManagerSuccess,
    isLoading: isUpdatingManager,
    mutateAsync: handleUpdateUserAsync,
  } = useUpdateManager();
  const {
    mutate: handleCreateNewManager,
    isSuccess: isCreateManagerSuccess,
    isLoading: isCreatingManager,
  } = useCreateNewManager();
  const {
    mutate: handleDeleteManager,
    isLoading: isDeletingManager,
    isSuccess: isDeleteManagerSuccess,
  } = useDeleteManager();

  const [managerFormValues, setManagerFormValues] = useRecoilState(
    ManagerFormValuesState
  );

  const [managerDeleteValues, setManagerDeleteValues] = useRecoilState(
    managerDeleteValuesState
  );

  const isLoading =
    isCreatingManager ||
    isUpdatingManager ||
    isDeletingManager ||
    isFetchingManagers;

  const handleCloseVehicleModal = () => {
    if (!isLoading) {
      setManagerFormValues(MANAGER_FORM_VALUES_DEFAULT);
    }
  };

  const handleCloseManagerModalDelete = () => {
    if (!isLoading) {
      setManagerDeleteValues(MANAGER_DELETE_DEFAULT);
    }
  };

  useEffect(() => {
    if (isCreateManagerSuccess) {
      setManagerFormValues(MANAGER_FORM_VALUES_DEFAULT);
    }
  }, [isCreateManagerSuccess, setManagerFormValues]);

  useEffect(() => {
    if (isUpdateManagerSuccess) {
      setManagerFormValues(MANAGER_FORM_VALUES_DEFAULT);
    }
  }, [isUpdateManagerSuccess, setManagerFormValues]);

  useEffect(() => {
    if (isDeleteManagerSuccess) {
      setManagerDeleteValues(MANAGER_DELETE_DEFAULT);
    }
  }, [isDeleteManagerSuccess, setManagerDeleteValues]);

  const columns: GridColDef[] = [
    {
      field: "editActions",
      headerName: "Edit",
      sortable: false,
      renderCell: (params: GridRenderCellParams<string>) =>
        renderEditActions(params),
      filterable: false,
    },
    {
      field: "user.id",
      headerName: "Id",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 50,
      renderCell: (params) => <GridColumnTypography value={params.row.id} />,
    },
    {
      field: "user.firstName",
      headerName: "First Name",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.firstName} />
      ),
    },
    {
      field: "user.lastName",
      headerName: "Last Name",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.lastName} />
      ),
    },
    {
      field: "user.status",
      headerName: "Status",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => {
        const status: number = params.row.status - 1;

        const statusText = userStatuses[status] || "";
        const statusColor =
          params.row.status === Status.ACTIVE ? "success" : "default";

        return (
          <Chip
            sx={{ m: 0.5, ml: 0, fontSize: "13px" }}
            color={statusColor}
            component="span"
            size="small"
            label={statusText}
          />
        );
      },
    },
    {
      field: "user.role",
      headerName: "Role",
      headerAlign: "left",
      align: "left",
      type: "number",
      width: 200,
      renderCell: (params) => {
        const role: number = params.row.role;

        const roleText =
          userRolesDisplay.find((r) => r.id === role)?.title || "";
        const roleColor =
          role === Role.DISPATCHER_MANAGER ? "warning" : "error";

        return (
          <Chip
            sx={{ m: 0.5, ml: 0, fontSize: "13px" }}
            color={roleColor}
            component="span"
            size="small"
            label={roleText}
          />
        );
      },
    },
    {
      field: "company.name",
      headerName: "Company Name",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.company.name} />
      ),
    },
    {
      field: "companyAddress",
      headerName: "Company Address",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.companyAddress} />
      ),
    },
    {
      field: "user.email",
      headerName: `Email`,
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 250,
      renderCell: (params) => <GridColumnTypography value={params.row.email} />,
    },
    {
      field: "phoneNumber",
      headerName: "phoneNumber",
      headerAlign: "left",
      type: "string",
      align: "left",
      width: 150,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.phoneNumber} />
      ),
    },
    {
      field: "vatNumber",
      headerName: "Vat Number",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 125,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.vatNumber} />
      ),
    },
    {
      field: "user.created_by",
      headerName: "Created By",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.created_by} />
      ),
    },
    {
      field: "created_at",
      headerName: "Created At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.created_at} />
      ),
    },
    {
      field: "updated_at",
      headerName: "Updated At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.updated_at} />
      ),
    },
    {
      field: "deleted_at",
      headerName: "Deleted At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.deleted_at} />
      ),
    },
  ];

  const renderEditActions = (params: GridRenderCellParams) => {
    const user: Manager = params.row;

    return (
      <Stack
        direction="row"
        justifyContent="center"
        alignItems="center"
        spacing={1}
      >
        <IconButton
          aria-label="update user"
          disabled={isLoading}
          size="small"
          onClick={() => {
            const formValues: ManagerFormValues = turnManagerIntoFormValues(
              user,
              "Update"
            );

            setManagerFormValues(formValues);
          }}
        >
          <Edit02Icon size={16} variant={"stroke"} />
        </IconButton>

        <IconButton
          aria-label="delete user"
          disabled={isLoading}
          color="error"
          size="small"
          onClick={() =>
            setManagerDeleteValues({
              managerId: user.id,
              managerTitle: `${user.firstName || ""} ${" "} ${
                user.lastName || ""
              }`,
              flow: "Delete",
            })
          }
        >
          <Delete01Icon size={16} variant={"stroke"} />
        </IconButton>
      </Stack>
    );
  };

  const onFilterChange = useCallback(
    (filterModel: GridFilterModel) => {
      if (onDatagridFiltersChange) {
        onDatagridFiltersChange(filterModel);
        updateGridStatePart("filterModel", filterModel);
      }
    },
    [onDatagridFiltersChange]
  );

  const onSortChange = useCallback(
    (sortModel: GridSortModel) => {
      if (handleSortModelChange) {
        handleSortModelChange(sortModel);
        updateGridStatePart("sortModel", sortModel);
      }
    },
    [handleSortModelChange]
  );

  return (
    <>
      <DataGrid
        sx={{
          borderTopLeftRadius: 0,
          borderTopRightRadius: 0,
          paddingTop: 0.5,
        }}
        pagination
        rowsPerPageOptions={[20, 40, 60, 80, 100]}
        rowCount={total}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        paginationMode="server"
        page={gridState.page - 1}
        pageSize={gridState.pageSize}
        columnVisibilityModel={gridState.columnVisibilityModel}
        onColumnVisibilityModelChange={(model) =>
          updateGridStatePart("columnVisibilityModel", model)
        }
        filterModel={gridState.filterModel}
        sortModel={gridState.sortModel}
        sortingMode={isServerDriven ? "server" : "client"}
        onSortModelChange={isServerDriven ? onSortChange : undefined}
        filterMode={isServerDriven ? "server" : "client"}
        onFilterModelChange={isServerDriven ? onFilterChange : undefined}
        components={{
          Toolbar: () => (
            <CeDataGridToolbar
              shouldRenderAddButton={shouldRenderAddButton}
              shouldRenderRefreshButton={shouldRenderRefreshButton}
              addButtonClickHandler={() => {
                setManagerFormValues({ ...managerFormValues, flow: "Create" });
              }}
              onRefreshButtonClick={refetchManagers}
              addButtonDescription="Add Manager"
            />
          ),
        }}
        columns={columns}
        rows={data}
        disableSelectionOnClick
        initialState={{
          columns: {
            columnVisibilityModel: {
              id: false,
              created_at: false,
              updated_at: false,
              deleted_at: false,
              created_by: false,
            },
          },
          sorting: {
            sortModel: [{ field: "role", sort: "asc" }],
          },
        }}
      />

      <ManagerModal
        initialFormValues={managerFormValues}
        isLoading={isLoading}
        handleCreateNewManager={handleCreateNewManager}
        handleUpdateManager={handleUpdateManager}
        handleCloseManagerModal={handleCloseVehicleModal}
      />

      <ManagerModalDelete
        flow={managerDeleteValues.flow}
        isLoading={isLoading}
        managerTitle={managerDeleteValues.managerTitle}
        managerId={managerDeleteValues.managerId}
        handleCloseUserModalDelete={handleCloseManagerModalDelete}
        handleDeleteUser={handleDeleteManager}
      />
    </>
  );
};
