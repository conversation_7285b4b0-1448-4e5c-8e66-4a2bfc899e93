import React from "react";
import { styled } from "@mui/material";

interface SvgIconWrapperProps {
  IconComponent: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
  size?: string;
  strokeWidth?: string;
  strokeColor?: string;
}

const StyledSvgIcon = styled("span")<{
  size?: string;
  strokeWidth?: string;
  strokeColor?: string;
}>`
  width: ${({ size }) => size || "20px"};
  height: ${({ size }) => size || "20px"};

  & > svg {
    display: block;
    width: 100%;
    height: auto;
  }

  & path {
    stroke: ${({ strokeColor }) => strokeColor || "currentColor"};
    stroke-width: ${({ strokeWidth }) => strokeWidth || "1.5"};
  }
`;

export const CustomSvgIconWrapper = ({
  IconComponent,
  size,
  strokeWidth,
  strokeColor
}: SvgIconWrapperProps) => {
  return (
    <StyledSvgIcon
      size={size}
      strokeWidth={strokeWidth}
      strokeColor={strokeColor}
    >
      <IconComponent />
    </StyledSvgIcon>
  );
};
