import { Theme } from "@mui/material";
import { STATUS_COLORS } from "src/common/constants";
import { InvoiceStatus } from "src/common/types";

export const buttonTextTransform = {
  textTransform: "none",
  fontWeight: "bold",
};

export type ElevationLevel = 0 | 1 | 2 | 3 | 4 | 5;

export const elevationStyles = {
  0: "none",
  1: "0px 0px 1px 0px rgba(0, 0, 0, 0.12), 0px 2px 5px 0px rgba(0, 0, 0, 0.08)",
  2: "0px 0px 1px 0px rgba(0, 0, 0, 0.12), 0px 2px 5px 0px rgba(0, 0, 0, 0.10)",
  3: "0px 0px 1px 0px rgba(0, 0, 0, 0.12), 0px 2px 5px 0px rgba(0, 0, 0, 0.14)",
  4: "0px 0px 1px 0px rgba(0, 0, 0, 0.12), 0px 2px 5px 0px rgba(0, 0, 0, 0.20)",
  5: "0px 0px 1px 0px rgba(0, 0, 0, 0.12), 0px 2px 5px 0px rgba(0, 0, 0, 0.24)",
};

export const priceListReviewRowStyles = {
  borderBottom: (theme: Theme) => `solid ${theme.palette.divider} 1px`,
  padding: 2,
};

export const invoiceChipColors = (status: InvoiceStatus) => {
  if (status === InvoiceStatus.PENDING) {
    return {
      color: STATUS_COLORS.NOT_STARTED.color,
      backgroundColor: STATUS_COLORS.NOT_STARTED.backgroundColor,
      borderColor: STATUS_COLORS.NOT_STARTED.borderColor,
    };
  }
  if (status === InvoiceStatus.COMPLETED) {
    return {
      color: STATUS_COLORS.COMPLETE.color,
      backgroundColor: STATUS_COLORS.COMPLETE.backgroundColor,
      borderColor: STATUS_COLORS.COMPLETE.borderColor,
    };
  }
  if (status === InvoiceStatus.FAILED || status === InvoiceStatus.CANCELLED) {
    return {
      color: STATUS_COLORS.CANCELLED.color,
      backgroundColor: STATUS_COLORS.CANCELLED.backgroundColor,
      borderColor: STATUS_COLORS.CANCELLED.borderColor,
    };
  }
};
