import { AxiosError } from "axios";
import { UseMutateFunction } from "react-query";
import { MainModal } from "src/common/components";
import {
  CreateDispatcherDto,
  Dispatcher,
  DispatcherFormValues,
  UpdateDispatcherDto,
  Vehicle,
  VehicleFormValues,
} from "src/common/types";
import { DispatcherForm } from "./DispatcherForm";

interface DispatcherModalProps {
  isLoading: boolean;
  initialFormValues: DispatcherFormValues;
  handleCloseDispatcherModal: () => void;
  handleCreateNewDispatcher?: UseMutateFunction<
    Dispatcher,
    AxiosError<CreateDispatcherDto, CreateDispatcherDto> | Error,
    CreateDispatcherDto,
    () => void
  >;
  handleUpdateDispatcher?: UseMutateFunction<
    Dispatcher,
    AxiosError<UpdateDispatcherDto, UpdateDispatcherDto> | Error,
    UpdateDispatcherDto,
    () => void
  >;
}
export const DispatcherModal: React.FC<DispatcherModalProps> = ({
  isLoading,
  initialFormValues,
  handleCloseDispatcherModal,
  handleCreateNewDispatcher,
  handleUpdateDispatcher,
}) => {
  return (
    <MainModal
      title={`${initialFormValues.flow || ""} Dispatcher`}
      isOpen={!!initialFormValues.flow}
      handleClose={handleCloseDispatcherModal}
    >
      <DispatcherForm
        initialFormValues={initialFormValues}
        isLoading={isLoading}
        handleCreateNewDispatcher={handleCreateNewDispatcher}
        handleUpdateDispatcher={handleUpdateDispatcher}
        handleClose={handleCloseDispatcherModal}
      />
    </MainModal>
  );
};
