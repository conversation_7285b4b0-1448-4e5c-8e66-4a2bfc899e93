import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Grid,
  <PERSON><PERSON><PERSON>,
  Stack,
  DialogContent,
  DialogActions,
  DialogContentText,
} from "@mui/material";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import {
  AccessTimeFilled,
  CropFree,
  Event,
  LocationOn,
  Phone,
  Store,
  Warning,
} from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import { format, isAfter } from "date-fns";
import { useNavigate, useParams } from "react-router-dom";
import { useWindowSize } from "@react-hook/window-size";
import { useReservation } from "src/common/api";
import { useUpdateJob } from "src/common/api/job";
import { Manager } from "src/common/types";
import { buttonTextTransform } from "src/common/components/custom/customCss";
import { CeDialog } from "src/common/components";

export const JobDetails = () => {
  const params = useParams();
  const navigate = useNavigate();
  const [, height] = useWindowSize();
  const { t } = useTranslation(["operator", "common"]);

  const [dialogOpen, setDialogOpen] = React.useState(false);

  const reservationId = Number(params.id);
  const { data: reservation, isLoading: isReservationLoading } = useReservation(
    reservationId,
    Boolean(reservationId)
  );
  const { mutateAsync: handleUpdateJobAsync, isLoading: isUpdatingJob } =
    useUpdateJob();
  const manager = reservation?.manager as unknown as Manager;

  const isLoading = isReservationLoading || isUpdatingJob;

  const isStartingJob =
    reservation?.job?.status === "NOT_STARTED" &&
    reservation.job.progress === 0;

  const isJobInProgress =
    reservation?.job?.status !== "NOT_STARTED" &&
    reservation?.job?.status !== "COMPLETE";

  const isReadyToStart = () => {
    if (
      reservation?.job?.status !== "COMPLETE" &&
      reservation?.job?.status !== "CANCELLED"
    ) {
      const currentDate = new Date();

      if (reservation?.dateFrom) {
        const reservationDate = new Date(reservation.dateFrom);

        // job can be started 60 minutes before the reservation date
        reservationDate.setMinutes(reservationDate.getMinutes() - 60);
        return isAfter(currentDate, reservationDate);
      }
      return false;
    }
  };

  return (
    <Box>
      <Stack spacing={2} mb={10}>
        <Grid container>
          <Grid item xs={1}>
            <Store color="primary" />
          </Grid>
          <Grid item xs={11}>
            <Typography variant="subtitle1" fontWeight="bold">
              {t("common:company")}
            </Typography>
            <Typography>{reservation?.clientDetails?.companyName}</Typography>
          </Grid>
        </Grid>

        <Grid container>
          <Grid item xs={1}>
            <Event color="primary" />
          </Grid>
          <Grid item xs={11}>
            <Typography variant="subtitle1" fontWeight="bold">
              {t("common:date")}
            </Typography>
            <Typography>
              {reservation?.dateFrom
                ? format(new Date(reservation.dateFrom), "dd/MM/yyyy")
                : "-"}
            </Typography>
          </Grid>
        </Grid>

        <Grid container>
          <Grid item xs={1}>
            <AccessTimeFilled color="primary" />
          </Grid>
          <Grid item xs={11}>
            <Typography variant="subtitle1" fontWeight="bold">
              {t("common:time")}
            </Typography>
            <Typography>
              {reservation?.dateFrom && reservation.dateTo
                ? format(new Date(reservation.dateFrom), "HH:mm") +
                  " - " +
                  format(new Date(reservation.dateTo), "HH:mm")
                : "-"}
            </Typography>
          </Grid>
        </Grid>

        <Grid container>
          <Grid item xs={1}>
            <LocationOn color="primary" />
          </Grid>
          <Grid item xs={11}>
            <Typography variant="subtitle1" fontWeight="bold">
              {t("common:address")}
            </Typography>
            <Typography>{reservation?.siteAddress || "-"}</Typography>
          </Grid>
        </Grid>

        <Grid container>
          <Grid item xs={1}>
            <CropFree color="primary" />
          </Grid>
          <Grid item xs={11}>
            <Typography variant="subtitle1" fontWeight="bold">
              {t("common:concrete-amount")}
            </Typography>
            <Typography>
              {reservation?.job?.amountOfConcrete
                ? `${reservation?.job?.amountOfConcrete} m³`
                : "-"}
            </Typography>
          </Grid>
        </Grid>

        <Grid container>
          <Grid item xs={1}>
            <Phone color="primary" />
          </Grid>
          <Grid item xs={11}>
            <Typography variant="subtitle1" fontWeight="bold">
              {t("contacts")}
            </Typography>
            <Typography>
              {t("common:client")}:{" "}
              {reservation?.clientDetails?.phoneNumber || "-"}
            </Typography>
            {reservation?.dispatcher ? (
              <Typography>
                {t("common:dispatcher")}: {reservation.dispatcher.phoneNumber}
              </Typography>
            ) : (
              <Typography>
                {t("common:manager")}: {manager?.phoneNumber}
              </Typography>
            )}
          </Grid>
        </Grid>

        <Grid container>
          <Grid item xs={1}>
            <Warning color="primary" />
          </Grid>
          <Grid item xs={11}>
            <Typography variant="subtitle1" fontWeight="bold">
              {t("details")}
            </Typography>
            <Grid container>
              <Grid item xs={12}>
                <Typography>
                  {t("common:extra-cement-bags")}:{" "}
                  {reservation?.job?.units || "-"}
                </Typography>
              </Grid>

              {reservation?.job?.flexiblePipeLength80Mm && (
                <Grid item xs={12}>
                  <Typography>
                    {t("common:flexible-pipe-80")}:{" "}
                    {reservation.job.flexiblePipeLength80Mm}m
                  </Typography>
                </Grid>
              )}
              {reservation?.job?.flexiblePipeLength90Mm && (
                <Grid item xs={12}>
                  <Typography>
                    {t("common:flexible-pipe-90")}:{" "}
                    {reservation.job.flexiblePipeLength90Mm}m
                  </Typography>
                </Grid>
              )}
              {reservation?.job?.rigidPipeLength100Mm && (
                <Grid item xs={12}>
                  <Typography>
                    {t("common:rigid-pipe-100")}:{" "}
                    {reservation.job.rigidPipeLength100Mm}m
                  </Typography>
                </Grid>
              )}
              {reservation?.job?.rigidPipeLength120Mm && (
                <Grid item xs={12}>
                  <Typography>
                    {t("common:rigid-pipe-120")}:{" "}
                    {reservation.job.rigidPipeLength120Mm}m
                  </Typography>
                </Grid>
              )}
              <Grid item xs={12}>
                <Typography>
                  {t("common:presence-of-power-lines")}:{" "}
                  {reservation?.job?.presenceOfPowerLines ? "Yes" : "No"}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography>
                  {t("common:parking")}: {reservation?.job?.parkingOn}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography>
                  {t("common:parking-permit-acquired")}:{" "}
                  {Boolean(reservation?.parkingPermitAcquiredKey)
                    ? "Yes"
                    : "No"}
                </Typography>
              </Grid>

              <Grid item xs={12}>
                <Typography>
                  {t("common:enlist-second-technician")}:{" "}
                  {reservation?.job?.enlistSecondTechnician ? "Yes" : "No"}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography>
                  {t("common:local-admin-authentication")}:{" "}
                  {Boolean(reservation?.localAdministrationAuthorizationKey)
                    ? "Yes"
                    : "No"}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography>
                  {t("common:traffic-plan")}:{" "}
                  {Boolean(reservation?.trafficPlanKey) || "-"}
                </Typography>
              </Grid>
              {reservation?.job?.comments && (
                <Grid item xs={12}>
                  {t("comment")}:{" "}
                  <Typography>{reservation.job.comments || "-"}</Typography>
                </Grid>
              )}
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12} pl={2} pr={2}>
          <Button
            fullWidth
            variant="outlined"
            size="small"
            sx={buttonTextTransform}
            onClick={() => {
              // here should open a modal to view the reservation attachments
            }}
          >
            {t("view-attachments")}
          </Button>
        </Grid>
      </Stack>

      <Box
        position="fixed"
        bottom={0}
        left={0}
        width="100%"
        bgcolor="background.paper"
        padding={1}
        borderColor="divider"
      >
        <Stack direction="row" justifyContent="space-between" spacing={2}>
          <Button
            startIcon={<CalendarMonthIcon />}
            color="primary"
            size="medium"
            disabled={isLoading}
            variant="outlined"
            onClick={async () => {
              navigate(`/operator-planning`);
            }}
            sx={buttonTextTransform}
          >
            {t("common:planning")}
          </Button>

          <Button
            color="primary"
            size="medium"
            disabled={isLoading || reservation?.job?.status === "COMPLETE"}
            sx={buttonTextTransform}
            variant="contained"
            onClick={async () => {
              // only start jobs that are not started.
              if (isStartingJob) {
                setDialogOpen(true);
              } else if (isJobInProgress) {
                // continue the job
                navigate(`/job-tracker/${reservationId}`);
              }
            }}
          >
            {isJobInProgress ? t("continue") : t("common:start")}
          </Button>

          <CeDialog
            open={dialogOpen}
            onClose={() => {
              setDialogOpen(false);
            }}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
          >
            <DialogContent>
              <DialogContentText id="alert-dialog-description">
                {t("initiate-job")}
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                sx={buttonTextTransform}
                onClick={() => setDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={async () => {
                  setDialogOpen(false);

                  await handleUpdateJobAsync({
                    status: "DRIVING_TO_SITE",
                    jobId: reservation?.job?.id!,
                    reservationId,
                  });

                  navigate(`/job-tracker/${reservationId}`);
                }}
                autoFocus
                sx={buttonTextTransform}
              >
                {t("common:confirm")}
              </Button>
            </DialogActions>
          </CeDialog>
        </Stack>
      </Box>
    </Box>
  );
};
