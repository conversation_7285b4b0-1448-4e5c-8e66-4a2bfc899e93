import {
  EventClickArg,
  EventContentArg,
  LocaleInput,
} from "@fullcalendar/core";
import deLocale from "@fullcalendar/core/locales/de";
import enLocale from "@fullcalendar/core/locales/en-gb";
import frLocale from "@fullcalendar/core/locales/fr";
import nlLocale from "@fullcalendar/core/locales/nl";
import dayGridPlugin from "@fullcalendar/daygrid";
import FullCalendar from "@fullcalendar/react";
import timeGridPlugin from "@fullcalendar/timegrid";
import Container from "@mui/material/Container";
import { useTranslation } from "react-i18next";
import { format } from "date-fns";

import { FC, useRef, useState } from "react";
import {
  getCurrentUser,
  useReservation,
  useReservations,
  useUser,
  useVehicle,
} from "src/common/api";
import { Operator, Reservation, getReservationDto } from "src/common/types";
import EventContentDay from "./event-content/EventContentDay";
import EventContentWeek from "./event-content/EventContentWeek";
import EventContentMonth from "./event-content/EventContentMonth";
import EventContentModal from "./event-content/EventContentModal";
import MenuViews from "./menu-views/menu";

type PlanningProps = {};

export const Planning: FC<PlanningProps> = () => {
  const localeText: { [key: string]: LocaleInput } = {
    en: enLocale,
    fr: frLocale,
    nl: nlLocale,
    de: deLocale,
  };
  const currentUser = getCurrentUser();

  const shouldEnableApis = currentUser?.id;

  const reservationsAttr: getReservationDto = {
    operatorIds: currentUser?.id ? [currentUser.id] : [],
    expressions:[],
    sortModel: [
      {
        field: "reservation.dateFrom",
        sort: "asc", 
      },
    ],
  };

  const {
    data: allReservations,
    isLoading: isReservationsLoading,
    isSuccess: isReservationSuccess,
  } = useReservations(reservationsAttr, Boolean(shouldEnableApis));
  const reservations = allReservations?.data || [];

  const [openJobEventModal, setOpenJobEventModal] = useState(false);
  const [jobEventArgs, setJobEventArgs] = useState<EventClickArg | null>(null);
  const { t, i18n } = useTranslation(["manager", "common", "dispatcher"]);
  const selectedLanguage = i18n.language;

  const calendarRef = useRef<any>();

  const events = [
    ...(reservations?.map((reservation: Reservation) => ({
      id: reservation.id.toString(),
      title: reservation.clientDetails?.companyName || "",
      start: reservation.dateFrom || "",
      end: reservation.dateTo || "",
      extendedProps: {
        schedule:
          reservation.dateFrom && reservation.dateTo
            ? `${format(
                new Date(reservation.dateFrom),
                "dd/MM/yyyy | HH:mm"
              )} - ${format(new Date(reservation.dateTo), "HH:mm")}`
            : "",
        date:
          reservation.dateFrom && reservation.dateTo
            ? `${format(new Date(reservation.dateFrom), "HH:mm")} - ${format(
                new Date(reservation.dateTo),
                "HH:mm"
              )}`
            : "",
        dateFrom: reservation.dateFrom
          ? `${format(new Date(reservation.dateFrom), "HH:mm")}`
          : "",
        address: reservation.siteAddress || "",
        quantity: reservation.job?.amountOfConcrete || "",
        comment: reservation.job?.comments || "",
        type: "job",
        jobStatus: reservation.job?.status || "",
        hasBeenRead: reservation.hasBeenRead
      },
      color: "Primary",
    })) || []),
  ];

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleOpenJobEventModal = (arg: EventClickArg) => {
    setOpenJobEventModal(true);
    setJobEventArgs(arg);
  };

  const handleCloseJobEventModal = () => {
    setOpenJobEventModal(false);
    setJobEventArgs(null);
  };

  const changeDayView = () => {
    const calendarApi = calendarRef.current.getApi();
    calendarApi.changeView("timeGridDay");
  };
  const changeWeekView = () => {
    const calendarApi = calendarRef.current.getApi();
    calendarApi.changeView("timeGridWeek");
  };
  const changeMonthView = () => {
    const calendarApi = calendarRef.current.getApi();
    calendarApi.changeView("dayGridMonth");
  };

  const dayEventContent = (arg: EventContentArg) => {
    return <EventContentDay arg={arg} />;
  };

  const weekEventContent = (arg: EventContentArg) => {
    return <EventContentWeek arg={arg} />;
  };

  const monthEventContent = (arg: EventContentArg) => {
    return <EventContentMonth arg={arg} />;
  };

  return (
    <>
      {jobEventArgs ? (
        <EventContentModal
          arg={jobEventArgs}
          open={openJobEventModal}
          onClose={handleCloseJobEventModal}
        />
      ) : null}

      <MenuViews
        anchorEl={anchorEl}
        changeDayView={changeDayView}
        changeMonthView={changeMonthView}
        changeWeekView={changeWeekView}
        handleClose={handleClose}
      />
      <FullCalendar
        ref={calendarRef}
        allDaySlot={false}
        firstDay={1}
        customButtons={{
          menuButton: {
            text: "≡",
            click: (event) => {
              handleClick(event);
            },
          },
        }}
        editable={true}
        eventClick={(arg: EventClickArg) => {
          // if (arg.event.extendedProps.type === "unavailable-period") {
          //   handleOpenUnavailablePeriodModal(arg);
          // }
          if (arg.event.extendedProps.type === "job") {
            handleOpenJobEventModal(arg);
          }
        }}
        eventDisplay={"block"}
        events={events}
        handleWindowResize={true}
        headerToolbar={{
          left: "prev,next",
          center: "title",
          right: "today,menuButton",
        }}
        height="calc(100vh - 100px)"
        initialView="timeGridDay"
        locale={localeText[selectedLanguage]}
        plugins={[timeGridPlugin, dayGridPlugin]}
        schedulerLicenseKey="CC-Attribution-NonCommercial-NoDerivatives"
        eventStartEditable={false}
        eventResourceEditable={false}
        selectable={true}
        views={{
          dayGridMonth: {
            titleFormat: { year: "2-digit", month: "short" },
            displayEventTime: false,
            eventContent: monthEventContent,
          },
          timeGridDay: {
            titleFormat: { month: "short", day: "2-digit" },
            eventContent: dayEventContent,
          },
          timeGridWeek: {
            titleFormat: { month: "short" },
            dayHeaderFormat: {
              weekday: "short",
              day: "2-digit",
            },
            eventContent: weekEventContent,
          },
        }}
      />
    </>
  );
};
