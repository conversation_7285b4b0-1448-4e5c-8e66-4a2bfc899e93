import { Alert, Box, Divider, Typography } from "@mui/material";
import { DeleteModal } from "src/common/components";
import {
  PriceListDeleteFlowEnum,
  PriceListModalDeleteFlow,
} from "src/common/types/priceList";
import { useTranslation } from "react-i18next";

interface PriceListVariantDeleteModalProps {
  flow: PriceListModalDeleteFlow;
  priceListTitle?: string;
  isLoading: boolean;
  handleClosePriceListModalDelete: () => void;
  handleDeletePriceList: () => void;
}
export const PriceListVariantDeleteModal: React.FC<
  PriceListVariantDeleteModalProps
> = ({
  flow,
  priceListTitle,
  isLoading,
  handleClosePriceListModalDelete,
  handleDeletePriceList,
}) => {
  const { t } = useTranslation(["common", "dispatcher"]);

  const DeleteModalHelperText = () => (
    <Box>
      <Typography sx={{ marginBottom: 1 }}>
        {t("delete-pricelist-message", {
          priceListTitle: priceListTitle || "unknown pricelist",
          entityType: "variant",
        })}
      </Typography>
    </Box>
  );

  return (
    <DeleteModal
      isOpen={flow === PriceListDeleteFlowEnum.VARIANT_DELETE}
      isLoading={isLoading}
      title={t("delete-vehicle")}
      helperText={DeleteModalHelperText()}
      handleSubmit={handleDeletePriceList}
      handleClose={handleClosePriceListModalDelete}
    />
  );
};
