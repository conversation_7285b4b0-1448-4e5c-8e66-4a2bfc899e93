import { CommonEntity, PossibleSortDir } from "./common";
import { Company } from "./company";

export interface CompanySettings extends CommonEntity {
  id: number;
  companyId: number;
  company?: Company;
  workSchedules?: WorkScheduleSettings[];
  cancellationId: string;
  cancellation?: CancellationSettings;
}

export interface CompanySettingsWithCount {
  totalCount: number;
  data: CompanySettings[];
}

export interface WorkScheduleSettings {
  day: string;
  startTime: string;
  endTime: string;
  companySettingsId: number;
  companySettings: CompanySettings;
}

export interface CancellationSettings {
  cancellationWindow: string;
  reducedCancellationWindow: string;
  minTimeBetweenJobs: string;
  settings: CompanySettings;
}

export interface CreateCompanySettingsDto {
  companyId: number;
  workSchedules?: WorkSchedule[];
  cancellation?: Cancellation;
}

export interface CompanySettingsFormValues {
  id?: number;
  companyId?: number;
  workSchedules: WorkSchedule[];
  cancellation: Cancellation;
}

export interface UpdateCompanySettingsDto extends CreateCompanySettingsDto {
  companySettingsId?: number;
  companyId: number;
}

export interface GetCompanySettingsDto {
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
}

export interface WorkSchedule {
  day: string;
  startTime: string;
  endTime: string;
}

export interface Cancellation {
  cancellationWindow?: string;
  reducedCancellationWindow?: string;
  minTimeBetweenJobs?: string;
}
