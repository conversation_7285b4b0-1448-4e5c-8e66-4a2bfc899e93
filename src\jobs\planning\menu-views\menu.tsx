import { Menu, MenuItem } from '@mui/material';
import { useTranslation } from "react-i18next";
import { FC } from 'react';

type Props = {
    anchorEl: HTMLElement | null;
    handleClose: () => void;
    changeDayView: () => void;
    changeWeekView: () => void;
    changeMonthView: () => void;
};

const MenuViews: FC<Props> = (props) => {
    const { t } = useTranslation(['operator']);
    const { anchorEl, handleClose, changeDayView, changeWeekView, changeMonthView } = props;
    const open = Boolean(anchorEl);

    return (
        <Menu
            MenuListProps={{
                'aria-labelledby': 'basic-button'
            }}
            anchorEl={anchorEl}
            id="basic-menu"
            open={open}
            onClose={handleClose}>
            <MenuItem
                onClick={() => {
                    handleClose();
                    changeDayView();
                }}>
                {t('day')}
            </MenuItem>
            <MenuItem
                onClick={() => {
                    handleClose();
                    changeWeekView();
                }}>
                {t('week')}
            </MenuItem>
            <MenuItem
                onClick={() => {
                    handleClose();
                    changeMonthView();
                }}>
                {t('month')}
            </MenuItem>
        </Menu>
    );
};

export default MenuViews;
