import { ArrowDownRight01Icon, ArrowUpRight01Icon } from '@hugeicons/react';
import { Box, Grid, Typography } from '@mui/material';
import { useWindowSize } from '@react-hook/window-size';
import { endOfYear, format, startOfYear } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { getCurrentUser } from 'src/common/api';
import { useTotalExpensesPerMonth, useTotalReservationsPerMonth } from 'src/common/api/analytics';
import { CeChip, CePaper } from 'src/common/components';
import { GetAnalyticstDto } from 'src/common/types/analytics';
import ExpenseChart from '../charts/ExpenseChart';
import ReservationsChart from '../charts/ReservationsChart';

interface ProgressData {
  progress: number | null;
  totalExpenses?: number;
  totalReservations?: number;
  month: string;
}

export const DashboardCharts = () => {
  const { t } = useTranslation(["dispatcher"]);
  const currentUser = getCurrentUser();
  const currentDate = new Date();
  const [, height] = useWindowSize();

  const attrs: GetAnalyticstDto = {
    dispatcherCompanyId: currentUser?.companyId,
    startDate: format(startOfYear(currentDate), 'yyyy-MM-dd'),
    endDate: format(endOfYear(currentDate), 'yyyy-MM-dd'),
  };

  const { data: totalReservationsPerMonth } = useTotalReservationsPerMonth(attrs, Boolean(currentUser?.id));
  const { data: totalExpensesPerMonth } = useTotalExpensesPerMonth(attrs, Boolean(currentUser?.id));

  const totalExpense = totalExpensesPerMonth?.totalExpensesPerMonth.reduce((sum, expense) => sum + expense.totalExpenses, 0);
  const totalReservations = totalReservationsPerMonth?.totalReservationsPerMonth.reduce((sum, expense) => sum + expense.totalReservations, 0);

  const calculateProgress = (dataArray: ProgressData[]) => {
    if (!dataArray || dataArray.length === 0) return 0;

    const completedMonths = dataArray.filter((data) => data.progress !== null);
    if (completedMonths.length === 0) return 0;

    const totalProgress = completedMonths.reduce((acc, data) => acc + (data.progress || 0), 0);
    return totalProgress / completedMonths.length;
  };

  const totalReservationsProgress = calculateProgress(totalReservationsPerMonth?.totalReservationsPerMonth!);
  const totalExpenseProgress = calculateProgress(totalExpensesPerMonth?.totalExpensesPerMonth!);

  const getProgressIcon = (progress: number) => {
    return progress >= 0 ? (
      <ArrowUpRight01Icon size={14} variant="stroke" />
    ) : (
      <ArrowDownRight01Icon size={14} variant="stroke" />
    );
  };

  return (
    <CePaper sx={{ height: `${height - 200 - 48}px`, p: 2, mt: 2 }}>
      <Grid container spacing={4}>
        <Grid item xs={12} sm={6}>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ fontSize: '16px', fontWeight: 700, mb: 1 }}
          >
            {t("total-expenses")}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography
                variant="h5"
                component="div"
                sx={{ fontWeight: 700, fontSize: '20px', mr: 1 }}
              >
                {totalExpense}
                <Typography
                  component="span"
                  sx={{ ml: 0.5, fontSize: '20px', fontWeight: 700 }}
                >
                  €
                </Typography>
              </Typography>
              <CeChip
                 label={`${totalExpenseProgress}%`}
                deleteIcon={getProgressIcon(totalExpenseProgress)}
                onDelete={() => null}
                size="small"
                status={totalExpenseProgress >= 0 ? 'positive' : 'negative'}
              />
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Box
                width={8}
                height={8}
                borderRadius="50%"
                bgcolor="#0057B2"
                mr={1}
              />
              <Typography variant="caption" color="text.secondary">
                {t("expenses")}
              </Typography>
            </Box>
          </Box>
          <ExpenseChart totalExpensesPerMonth={totalExpensesPerMonth?.totalExpensesPerMonth || []} />
        </Grid>
        <Grid item xs={12} sm={6}>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ fontSize: '16px', fontWeight: 700, mb: 1 }}
          >
            {t("total-reservations")}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography
                variant="h5"
                component="div"
                sx={{ fontWeight: 700, fontSize: '20px', mr: 1 }}
              >
                {totalReservations}
              </Typography>
              <CeChip
                label={`${totalReservationsProgress}%`}
                deleteIcon={getProgressIcon(totalReservationsProgress)}
                onDelete={() => null}
                size="small"
                status={totalReservationsProgress >= 0 ? 'positive' : 'negative'}
              />
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Box
                width={8}
                height={8}
                borderRadius="50%"
                bgcolor="#29B6F6"
                mr={1}
              />
              <Typography variant="caption" color="text.secondary">
                {t("reservations")}
              </Typography>
            </Box>
          </Box>
          <ReservationsChart totalReservationsPerMonth={totalReservationsPerMonth?.totalReservationsPerMonth || []} />
        </Grid>
      </Grid>
    </CePaper>
  );
};
