import { GridSortModel } from "@mui/x-data-grid";
import { CommonEntity, PossibleSortDir } from "./common";
import { Company } from "./company";
import { ContainerPricelists } from "./priceList";
import { Expression } from "./filters";

export enum PartnerStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export interface GetPartnersDto {
  sortModel: GridSortModel;
  expressions: Expression[];
  relations?: string[];
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
}

export interface Partner extends CommonEntity {
  id: number;
  dispatcherCompanyId: number;
  operatorCompanyId: number;
  containerPricelistsId?: number;
  dispatcherCompany: Company;
  operatorCompany: Company;
  container?: ContainerPricelists;
  status: PartnerStatus;
}

export interface PartnerWithCount {
  totalCount: number;
  data: Partner[];
}

export interface CreatePartnerDto {
  operatorCompanyIds: number[];
}

export interface DeletePartnerDto {
  partnerId: number;
}
export interface PartnerFormValues {
  operatorCompanies: Company[] | [];
  partnerId: number | null;
  flow: "Add" | "Delete" | null;
}
