import { decodeToken, isExpired } from "react-jwt";
import { processApiError } from "../utils/errors";
import axios, { AxiosError } from "axios";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { DecodedToken, SignatureFormValue, TrackAndTrace, TrackAndTraceArgs } from "../types";

const backendUrl = process.env.REACT_APP_API_URL;


function setTrackTraceTokenToStorage(token: string): void {
    localStorage.setItem("track_and_trace_token", token);
}

export function rmTrackTraceTokenToStorage() {
    localStorage.removeItem("track_and_trace_token");
}

export const handleTrackAndTraceAuthentication = (
    trackAndTraceArgs: TrackAndTraceArgs
  ) => {
    return axios
      .post(`${backendUrl}/track-and-trace/authenticate`, trackAndTraceArgs, {
        withCredentials: true,
      })
      .then((response) => response.data);
  };
  
  export const useTrackAndTraceAuthentication = () => {
    return useMutation<
      { token: string },
      AxiosError | Error,
      TrackAndTraceArgs,
      () => void
    >(
      (trackAndTraceArgs) => handleTrackAndTraceAuthentication(trackAndTraceArgs),
      {
        onError: (err) => processApiError("Unable to authenticate for tracking", err),
        onSuccess: (response) => {
          setTrackTraceTokenToStorage(response.token)
        },
      }
    );
  };

  export const getTrackAndTraceDetails = async (orderNumber: string) => {
    const token = localStorage.getItem("track_and_trace_token");
    if (!orderNumber) {
        throw new Error("orderNumber was not provided");
      }

    if (!token) {
      throw new Error("Authorization token was not provided");
    }

    return axios
      .get(`${backendUrl}/track-and-trace/${orderNumber}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      })
      .then((response) => response.data);
  };

  export const useTrackAndTraceDetails = (
    orderNumber: string,
    enabled: boolean = true
  ) => {
    return useQuery<TrackAndTrace, AxiosError | Error>(
      ["trackAndTraceDetails", orderNumber],
      () => getTrackAndTraceDetails(orderNumber),
      {
        keepPreviousData: true,
        onError: (err) => processApiError("Unable to fetch trackAndTraceDetails", err),
        enabled,
      }
    );
  };

  export const handleAddJobSignature = (jobSignatureArgs: SignatureFormValue) => {
    const token = localStorage.getItem("track_and_trace_token");
    if (!token) {
      throw new Error("Authorization token was not provided");
    }
    return axios
      .post(`${backendUrl}/track-and-trace/add-job-signature`, jobSignatureArgs, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      })
      .then((response) => response.data);
  };
  
  export const useAddJobSignature = () => {
    const queryClient = useQueryClient();
    return useMutation<
      void,
      AxiosError | Error,
      SignatureFormValue,
      () => void
    >(
      (jobSignatureArgs) => handleAddJobSignature(jobSignatureArgs),
      {
        onError: (err) => processApiError("Unable to add job signature", err),
        onSuccess: () => {
          queryClient.invalidateQueries("trackAndTraceDetails");
        },
      }
    );
  };
  
  export const isTrackAndTraceAuthenticated = () => {
    const token = localStorage.getItem("track_and_trace_token");
  
    if (!token) {
      return {
        isAuth: false,
      };
    }
    const isTokenExpired = isExpired(token);
  
    const decodedToken = decodeToken(token) as DecodedToken;
  
    return {
      isAuth: !isTokenExpired,
      orderDetails: {
        orderNumber: decodedToken.orderNumber,
        clientEmail: decodedToken.clientEmail,
      },
    };
  };
  