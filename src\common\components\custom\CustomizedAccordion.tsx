import * as React from "react";
import { styled } from "@mui/material/styles";
import {
  ArrowDown01Icon,
  ArrowRight01Icon,
  ArrowRight02Icon
} from "@hugeicons/react";

import MuiAccordion, { AccordionProps } from "@mui/material/Accordion";
import MuiAccordionSummary, {
  AccordionSummaryProps
} from "@mui/material/AccordionSummary";
import MuiAccordionDetails from "@mui/material/AccordionDetails";
import Typography from "@mui/material/Typography";

const Accordion = styled((props: AccordionProps) => (
  <MuiAccordion disableGutters elevation={0} square {...props} />
))(({ theme }) => ({
  border: `1px solid ${theme.palette.divider}`,
  overflow: "hidden",
  "&:first-of-type": {
    borderRadius: "8px 8px 0 0"
  },
  "&:last-child": {
    borderRadius: "0 0 8px 8px "
  },
  "&:not(:last-child)": {
    borderBottom: 0
  },
  "&::before": {
    display: "none"
  }
}));

const AccordionSummary = styled((props: AccordionSummaryProps) => (
  <MuiAccordionSummary
    expandIcon={<ArrowDown01Icon size={"1.2rem"} />}
    {...props}
  />
))(({ theme }) => ({
  backgroundColor: theme.palette.grey[100],
  minHeight: "36px",
  padding: "8px 16px ",
  "& .MuiAccordionSummary-content": {
    marginLeft: theme.spacing(1),
    margin: 0
  },
  "& .MuiTypography-root": {
    fontSize: "1rem"
  }
}));

const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
  padding: 0,
  borderTop: "1px solid rgba(0, 0, 0, .125)",
  "& .MuiTypography-root": {
    fontSize: "0.9rem"
  }
}));

interface CustomizedAccordionsProps {
  children: React.ReactNode;
}

export default function CustomizedAccordions({
  children
}: CustomizedAccordionsProps) {
  const firstChildId = React.Children.toArray(children)?.[0];
  const id = React.isValidElement(firstChildId)
    ? firstChildId.props.id
    : undefined;
  const [expanded, setExpanded] = React.useState<string | false>(id || false);

  const handleChange =
    (panel: string) => (event: React.SyntheticEvent, newExpanded: boolean) => {
      setExpanded(newExpanded ? panel : false);
    };

  const clonedChildren = React.Children.map(children, (child: any) =>
    React.cloneElement(child, { expanded, handleChange })
  );

  return <div>{clonedChildren}</div>;
}

interface AccordionItemProps {
  id: string;
  title: React.ReactNode;
  children: React.ReactNode;
  expanded?: string | false;
  handleChange?: (
    panel: string
  ) => (event: React.SyntheticEvent, newExpanded: boolean) => void;
}

export function AccordionItem({
  id,
  title,
  children,
  expanded,
  handleChange
}: AccordionItemProps) {
  return (
    <Accordion
      expanded={expanded === id}
      onChange={handleChange && handleChange(id)}
    >
      <AccordionSummary aria-controls={`${id}-content`} id={`${id}-header`}>
        <Typography component="div">{title}</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Typography component="div">{children}</Typography>
      </AccordionDetails>
    </Accordion>
  );
}
