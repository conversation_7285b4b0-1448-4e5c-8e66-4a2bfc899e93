import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ack, Typography } from "@mui/material";
import { DataGrid, GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { useTranslation } from "react-i18next";
import { CeAvatar, FeatureFlag } from "src/common/components";
import { useEffect } from "react";
import { VehicleTypesDeleteModal } from "../VehicleTypesDeleteModal";
import { VehicleTypeFormValues, VehicleType } from "src/common/types";
import {
  useCreateNewVehicleType,
  useDeleteVehicleType,
  useUpdateVehicleType,
} from "src/common/api";
import {
  vehicleTypeDeleteValuesState,
  vehicleTypeFormValuesState,
} from "src/common/state";
import {
  VEHICLE_TYPE_DELETE_DEFAULT,
  VEHICLE_TYPE_FORM_VALUES,
} from "src/common/constants";
import { turnVehicleTypeIntoFormValues } from "src/common/utils";
import { VehicleTypeModal } from "../VehicleTypesModal";
import { useRecoilState } from "recoil";
import { Delete01Icon, Edit02Icon } from "@hugeicons/react";
import { CeDataGridToolbar } from "src/common/components/custom/company/CeDataGridToolbar";
import usePersistentGridState from "src/common/utils/gridState";
import { useLocation } from "react-router-dom";
import { stringAvatar } from "src/common/utils/avatar";

interface VehicleTypesDatagridProps {
  data: VehicleType[];
  isFetchingVehicleTypes: boolean;
  refetchVehicleTypes: () => void;
  shouldRenderRefreshButton: boolean;
  page: number;
  pageSize: number;
  total: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
}
export const VehicleTypesDatagrid: React.FC<VehicleTypesDatagridProps> = ({
  data,
  shouldRenderRefreshButton,
  isFetchingVehicleTypes,
  refetchVehicleTypes,
  page,
  pageSize,
  total,
  onPageChange,
  onPageSizeChange,
}) => {
  const { t } = useTranslation(["manager", "common", "dispatcher"]);
  const location = useLocation();
  const localStorageKey = location.pathname.slice(1);
  const [gridState, updateGridStatePart] = usePersistentGridState(
    localStorageKey,
    page,
    pageSize
  );
  const {
    mutate: handleUpdateVehicleType,
    isSuccess: isUpdateVehicleTypeSuccess,
    isLoading: isUpdatingVehicleType,
  } = useUpdateVehicleType();

  const {
    mutate: handleCreateNewVehicleType,
    isSuccess: isCreateVehicleTypeSuccess,
    isLoading: isCreatingVehicleType,
  } = useCreateNewVehicleType();

  const {
    mutate: handleDeleteVehicleType,
    isLoading: isDeletingVehicleType,
    isSuccess: isDeleteVehicleTypeSuccess,
  } = useDeleteVehicleType();

  const [vehicleTypeFormValues, setVehicleTypeFormValues] = useRecoilState(
    vehicleTypeFormValuesState
  );

  const [vehicleTypeDeleteFormValues, setVehicleTypeDeleteFormValues] =
    useRecoilState(vehicleTypeDeleteValuesState);

  const isLoading =
    isFetchingVehicleTypes ||
    isCreatingVehicleType ||
    isDeletingVehicleType ||
    isUpdatingVehicleType;

  useEffect(() => {
    if (isCreateVehicleTypeSuccess) {
      setVehicleTypeFormValues(VEHICLE_TYPE_FORM_VALUES);
    }
  }, [isCreateVehicleTypeSuccess, setVehicleTypeFormValues]);

  useEffect(() => {
    if (isUpdateVehicleTypeSuccess) {
      setVehicleTypeFormValues(VEHICLE_TYPE_FORM_VALUES);
    }
  }, [isUpdateVehicleTypeSuccess, setVehicleTypeFormValues]);

  useEffect(() => {
    if (isDeleteVehicleTypeSuccess) {
      setVehicleTypeDeleteFormValues(VEHICLE_TYPE_DELETE_DEFAULT);
    }
  }, [isDeleteVehicleTypeSuccess, setVehicleTypeDeleteFormValues]);

  const handleCloseVehicleModalDelete = () => {
    if (!isLoading) {
      setVehicleTypeDeleteFormValues(VEHICLE_TYPE_DELETE_DEFAULT);
    }
  };
  const handleCloseVehicleModal = () => {
    if (!isLoading) {
      setVehicleTypeFormValues(VEHICLE_TYPE_FORM_VALUES);
    }
  };

  const renderEditActions = (params: GridRenderCellParams) => {
    const vehicleType: VehicleType = params.row;

    return (
      <Stack
        direction="row"
        justifyContent="center"
        alignItems="center"
        spacing={1}
      >
        <IconButton
          aria-label="update vehicle Type"
          disabled={isUpdatingVehicleType}
          size="small"
          onClick={() => {
            const formValues: VehicleTypeFormValues =
              turnVehicleTypeIntoFormValues(vehicleType, "Update");
            setVehicleTypeFormValues(formValues);
          }}
        >
          <Edit02Icon size={16} variant={"stroke"} />
        </IconButton>

        <FeatureFlag flags={["operator manager"]}>
          <IconButton
            aria-label="delete vehicle type"
            disabled={isDeletingVehicleType}
            color="error"
            size="small"
            onClick={() =>
              setVehicleTypeDeleteFormValues({
                vehicleTypeId: vehicleType.id,
                vehicleTypeTitle: vehicleType.name,
                flow: "Delete",
              })
            }
          >
            <Delete01Icon size={16} variant={"stroke"} />
          </IconButton>
        </FeatureFlag>
      </Stack>
    );
  };

  const columns: GridColDef[] = [
    {
      field: "editActions",
      headerName: `${t("common:edit")}`,
      sortable: false,
      renderCell: (params: GridRenderCellParams<string>) =>
        renderEditActions(params),
      filterable: false,
    },
    {
      field: "id",
      headerName: `id`,
      headerAlign: "left",
      align: "left",
      width: 100,
    },
    {
      field: "name",
      headerName: `${t("common:type")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
    },
    {
      field: "operator.name",
      headerName: `${t("common:operator-manager")}`,
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => {
        if (!params.row?.operatorManager) {
          return "";
        }

        const firstName = params.row?.operatorManager?.firstName || "";
        const lastName = params.row?.operatorManager?.lastName || "";
        const fullName = `${firstName} ${lastName}`;

        return (
          <Box alignItems="center" display="flex">
            {fullName && (
              <Box alignItems="center" display="flex" mr={1}>
                <CeAvatar size="medium" {...stringAvatar(fullName)} />
                <Typography ml={1} fontSize={13} variant="caption">
                  {fullName}
                </Typography>
              </Box>
            )}
          </Box>
        );
      },
    },
  ];

  return (
    <>
      <DataGrid
        sx={{
          border: "none",
          paddingTop: 0.5,
          background: (theme) => theme.palette.background.paper,
        }}
        pagination
        page={gridState.page - 1}
        pageSize={gridState.pageSize}
        rowsPerPageOptions={[20, 40, 60, 80, 100]}
        rowCount={total}
        onPageChange={(newPage) => {
          onPageChange(newPage);
          updateGridStatePart("page", newPage);
        }}
        onPageSizeChange={(newPageSize) => {
          onPageSizeChange(newPageSize);
          updateGridStatePart("pageSize", newPageSize);
        }}
        columnVisibilityModel={gridState.columnVisibilityModel}
        onColumnVisibilityModelChange={(model) =>
          updateGridStatePart("columnVisibilityModel", model)
        }
        filterModel={gridState.filterModel}
        onFilterModelChange={(model) =>
          updateGridStatePart("filterModel", model)
        }
        sortModel={gridState.sortModel}
        onSortModelChange={(model) => updateGridStatePart("sortModel", model)}
        paginationMode="server"
        components={{
          Toolbar: () => (
            <CeDataGridToolbar
              shouldRenderRefreshButton={shouldRenderRefreshButton}
              onRefreshButtonClick={refetchVehicleTypes}
              shouldRenderAddButton={shouldRenderRefreshButton}
              addButtonClickHandler={() => {
                setVehicleTypeFormValues({
                  ...vehicleTypeFormValues,
                  flow: "Create",
                });
              }}
              addButtonDescription={t("common:add-vehicle-type")}
            />
          ),
        }}
        columns={columns}
        rows={data}
        disableSelectionOnClick
      />
      <VehicleTypeModal
        initialFormValues={vehicleTypeFormValues}
        setInitialFormValues={setVehicleTypeFormValues}
        isLoading={isLoading}
        handleCreateNewVehicleType={handleCreateNewVehicleType}
        handleUpdateVehicleType={handleUpdateVehicleType}
        handleCloseVehicleModal={handleCloseVehicleModal}
      />
      <VehicleTypesDeleteModal
        flow={vehicleTypeDeleteFormValues.flow}
        isLoading={isLoading}
        vehicleTypeTitle={vehicleTypeDeleteFormValues.vehicleTypeTitle!}
        vehicleTypeId={vehicleTypeDeleteFormValues.vehicleTypeId}
        handleCloseVehicleModalDelete={handleCloseVehicleModalDelete}
        handleDeleteVehicleType={handleDeleteVehicleType}
      />
    </>
  );
};
