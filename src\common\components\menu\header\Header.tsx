import { useRecoilValue } from "recoil";
import { styled } from "@mui/material/styles";
import MuiAppBar, { AppBarProps as MuiAppBarProps } from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import MenuIcon from "@mui/icons-material/Menu";
import { useTranslation } from "react-i18next";

import { themeState } from "src/common/state";
import { Box, Button } from "@mui/material";
import { useLogoutUser } from "src/common/api/auth";
import { FeatureFlag } from "../../featureflag/FeatureFlag";
import LanguageSwitcher from "../../languageSwitcher/LanguageSwitcher";

const drawerWidth = 260;

interface AppBarProps extends MuiAppBarProps {
  open?: boolean;
}

interface HeaderProps {
  open: boolean;
  handleDrawerToggle(): void;
}

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== "open",
})<AppBarProps>(({ theme, open }) => ({
  zIndex: theme.zIndex.drawer + 1,
  boxShadow: "none",
  borderBottom: `1px solid ${theme.palette.mode === "dark" ? null : "#ccc"}`,
  transition: theme.transitions.create(["width", "margin"], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  ...(open && {
    marginLeft: drawerWidth,
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create(["width", "margin"], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));

export const Header: React.FC<HeaderProps> = ({ open }) => {
  const theme = useRecoilValue(themeState);
  const { t } = useTranslation(["common", "manager", "dispatcher"]);

  const { mutate: handleLogoutUser } = useLogoutUser();

  return (
    <AppBar
      position="fixed"
      open={open}
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
        bgcolor: theme === "light" ? "#fff" : null,
      }}
    >
      <Toolbar
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            width: "100%",
          }}
        >
          <LanguageSwitcher />
        </Box>
      </Toolbar>
    </AppBar>
  );
};
