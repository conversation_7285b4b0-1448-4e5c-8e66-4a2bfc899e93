import { atom } from "recoil";
import { DeleteOperatorModalValues, OperatorFormValues } from "../types";
import {
  OPERATOR_DELETE_DEFAULT,
  OPERATOR_FORM_VALUES_DEFAULT,
} from "../constants/operator";

export const operatorFormValuesState = atom<OperatorFormValues>({
  key: "operatorFormValuesState",
  default: OPERATOR_FORM_VALUES_DEFAULT,
});

export const operatorDeleteValuesState = atom<DeleteOperatorModalValues>({
  key: "operatorDeleteValuesState",
  default: OPERATOR_DELETE_DEFAULT,
});
