import { FC, useCallback, useEffect } from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";

import {
  Stack,
  IconButton,
  Typography,
  Tooltip,
} from "@mui/material";

import { useRecoilState } from "recoil";
import { DataGrid } from "@mui/x-data-grid";
import { useTranslation } from "react-i18next";

import {
  PRICELIST_DELETE_DEFAULT,
  PRICELIST_FORM_VALUES_DEFAULT,
} from "src/common/constants/priceList";

import {
  PriceList,
  PriceListDeleteFlowEnum,
  PriceListFormValues,
  PricelistTypeEnum,
  PumpTier,
  TransportRate,
} from "src/common/types/priceList";

import {
  useCreateNewPriceList,
  useDeletePriceList,
  useUpdatePriceList,
} from "src/common/api/priceList";

import {
  priceListDeleteValuesState,
  priceListFormValuesState,
} from "src/common/state/priceList";

import { PriceListVariantDeleteModal } from "../PriceListVariantDeleteModal";
import {
  turnPriceListIntoFormValues,
  turnPriceListRowIntoIntoUpdateDto,
} from "src/common/utils/priceList";
import { Delete01Icon, Edit02Icon } from "@hugeicons/react";
import { NumberParam, useQueryParam } from "use-query-params";
import { CeDataGridToolbar } from "src/common/components/custom/company/CeDataGridToolbar";
import { GridColumnTypography } from "src/common/components/custom/company/GridColumnTypography";
import PriceListDrawer from "../PriceListDrawer";
import { GridStateSnapshot, UpdateGridStatePart } from "src/common/utils/gridState";

interface PriceListDatagridProps {
  data: PriceList[];
  isFetchingPriceLists: boolean;
  refetchPriceLists: () => void;
  shouldRenderRefreshButton: boolean;
  shouldRenderAddButton: boolean;
  shouldRenderEditActionsColumn: boolean;
  total: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
  gridState: GridStateSnapshot;
  updateGridStatePart: UpdateGridStatePart;
}

export const PriceListDatagrid: FC<PriceListDatagridProps> = ({
  data,
  shouldRenderRefreshButton,
  shouldRenderAddButton,
  shouldRenderEditActionsColumn,
  isFetchingPriceLists,
  refetchPriceLists,
  total,
  onPageChange,
  onPageSizeChange,
  gridState,
  updateGridStatePart,
}) => {
  const { t } = useTranslation(["dispatcher", "manager", "common"]);

  const [id] = useQueryParam("id", NumberParam);
  const containerId = Number(id);

  const {
    mutate: handleUpdatePriceList,
    isSuccess: isUpdatePriceListSuccess,
    isLoading: isUpdatingPriceList,
    mutateAsync: handleUpdatePriceListAsync,
    isError: isUpdatePriceListError,
  } = useUpdatePriceList();
  const {
    mutate: handleCreateNewPriceList,
    isSuccess: isCreatePriceListSuccess,
    isLoading: isCreatingPriceList,
  } = useCreateNewPriceList();
  const {
    mutate: handleDeletePriceList,
    isLoading: isDeletingPriceList,
    isSuccess: isDeletePriceListSuccess,
  } = useDeletePriceList();

  const [priceListFormValues, setPriceListFormValues] = useRecoilState(
    priceListFormValuesState
  );

  const [priceListDeleteValues, setPriceListDeleteValues] = useRecoilState(
    priceListDeleteValuesState
  );

  const isLoading =
    isCreatingPriceList ||
    isUpdatingPriceList ||
    isDeletingPriceList ||
    isFetchingPriceLists;

  const onPriceListDelete = () => {
    const priceListId = priceListDeleteValues?.priceListId;
    if (priceListId) {
      handleDeletePriceList({ priceListId });
    }
  };

  const handleClosePriceListDrawer = () => {
    if (!isLoading) {
      setPriceListFormValues(PRICELIST_FORM_VALUES_DEFAULT);
    }
  };

  const handleClosePriceListModalDelete = () => {
    if (!isLoading) {
      setPriceListDeleteValues(PRICELIST_DELETE_DEFAULT);
    }
  };

  useEffect(() => {
    if (isCreatePriceListSuccess) {
      setPriceListFormValues(PRICELIST_FORM_VALUES_DEFAULT);
    }
  }, [isCreatePriceListSuccess, setPriceListFormValues]);

  useEffect(() => {
    if (isUpdatePriceListSuccess) {
      setPriceListFormValues(PRICELIST_FORM_VALUES_DEFAULT);
    }
  }, [isUpdatePriceListSuccess, setPriceListFormValues]);

  useEffect(() => {
    if (isDeletePriceListSuccess) {
      setPriceListDeleteValues(PRICELIST_DELETE_DEFAULT);
    }
  }, [isDeletePriceListSuccess, setPriceListDeleteValues]);

  const processRowUpdate = async (newRow: PriceList, oldRow: PriceList) => {
    try {
      const payload = turnPriceListRowIntoIntoUpdateDto(newRow);
      const updatedRow = await handleUpdatePriceListAsync(payload);
      return updatedRow;
    } catch (error: any) {
      console.error(error.message);
      return oldRow;
    }
  };
  const vehiclesWithPricelists: number[] = data
    .map((pricelist) => pricelist.vehicleId)
    .filter((id): id is number => id !== null && id !== undefined);
  const inheritFromExistingPricelist = (pricelist: PriceList) => {
    const existingPriceListFormValues = turnPriceListIntoFormValues(
      pricelist,
      PricelistTypeEnum.CREATE_VARIANT
    );
    setPriceListFormValues({
      ...existingPriceListFormValues,
      containerId: containerId,
      flow: PricelistTypeEnum.CREATE_VARIANT,
      priceListId: null,
      vehicleId: null,
    });
  };
  const handleProcessRowUpdateError = useCallback((error: Error) => {
    console.error(error.message);
  }, []);
  const columns: GridColDef[] = [
    {
      field: "editActions",
      headerName: t("common:edit"),
      sortable: false,
      renderCell: (params: GridRenderCellParams<string>) =>
        renderEditActions(params),
      filterable: false,
    },
    {
      field: "id",
      headerName: "Id",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 50,
      renderCell: (params) => <GridColumnTypography value={params.row.id} />,
    },
    {
      field: "title",
      headerName: t("title"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => <GridColumnTypography value={params.row.title} />,
      editable: true,
    },
    {
      field: "packageFlatFee",
      headerName: t("package-flat-fee"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.packageFlatFee} />
      ),
      editable: true,
    },
    {
      field: "packageFlatFeeDuration",
      headerName: t("packageFlatFeeDuration"),
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.packageFlatFeeDuration} />
      ),
      editable: true,
    },
    {
      field: "pipeInvoicingStartsFrom",
      headerName: t("pipeInvoicingStartsFrom"),
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.pipeInvoicingStartsFrom} />
      ),
      editable: true,
    },
    {
      field: "pricePerMeterPumped",
      headerName: t("price-per-meter-pumped"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.pricePerMeterPumped} />
      ),
      editable: true,
    },
    {
      field: "additionalHour",
      headerName: t("additional-hour"),
      headerAlign: "left",
      type: "string",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.additionalHour} />
      ),
      editable: true,
    },
    {
      field: "cleaningFee",
      headerName: t("cleaning-fee"),
      headerAlign: "left",
      type: "string",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.cleaningFee} />
      ),
      editable: true,
    },
    {
      field: "pricePerMeterOfFlexiblePipeLength80Mm",
      headerName: t("price-per-meter-of-flexible-pipe-length-80Mm"),
      headerAlign: "left",
      type: "string",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography
          value={params.row.pricePerMeterOfFlexiblePipeLength80Mm}
        />
      ),
      editable: true,
    },
    {
      field: "pricePerMeterOfFlexiblePipeLength90Mm",
      headerName: t("price-per-meter-of-flexible-pipe-length-90Mm"),
      headerAlign: "left",
      type: "string",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography
          value={params.row.pricePerMeterOfFlexiblePipeLength90Mm}
        />
      ),
      editable: true,
    },
    {
      field: "pricePerMeterOfFlexiblePipeLength100Mm",
      headerName: t("price-per-meter-of-flexible-pipe-length-100Mm"),
      headerAlign: "left",
      type: "string",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography
          value={params.row.pricePerMeterOfFlexiblePipeLength100Mm}
        />
      ),
      editable: true,
    },
    {
      field: "pricePerMeterOfRigidPipeLength120Mm",
      headerName: t("price-per-meter-of-rigid-pipe-length-120Mm"),
      headerAlign: "left",
      type: "string",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography
          value={params.row.pricePerMeterOfRigidPipeLength120Mm}
        />
      ),
      editable: true,
    },
    {
      field: "supplyOfTheChemicalSlushie",
      headerName: t("supply-of-the-chemical-slushie"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.supplyOfTheChemicalSlushie} />
      ),
      editable: true,
    },
    {
      field: "barbotine",
      headerName: t("barbotine"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.barbotine} />
      ),
      editable: true,
    },
    {
      field: "extraCementBagPrice",
      headerName: t("extra-cement-bag-price"),
      headerAlign: "left",
      type: "string",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.extraCementBagPrice} />
      ),
      editable: true,
    },
    {
      field: "pumpTiers",
      headerName: t("pump-tiers"),
      sortable: false,
      filterable: false,
      width: 200,
      renderCell: (params: GridRenderCellParams) => {
        const { value } = params;
        const tooltipContent = value
          .map(
            (tier: PumpTier) =>
              `${tier.name}: ${tier.price ?? "N/A"}€, ${
                tier.minimum ?? "N/A"
              }m³-${tier.maximum ?? "N/A"}m³`
          )
          .join("\n");

        const displayContent =
          tooltipContent.length > 20
            ? `${tooltipContent.substring(0, 20)}...`
            : tooltipContent;

        return (
          <Tooltip
            title={
              <Typography variant="caption" sx={{ whiteSpace: "pre-line" }}>
                {tooltipContent}
              </Typography>
            }
          >
            <Typography
              variant="caption"
              sx={{ paddingRight: 1 }}
              fontSize={13}
            >
              {displayContent}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      field: "transportRates",
      headerName: t("transport-rates"),
      sortable: false,
      filterable: false,
      width: 200,
      renderCell: (params: GridRenderCellParams) => {
        const { value } = params;
        const tooltipContent = value
          .map(
            (rate: TransportRate) =>
              `${rate.name}: ${rate.tariff ?? "N/A"}€, ${
                rate.from ?? "N/A"
              }km-${rate.to ?? "N/A"}km`
          )
          .join("\n");

        const displayContent =
          tooltipContent.length > 20
            ? `${tooltipContent.substring(0, 20)}...`
            : tooltipContent;

        return (
          <Tooltip
            title={
              <Typography variant="caption" sx={{ whiteSpace: "pre-line" }}>
                {tooltipContent}
              </Typography>
            }
          >
            <Typography
              variant="caption"
              sx={{ paddingRight: 1 }}
              fontSize={13}
            >
              {displayContent}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      field: "secondTechnicianFee",
      headerName: t("secondTechnicianFee"),
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.secondTechnicianFee} />
      ),
      editable: true,
    },
    {
      field: "backupPumpPackage",
      headerName: t("backupPumpPackage"),
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.backupPumpPackage} />
      ),
      editable: true,
    },
    {
      field: "created_by",
      headerName: "Created By",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.created_by} />
      ),
    },
    {
      field: "created_at",
      headerName: "Created At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.created_at} />
      ),
    },
    {
      field: "updated_at",
      headerName: "Updated At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.updated_at} />
      ),
    },
    {
      field: "deleted_at",
      headerName: "Deleted At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.deleted_at} />
      ),
    },
  ];

  const renderEditActions = (params: GridRenderCellParams) => {
    const priceList: PriceList = params.row;

    return (
      <Stack
        direction="row"
        justifyContent="center"
        alignItems="center"
        spacing={1}
      >
        {shouldRenderAddButton ? (
          <IconButton
            aria-label="update pricelist"
            disabled={isLoading}
            size="small"
            onClick={(event) => {
              event.preventDefault();
              event.stopPropagation();
              const formValues: PriceListFormValues =
                turnPriceListIntoFormValues(
                  priceList,
                  PricelistTypeEnum.UDPATE_VARIANT
                );
              setPriceListFormValues(formValues);
            }}
          >
            <Edit02Icon size={16} variant={"stroke"} />
          </IconButton>
        ) : null}

        <IconButton
          aria-label="delete pricelist"
          disabled={isLoading}
          color="error"
          size="small"
          onClick={(event) => {
            event.preventDefault();
            event.stopPropagation();
            setPriceListDeleteValues({
              priceListId: priceList.id,
              priceListTitle: priceList.title || "",
              flow: PriceListDeleteFlowEnum.VARIANT_DELETE,
            });
          }}
        >
          <Delete01Icon size={16} variant={"stroke"} />
        </IconButton>
      </Stack>
    );
  };
  return (
    <>
      <DataGrid
        sx={{
          border: "none",
          paddingTop: 0.5,
        }}
        pagination
        page={gridState.page - 1}
        pageSize={gridState.pageSize}
        rowsPerPageOptions={[20, 40, 60, 80, 100]}
        rowCount={total}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        columnVisibilityModel={gridState.columnVisibilityModel}
        onColumnVisibilityModelChange={(model) =>
          updateGridStatePart("columnVisibilityModel", model)
        }
        filterModel={gridState.filterModel}
        onFilterModelChange={(model) =>
          updateGridStatePart("filterModel", model)
        }
        sortModel={gridState.sortModel}
        onSortModelChange={(model) => updateGridStatePart("sortModel", model)}
        paginationMode="server"
        editMode="row"
        processRowUpdate={processRowUpdate}
        onProcessRowUpdateError={handleProcessRowUpdateError}
        experimentalFeatures={{ newEditingApi: true }}
        components={{
          Toolbar: () => (
            <CeDataGridToolbar
              shouldRenderAddButton={shouldRenderAddButton}
              shouldRenderRefreshButton={shouldRenderRefreshButton}
              onRefreshButtonClick={refetchPriceLists}
              addButtonClickHandler={() => {
                if (containerId) {
                  setPriceListFormValues({
                    ...priceListFormValues,
                    containerId: containerId,
                    flow: PricelistTypeEnum.CREATE_VARIANT,
                  });
                }
              }}
              addButtonDescription={t("common:add-vehicle")}
            />
          ),
        }}
        columns={columns}
        rows={data}
        disableSelectionOnClick
      />

      <PriceListDrawer
        vehiclesWithExistingPricelists={vehiclesWithPricelists}
        initialFormValues={priceListFormValues}
        pricelists={data}
        isLoading={isLoading}
        handleCreateNewPriceList={handleCreateNewPriceList}
        handleUpdatePriceList={handleUpdatePriceList}
        handleClosePriceListDrawer={handleClosePriceListDrawer}
        inheritFromExistingPricelist={inheritFromExistingPricelist}
      />

      <PriceListVariantDeleteModal
        flow={priceListDeleteValues.flow}
        isLoading={isLoading}
        priceListTitle={priceListDeleteValues.priceListTitle}
        handleClosePriceListModalDelete={handleClosePriceListModalDelete}
        handleDeletePriceList={onPriceListDelete}
      />
    </>
  );
};
