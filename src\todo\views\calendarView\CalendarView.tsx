import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import deLocale from "@fullcalendar/core/locales/de";
import enLocale from "@fullcalendar/core/locales/en-gb";
import frLocale from "@fullcalendar/core/locales/fr";
import nlLocale from "@fullcalendar/core/locales/nl";
import { Task, TaskFormFlow, UpdateTaskDto } from "src/common/types/tasks";
import { LocaleInput } from "@fullcalendar/core";
import { useTranslation } from "react-i18next";
import { Box } from "@mui/material";
import { isBefore, set, startOfDay } from "date-fns";
import interactionPlugin from "@fullcalendar/interaction";
import { DayEventContent } from "./DayEventContent";
import { useRecoilState } from "recoil";
import { taskFormValuesState } from "src/common/state/task";

interface CalendarViewProps {
  taskData: Task[];
  clickEventHandler: (task: Task) => void;
  onDragorResize: (task: UpdateTaskDto) => void;
}
const CalendarView = ({
  taskData,
  clickEventHandler,
  onDragorResize
}: CalendarViewProps) => {
  const [taskFormValues, setTaskFormValues] =
    useRecoilState(taskFormValuesState);
  const { t, i18n } = useTranslation(["manager", "common", "dispatcher"]);
  const localeText: { [key: string]: LocaleInput } = {
    en: enLocale,
    fr: frLocale,
    nl: nlLocale,
    de: deLocale
  };
  const selectedLanguage = i18n.language;

  const events = taskData.map((task) => {
    return {
      id: String(task.id),
      title: `Task ${task.id}`,
      start: task.startDate
        ? task.startDate
        : task.created_at
        ? new Date(task.created_at!).toISOString()
        : "",
      end: task.dueDate ? new Date(task.dueDate).toISOString() : "",
      description: task.description,
      editable: true,
      durationEditable: true,
      extendedProps: {
        id: task.id,
        description: task.description,
        scheduleFrom: task.startDate
          ? task.startDate
          : task.created_at
          ? task.created_at
          : "",
        scheduleTo: task.dueDate,
        labelColor: task.label?.color,
        created_at: task.created_at
      }
    };
  });

  const onEventClick = (id: number) => {
    const taskEvent = taskData.find((task) => task.id === id);
    if (taskEvent) {
      clickEventHandler(taskEvent);
    }
  };

  return (
    <Box className="taskCalendarView">
      <FullCalendar
        firstDay={1}
        events={events}
        aspectRatio={1}
        dayHeaderDidMount={(arg) => {
          const columnHeaderCell = arg.el;
          columnHeaderCell.style.fontWeight = "normal";
          setTimeout(() => window.dispatchEvent(new Event("resize")), 500);
        }}
        eventClick={(arg) => {
          return (
            (arg.el.style.backgroundColor = "transparent"),
            onEventClick(arg.event.extendedProps.id)
          );
        }}
        dateClick={(arg) => {
          const clickedDate = arg.date;
          const now = new Date();

          const startDate = set(clickedDate, {
            hours: now.getHours(),
            minutes: now.getMinutes(),
            seconds: now.getSeconds()
          });
          if (isBefore(startDate, startOfDay(now))) {
            return;
          }
          setTaskFormValues({
            ...taskFormValues,
            startDate,
            flow: TaskFormFlow.CREATE
          });
        }}
        editable={true}
        eventStartEditable={true}
        eventResizableFromStart={false}
        eventDrop={(dropInfo) => {
          const updatedEvent = dropInfo.event;
          const newStartDate = updatedEvent.start;
          const newDueDate = updatedEvent.end;
          const dateCreated = new Date(updatedEvent.extendedProps.created_at);
          if (newStartDate && newStartDate < dateCreated) {
            dropInfo.revert();
            return;
          }
          onDragorResize({
            taskId: Number(updatedEvent.id),
            startDate: newStartDate,
            dueDate: newDueDate!
          });
        }}
        height="auto"
        initialView="dayGridMonth"
        locale={localeText[selectedLanguage]}
        plugins={[dayGridPlugin, interactionPlugin]}
        eventMouseEnter={(info) => {
          info.el.style.backgroundColor = "transparent";
        }}
        eventDidMount={(arg) => {
          const { event, isStart, isEnd } = arg;
          arg.el.style.marginLeft = isStart ? "16px" : "";
          arg.el.style.marginRight = isEnd ? "16px" : "";
        }}
        eventMouseLeave={(info) => {
          info.el.style.backgroundColor = "";
        }}
        slotLabelDidMount={(arg) => {
          const columnHeaderCell = arg.el;
          columnHeaderCell.style.fontWeight = "lighter";
        }}
        views={{
          dayGridMonth: {
            buttonText: t("common:month"),
            slotDuration: { days: 1 },
            slotLabelInterval: { days: 1 },
            eventContent: DayEventContent,
            viewDidMount: (arg) => {
              setTimeout(() => window.dispatchEvent(new Event("resize")), 500);
            }
          }
        }}
      />
    </Box>
  );
};

export default CalendarView;
