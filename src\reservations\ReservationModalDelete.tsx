import { Alert, Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { DeleteModal } from "src/common/components";
import {
  DeleteOperatorDto,
  DeleteReservationDto,
  ReservationModalDeleteFlow,
} from "src/common/types";

interface ReservationModalDeleteProps {
  flow: ReservationModalDeleteFlow;
  reservationTitle?: string;
  isLoading: boolean;
  reservationId?: number;
  handleCloseReservationModalDelete: () => void;
  handleDeleteReservation: (args: DeleteReservationDto) => void;
}
export const ReservationModalDelete: React.FC<ReservationModalDeleteProps> = ({
  flow,
  reservationTitle,
  reservationId,
  isLoading,
  handleCloseReservationModalDelete,
  handleDeleteReservation,
}) => {
  const { t } = useTranslation(["common", "dispatcher"]);

  const onDeleteReservation = () => {
    if (reservationId) {
      handleDeleteReservation({ reservationId });
    }
  };

  const DeleteModalHelperText = () => (
    <Box>
      <Typography sx={{ marginBottom: 1 }}>
        {t("delete-reservation-message", {
          reservationId: reservationId || "unknown reservation",
        })}
      </Typography>
    </Box>
  );

  return (
    <DeleteModal
      isLoading={isLoading}
      title={t("common:delete-reservation")}
      helperText={DeleteModalHelperText()}
      isOpen={flow === "Delete"}
      handleSubmit={() => onDeleteReservation()}
      handleClose={handleCloseReservationModalDelete}
    />
  );
};
