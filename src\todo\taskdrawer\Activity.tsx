import { Box, Typography, Stack } from "@mui/material";
import { stringAvatar } from "src/common/utils/avatar";
import { CeAvatar } from "src/common/components";
import { useUser } from "src/common/api";
import { ActionType, TaskActivity } from "src/common/types/tasks";
import { formatDateToDayMonthYear } from "src/common/components/custom/CalendarEventWrapper";
interface ActivityProps {
  activity: TaskActivity;
}
const Activity = ({ activity }: ActivityProps) => {
  const {
    data: author,
    isSuccess: isAuthorSuccess,
    error: isAuthorError,
  } = useUser(activity.created_by!);

  const fullName = `${author?.firstName} ${author?.lastName}`;
  const dataFormated = activity?.created_at
    ? formatDateToDayMonthYear(activity?.created_at)
    : "";

  const getActivityDescription = () => {
    if (activity.actionType === ActionType.STATUS_UPDATE)
      return "updated the status";
    if (activity.actionType === ActionType.PRIORITY_UPDATE)
      return "changed the priority";
    if (activity.actionType === ActionType.ASSIGNEE_CHANGE)
      return "changed the assignee";
    if (activity.actionType === ActionType.LABEL_CHANGE)
      return "changed the label";
  };
  const activityDescription = getActivityDescription();
  return (
    <Box
      sx={{
        padding: 1.5,
      }}
    >
      <Stack direction="row" spacing={2} alignItems="flex-start">
        <CeAvatar
          size="large"
          {...stringAvatar(fullName)}
          sx={{
            backGroundColor: "grey.50",
          }}
        />
        <Box>
          <Stack direction="row" spacing={1} alignItems="center">
            <Typography variant="body1" fontWeight="bold">
              {fullName}
            </Typography>
            <Typography variant="body2" sx={{ color: "#616161" }}>
              {dataFormated}
            </Typography>
          </Stack>
          <Typography variant="body2" fontStyle="italic" sx={{ marginTop: 1 }}>
            {activityDescription}
          </Typography>
        </Box>
      </Stack>
    </Box>
  );
};

export default Activity;
