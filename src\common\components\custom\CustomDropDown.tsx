import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Box,
  ClickAwayListener,
  Divider,
  Fade,
  Grid,
  List,
  ListItem,
  ListItemButton,
  Typography,
  useTheme,
} from "@mui/material";
import { useClients } from "src/common/api/client";
import { useDebounce } from "use-debounce";
import { Client } from "src/common/types/client";
import { CeTextField } from "./company/CeTextField";
import { CePaper } from "./company/CePaper";

interface CustomDropDownProps {
  onClientClick: (client: Client) => void;
}

const CustomDropdown = ({ onClientClick }: CustomDropDownProps) => {
  const theme = useTheme();
  const [clientSearch, setClientSearch] = useState<string>("");
  const [searchValue] = useDebounce(clientSearch, 500);
  const { t, i18n } = useTranslation(["manager", "common"]);
  const { data: clientSearchResults } = useClients(
    { searchText: searchValue, expressions: [], sortModel: [] },
    Bo<PERSON>an(searchValue)
  );

  const handleCloseDropdown = () => {
    setClientSearch("");
  };

  const handleInputValueChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setClientSearch(event.target.value);
  };

  const onListButtonClick = (client: Client) => {
    onClientClick(client);
    handleCloseDropdown();
  };

  return (
    <ClickAwayListener onClickAway={handleCloseDropdown}>
      <Box sx={{ position: "relative" }}>
        <CeTextField
          label={t("common:search-clients")}
          name="search-clients"
          value={clientSearch}
          onChange={handleInputValueChange}
          InputLabelProps={{ shrink: true }}
          sx={{
            "& .MuiInputPlaceholder": {
              fontSize: "1.75rem !important",
            },
          }}
          required
          variant="outlined"
          size="small"
          fullWidth
          placeholder={`${t("common:first-name")} | ${t(
            "common:last-name"
          )} | ${t("common:phone-number")} | ${t("common:company-name")}   `}
        />
        <Fade in={Boolean(searchValue)} mountOnEnter unmountOnExit>
          <CePaper
            sx={{
              position: "absolute",
              zIndex: "100",
              width: "100%",
              p: 0,
              overflow: "hidden",
            }}
          >
            <List
              sx={{
                p: 0,
                display: "flex",
                flexDirection: "column",
                gap: "2px",
              }}
            >
              {clientSearchResults?.data?.map((option, index) => (
                <>
                  <ListItemButton
                    key={option.id}
                    onClick={() => onListButtonClick(option)}
                    sx={{
                      p: "4px 12px",
                    }}
                  >
                    <Grid
                      container
                      alignItems="flex-start"
                      columnGap={2}
                      sx={{ opacity: 0.7 }}
                    >
                      <Grid item xs={2}>
                        <Typography variant="body2">{option.name}</Typography>
                      </Grid>
                      <Grid item xs={2}>
                        <Typography variant="body2">
                          {option.lastName}
                        </Typography>
                      </Grid>
                      <Grid item xs={2}>
                        <Typography variant="body2">
                          {option.phoneNumber}
                        </Typography>
                      </Grid>
                      <Grid item xs={2}>
                        <Typography variant="body2">
                          {option.companyName}
                        </Typography>
                      </Grid>
                    </Grid>
                  </ListItemButton>
                  <Divider component="li" />
                </>
              ))}
              {clientSearchResults?.totalCount === 0 && (
                <ListItem>No client found</ListItem>
              )}
            </List>
          </CePaper>
        </Fade>
      </Box>
    </ClickAwayListener>
  );
};

export default CustomDropdown;
