import {
  Box,
  ImageList,
  ImageListItem,
  Button,
  Grid,
  Typography,
} from "@mui/material";
import { Vehicle } from "src/common/types";
import { FC } from "react";
import TipsAndUpdatesOutlinedIcon from "@mui/icons-material/TipsAndUpdatesOutlined";
import { VehicleItem } from "./VehicleItem";
import { useTranslation } from "react-i18next";
import { VehicleListPagination } from "src/common/components/custom/VehiclePagination";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import { CeButton } from "src/common/components";

export interface VehicleCardsProps {
  vehicles: Vehicle[];
  page: number;
  total: number;
  shouldShowSimilarResultsButton: boolean;
  handleChangePage: (event: React.ChangeEvent<unknown>, value: number) => void;
  handleVehicleSelect: (vehicle: Vehicle) => void;
  handleVehicleModal: (vehicle: Vehicle) => void;
  handleShowSimilarResults?: () => void;
}
export const VehicleCards: FC<VehicleCardsProps> = ({
  vehicles,
  page,
  total,
  shouldShowSimilarResultsButton,
  handleChangePage,
  handleVehicleSelect,
  handleVehicleModal,
  handleShowSimilarResults,
}) => {
  const { t } = useTranslation(["common", "manager", "dispatcher"]);

  return (
    <Box
      sx={{
        minHeight: "calc(100vh - 200px)",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box sx={{ flexGrow: 1, marginBottom: "1.5rem" }}>
        {!vehicles.length && (
          <Box
            sx={{
              marginTop: 5,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <TipsAndUpdatesOutlinedIcon />
            <Typography>{t("common:no-available-vehicles")}</Typography>
          </Box>
        )}
        <Grid container spacing={2}>
          {vehicles.map((vehicle) => (
            <Grid item key={vehicle.id} xs={12} sm={6} md={4} lg={3} xl={2.4}>
              <VehicleItem
                vehicle={vehicle}
                handleVehicleSelect={handleVehicleSelect}
                handleVehicleModal={handleVehicleModal}
              />
            </Grid>
          ))}
        </Grid>
      </Box>
      {shouldShowSimilarResultsButton ? (
        <Box sx={{ marginInline: "auto" }}>
          <CeButton
            variant="text"
            onClick={handleShowSimilarResults}
            endIcon={<ArrowDownwardIcon sx={{ fontSize: "small" }} />}
          >
            <Typography color="info">Show similar results</Typography>
          </CeButton>
        </Box>
      ) : null}
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          marginTop: 6,
          marginBottom: 4,
        }}
      >
        <VehicleListPagination
          page={page}
          total={total}
          handleChangePage={handleChangePage}
        />
      </Box>
    </Box>
  );
};
