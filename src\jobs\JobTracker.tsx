import {
  Check<PERSON>ir<PERSON>,
  KeyboardArrowLeft,
  KeyboardArrowRight,
  Pending,
} from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import {
  Box,
  Button,
  Chip,
  Divider,
  MobileStepper,
  Typography,
} from "@mui/material";
import { JOB_STEPS } from "src/common/constants";
import { useNavigate, useParams } from "react-router-dom";
import { useReservation } from "src/common/api";
import {
  useCreateJobLocation,
  useUpdateJob,
  useUpdateReport,
  useUpdateSecurityValidation,
} from "src/common/api/job";
import { useRecoilState } from "recoil";
import { JobReportForm } from "./JobReportForm";
import SignatureForm from "./SignatureForm";
import { useEffect } from "react";
import { JobStatus } from "src/common/types";
import { SecurityValidationForm } from "./SecurityValidationForm";
import {
  jobReportFormValuesState,
  jobSecurityValidationFormValuesState,
} from "src/common/state";
import { useGeolocated } from "react-geolocated";
import toast from "react-hot-toast";
import { buttonTextTransform } from "src/common/components/custom/customCss";

export const JobTracker = () => {
  const {
    coords,
    isGeolocationAvailable,
    isGeolocationEnabled,
    positionError,
  } = useGeolocated({
    positionOptions: {
      enableHighAccuracy: false,
    },
    userDecisionTimeout: 5000,
  });
  const isGeolocationReady = isGeolocationAvailable && isGeolocationEnabled;
  const params = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation(["common", "operator"]);
  const reservationId = Number(params.id);

  const [jobReportFormValues, setJobReportFormValues] = useRecoilState(
    jobReportFormValuesState
  );

  const [jobSecurityValidationFormValues, setJobSecurityValidationFormValues] =
    useRecoilState(jobSecurityValidationFormValuesState);

  const { data: reservation, isLoading: isReservationLoading } = useReservation(
    reservationId,
    Boolean(reservationId)
  );

  const job = reservation?.job;

  const { mutate: handleUpdateJob, isLoading: isUpdatingJob } = useUpdateJob();
  const { mutate: handleCreateJobLocation, isLoading: isCreateJobLocation } =
    useCreateJobLocation();
  const {
    mutateAsync: handleUpdateSecurityValidation,
    isLoading: isSecurityValidationLoading,
  } = useUpdateSecurityValidation();

  const { mutateAsync: handleUpdateReport, isLoading: isUpdateReportLoading } =
    useUpdateReport();

  const isLoading =
    isReservationLoading ||
    isUpdatingJob ||
    isSecurityValidationLoading ||
    isUpdateReportLoading ||
    isCreateJobLocation;

  const activeStep = t(
    JOB_STEPS?.find((step) => step.progress === job?.progress)?.label || ""
  );

  const currentStepIndex =
    JOB_STEPS.findIndex((step) => step.progress === job?.progress) || 0;

  const isJobReportStep =
    job?.progress ===
    JOB_STEPS.find((step) => step.name === JobStatus.REPORT)?.progress;

  const isSecurityValidationStep =
    job?.progress ===
    JOB_STEPS.find((step) => step.name === JobStatus.SECURITY_VALIDATION)
      ?.progress;

  const isSignatureStep =
    job?.progress ===
    JOB_STEPS.find((step) => step.name === JobStatus.SIGNATURE)?.progress;

  useEffect(() => {
    if (!isGeolocationAvailable) {
      toast.error("Geolocation is not supported by this browser.");
    } else if (!isGeolocationEnabled) {
      toast.error(
        "Geolocation is not enabled. Please enable it in your browser settings."
      );
    } else if (positionError) {
      toast.error("An error occurred while accessing your location.");
    }
  }, [isGeolocationAvailable, isGeolocationEnabled, positionError]);

  const handleNext = async () => {
    const nextStep = JOB_STEPS[currentStepIndex + 1].name;
    const currentStep = JOB_STEPS[currentStepIndex].name;

    if (currentStep && currentStep === JobStatus.SECURITY_VALIDATION) {
      // handle update security validation here
      await handleUpdateSecurityValidation({
        ...jobSecurityValidationFormValues,
        jobId: job?.id,
      });
    }

    if (currentStep && currentStep === JobStatus.REPORT) {
      await handleUpdateReport({
        ...jobReportFormValues,
        jobId: job?.id!,
      });
    }

    handleUpdateJob({
      status: nextStep,
      jobId: job?.id!,
      reservationId,
    });
    handleCreateJobLocation({
      jobId: job?.id!,
      state: job?.state!,
      progress: job?.progress!,
      status: nextStep,
      coords: {
        longitude: coords?.longitude!,
        latitude: coords?.latitude!,
      },
    });
  };

  const handleBack = () => {
    const nextStep = JOB_STEPS[currentStepIndex - 1].name;

    handleUpdateJob({
      status: nextStep,
      jobId: job?.id!,
      reservationId,
    });
    handleCreateJobLocation({
      jobId: job?.id!,
      state: job?.state!,
      progress: job?.progress!,
      status: nextStep,
      coords: {
        longitude: coords?.longitude!,
        latitude: coords?.latitude!,
      },
    });
  };

  const handleFinish = () => {
    handleUpdateJob({
      status: "COMPLETE",
      jobId: job?.id!,
      reservationId,
    });
    handleCreateJobLocation({
      jobId: job?.id!,
      state: job?.state!,
      progress: job?.progress!,
      status: JobStatus.COMPLETE,
      coords: {
        longitude: coords?.longitude!,
        latitude: coords?.latitude!,
      },
    });
    navigate("/upcoming-jobs");
  };

  useEffect(() => {
    if (isJobReportStep && job) {
      setJobReportFormValues({
        amountOfConcrete: job?.amountOfConcrete || 0,
        cleaningTime: 0,
        flexiblePipeLength80Mm: job?.flexiblePipeLength80Mm || 0,
        flexiblePipeLength90Mm: job?.flexiblePipeLength90Mm || 0,
        rigidPipeLength100Mm: job?.rigidPipeLength100Mm || 0,
        rigidPipeLength120Mm: job?.rigidPipeLength120Mm || 0,
        secondTechnician: job?.enlistSecondTechnician || false,
        extraCementBags: job?.extraCementBag || false,
        cementBags: job?.units || 0,
        flow: "Open",
      });
    }
  }, [job, isJobReportStep, setJobReportFormValues]);

  useEffect(() => {
    if (isSecurityValidationStep && job) {
      setJobSecurityValidationFormValues({
        isElectricalRisk: job.isElectricalRisk || false,
        electricalRiskComment: job.electricalRiskComment || "",
        electricalRiskKey: job.electricalRiskKey || "",
        electricalRiskFile: null,
        isAccessCompliance: job.isAccessCompliance,
        accessComplianceComment: job.accessComplianceComment || "",
        accessComplianceKey: job.accessComplianceKey || "",
        accessComplianceFile: null,
        isParkingCompliance: job.isParkingCompliance || false,
        parkingComplianceComment: job.parkingComplianceComment || "",
        parkingComplianceKey: job.parkingComplianceKey || "",
        parkingComplianceFile: null,
        isTerrainStability: job.isTerrainStability || false,
        terrainStabilityComment: job.terrainStabilityComment || "",
        terrainStabilityKey: job.terrainStabilityKey || "",
        terrainStabilityFile: null,
        flow: "Open",
      });
    }
  }, [job, isSecurityValidationStep, setJobSecurityValidationFormValues]);

  const renderStepsOrForm = () => {
    if (isSecurityValidationStep) {
      return (
        <SecurityValidationForm
          initialFormValues={jobSecurityValidationFormValues}
          setInitialFormValues={setJobSecurityValidationFormValues}
          isLoading={isLoading}
        />
      );
    }

    if (isJobReportStep) {
      return (
        <JobReportForm
          initialFormValues={jobReportFormValues}
          setInitialFormValues={setJobReportFormValues}
          isLoading={isLoading}
        />
      );
    }

    if (isSignatureStep) {
      return (
        <SignatureForm isLoading={isLoading} handleUpdateSignature={() => {}} />
      );
    }

    return (
      <Box mb={10}>
        {JOB_STEPS.filter(
          (step) => step.progress !== null && step.progress !== 0
        ).map((step) => (
          <Box
            key={step.progress}
            sx={{ py: 2, display: "flex", justifyContent: "space-between" }}
          >
            {step.name !== "COMPLETE" ? (
              <Typography variant="body1" color={"text.secondary"}>
                {t(step.label)}
              </Typography>
            ) : null}

            {job?.progress ? (
              <>
                {step.progress !== null && job.progress > step.progress && (
                  <CheckCircle
                    sx={{
                      color: (theme) => theme.palette.secondary.main,
                    }}
                  />
                )}
                {step.progress !== null && job?.progress === step.progress && (
                  <Pending
                    sx={{
                      color: (theme) => theme.palette.primary.light,
                    }}
                  />
                )}
              </>
            ) : null}
          </Box>
        ))}
      </Box>
    );
  };

  return (
    <Box>
      <Box
        display="flex"
        py={1}
        justifyContent="space-between"
        alignItems={"center"}
      >
        <Typography variant="body1" fontWeight={700}>
          {t("common:job-tracker")}
        </Typography>

        <Chip label={activeStep} />
      </Box>
      <Divider />

      {renderStepsOrForm()}

      {job?.progress !== 96 ? (
        <MobileStepper
          activeStep={currentStepIndex}
          sx={{ background: (theme) => theme.palette.background.paper }}
          backButton={
            <Button
              disabled={(job?.progress || 0) === 0 || !isGeolocationReady}
              size="medium"
              onClick={handleBack}
              sx={buttonTextTransform}
            >
              <KeyboardArrowLeft />
              {t("back")}
            </Button>
          }
          nextButton={
            <Button
              sx={buttonTextTransform}
              disabled={isLoading || !isGeolocationReady}
              size="medium"
              onClick={handleNext}
            >
              {t("next")}
              <KeyboardArrowRight />
            </Button>
          }
          position="bottom"
          steps={13}
          variant="progress"
        />
      ) : (
        <Box
          position="fixed"
          bottom={0}
          left={0}
          width="100%"
          bgcolor="background.paper"
          padding={1}
          borderColor="divider"
        >
          <Box display={"flex"} justifyContent="center">
            <Button
              color="primary"
              variant="contained"
              size="medium"
              onClick={handleFinish}
              sx={buttonTextTransform}
            >
              {t("common:finish")}
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
};
