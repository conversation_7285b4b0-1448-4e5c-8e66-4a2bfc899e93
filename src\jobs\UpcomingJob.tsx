import { getCurrentUser } from "src/common/api";
import { useWindowSize } from "@react-hook/window-size";
import {
  Box,
  Button,
  CircularProgress,
  Grid,
  Typography,
  useTheme
} from "@mui/material";
import { useTranslation } from "react-i18next";

import { format } from "date-fns";
import {
  AccessTimeFilled,
  CropFree,
  LocationOn,
  Store,
  Event
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { useUpcomingReservationJob } from "src/common/api/job";
import { buttonTextTransform } from "src/common/components/custom/customCss";
import { CePaper } from "src/common/components";
const style = {
  marginTop: 2
};

const styleIcon = {
  fontSize: 22,
  marginRight: 1
};
export const UpcomingJob = () => {
  const theme = useTheme();
  const [, height] = useWindowSize();
  const navigate = useNavigate();
  const currentUser = getCurrentUser();

  const { t } = useTranslation(["operator", "common"]);

  const shouldEnableApi = Boolean(currentUser?.id);

  const { data: reservation, isLoading: isReservationsLoading } =
    useUpcomingReservationJob(currentUser?.id, shouldEnableApi);

  return (
    <Box>
      <Typography variant="h6">{t("upcoming-job")}</Typography>
      {isReservationsLoading ? (
        <Box
          alignItems="center"
          display="flex"
          justifyContent="center"
          minHeight={256}
        >
          <CircularProgress />
        </Box>
      ) : !reservation ? (
        <Typography>{t("no-upcoming-job")}</Typography>
      ) : (
        <CePaper sx={{ padding: 1 }} variant="outlined">
          <Grid container sx={style}>
            <Grid item display="inline-flex" xs={12}>
              <Store sx={styleIcon} color="primary" />
              <Typography fontWeight="bold">{t("common:company")}</Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography>{reservation?.clientDetails?.companyName}</Typography>
            </Grid>
          </Grid>
          <Grid container sx={style}>
            <Grid item display="inline-flex" xs={12}>
              <Event sx={styleIcon} color="primary" />
              <Typography fontWeight="bold">{t("common:date")}</Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography>
                {reservation.dateFrom
                  ? format(new Date(reservation.dateFrom), "dd/MM/yyyy")
                  : "-"}
              </Typography>
            </Grid>
          </Grid>
          <Grid container sx={style}>
            <Grid item display="inline-flex" xs={12}>
              <AccessTimeFilled sx={styleIcon} color="primary" />
              <Typography fontWeight="bold">{t("common:time")}</Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography>
                {reservation.dateFrom && reservation.dateTo
                  ? format(new Date(reservation.dateFrom), "HH:mm") +
                    " - " +
                    format(new Date(reservation.dateTo), "HH:mm")
                  : "-"}
              </Typography>
            </Grid>
          </Grid>
          <Grid container sx={style}>
            <Grid item display="inline-flex" xs={12}>
              <LocationOn sx={styleIcon} color="primary" />
              <Typography fontWeight="bold">{t("common:address")}</Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography>{reservation.siteAddress}</Typography>
            </Grid>
          </Grid>
          <Grid container sx={style}>
            <Grid item display="inline-flex" xs={12}>
              <CropFree sx={styleIcon} color="primary" />
              <Typography fontWeight="bold">
                {t("common:concrete-amount")}
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography>{reservation.job?.amountOfConcrete} m³</Typography>
            </Grid>
          </Grid>

          <Box
            display="center"
            justifyContent="center"
            sx={{ marginTop: 2, marginBottom: 2 }}
          >
            <Button
              color="primary"
              variant="contained"
              onClick={() => {
                navigate(`/job-details/${reservation.id}`);
              }}
              sx={buttonTextTransform}
            >
              {t("view-details")}
            </Button>
          </Box>
        </CePaper>
      )}
    </Box>
  );
};
