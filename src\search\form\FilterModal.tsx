import { AxiosError } from "axios";
import { UseMutateFunction } from "react-query";
import { MainModal } from "src/common/components";
import {
  ManagerFormValues,
  Vehicle,
  FilterVehiclesDto,
  FilterVehiclesFormValues,
} from "src/common/types";
import { FilterForm } from "./FilterForm";

interface FIlterModalProps {
  isLoading: boolean;
  initialFormValues: FilterVehiclesFormValues;
  handleCloseFilterModal: () => void;
  handleFilterVehicle?: UseMutateFunction<
    Vehicle,
    AxiosError<FilterVehiclesDto, FilterVehiclesDto> | Error,
    FilterVehiclesDto,
    () => void
  >;
}
export const FilterModal: React.FC<FIlterModalProps> = ({
  isLoading,
  initialFormValues,
  handleCloseFilterModal,
  handleFilterVehicle,
}) => {
  return (
    <MainModal
      title={`${initialFormValues.flow || ""} Manager`}
      isOpen={!!initialFormValues.flow}
      handleClose={handleCloseFilterModal}
    >
      <FilterForm
        initialFormValues={initialFormValues}
        isLoading={isLoading}
        handleFilterVehicle={handleFilterVehicle}
        handleClose={handleCloseFilterModal}
      />
    </MainModal>
  );
};
