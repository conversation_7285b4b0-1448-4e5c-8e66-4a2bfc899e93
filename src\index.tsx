import React from "react";
import ReactDOM from "react-dom";
import { QueryCache, QueryClient, QueryClientProvider } from "react-query";
import { RecoilRoot } from "recoil";

import "./index.css";
import reportWebVitals from "./app/reportWebVitals";
import './i18n'
import { Router } from "./app/Router";
import { Theme } from "./app/Theme";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
  queryCache: new QueryCache({
    onError: async (error: any) => {
      if (
        error.request &&
        (error.request.status === 401 || error.request.status === 403)
      ) {
        window.location.reload(); // using browser native features instead of react router to get a full page refresh to pick up any new deployments
      }
    },
  }),
});


ReactDOM.render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <RecoilRoot>
        <Theme>
          <Router />
        </Theme>
      </RecoilRoot>
    </QueryClientProvider>
  </React.StrictMode>,
  document.getElementById("root")
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
