import { CommonEntity } from "./common";
import { Role, Status, UserEnumDisplay } from "./user";

export interface Dispatcher extends CommonEntity {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  status: Status | null;
  role: Role | null;
}

export interface DispatcherFormValues {
  dispatcherId?: number;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  status: UserEnumDisplay | null;
  role: UserEnumDisplay | null;
  flow: DispatcherModalFlow;
}

export interface CreateDispatcherDto {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  status: number;
  role: number;
}

export interface UpdateDispatcherDto
  extends Omit<CreateDispatcherDto, "password"> {
  dispatcherId: number;
}

export interface DeleteDispatcherDto {
  dispatcherId: number;
}

export interface DeleteDispatcherModalValues {
  dispatcherId?: number;
  dispatcherTitle?: string;
  flow: DispatcherModalDeleteFlow;
}

export type DispatcherModalFlow = "Create" | "Update" | null;
export type DispatcherModalDeleteFlow = "Delete" | null;
