import { isAuthenticated } from "src/common/api/auth";
import { Role, UserRole, userTypeHierarchy } from "src/common/types";

interface FeatureFlagProps {
  flags: UserRole[];
  children: React.ReactNode;
}

export const FeatureFlag: React.FC<FeatureFlagProps> = ({
  flags,
  children,
}) => {
  const { role } = isAuthenticated();
  if (!role) {
    return null;
  }

  const userRole = userTypeHierarchy[role - 1];

  const hasAccess = (flags: UserRole[], userType?: UserRole): boolean => {
    const containFlags = userTypeHierarchy.some((type) => flags.includes(type));

    if (containFlags && userType) {
      const shouldHaveAccess = flags.includes(userType);
      return shouldHaveAccess;
    }

    return false;
  };

  if (!hasAccess(flags, userRole)) {
    return null;
  }

  return <>{children}</>;
};
