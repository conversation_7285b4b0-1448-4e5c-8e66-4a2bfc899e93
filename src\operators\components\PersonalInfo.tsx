import React from 'react';
import { Box, Chip, Stack, Typography, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

export interface PersonalInfoField {
  label: string;
  value: string;
}

interface PersonalInfoProps {
  fields: PersonalInfoField[];
}

const PersonalInfo: React.FC<PersonalInfoProps> = ({ fields }) => {
  const theme = useTheme();
  const { t } = useTranslation("common");
  return (
    <Stack sx={{mx: 2, my:4}}>
      {fields.map((field) => (
        <Box
          key={field.label}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            backgroundColor: theme.palette.grey[50],
            borderRadius: theme.shape.borderRadius,
            padding: 2,
            mb:2
          }}
        >
          <Typography variant="subtitle2" color="text.secondary">
            {field.label}
          </Typography>
          {field.label === t("Status") ? (
            <Chip
              sx={{ m: 0.5, ml: 0, fontSize: '14px', textTransform: 'lowercase' }}
              color={field.value === 'Active' ? 'success' : 'default'}
              component="span"
              size="small"
              label={field.value}
            />
          ) : (
            <Typography variant="subtitle2" fontWeight={500}>
              {field.value}
            </Typography>
          )}
        </Box>
      ))}
    </Stack>
  );
};

export default PersonalInfo;
