import { GridSortModel } from "@mui/x-data-grid";
import { CommonEntity, PossibleSortDir } from "./common";
import { Expression } from "./filters";

export interface Client extends CommonEntity {
  name: string | null;
  lastName: string | null;
  email: string | null;
  phoneNumber: string | null;
  companyName: string | null;
  companyVatNumber: string | null;
  id: string | null;
}

export interface GetClientDto {
  sortModel?: GridSortModel;
  expressions?: Expression[];
  relations?: string[];
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string | null;
}

export interface ClientsWithCount {
  totalCount: number;
  data: Client[];
}

export interface CreateClientDto extends Omit<Client, "id"> {}

export interface UpdateClientDto extends Client {}

export type ClientModalFlow = "Create" | "Update" | null;
export type ClientDeleteModalFlow = "Delete" | null;

export interface ClientFormValues extends Client {
  flow: ClientModalFlow;
}
export interface ClientDeleteModalValues {
  id: string | null;
  flow: ClientDeleteModalFlow;
}
