import React from "react";
import { styled, TextField, TextFieldProps } from "@mui/material";

export const CeTextField = styled(
  ({ className, sx, ...props }: TextFieldProps) => (
    <TextField className={className} sx={sx} {...props} />
  )
)(({ theme }) => ({
  "& .MuiOutlinedInput-root": {
    borderRadius: "8px",
    "& fieldset, &:hover fieldset, &.Mui-focused fieldset": {
      borderWidth: "2px",
      borderColor: theme.palette.action,
    },
    "&.Mui-disabled fieldset": {
      borderColor:
        theme.palette.mode === "light"
          ? theme.palette.action.disabled
          : theme.palette.action.disabled,
    },
  },

  "& .MuiFilledInput-root": {
    borderRadius: "8px",
    "&:before, &:after": {
      borderBottomWidth: "2px",
      borderColor: theme.palette.action,
    },
    "&.Mui-disabled:before": {
      borderBottomColor:
        theme.palette.mode === "light"
          ? theme.palette.action.disabled
          : theme.palette.action.disabled,
    },
  },

  "& .MuiInput-root": {
    "&:before, &:after": {
      borderBottomWidth: "2px",
      borderColor: theme.palette.action,
    },
    "&.Mui-disabled:before": {
      borderBottomColor:
        theme.palette.mode === "light"
          ? theme.palette.action.disabled
          : theme.palette.action.disabled,
    },
  },

  "& .MuiInputLabel-root": {
    color:
      theme.palette.mode === "light"
        ? theme.palette.action
        : theme.palette.primary.contrastText,
    "&.Mui-disabled": {
      color: theme.palette.action.disabled,
    },
  },

  "& .MuiInputAdornment-root": {
    color: theme.palette.action.disabled,
  },
}));
