{"access-compliance": "Access compliance", "action": "Action", "actions": "Actions", "activities": "Activities", "add-additional-tier": "Add additional Tier", "add-articulation": "Add articulation", "add-company-to-favorites": "Add company to favorites", "add-date": "Add date", "add-dispatcher": "Add Dispatcher", "add-label": "Add label", "add-new-label": "Add new label", "add-new-operator": "Add new operator", "add-new-vehicle": "Add new vehicle", "add-operator": "Add operator", "add-pricelist": "Add Price list", "add-vehicle-to-pricelist": "Add vehicle to price list", "add-pump-model": "Add Pump Model", "add-reservation": "Add reservation", "add-tariff": "Add additional tariff", "add-task": "Add task", "add-tax-bracket": "Add new tax bracket", "add-unavailable-period": "Add unavailable period", "add-variant": "<PERSON><PERSON>", "add-vehicle-type": "Add Vehicle Type", "add-vehicle": "Add Vehicle", "additional-address-line": "Additional address line", "additional-hour-beyond": "Additional hour", "additional-hour": "Additional hour", "additional-information": "Additional information", "address-line": "Address line", "address": "Address", "admin-cleanup": "Admin and cleanup", "advanced-view": "Planning view", "all": "All", "amount-of-concrete": "Amount of concrete", "amount": "Amount", "assignee": "Assignee", "at-least-one-dispatcher-required": "At least one dispatcher must be selected", "authorized-weight": "Authorized weight", "available-flexible-pipe-length-100": "Available rigid pipe (100 mm)", "available-flexible-pipe-length-120": "Available rigid pipe (120 mm)", "available-flexible-pipe-length-80": "Available flexible pipe (80 mm)", "available-flexible-pipe-length-90": "Available flexible pipe (90 mm)", "available-rigid-pipe-length": "Available rigid pipe length", "average-job-completion-time": "Average job completion time", "average-m3-per-hour": "Average m³ per hour", "average-m3-per-job": "Average m³ per job", "bac-exit-reverse": "BAC exit reverse", "bac-exit": "BAC exit", "back": "Back", "backup-package": "Backup package", "backup-pump-package": "Backup pump package", "backupPumpPackage": "Backup pump package", "bag": "bag", "balance": "Balance", "bar": "bar", "barbotine": "<PERSON><PERSON><PERSON>", "below-span-limit": "Span too small", "billed-to": "Billed to", "billed": "Billed", "billing-address": "Billing address", "board": "Board", "boom-rotation": "Boom rotation", "boom-section-articulation": "Boom section articulation", "boom-unfolding-system": "Boom unfolding system", "bulk-modal-title": "Generate Bulk Invoice", "boom-specifications": "Boom specifications", "calendar": "Calendar", "cancel": "Cancel", "late-cancellation-fee": "Late cancellation fee ", "standard-cancellation-fee": "Standard cancellation fee", "standard-cancellation-period": "Standard cancellation period", "late-cancellation-period": "Late cancellation period", "incur-cancelation-fee": "This will incur a cancelation fee of {{cancelationFee}} EUR.", "confirm-reservation-cancelation": "Are you sure you want to cancel reservation {{reservationTitle}}?", "cancelled": "Cancelled", "cancel-fee-as-day-contract-fee": "Same as Day contract fee", "cannot-select-past-date-from": "Cannot select a past date for date from", "cannot-select-past-date-to": "Cannot select a past date for date to", "cannot-update-delete-operator": "You cannot update or delete this operator because he/she is already selected for a reservation.", "cannot-update-delete-vehicle": "You cannot update or delete this vehicle because it is already reserved.", "cement-bag-price": "Cement bag price", "cement-bags": "Cement bags", "change-email": "Change your e-mail address", "change-password": "Change password", "chartered-equipment": "Chartered equipment", "check-inbox": "Please check your inbox for directions on how to reset your password", "city": "City", "cleaning-fee": "Cleaning fee", "cleaning-price": "Cleaning price", "cleaning-time": "Cleaning time", "cleaning": "Cleaning:", "cleanup-in-concrete-plant": "Cleanup in concrete plant", "delay": "Delay", "clear": "Clear", "client-company-name-exceeds": "The clients's company name length must not exceed 100 characters", "client-company-name": "Client company name", "client-companyVat-number-exceeds": "The clients's VAT number length must not exceed 100 characters", "client-details": "Client details", "client-email-exceeds": "The clients's e-mail length must not exceed 100 characters", "client-email": "Client email", "client-info": "Client info", "client-lastName-exceeds": "The clients's last name length must not exceed 100 characters", "client-name-exceeds": "The clients's first name length must not exceed 100 characters", "client-name": "Name", "client-phone-number-exceeds": "The clients's phone number length must not exceed 100 characters", "client-phone-number": "Client phone number", "client": "Client", "client-company": "Client company", "clients": "Clients", "comment-exceeds": "Comment exceeds 500 characters", "comment": "Comment", "comments": "Comment", "company-address": "Company address", "company-email-billing": "Company e-mail address (billing)", "company-email-contact": "Company e-mail address (contact)", "company-email-planning": "Company e-mail address (planning)", "company-name-exceeds": "The company name length must not exceed 100 characters in length", "company-name-type": "Company name & type", "company-name": "Company name", "company-phone-number": "Company phone number", "company-vat-number": "Company VAT number", "company": "Company", "complete": "Complete", "completed-jobs": "Completed jobs", "completed": "Completed", "completion-time": "Completion time:", "concrete-amount": "Concrete amount", "confirm-account": "Confirm my account", "confirm-action-needed": "Confirmation needed", "confirm-delete-operator": "Are you sure you want to delete this operator?", "confirm-delete-unavailable-period": "Are you sure you want to delete this unavailable period?", "confirm-delete-vehicle": "Are you sure you want to delete this vehicle?", "confirm-default-pricelist": "Are you sure you want to set this price list as default?", "confirm-reservation-deletion": "Confirm reservation deletion?", "confirm-reservation": "Confirm reservation", "confirm": "Confirm", "confirm-reservation-changes": "Confirm Reservation Changes", "are-you-sure": "Are you sure you want to make this change?", "confirmation": "Confirmation", "contact-address": "Contact address", "contacts": "Contacts", "container": "Container", "contingencies": "Contingencies", "continue": "Continue", "contract-price-weekdays": "Contract price (Weekdays)", "contract-price-weekend": "Contract price (Weekend)", "contract-price": "Contract price", "contracts": "Contracts", "copy-from": "Copy from", "cost-estimation": "Cost Estimation", "cost": "Cost", "country": "Country", "coverage": "Coverage", "create-client": "Create Client", "create-vehicle-type": "Create Vehicle Type", "create-vehicle-model": "Create Vehicle Model", "current-status": "Current status", "ciaw": "CIAW (Check in At Work)", "dark-mode": "Dark mode", "dashboard": "Dashboard", "date-created": "Created Date", "expected-date": "Expected Date", "date-from-error": "Date From must be before Date To", "date-from": "From", "date-of-birth": "Date of birth", "date-time": "Date/Time", "date-to-error": "Date To must be after Date From", "date-to": "Date to", "date": "Date", "day-contract-duration": "Day contract duration", "day-contract-fee": "Day contract fee", "day-contract-overtime-rate": "Day contract overtime rate", "day-contract": "Day contract", "day": "Day", "days": "Days", "default-language": "Default language", "default-pricelist": "Default pricelist", "delete-client-confirm": "Are you sure you want to delete this customer?", "delete-client": "Delete client", "delete-dispatcher-message": "Are you sure you want to delete dispatcher {{dispatcherTitle}}? This action is irreversible!", "delete-dispatcher": "Delete dispatcher", "delete-manager-message": "Are you sure you want to delete manager {{managerTitle}}? This action is irreversible!", "delete-partner": "Are you sure you want to delete this partner? This action is irreversible!", "delete-operator-message": "Are you sure you want to delete operator {{operatorTitle}}? This action is irreversible!", "delete-unavailable-period-message": "Are you sure you want to delete unavailable period {{timeOffTitle}}? This action is irreversible!", "delete-price-list": "Delete pricelist", "delete-vehicle": "Delete vehicle", "delete-pricelist-message": "Are you sure you want to delete {{entityType}}  {{priceListTitle}}? This action is irreversible!", "delete-reservation-message": "Are you sure you want to delete reservation {{reservationId}}? This action is irreversible!", "delete-reservation": "Delete reservation", "delete-unavailable-period": "Delete unavailable period", "delete-vehicle-message": "Are you sure you want to delete vehicle {{vehicleTitle}}? This action is irreversible!", "delete-vehicle-model-message": "Are you sure you want to delete vehicle model {{vehicleModelTitle}}? This action is irreversible!", "delete-vehicle-model": "Delete vehicle model", "delete-vehicle-type-message": "Are you sure you want to delete vehicle type {{vehicleTypeTitle}}? This action is irreversible!", "delete-vehicle-type": "Delete vehicle type", "delete": "Delete", "delivery-pressure": "Delivery pressure (bars)", "departure-time-must-be-after-end-time": "Departure time must be after job end time", "departure-time": "Departure time", "description": "Description", "details-of-issue": "Please write the details of the issue in the field here under", "details-reservation": "Reservation details", "details": "Details", "diesel": "Diesel", "dimensions-varying-according-vehicle": "*Dimensions may vary according to vehicle assembly", "dispatcher-company": "Dispatcher company", "dispatcher-managers": "Dispatcher Managers", "dispatcher-phone-number-exceeds": "The dispatcher's phone number length must not exceed 100 characters", "dispatcher": "Di<PERSON>atcher", "dispatchers-required": "Dispatchers are required", "dispatchers": "Dispatchers", "distance-asc": "Distance (closest to farthest)", "distance-desc": "Distance (farthest to closest)", "download": "Download", "downloading": "Downloading...", "driving-to-site": "Driving to site", "due-date": "Due date", "edit": "Edit", "electric": "Electric", "electrical-risk": "Electrical risk", "email-address": "E-mail address", "email-exceeds": "The e-mail length must not exceed 100 characters", "email-sent-to-the-operator": "An invitation has been sent to the operator at the following address: ", "email-sent": "Email sent!", "email": "Email", "email-valid": "Enter a valid email", "email-required": "Email is required", "empty-search-hint": "Fill in the address, date and vehicle type, then hit search to view results", "end-cleanup": "End cleanup", "end-date": "End date", "end-hose-length": "End hose length", "end-pumping": "End pumping", "end-setup": "End setup", "end-time-must-be-after-start-time": "End time must be after start time", "end-time": "End time", "endHoseLength": "End hose length", "engine-type": "Engine type", "enlist-second-technician": "Enlist second technician", "enter-email": "Please enter the email you use to sign into ConcretEasy", "error-notification": "This service is currently experiencing some issues, an error notification has been sent. Please try again later.", "exceeds-available-stock": "Exceeds available stock", "exceeds-height-limit": "Exceeds height limit", "exceeds-weight-limit": "Exceeds weight limit", "expenses": "Expenses", "extra-cement-bag-price": "Extra cement bag price", "extra-cement-bags": "Extra cement bags", "favorite-company": "Favorite company", "filter-by": "Filter by", "finance": "Finance", "finish": "Finish", "first-name": "First name", "flat-fee-weekdays": "Flat fee (weekdays)", "flat-fee-weekend": "Flat fee (Weekend, {{duration}} hour/s)", "flat-fee": "Flat fee ({{duration}} hour/s)", "flat-fee-night": "Flat fee (Night , {{duration}} hour/s)", "flat-weekdays-fee": "Flat weekdays fee", "flat-weekend-fee": "Flat weekend fee", "flexible-pipe-80": "Flexible pipe (80 mm)", "flexible-pipe-90": "Flexible pipe (90 mm)", "flexible-pipe-length-80": "Flexible pipe (80 mm)", "flexible-pipe-length-80mm": "Flexible pipe (80 mm)", "flexible-pipe-length-90": "Flexible pipe (90 mm)", "flexible-pipe-length-90mm": "Flexible pipe (90 mm)", "flow-rate": "Flow rate", "forgot-password": "Forgot password?", "frequently-used-vehicles": "Frequently used vehicles", "from": "From", "front-outrigger-span": "Front outrigger span", "front-outriggers-span": "Front outriggers span", "front-pressure-on-outrigger": "Front pressure on outrigger", "front-side-opening": "Front side opening", "gasoline": "Petrol/Gasoline", "general": "General", "generate-invoice": "Generate Invoice", "generate": "Generate", "go-to-reservation-details": "Go to reservation details", "has-been-read": "<PERSON> <PERSON>", "height-limit": "Height limit", "height-restriction": "Height restriction", "height": "Height (folded)", "history": "History", "home": "Home", "horizontal-reach": "Max. horizontal reach", "hours": "Hour/s", "hourly-price-weekdays": "Hourly price (Weekdays)", "hourly-price-weekend": "Hourly price (Weekend)", "hourly-price": "Hourly price", "hourly-rate": "Hourly rate", "hours-before-job": "hours before job", "hybrid": "Hybrid", "i-am-dispatcher": "I am a dispatcher", "i-am-manager": "I am a manager", "i-am-operator": "I am an operator", "info-finish-job-text": "Click the Finish button when you have left the site to finish the job", "info-start-job-text": "Click the Start button when you are ready to start driving to your destination", "info-text": "This number is only an estimation and may vary depending on external factors. Please advise the client that this price is subject to modification", "initiate-job": "Thank you for initiating the job. Before proceeding, could you please confirm your readiness to start driving towards your destination ?", "invalid-date": "Invalid date entered. Please use the correct format (e.g., 08/03/2023) and retry.", "invalid-email-format": "Must be a valid email address", "invalid-phone-format": "Phone number is not valid", "invalid-step": "Invalid Step", "invoice-number": "Invoice no.", "invoice": "Invoice", "invoiced-time": "Invoiced time:", "invoiced": "Invoiced", "invoices": "Invoices", "invoicing-pipes-from": "Invoicing pipes from", "issue-description": "Issue description", "job-details": "Job Details", "job-interrupted": "This job will now be interrupted", "job-status": "Job status", "job-tracker": "Job Tracker", "job": "Job", "job-type": "Job type", "kilometer": "kilometer", "label-already-exists": "This label already exists", "label": "Label", "last-name": "Last name", "leave-site": "Leaving site", "length": "Length (folded)", "less-filters": "Less filters", "license-plate-number-exceeds": "The license plate number length must not exceed 100 characters", "license-plate-number": "License plate number", "light-mode": "Light mode", "linked-operator": "Linked operator", "linked-vehicle": "Linked vehicle", "list": "list", "local-admin-authentication": "Local admin. authentication", "local-admin-authorization": "Local admin. authorization", "make-a-reservation": "Make a reservation", "manager-phone-number": "Manager phone number", "manager": "Manager", "managers": "Managers", "mark-complete": "Mark complete", "max-concrete-pressure": "Max. concrete pressure", "max-downward-reach": "Max. downward reach", "max-flow-rate": "Max. flow rate", "max-m3": "Maximum m³", "maximum-concrete-pressure-rod-end": "Maximum concrete pressure (rod end)", "maximum-flow-rate-rod-end": "Maximum flow rate (rod end)", "media": "Media", "meter-symbol": "m", "meter": "meter", "min-m3": "Minimum m³", "min-unfolding-height": "Min. unfolding height", "minimum-charge": "Minimum charge (Flat Fee)", "minimum-m3-charged": "Minimum m³ charged", "minimum-time-between-jobs": "Minimum time between jobs", "minutes": "minutes", "seconds": "seconds", "modifications-saved": "Modifications saved", "modify-operator-details": "Modify operator details", "month": "Month", "more-filters": "More filters", "most-active-contractors": "Most active contractors", "must-be-greater-than-or-equal-to-20": "Vehicle type must be greater than or equal to 20", "must-be-less-than-or-equal-to-80": "Vehicle type must be less than or equal to 80", "name-exceeds": "The name length must not exceed 100 characters", "name-lastname": "Name & Last name", "name": "Name", "next": "Next", "night-flat-fee": "Package / flat fee (night)", "night-hour": "Additional Hour (night)", "night-pricing": "Night Pricing", "no-assignee": "No assignee", "no-available-vehicles": "Unfortunately, we couldn't find any vehicles matching your search criteria ", "no-balance": "no additional balance", "no-labels-available": "No labels available", "no-linked-operator": "No linked operator", "no-linked-vehicle": "No linked vehicle", "no-of-reservations": "No. of reservations", "no-reservations": "No reservations to display", "no-results": "Sorry, there are no results for your research", "no-upcoming-job": "No upcoming job", "no-uploads": "No uploads", "no-vehicle-registered": "You currently have no vehicles registered", "no": "No", "not-started": "Not started", "num-reservations": "No. of reservations", "number-of-boom-sections": "Number of boom sections", "ok": "Ok", "one-day-cancellation-deadline": "Time of day by which a reservation must be canceled on the day before to avoid a fee.", "only-favorites": "Only favorite companies", "operating-weight": "Operating weight", "operator-details": "Operator details", "operator-manager": "Operator Manager", "operator-not-found": "Operator not found", "operator-successfully-deleted": "Operator successfully deleted", "operator": "Operator", "operator-company": "Operator Company", "operators": "Operators", "order-information": "Order information", "order-number": "Order Number", "order-time": "Order time", "orderNumberRequired": "Order Number is required", "trackOrder": "Track Order", "other": "Other", "out-coverage-100Km-additional-fee": "Out coverage 100 km additional fee", "out-coverage-50Km-additional-fee": "Out coverage 50 km additional fee", "out-coverage-fifty-km": "Out coverage 50km fee", "out-coverage-hundred-km": "Out coverage 100km fee", "out-of-range": "Value out of range", "overwrite-job-hours": "Based on the entered Flow rate, the job duration will be adjusted to a duration of {{duration}} and is estimated to finish by {{time}}. Do you want to continue with this adjustment?", "package-flat-fee": "Package/Flat fee", "minimum-package-flat-fee": "Package/Flat fee (Minimum)", "packageFlatFeeDuration": "Package/Flat fee duration", "parking-compliance": "Parking compliance", "parking-on": "Parking on", "parking-permit-acquired": "Parking Permit Acquired", "parking-permit": "Parking permit", "password-must-include": "Your password must include:", "password": "Password", "partners": "Partners", "percentage": "Percentage", "phone-number-exceeds": "The phone number length must not exceed 100 characters", "phone-number-personal": "Phone number (personal)", "phone-number": "Phone number", "pipe-length-80Mm-warning": "This vehicle only has {{availableAmount}} meters of flexible pipe (80 mm). The amount requested is above. Please contact the company manager if you wish to make the reservation anyway.", "pipe-length-90Mm-warning": "This vehicle only has {{availableAmount}} meters of flexible pipe (90 mm). The amount requested is above. Please contact the company manager if you wish to make the reservation anyway.", "pipe-length-for-second-technician": "Pipe length for 2nd technician", "pipe-starting-from-bac": "Pipe starting from BAC", "pipeInvoicingStartsFrom": "Pipe invoicing starts from", "pipes": "Pipes", "planning-address": "Planning address", "planning": "Planning", "please-contact-client": "Please ensure you have contacted the client before deleting this reservation.", "please-contact-manager-and-client": "Please ensure you have contacted both the client and the pump manager before deleting this reservation.", "power-line": "Power line", "preferences": "Preferences", "presence-of-power-lines": "Presence of power lines", "price-for-tier-1": "Price for Tier 1 (m³)", "price-for-tier-2": "Price for Tier 2 (m³)", "price-for-tier-3": "Price for Tier 3 (m³)", "price-for-tier-4": "Price for Tier 4 (m³)", "price-for-tier-5": "Price for Tier 5 (m³)", "price-for-tier-6": "Price for Tier 6 (m³)", "price-per-m³-pumped": "Price per m³ pumped", "price-per-meter-of-flexible-pipe-length-100Mm": "Rigid pipe (100 mm)", "price-per-meter-of-flexible-pipe-length-80Mm": "Flexible pipe (80 mm)", "price-per-meter-of-flexible-pipe-length-90Mm": "Flexible pipe (90 mm)", "price-per-meter-of-flexible-pipe-placed": "Price per meter of flexible pipe placed", "price-per-meter-of-flexible-placed": "Price per meter of flexible pipe placed", "price-per-meter-of-rigid-pipe-length-120Mm": "Rigid pipe (120 mm)", "price-per-meter-of-rigid-pipe-placed": "Price per meter of rigid pipe placed", "price-per-meter-of-rigid-placed": "Price per meter of rigid pipe placed", "price-per-meter-pumped": "Price per m³ pumped", "price-weekday-asc": "Hourly price weekday (lowest to highest)", "price-weekday-desc": "Hourly price weekday (highest to lowest)", "price-weekdays": "Price weekdays", "price-weekend-asc": "Hourly price weekend (lowest to highest)", "price-weekend-desc": "Hourly price weekend (highest to lowest)", "price-weekend": "Price weekend", "pricelist": "Pricelists", "select-pricelist": "Select Pricelist", "print": "Print", "priority": "Priority", "proceed": "Proceed", "profile-details": "Profile details", "profile-operator-registered": "Operator successfully added", "profile-registered": "Your profile has been registered", "profile": "Profile", "pump-company-name": "Pump company name", "pump-tiers": "Tier pricing", "pumped-volume": "Pumped volume", "pumping": "Pumping", "pump-specifications": "Pump specifications", "pump-models": "Pump Models", "quantity-pumped": "Quantity pumped", "rear-outrigger-span": "Rear outrigger span", "rear-outriggers-span": "Rear outriggers span", "rear-pressure-on-outrigger": "Rear pressure on outrigger", "rear-side-opening": "Rear side opening", "red-diesel": "Diesel (red diesel)", "reduced-cancellation-window": "Reduced cancellation window", "region": "Region", "register-a-vehicle": "Register a vehicle", "register-vehicle-step-1": "Vehicle details (1)", "register-vehicle-step-2": "Vehicle details (2)", "register-vehicle-step-3": "Job details", "register-vehicle-step-4": "Confirmation", "register-your-first-vehicle": "Register your first vehicle here", "register": "Register", "repeat-password": "Repeat password", "report-sent-description": "Thank you for submitting your report. An email has been sent to your manager and dispatcher with all the details", "report-sent-manager-description": "Thank you for submitting your report. An email has been sent to your manager with all the details", "report-sent-title": "Report sent", "report": "Report", "request-contract": "Request Contract", "required": "Required", "reservation-complete": "Reservation complete", "reservation-confirmation-email": "Confirmation emails have been sent to the client and your inbox.", "reservation-details": "Reservation details", "reservation-order-number": "Order #2193jiwqq", "reservation-successfully-deleted": "Reservation succesfully deleted", "reservations": "Reservations", "reset-filters": "Reset filters", "reset-password-button": "Reset password", "reset-password": "Reset password?", "reset": "Reset", "results": "results", "return-to-planning": "Return to planning", "return-to-search-results": "Return to search results", "rigid-pipe-100": "Rigid pipe (100 mm)", "rigid-pipe-120": "Rigid pipe (120 mm)", "rigid-pipe-length-100": "Rigid pipe (100 mm)", "rigid-pipe-length-100mm": "Rigid pipe (100 mm)", "rigid-pipe-length-120mm": "Rigid pipe (120 mm)", "rigid-pipe-length-warning": "This vehicle only has {{availableAmount}} meters of rigid pipe length. The amount requested is above. Please contact the company manager if you wish to make the reservation anyway.", "rigid-pipe-length": "Rigid pipe (120 mm)", "role": "Role", "rows": "Rows: ", "rubber-end-hose-length": "Rubber end hose length", "save": "Save", "search-clients": "Search Clients", "search-vehicles": "Search Vehicles", "search-companies": "Search Companies", "search-category": "Search Category", "companies": "Companies", "search": "Search", "second-technician-fee-weekend": "2nd technician fee (weekend)", "second-technician": "Second technician", "secondTechnicianFee": "2nd technician fee", "second-technician-fee-night": "2nd technician fee (night)", "secured": "Secured", "security-validation": "Security validation", "select-all": "Select All", "select-theme": "Select theme", "select": "Select", "selected": "Selected:", "send-email": "Send Email", "send-password-link": "Send password reset link", "services": "Services", "settings": "Settings", "setup": "Setup", "side-outrigger-span": "Side outrigger span", "sign-in": "Sign in", "sign-out": "Logout", "signal-an-issue": "Signal an issue", "signature": "Signature", "add-signature": "Add Signature", "please-sign-here": "Please sign here:", "signature-required": "Signature is required", "site-address": "Site address", "site-arrival": "Site arrival", "sort-by": "Sort by", "start-cleanup": "Start cleanup", "start-date": "Start date", "start-pumping": "Start pumping", "start-setup": "Start setup", "start-time-must-be-now-or-in-the-past": "Start time must be now or in the past", "start-time": "Start time", "start": "Start", "status": "Status", "steps-completed": "All steps completed - you're finished", "strangler": "<PERSON><PERSON><PERSON>", "submit": "Submit", "success-registered": "Your profile has been successfully created. You can register your vehicles here", "sum": "Sum", "supply-of-the-chemical-slushie": "Supply of the chemical slushie", "surname-exceeds": "The surname length must not exceed 100 characters", "system-settings": "System settings", "task": "Task", "tax-name": "Tax name", "taxes": "Taxes", "terrain-stability": "Terrain stability", "terrain": "Terrain", "theme": "Theme", "tier-pricing": "Tier Pricing", "time": "Time", "title-exceeds": "The title length must not exceed 100 characters", "title": "Title", "to": "To", "todo": "To Do", "tonnage-restriction": "Tonnage restriction", "tons": "tons", "total-expenses-per-month": "Total expenses per month", "total-expenses": "Total Expenses", "total-invoiced": "Total invoiced", "total-reservations-per-month": "Total reservations per month", "total-reservations": "Total Reservations", "total": "Total:", "traffic-plan": "Traffic plan", "transport-rates": "Transport rates", "transport-rate": "Transport rate", "two-days-cancellation-deadline": "Time of day by which a reservation must be canceled two days before to avoid a fee.", "two-weeks": "2 weeks", "type-of-motorisation": "Type of motorisation", "type": "Type", "unavailable-period-successfully-deleted": "Unavailable period succesfully deleted", "unavailable": "Unavailable", "unique-identification-number": "Unique ID", "units": "Units", "upcoming-job": "Upcoming Job", "update-client": "Update client", "update-pricelist": "Update pricelist", "update-pricelist-for-vehicle": "Update price list for {{vehicleName}}", "update-reservation": "Update reservation", "update-vehicle-type": "Update Vehicle Type", "update-vehicle-model": "Update Vehicle Model", "upload": "Upload", "user-management": "User management", "vehicle-specifications": "Vehicle specifications", "variant": "<PERSON><PERSON><PERSON>", "vat-info": "All price listed are exclusive of VAT", "vat-number-exceeds": "The VAT number length must not exceed 100 characters in length", "vat-number": "VAT number", "vehicle-boom-size": "Boom size", "vehicle-company": "Vehicle/Company", "vehicle-details-one": "Vehicle details (1)", "vehicle-details-two": "Vehicle details (2)", "vehicle-details": "Vehicle details", "vehicle-height-folded": "Vehicle height (folded)", "vehicle-length-folded": "Vehicle length (folded)", "vehicle-model-details-one": "Vehicle Model details (1)", "vehicle-model-details-two": "Vehicle Model details (2)", "vehicle-missing-pricelist": "The vehicle has no pricelist", "vehicle-name": "Vehicle name", "vehicle-registered": "Vehicle registered", "vehicle-succesfully-registered": "Vehicle succesfully registered", "vehicle-successfully-deleted": "Vehicle successfully deleted", "vehicle-types": "Vehicle Types", "vehicle-width-folded": "Vehicle width (folded)", "vehicle": "Vehicle", "vehicles": "Vehicles", "vertical-reach": "Max. vertical reach", "vehicle-brand": "Vehicle brand", "brand-model": "Brand model", "view-attachments": "View Attachments", "view-details": "View Details", "view-operators": "View all the operators", "view-reservations": "View reservations", "view-vehicles": "View all the vehicles", "voltage": "Voltage", "waiting": "Waiting", "week": "Week", "weekdays": "Weekdays", "weekend-additional-hour": "Additional Hour (Weekend)", "weekend-flat-fee": "Package / flat fee (weekend)", "weekend-pricing": "Weekend pricing", "weekend": "Weekend", "weight-restriction": "Weight restriction", "weight": "Weight", "welcome": "Welcome", "width": "<PERSON><PERSON><PERSON>", "work-schedule": "Work schedules", "worksite-name": "Worksite name", "worksite": "Worksite", "yes": "Yes", "zip-code": "Zip code", "not-contracted": "Reservation not possible: This vehicle is not contracted.", "slot-not-available": "Unable to create reservation: Vehicle is unavailable for reservation on this date!", "tel-operator": "Tel. Operator", "tel-dispatcher": "Tel. Dispatcher", "unavailableFrom": "Unavailable From", "unavailableTo": "Unavailable To", "unavailableFromRequired": "Unavailable From date is required", "unavailableToRequired": "Unavailable To date is required", "unavailableToAfterFrom": "Unavailable To date must be after From date", "operatorUnavailable": "Operator is unavailable for the date you have selected", "vehicleUnavailable": "Vehicle is unavailable for the date you have selected", "assignTimeOff": "Assign time off", "updateTimeOff": "Update time off", "upcoming": "Upcoming", "noUpcomingTimeOff": "No upcoming time off.", "timeOffHistory": "Time off history", "noTimeOffHistory": "No time off history.", "showMore": "Show more", "personalInfo": "Personal info", "timeOff": "Time off", "current": "Current", "used": "Used", "update": "Update", "maxVerticalReach": "Maximum vertical reach", "maxHorizontalReach": "Maximum horizontal reach", "minUnfoldingHeight": "Minimum unfolding height", "maxDownwardReach": "Maximum downward reach", "assign_unavailability_period": "Assign unavailability period", "update_unavailability_period": "Update unavailability period", "assign_unavailability": "Assign Unavailability", "meters": " meters", "bars": " bar", "degrees": " degrees", "vehicle_information": "Vehicle Information", "unavailability": "Unavailability", "vehicle_specifications": "Vehicle specifications", "boom_specifications": "Boom specifications", "pump_specifications": "Pump specifications"}