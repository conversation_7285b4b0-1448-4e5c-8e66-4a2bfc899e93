import {
  QuillWrite01Icon,
  HourglassIcon,
  Tick01Icon,
  Edit02Icon
} from "@hugeicons/react";
import {
  MenuItem,
  ListItemIcon,
  Typography,
  useTheme,
  Box,
  IconButton
} from "@mui/material";
import { TaskStatus } from "src/common/types/tasks";
import { CeTextField } from "src/common/components";

interface StatusFieldProps {
  formik: {
    values: {
      status: TaskStatus | null;
    };
    setFieldValue: (field: string, value: any) => void;
    handleChange: (e: React.ChangeEvent<any>) => void;
    touched: {
      status?: boolean;
    };
    errors: {
      status?: string;
    };
    setFieldTouched: (
      field: string,
      touched: boolean,
      shouldValidate?: boolean
    ) => void;
  };
}
const StatusField = ({ formik }: StatusFieldProps) => {
  const theme = useTheme();
  const statusData = [
    {
      status: TaskStatus.TODO,
      icon: (
        <QuillWrite01Icon
          size={20}
          color={theme.palette.action.active}
          variant="solid"
          type="rounded"
        />
      )
    },
    {
      status: TaskStatus.DOING,
      icon: (
        <HourglassIcon
          size={20}
          color={theme.palette.action.active}
          variant="solid"
          type="rounded"
        />
      )
    },
    {
      status: TaskStatus.DONE,
      icon: (
        <Tick01Icon
          size={20}
          color={theme.palette.action.active}
          variant="solid"
          type="rounded"
        />
      )
    }
  ];

  if (!formik.values.status) {
    return (
      <CeTextField
        select
        name="status"
        value={formik.values.status || ""}
        onChange={formik.handleChange}
        variant="outlined"
        size="small"
        fullWidth
        onBlur={() => formik.setFieldTouched("status", true)}
        error={!!formik.errors.status && formik.touched.status}
        helperText={formik.touched.status && formik.errors.status}
      >
        {statusData.map(({ status, icon }) => (
          <MenuItem key={status} value={status}>
            <ListItemIcon>{icon}</ListItemIcon>
            <Typography variant="body1" fontSize={16} letterSpacing={0.15}>
              {status}
            </Typography>
          </MenuItem>
        ))}
      </CeTextField>
    );
  }
  return (
    <Box alignSelf="center" display="flex" alignItems="center" gap={1}>
      <>
        {statusData.find((item) => item.status === formik.values.status)?.icon}
        <Typography fontWeight={400} fontSize={16} letterSpacing={0.15}>
          {formik.values.status}
        </Typography>
      </>
      <IconButton
        size="small"
        onClick={() => formik.setFieldValue("status", "")}
        sx={{ ml: 1 }}
      >
        <Edit02Icon size={16} variant="stroke" />
      </IconButton>
    </Box>
  );
};

export default StatusField;
