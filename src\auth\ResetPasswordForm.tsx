import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  useTheme
} from "@mui/material";
import { useFormik } from "formik";
import PasswordChecklist from "react-password-checklist";
import * as yup from "yup";

import { ResetPasswordDto } from "src/common/types";
import { useTranslation } from "react-i18next";
import { buttonTextTransform } from "src/common/components/custom/customCss";
import { CeCard, CeTextField } from "src/common/components";

interface ResetPasswordFormProps {
  handleResetPassword: (attrs: ResetPasswordDto) => void;
  isLoading: boolean;
  passwordResetToken: string;
}
export const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({
  isLoading,
  passwordResetToken,
  handleResetPassword
}) => {
  const { t } = useTranslation("common");
  const [isPasswordValid, setIsPasswordValid] = useState(false);
  const theme = useTheme();
  const formik = useFormik({
    initialValues: {
      newPassword: "",
      newPasswordConfirmation: ""
    },
    validationSchema: yup.object({
      newPassword: yup.string().required("Password is required"),
      newPasswordConfirmation: yup
        .string()
        .oneOf([yup.ref("newPassword"), null], "Passwords must match")
    }),
    onSubmit: (values) => {
      const formValues = {
        ...values,
        token: passwordResetToken,
        password: values.newPassword,
        confirmPassword: values.newPasswordConfirmation
      };
      handleResetPassword(formValues);
    }
  });

  return (
    <>
      <CeCard sx={{ minWidth: 275, padding: 2 }}>
        <CardContent>
          <Stack>
            <Typography
              variant="h5"
              textAlign="center"
              sx={{ marginTop: 0, marginBottom: 1 }}
            >
              {t("reset-password")}
            </Typography>
          </Stack>

          <Typography
            color="text.secondary"
            sx={{ fontSize: "16px", marginTop: "5px" }}
          >
            {t("password-must-include")}
          </Typography>

          <PasswordChecklist
            rules={["minLength", "specialChar", "number", "match"]}
            minLength={5}
            iconSize={14}
            invalidColor={theme.palette.error.light}
            style={{ marginBottom: "15px" }}
            value={formik.values.newPassword}
            valueAgain={formik.values.newPasswordConfirmation}
            messages={{
              minLength: "At least six characters",
              specialChar: "One special character (!, %, @, #, etc.)",
              number: "One number (0-9)",
              match: "Passwords must match"
            }}
            onChange={(isValid) => {
              setIsPasswordValid(isValid);
            }}
          />

          <Stack
            component="form"
            sx={{ width: "30ch" }}
            spacing={2}
            noValidate
            onSubmit={formik.handleSubmit}
          >
            <CeTextField
              fullWidth
              id="newPassword"
              name="newPassword"
              label={t("password")}
              type="password"
              size="small"
              value={formik.values.newPassword}
              onChange={formik.handleChange}
              error={
                formik.touched.newPassword && Boolean(formik.errors.newPassword)
              }
              helperText={
                formik.touched.newPassword && formik.errors.newPassword
              }
              disabled={isLoading}
              required
            />
            <CeTextField
              fullWidth
              id="newPasswordConfirmation"
              name="newPasswordConfirmation"
              label={t("repeat-password")}
              type="password"
              size="small"
              value={formik.values.newPasswordConfirmation}
              onChange={formik.handleChange}
              error={
                formik.touched.newPasswordConfirmation &&
                Boolean(formik.errors.newPasswordConfirmation)
              }
              helperText={
                formik.touched.newPasswordConfirmation &&
                formik.errors.newPasswordConfirmation
              }
              disabled={isLoading}
              required
            />
            <Button
              color="primary"
              variant="contained"
              fullWidth
              type="submit"
              disabled={isLoading || !isPasswordValid}
              sx={buttonTextTransform}
            >
              {t("reset-password-button")}
            </Button>
          </Stack>
        </CardContent>
      </CeCard>
    </>
  );
};
