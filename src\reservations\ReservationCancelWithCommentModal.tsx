import {
  Box,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography,
  useTheme,
} from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { CeDialog, CeButton, CeTextField } from "src/common/components";
import { ReservationModalCancelFlow } from "src/common/types";

interface ReservationModalCancelProps {
  flow: ReservationModalCancelFlow;
  reservationTitle: string | null;
  isLoading: boolean;
  cancelationFee: number | null;
  handleCloseReservationModalCancel: () => void;
  onSubmit: (comment: string) => void;
}

export const ReservationCancelWithCommentModal: React.FC<
  ReservationModalCancelProps
> = ({
  flow,
  reservationTitle,
  isLoading,
  handleCloseReservationModalCancel,
  onSubmit,
  cancelationFee,
}) => {
  const { t } = useTranslation(["common"]);
  const theme = useTheme();
  const [comment, setComment] = useState<string>("");
  const characterLimit = 400;

  const handleCommentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.length <= characterLimit) {
      setComment(value);
    }
  };
  return (
    <CeDialog
      open={flow === "Cancel"}
      onClose={handleCloseReservationModalCancel}
      maxWidth="md"
      aria-labelledby={`main-dialog-${reservationTitle}`}
      PaperProps={{
        sx: { borderRadius: "12px" },
      }}
    >
      <DialogTitle>
        <Typography variant="h6" fontWeight="bold" sx={{ textAlign: "center" }}>
          Cancel Reservation
        </Typography>
      </DialogTitle>
      <DialogContent dividers>
        <DialogContentText
          sx={{
            textAlign: "center",
            marginBottom: 5,
            marginTop: 3,
            color: "black",
          }}
        >
          <Box>
            <Typography variant="body1">
              {t("common:confirm-reservation-cancelation", {
                reservationTitle: `"${reservationTitle}"`,
              })}
            </Typography>
            {cancelationFee ? (
              <Typography variant="body1">
                {t("common:incur-cancelation-fee", {
                  cancelationFee,
                })}
              </Typography>
            ) : null}
          </Box>
        </DialogContentText>
        <Box>
          <CeTextField
            fullWidth
            id="comment"
            name="comment"
            label="Reason for cancelation *"
            size="small"
            multiline
            rows={3}
            value={comment}
            onChange={(e) => {
              setComment(e.target.value);
            }}
            InputLabelProps={{ shrink: true }}
            disabled={isLoading}
            helperText={
              comment.length > characterLimit
                ? `Exceeded character limit! (${comment.length}/${characterLimit})`
                : `${comment.length}/${characterLimit} characters`
            }
            FormHelperTextProps={{
              sx: {
                color:
                  comment.length > characterLimit
                    ? theme.palette.error.main
                    : theme.palette.text.secondary,
              },
            }}
            sx={{ wordWrap: "break-word", whiteSpace: "pre-wrap" }}
          />
        </Box>
      </DialogContent>
      <DialogActions sx={{ flexDirection: "column", padding: "16px" }}>
        <CeButton
          variant="contained"
          onClick={() => onSubmit(comment)}
          onChange={() => handleCommentChange}
          disabled={isLoading || comment.length > characterLimit}
          sx={{ width: "60%", marginBottom: "8px" }}
        >
          {t("common:confirm")}
        </CeButton>
        <CeButton
          variant="text"
          onClick={handleCloseReservationModalCancel}
          disabled={isLoading}
          sx={{ width: "60%" }}
        >
          {t("common:cancel")}
        </CeButton>
      </DialogActions>
    </CeDialog>
  );
};
