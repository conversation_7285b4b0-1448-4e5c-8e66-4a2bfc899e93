import { FC, useCallback, useEffect, useState } from "react";
import { GridColDef, GridRenderCellParams, DataGrid, GridRowParams } from "@mui/x-data-grid";
import { Link as MuiLink, Stack, IconButton, Chip } from "@mui/material";
import { useRecoilState } from "recoil";
import { useTranslation } from "react-i18next";

import {
  boomUnfoldingSystemTypes,
  Status,
  UpdateVehicleDto,
  userStatuses,
  Vehicle,
  VehicleFormValues,
  VehicleMotorisationTypes,
  vehicleTypes,
} from "src/common/types";
import {
  vehicleDeleteValuesState,
  vehicleFormValuesState,
} from "src/common/state/vehicle";
import { VehicleModal } from "../VehicleModal";
import {
  VEHICLE_DELETE_DEFAULT,
  VEHICLE_FORM_VALUES,
} from "src/common/constants";
import {
  useCreateNewVehicle,
  useDeleteVehicle,
  useUpdateVehicle,
  useUsers,
  useVehicleTypes,
} from "src/common/api";
import { FeatureFlag } from "src/common/components";
import { VehicleModalDelete } from "../VehicleModalDelete";
import { stringAvatar } from "src/common/utils/avatar";
import { turnVehicleIntoFormValues } from "src/common/utils";
import { Delete01Icon, Edit02Icon } from "@hugeicons/react";
import { CeAvatar } from "src/common/components";
import { CeDataGridToolbar } from "src/common/components/custom/company/CeDataGridToolbar";
import { GridColumnTypography } from "src/common/components/custom/company/GridColumnTypography";
import { getUserFullName } from "src/common/utils/user";
import { GridStateSnapshot, UpdateGridStatePart } from "src/common/utils/gridState";
import VehicleDetailsModal from "../VehicleDetailsModal";

interface VehiclesDatagridProps {
  data: Vehicle[];
  isFetchingVehicles: boolean;
  refetchVehicles: () => void;
  shouldRenderRefreshButton: boolean;
  shouldRenderAddButton: boolean;
  shouldRenderEditActionsColumn: boolean;
  total: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
  gridState: GridStateSnapshot;
  updateGridStatePart: UpdateGridStatePart;
}

export const VehiclesDatagrid: FC<VehiclesDatagridProps> = ({
  data,
  shouldRenderRefreshButton,
  shouldRenderAddButton,
  shouldRenderEditActionsColumn,
  isFetchingVehicles,
  refetchVehicles,
  gridState,
  updateGridStatePart,
  total,
  onPageChange,
  onPageSizeChange,
}) => {
  const { t } = useTranslation(["manager", "common", "dispatcher"]);
 
  const [editMode, setEditMode] = useState<boolean>(false);
  const {
    mutateAsync: handleUpdateVehicleRowGrid,
    mutate: handleUpdateVehicle,
    isSuccess: isUpdateVehicleSuccess,
    isLoading: isUpdatingVehicle,
  } = useUpdateVehicle();
  const {
    mutate: handleCreateNewVehicle,
    isSuccess: isCreateVehicleSuccess,
    isLoading: isCreatingVehicle,
  } = useCreateNewVehicle();
  const {
    mutate: handleDeleteVehicle,
    isLoading: isDeletingVehicle,
    isSuccess: isDeleteVehicleSuccess,
  } = useDeleteVehicle();

  const [vehicleFormValues, setVehicleFormValues] = useRecoilState(
    vehicleFormValuesState
  );

  const [vehicleDeleteFormValues, setVehicleDeleteFormValues] = useRecoilState(
    vehicleDeleteValuesState
  );

  const processRowUpdate = async (newRow: Vehicle, oldRow: Vehicle) => {
    try {
      const payload: UpdateVehicleDto = {
        ...newRow,
        vehicleId: newRow.id,
        operatorId: newRow.operator?.id || null,
      };
      const updatedRow = await handleUpdateVehicleRowGrid(payload);
      return updatedRow;
    } catch (error: any) {
      console.error(error.message);
      return oldRow;
    }
  };
  const handleProcessRowUpdateError = useCallback((error: Error) => {
    console.error(error.message);
  }, []);

  const isLoading =
    isFetchingVehicles ||
    isCreatingVehicle ||
    isDeletingVehicle ||
    isUpdatingVehicle;

  useEffect(() => {
    if (isCreateVehicleSuccess) {
      setVehicleFormValues(VEHICLE_FORM_VALUES);
    }
  }, [isCreateVehicleSuccess, setVehicleFormValues]);

  useEffect(() => {
    if (isUpdateVehicleSuccess) {
      setVehicleFormValues(VEHICLE_FORM_VALUES);
    }
  }, [isUpdateVehicleSuccess, setVehicleFormValues]);

  useEffect(() => {
    if (isDeleteVehicleSuccess) {
      setVehicleDeleteFormValues(VEHICLE_DELETE_DEFAULT);
    }
  }, [isDeleteVehicleSuccess, setVehicleDeleteFormValues]);

  const handleCloseVehicleModal = () => {
    if (!isLoading) {
      setVehicleFormValues(VEHICLE_FORM_VALUES);
    }
  };
  const getValuesFromExistingVehicle = (vehicle: Vehicle) => {
    const selectedVehicle = turnVehicleIntoFormValues(vehicle, "Create");
    setVehicleFormValues({
      ...selectedVehicle,
      type: vehicleFormValues.type,
      vehicleId: vehicle.id,
      completedJobs: 0,
    });
  };

  const handleCloseVehicleModalDelete = () => {
    if (!isLoading) {
      setVehicleDeleteFormValues(VEHICLE_DELETE_DEFAULT);
    }
  };

   const handleCloseVehicleDetailsModal = () => {
      if (!isLoading) {
        setVehicleFormValues(VEHICLE_FORM_VALUES);
      }
    }

  const { data: operatorsData } = useUsers(
    { sortModel: [], expressions: [] },
    editMode
  );

  const operators = operatorsData?.data || [];

  const renderEditActions = (params: GridRenderCellParams) => {
    const vehicle: Vehicle = params.row;

    return (
      <Stack
        direction="row"
        justifyContent="center"
        alignItems="center"
        spacing={1}
      >
        <IconButton
          aria-label="update vehicle"
          disabled={isUpdatingVehicle}
          size="small"
          onClick={(event) => {
            event.stopPropagation();
            const formValues: VehicleFormValues = turnVehicleIntoFormValues(
              vehicle,
              "Update"
            );

            setVehicleFormValues(formValues);
          }}
        >
          <Edit02Icon size={16} variant={"stroke"} />
        </IconButton>
        <FeatureFlag flags={["operator manager"]}>
          <IconButton
            aria-label="delete vehicle"
            disabled={isDeletingVehicle}
            color="error"
            size="small"
            onClick={(event) => {
              event.stopPropagation();
              setVehicleDeleteFormValues({
                vehicleId: vehicle.id,
                // this 'vehicleTitle' value needs to be changed to some vehicle identification name
                vehicleTitle: `${vehicle.type}-${vehicle?.boomSize}`,
                flow: "Delete",
              })
            }
            }
          >
            <Delete01Icon size={16} variant={"stroke"} />
          </IconButton>
        </FeatureFlag>
      </Stack>
    );
  };

  const columns: GridColDef[] = [
    {
      field: "editActions",
      headerName: `${t("common:edit")}`,
      sortable: false,
      renderCell: (params: GridRenderCellParams<string>) =>
        renderEditActions(params),
      filterable: false,
    },
    {
      field: "status",
      headerName: `${t("common:status")}`,
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => {
        const status: number = params.row.status - 1;

        const statusText = userStatuses[status] || "";
        const statusColor =
          params.row.status === Status.ACTIVE ? "success" : "default";

        return (
          <Chip
            sx={{ m: 0.5, ml: 0, fontSize: "13px" }}
            color={statusColor}
            component="span"
            size="small"
            label={statusText}
          />
        );
      },
    },
    {
      field: "uniqueIdentificationNumber",
      headerName: `${t("common:unique-identification-number")}`,
      headerAlign: "left",
      renderCell: (params) => (
        <GridColumnTypography value={params.row?.uniqueIdentificationNumber} />
      ),
      align: "left",
      width: 150,
    },
    {
      field: "id",
      headerName: `id`,
      headerAlign: "left",
      align: "left",
      width: 100,
      renderCell: (params) => <GridColumnTypography value={params.row.id} />,
    },
    {
      field: "type",
      headerName: `${t("common:type")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      type: "singleSelect",
      valueOptions: () => {
        return vehicleTypes.map((type) => ({
          value: type,
          label: type,
        }));
      },
      valueGetter: (params) => {
        return params.row.type;
      },
      valueSetter: (params) => {
        const selectedType = vehicleTypes.find((type) => type === params.value);
        if (!selectedType) {
          return params.row;
        }
        return { ...params.row, type: selectedType || [] };
      },
      renderCell: (params) => (
        <GridColumnTypography value={params.row.type || "-"} />
      ),
    },
    {
      field: "vehicleSize",
      headerName: `${t("common:vehicle-boom-size")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      renderCell: (params) => (
        <GridColumnTypography value={params.row?.boomSize} />
      ),
    },
    {
      field: "vehicleBrand",
      headerName: `${t("common:vehicle-brand")}`,
      headerAlign: "left",
      align: "left",
      width: 150,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.vehicleBrand} />
      ),
    },
    {
      field: "brandModel",
      headerName: `${t("common:brand-model")}`,
      headerAlign: "left",
      align: "left",
      width: 150,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.brandModel} />
      ),
    },
    {
      field: "operator",
      headerName: `${t("common:operator")}`,
      headerAlign: "left",
      align: "left",
      width: 200,
      editable: true,
      type: "singleSelect",
      valueOptions: () => {
        return operators.map((operator) => {
          const operatorFullName = getUserFullName(
            operator?.firstName,
            operator?.lastName
          );
          return {
            value: operator.id || "",
            label: operatorFullName || "-",
          };
        });
      },
      valueGetter: (params) => {
        if (!operators || !operators.length) return "";
        return params.row?.operator?.id;
      },
      valueSetter: (params) => {
        const selectedOperator = operators.find(
          (type) => type.id === params.value
        );
        if (!selectedOperator) {
          return params.row;
        }
        return { ...params.row, operator: selectedOperator || [] };
      },
      renderCell: (params) => {
        if (!params.row?.operator) {
          return "";
        }

        const fullName = getUserFullName(
          params.row?.operator?.firstName,
          params.row?.operator?.lastName
        );

        return (
          <>
            {fullName && (
              <GridColumnTypography value={fullName}>
                <CeAvatar size="medium" {...stringAvatar(fullName)} />
              </GridColumnTypography>
            )}
          </>
        );
      },
    },
    {
      field: "siteAddress",
      headerName: `${t("common:site-address")}`,
      headerAlign: "left",
      align: "left",
      width: 150,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.siteAddress} />
      ),
    },
    {
      field: "licensePlateNumber",
      headerName: `${t("common:license-plate-number")}`,
      headerAlign: "left",
      align: "left",
      width: 150,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.licensePlateNumber} />
      ),
    },
    {
      field: "typeOfMotorization",
      headerName: `${t("common:type-of-motorisation")}`,
      headerAlign: "left",
      align: "left",
      width: 150,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.typeOfMotorization || "-"} />
      ),
      type: "singleSelect",
      valueOptions: VehicleMotorisationTypes,

      valueSetter: (params) => {
        const selectedType = VehicleMotorisationTypes.find(
          (type) => type === params.value
        );
        if (!selectedType) {
          return params.row;
        }
        return { ...params.row, typeOfMotorization: selectedType || null };
      },
    },
    {
      field: "weight",
      headerName: `${t("common:weight")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.weight} />
      ),
    },
    {
      field: "height",
      headerName: `${t("common:height")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.height} />
      ),
    },
    {
      field: "length",
      headerName: `${t("common:length")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.length} />
      ),
    },
    {
      field: "width",
      headerName: `${t("common:width")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => <GridColumnTypography value={params.row.width} />,
    },
    {
      field: "maxVerticalReach",
      headerName: `${t("common:vertical-reach")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.maxVerticalReach} />
      ),
    },
    {
      field: "maxHorizontalReach",
      headerName: `${t("common:horizontal-reach")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.maxHorizontalReach} />
      ),
    },
    {
      field: "endHoseLength",
      headerName: `${t("common:endHoseLength")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.endHoseLength} />
      ),
    },
    {
      field: "maxFlowRate",
      headerName: `${t("common:max-flow-rate")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.maxFlowRate} />
      ),
    },
    {
      field: "maxConcretePressure",
      headerName: `${t("common:max-concrete-pressure")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.maxConcretePressure} />
      ),
    },
    {
      field: "availableFlexiblePipeLength80Mm",
      headerName: `${t("common:available-flexible-pipe-length-80")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography
          value={params.row.availableFlexiblePipeLength80Mm}
        />
      ),
    },
    {
      field: "availableFlexiblePipeLength90Mm",
      headerName: `${t("common:available-flexible-pipe-length-90")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography
          value={params.row.availableFlexiblePipeLength90Mm}
        />
      ),
    },
    {
      field: "availableFlexiblePipeLength100Mm",
      headerName: `${t("common:available-flexible-pipe-length-100")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography
          value={params.row.availableFlexiblePipeLength100Mm}
        />
      ),
    },
    {
      field: "availableFlexiblePipeLength120Mm",
      headerName: `${t("common:available-flexible-pipe-length-120")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography
          value={params.row.availableFlexiblePipeLength120Mm}
        />
      ),
    },
    {
      field: "maxDownwardReach",
      headerName: `${t("common:max-downward-reach")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.maxDownwardReach} />
      ),
    },
    {
      field: "numberOfBoomSections",
      headerName: `${t("common:number-of-boom-sections")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.numberOfBoomSections} />
      ),
    },
    {
      field: "minUnfoldingHeight",
      headerName: `${t("common:min-unfolding-height")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.minUnfoldingHeight} />
      ),
    },
    {
      field: "boomRotation",
      headerName: `${t("common:boom-rotation")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.boomRotation} />
      ),
    },
    {
      field: "frontOutriggerSpan",
      headerName: `${t("common:front-outriggers-span")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.frontOutriggerSpan} />
      ),
    },
    {
      field: "rearOutriggerSpan",
      headerName: `${t("common:rear-outriggers-span")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.rearOutriggerSpan} />
      ),
    },
    {
      field: "frontPressureOnOutrigger",
      headerName: `${t("common:front-pressure-on-outrigger")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.frontPressureOnOutrigger} />
      ),
    },
    {
      field: "rearPressureOnOutrigger",
      headerName: `${t("common:rear-pressure-on-outrigger")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.rearPressureOnOutrigger} />
      ),
    },

    {
      field: "invoicingPipesFrom",
      headerName: `${t("common:invoicing-pipes-from")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.invoicingPipesFrom} />
      ),
    },
    {
      field: "pipeLengthForSecondTechnician",
      headerName: `${t("common:pipe-length-for-second-technician")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography
          value={params.row.pipeLengthForSecondTechnician}
        />
      ),
    },
    {
      field: "boomUnfoldingSystem",
      headerName: `${t("common:boom-unfolding-system")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.boomUnfoldingSystem || "-"} />
      ),
      type: "singleSelect",
      valueOptions: boomUnfoldingSystemTypes,

      valueSetter: (params) => {
        const selectedType = boomUnfoldingSystemTypes.find(
          (type) => type === params.value
        );
        if (!selectedType) {
          return params.row;
        }
        return { ...params.row, boomUnfoldingSystem: selectedType || null };
      },
    },
    {
      field: "bacExit",
      headerName: `${t("common:bac-exit")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      type: "singleSelect",
      valueOptions: ["Yes", "No"],
      valueGetter: (params) => {
        return params.row.isActive ? "Yes" : "No";
      },
      valueSetter: (params) => {
        return { ...params.row, bacExit: params.value === "Yes" };
      },
      renderCell: (params) => (
        <GridColumnTypography value={params.row.bacExit ? "Yes" : "No"} />
      ),
    },
    {
      field: "bacExitReverse",
      headerName: `${t("common:bac-exit-reverse")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      type: "singleSelect",
      valueOptions: ["Yes", "No"],
      valueGetter: (params) => {
        return params.row.isActive ? "Yes" : "No";
      },
      valueSetter: (params) => {
        return { ...params.row, bacExitReverse: params.value === "Yes" };
      },
      renderCell: (params) => (
        <GridColumnTypography
          value={params.row.bacExitReverse ? "Yes" : "No"}
        />
      ),
    },
    {
      field: "hasStrangler",
      headerName: `${t("common:strangler")}`,
      headerAlign: "left",
      align: "left",
      width: 100,
      editable: true,
      type: "singleSelect",
      valueOptions: ["Yes", "No"],
      valueGetter: (params) => {
        return params.row.isActive ? "Yes" : "No";
      },
      valueSetter: (params) => {
        return { ...params.row, hasStrangler: params.value === "Yes" };
      },
      renderCell: (params) => (
        <GridColumnTypography value={params.row.hasStrangler ? "Yes" : "No"} />
      ),
    },
  ];

  const handleRowClick  = (params: GridRowParams) =>{
      const vehicleDetails = params.row;
      const formValues: VehicleFormValues = turnVehicleIntoFormValues(
        vehicleDetails,
        "Details"
      )
      setVehicleFormValues(formValues);
  }

  return (
    <>
      <DataGrid
        onRowClick={handleRowClick}
        sx={{
          border: "none",
          paddingTop: 0.5,
          background: (theme) => theme.palette.background.paper,
        }}
        pagination
        page={gridState.page - 1}
        pageSize={gridState.pageSize}
        rowsPerPageOptions={[20, 40, 60, 80, 100]}
        rowCount={total}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        onColumnVisibilityModelChange={(model) =>
          updateGridStatePart("columnVisibilityModel", model)
        }
        filterModel={gridState.filterModel}
        onFilterModelChange={(model) =>
          updateGridStatePart("filterModel", model)
        }
        sortModel={gridState.sortModel}
        onSortModelChange={(model) => updateGridStatePart("sortModel", model)}
        paginationMode="server"
        components={{
          Toolbar: () => (
            <CeDataGridToolbar
              shouldRenderRefreshButton={shouldRenderRefreshButton}
              onRefreshButtonClick={refetchVehicles}
              shouldRenderAddButton={shouldRenderAddButton}
              addButtonClickHandler={() => {
                setVehicleFormValues({ ...vehicleFormValues, flow: "Create" });
              }}
              addButtonDescription={t("add-vehicle")}
            />
          ),
        }}
        columns={columns}
        rows={data}
        disableSelectionOnClick
        initialState={{
          columns: {
            columnVisibilityModel: {
              ...gridState.columnVisibilityModel,
              uniqueIdentificationNumber: false,
              availableRigidPipeLength: false,
              availableFlexiblePipeLength80Mm: true,
              availableFlexiblePipeLength90Mm: true,
              maxDownwardReach: true,
              numberOfBoomSections: true,
              minUnfoldingHeight: true,
              boomRotation: true,
              frontOutriggerSpan: true,
              rearOutriggerSpan: true,
              boomUnfoldingSystem: true,
              bacExit: true,
              bacExitReverse: true,
              Strangler: true,
              siteAddress: true,
            },
          },
        }}
        editMode="row"
        processRowUpdate={processRowUpdate}
        onProcessRowUpdateError={handleProcessRowUpdateError}
        experimentalFeatures={{ newEditingApi: true }}
        onRowEditStart={(params) => {
          setEditMode(!!params);
        }}
      />

      <VehicleModal
        existingVehicles={data}
        initialFormValues={vehicleFormValues}
        getValuesFromExistingVehicle={getValuesFromExistingVehicle}
        setInitialFormValues={setVehicleFormValues}
        isLoading={isLoading}
        handleCreateNewVehicle={handleCreateNewVehicle}
        handleUpdateVehicle={handleUpdateVehicle}
        handleCloseVehicleModal={handleCloseVehicleModal}
      />

      <VehicleModalDelete
        flow={vehicleDeleteFormValues.flow}
        isLoading={isLoading}
        vehicleTitle={vehicleDeleteFormValues.vehicleTitle}
        vehicleId={vehicleDeleteFormValues.vehicleId}
        handleCloseVehicleModalDelete={handleCloseVehicleModalDelete}
        handleDeleteVehicle={handleDeleteVehicle}
      />

      <VehicleDetailsModal
        initialFormValues={vehicleFormValues}
        isLoading={isLoading}
        handleCloseVehicleDetailsModal={handleCloseVehicleDetailsModal}
      />
    </>
  );
};
