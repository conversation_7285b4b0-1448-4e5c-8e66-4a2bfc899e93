import { ReservationFormValues, ReservationModalFlow } from "src/common/types";
import { MainModal } from "src/common/components";
import { ReservationEditForm } from "./ReservationEditForm";

interface ReservationEditModalProps {
  flow?: ReservationModalFlow;
  isLoading: boolean;
  handleCloseReservationEditModal: () => void;
  handleEditReservation: (values: ReservationFormValues) => void;
  initialFormValues: ReservationFormValues;
  fullUpdate?: boolean;
}
export const ReservationEditModal: React.FC<ReservationEditModalProps> = ({
  isLoading,
  handleCloseReservationEditModal,
  handleEditReservation,
  initialFormValues,
  fullUpdate,
}) => {
  return (
    <MainModal
      title={`${initialFormValues.flow || ""} Reservation ${
        initialFormValues?.reservationId
      }`}
      isOpen={!!initialFormValues.flow}
      handleClose={handleCloseReservationEditModal}
      maxWidth="lg"
    >
      <ReservationEditForm
        initialFormValues={initialFormValues}
        isLoading={isLoading}
        handleSubmit={handleEditReservation}
        handleClose={handleCloseReservationEditModal}
        fullUpdate={fullUpdate}
      />
    </MainModal>
  );
};
