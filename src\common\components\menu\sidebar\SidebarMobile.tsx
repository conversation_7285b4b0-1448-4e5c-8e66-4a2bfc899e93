import { styled} from "@mui/material/styles";
import List from "@mui/material/List";
import { useTranslation } from "react-i18next";
import Divider from "@mui/material/Divider";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import { staticItems } from "../static/items";
import { Link as RouterLink, useLocation } from "react-router-dom";
import { Box, Drawer, Tooltip, Typography } from "@mui/material";
import { useCallback, useEffect, useState } from "react";
import { FeatureFlag } from "../../featureflag/FeatureFlag";

const drawerWidth = 260;

const DrawerHeader = styled("div")(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  padding: theme.spacing(0, 1),
  // necessary for content to be below app bar
  ...theme.mixins.toolbar,
}));

interface SideBarProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export const SideBarMobile: React.FC<SideBarProps> = ({ open, setOpen }) => {
  const location = useLocation();
  const params = location.pathname.substring(1) + location.search;


  const { t } = useTranslation(["common", "operator"]);


  const getSelectedId = useCallback(() => {
    if (!params.includes("settings")) {
      const foundItem = staticItems.find(({ path }) => params.includes(path));
      return foundItem?.id;
    } else {
      const foundItem = staticItems.find(({ path }) =>
        path.includes("settings")
      );
      return foundItem?.id;
    }
  }, [params]);

  const [selectedId, setSelectedId] = useState<string | undefined>(
    getSelectedId()
  );

  useEffect(() => {
    if (getSelectedId()) {
      setSelectedId(getSelectedId());
    }
  }, [params, getSelectedId]);

  const handleClick = (e: React.SyntheticEvent, itemId: string) => {
    setSelectedId(itemId);
    setOpen(false);
  };

  return (
    <Drawer open={open} variant="temporary" sx={{ width: 260 }}>
      <DrawerHeader>
        <img src="/logo.png" width={150} height={"auto"} alt="" />
      </DrawerHeader>
      <Divider />
      <List sx={{ width: 260 }}>
        {staticItems.map((item) => (
          <FeatureFlag key={item.id} flags={item.flags}>
            <div key={item.id}>
              {item.hasDivider ? <Divider /> : null}
              {item.hasTitle ? (
                <Typography
                  variant="body1"
                  sx={{ fontSize: "12px", color: "text.secondary", pl: 3 }}
                >
                  {open && item.title}
                </Typography>
              ) : null}
              <ListItemButton
                selected={getSelectedId() === item.id}
                onClick={(e: React.SyntheticEvent) => handleClick(e, item.id)}
                component={RouterLink}
                to={item.path}
                key={item.id}
                sx={{
                  minHeight: 48,
                  justifyContent: open ? "initial" : "center",
                  px: 2.5,
                }}
              >
                <Tooltip title={t(item.menuName)}>
                  <ListItemIcon
                    sx={{
                      minWidth: 0,
                      mr: open ? 3 : "auto",
                      justifyContent: "center",
                    }}
                  >
                    {item.icon}
                  </ListItemIcon>
                </Tooltip>

                <ListItemText
                  primary={t(item.menuName)}
                  sx={{ opacity: open ? 1 : 0 }}
                />
              </ListItemButton>
            </div>
          </FeatureFlag>
        ))}
      </List>
    </Drawer>
  );
};
