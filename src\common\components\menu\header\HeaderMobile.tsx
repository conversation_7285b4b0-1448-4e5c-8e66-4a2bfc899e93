import { useRecoilValue } from "recoil";
import { styled } from "@mui/material/styles";
import MuiAppBar, { AppBarProps as MuiAppBarProps } from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import MenuIcon from "@mui/icons-material/Menu";
import LogoutIcon from "@mui/icons-material/Logout";
import { useTranslation } from "react-i18next";

import { themeState } from "src/common/state";
import { Box, Button, useTheme } from "@mui/material";
import { useLogoutUser } from "src/common/api/auth";
import { FeatureFlag } from "../../featureflag/FeatureFlag";
import LanguageSwitcher from "../../languageSwitcher/LanguageSwitcher";

const drawerWidth = 260;

interface AppBarProps extends MuiAppBarProps {
  open?: boolean;
}

interface HeaderProps {
  open: boolean;
  handleDrawerToggle(): void;
}

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== "open"
})<AppBarProps>(({ theme, open }) => ({
  zIndex: theme.zIndex.drawer + 1,
  boxShadow: "none",
  borderBottom: `1px solid ${theme.palette.mode === "dark" ? null : "#ccc"}`,
  transition: theme.transitions.create(["width", "margin"], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen
  }),
  ...(open && {
    marginLeft: drawerWidth,
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create(["width", "margin"], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen
    })
  })
}));

export const HeaderMobile: React.FC<HeaderProps> = ({
  open,
  handleDrawerToggle
}) => {
  const theme = useRecoilValue(themeState);
  const customTheme = useTheme();
  const { t } = useTranslation(["common", "manager", "dispatcher"]);

  const { mutate: handleLogoutUser } = useLogoutUser();

  return (
    <AppBar
      position="fixed"
      open={open}
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
        bgcolor:
          theme === "light" ? customTheme.palette.primary.contrastText : null
      }}
    >
      <Toolbar
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between"
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            width: "100%"
          }}
        >
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={handleDrawerToggle}
            edge="start"
            sx={{
              marginRight: 1,
              color: "black"
            }}
          >
            <MenuIcon />
          </IconButton>
        </Box>
        <Box sx={{ marginLeft: 2,display:'flex', flexDirection:'row', gap:1 }}>
        <LanguageSwitcher />
          <IconButton
            onClick={() => {
              handleLogoutUser();
            }}
          >
            <LogoutIcon color="primary" />
          </IconButton>
        </Box>
      </Toolbar>
    </AppBar>
  );
};
