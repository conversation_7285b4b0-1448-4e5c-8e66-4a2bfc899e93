import { atom } from "recoil";
import {
  ContainerModalFormValues,
  DeletePriceListModalValues,
  PriceListFormValues,
  ReviewPriceListModalValues,
} from "../types/priceList";
import {
  CONTAINER_FORM_DEFAULT_VALUES,
  PRICELIST_DELETE_DEFAULT,
  PRICELIST_FORM_VALUES_DEFAULT,
  PRICELIST_REVIEW_DEFAULT,
} from "../constants/priceList";

export const priceListFormValuesState = atom<PriceListFormValues>({
  key: "priceListFormValuesState",
  default: PRICELIST_FORM_VALUES_DEFAULT,
});

export const priceListDeleteValuesState = atom<DeletePriceListModalValues>({
  key: "priceListDeleteValuesState",
  default: PRICELIST_DELETE_DEFAULT,
});

export const priceListReviewValuesState = atom<ReviewPriceListModalValues>({
  key: "priceListReviewValuesState",
  default: PRICELIST_REVIEW_DEFAULT,
});

export const containerFormValuesState = atom<ContainerModalFormValues>({
  key: "containerFormValuesState",
  default: CONTAINER_FORM_DEFAULT_VALUES,
});
