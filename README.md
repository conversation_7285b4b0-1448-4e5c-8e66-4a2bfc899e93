## Global State

- Recoil is used for global state that did not come from the API.

## API

- React query is used for all API calls. React query becomes global state for all data that came from API.

## Forms

- Formik is used for all form fields

## Development set up

- Obtain `.env.development` file and place it into the root of the repo

- `npm install`

- `npm start`

- go to `http://localhost:3000/`
