import { Stack } from "@mui/material";
import React from "react";
import { useTranslation } from "react-i18next";
import { CeButton, MainModal } from "src/common/components";

interface DeletePartnerModalProps {
  isModalOpen: boolean;
  handleCloseModal: () => void;
  onDeleteClick: () => void;
}
const DeletePartnerModal = ({
  isModalOpen,
  handleCloseModal,
  onDeleteClick,
}: DeletePartnerModalProps) => {
  const { t } = useTranslation(["common"]);
  return (
    <MainModal
      title="Delete Partner"
      isOpen={isModalOpen}
      handleClose={handleCloseModal}
      shouldRenderDialogActions
      helperText={t("delete-partner")}
      actionsChildren={
        <Stack direction="row" alignItems="center" gap={1} px={2}>
          <CeButton variant="text" onClick={handleCloseModal} size="medium">
            Cancel
          </CeButton>
          <CeButton onClick={onDeleteClick} size="medium">
            Delete Partner
          </CeButton>
        </Stack>
      }
    ></MainModal>
  );
};

export default DeletePartnerModal;
