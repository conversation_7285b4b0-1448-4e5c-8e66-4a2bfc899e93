import { FC, useEffect, useState } from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { Stack, Chip, Typography, IconButton, Tooltip } from "@mui/material";

import { useRecoilState } from "recoil";

import { DataGrid } from "@mui/x-data-grid";
import { useTranslation } from "react-i18next";

import {
  CancelContractDto,
  Contract,
  ContractResponse,
  Role,
} from "src/common/types";

import { priceListReviewValuesState } from "src/common/state/priceList";

import {
  getCurrentUser,
  useAcceptContract,
  useAddSuspensionPeriod,
  useRejectContract,
  useRequestContract,
  useUnavailablePeriods,
  useVehicles,
} from "src/common/api";
import {
  CANCEL_CONTRACT_DEFAULT_FORM_VALUES,
  CONTRACT_DEFAULT_FORM_VALUES,
} from "src/common/constants";
import {
  cancelOrSuspendContractState,
  contractFormValuesState,
} from "src/common/state";
import { PRICELIST_REVIEW_DEFAULT } from "src/common/constants/priceList";

import { buttonTextTransform } from "src/common/components/custom/customCss";
import { CeDataGridToolbar } from "src/common/components/custom/company/CeDataGridToolbar";
import {
  Building06Icon,
  Cancel01Icon,
  SearchList01Icon,
  UnavailableIcon,
} from "@hugeicons/react";
import { GridColumnTypography } from "src/common/components/custom/company/GridColumnTypography";
import { CeButton } from "src/common/components";
import usePersistentGridState from "src/common/utils/gridState";
import { useLocation } from "react-router-dom";
import { ContractFormDrawer } from "../createContractForm/ContractFormDrawer";
import { usePartners } from "src/common/api/partner";
import ContractResponseModal from "../ContractResponseModal";
import PriceListReviewModal from "../PriceListReviewModal";
import CancelOrSuspendContractModal from "../CancelOrSuspendContractModal";

interface ContractDatagridProps {
  data: Contract[];
  isFetchingContracts: boolean;
  refetchContracts: () => void;
  shouldRenderRefreshButton: boolean;
  shouldRenderAddButton: boolean;
  shouldRenderEditActionsColumn: boolean;
  page: number;
  pageSize: number;
  total: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
}

export const ContractDatagrid: FC<ContractDatagridProps> = ({
  data,
  shouldRenderRefreshButton,
  shouldRenderAddButton,
  shouldRenderEditActionsColumn,
  isFetchingContracts,
  refetchContracts,
  page,
  pageSize,
  total,
  onPageChange,
  onPageSizeChange,
}) => {
  const location = useLocation();
  const { t } = useTranslation(["dispatcher", "common"]);
  const [contractResponse, setContractResponse] =
    useState<ContractResponse | null>(null);

  const currentUser = getCurrentUser();
  const isOperatorManager = currentUser?.role === Role.OEPRATOR_MANAGER;
  const [contractFormValues, setContractFormValues] = useRecoilState(
    contractFormValuesState
  );

  const [cancelContractValues, setCancelContractValues] = useRecoilState(
    cancelOrSuspendContractState
  );

  const { data: allVehicles } = useVehicles(
    { operatorManagerId: currentUser?.id },
    !!contractFormValues.flow
  );

  const {
    data: allPartners,
    isLoading: isLoadingPartners,
    refetch: refetchPartners,
  } = usePartners(
    {
      expressions: [],
      sortModel: [],
    },
    !!contractFormValues.flow
  );

  const {
    mutate: handleRequestContract,
    isSuccess: isRequestContractSuccess,
    isLoading: isRequestContractLoading,
  } = useRequestContract();

  const {
    mutate: rejectContract,
    isSuccess: isRejectContractSuccess,
    isLoading: isRejectContractLoading,
  } = useRejectContract();
  const {
    mutate: acceptContract,
    isSuccess: isAcceptContractSuccess,
    isLoading: isAcceptContractLoading,
  } = useAcceptContract();
  const {
    mutate: suspendContract,
    isSuccess: isSuspendContractSuccess,
    isLoading: isSuspendContractLoading,
  } = useAddSuspensionPeriod();
  const [pricelistReviewValues, setpricelistReviewValues] = useRecoilState(
    priceListReviewValuesState
  );

  const localStorageKey = location.pathname.slice(1);

  const [gridState, updateGridStatePart] = usePersistentGridState(
    localStorageKey,
    page,
    pageSize,
    [{ field: "status", sort: "asc" }]
  );

  const isLoading =
    isRequestContractLoading ||
    isRejectContractLoading ||
    isFetchingContracts ||
    isAcceptContractLoading;

  const handleCloseContractModal = () => {
    if (!isLoading) {
      setContractFormValues(CONTRACT_DEFAULT_FORM_VALUES);
    }
  };
  const handleOpenContractModal = () => {
    setContractFormValues({
      ...CONTRACT_DEFAULT_FORM_VALUES,
      flow: "Request",
    });
  };
  const handleClosePriceListModal = () => {
    if (!isLoading) {
      setpricelistReviewValues(PRICELIST_REVIEW_DEFAULT);
    }
  };
  const handleCloseCancelModal = () => {
    setCancelContractValues(CANCEL_CONTRACT_DEFAULT_FORM_VALUES);
  };

  const handleContractResponse = (response: ContractResponse) => {
    setContractResponse(response);
  };

  const handleRejectContract = (comment: string) => {
    const { contractId } = pricelistReviewValues;
    if (!contractId) {
      throw new Error("Contract ID was not provided");
    }

    const payload = {
      comment,
    };
    rejectContract({ id: contractId, payload });
  };

  const handleAcceptContract = (comment: string) => {
    const { contractId } = pricelistReviewValues;

    if (!contractId) {
      throw new Error("Contract ID was not provided");
    }

    const payload = {
      comment,
    };
    acceptContract({ id: contractId, payload });
  };

  useEffect(() => {
    if (isRequestContractSuccess) {
      setContractFormValues(CONTRACT_DEFAULT_FORM_VALUES);
    }
  }, [isRequestContractSuccess, setContractFormValues]);

  useEffect(() => {
    if (isAcceptContractSuccess) {
      setpricelistReviewValues(PRICELIST_REVIEW_DEFAULT);
      setContractResponse(null);
    }
  }, [isAcceptContractSuccess, setContractFormValues]);

  useEffect(() => {
    if (isRejectContractSuccess) {
      setpricelistReviewValues(PRICELIST_REVIEW_DEFAULT);
      setContractResponse(null);
      setCancelContractValues(CANCEL_CONTRACT_DEFAULT_FORM_VALUES);
    }
  }, [isRejectContractSuccess, setContractFormValues, setCancelContractValues]);

  useEffect(() => {
    if (isSuspendContractSuccess) {
      setCancelContractValues(CANCEL_CONTRACT_DEFAULT_FORM_VALUES);
    }
  }, [isSuspendContractSuccess, setCancelContractValues]);

  const columns: GridColDef[] = [
    {
      field: "editActions",
      headerName: t("common:actions"),
      sortable: false,
      width: isOperatorManager ? 100 : 200,
      renderCell: (params: GridRenderCellParams<string>) => {
        return renderEditActions(params);
      },
      filterable: false,
    },
    {
      field: "id",
      headerName: "Id",
      type: "number",
      headerAlign: "left",
      align: "left",
      renderCell: (params) => <GridColumnTypography value={params.row.id} />,
    },
    {
      field: "status",
      headerName: `${t("status")}`,
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => {
        const status: string = params.row.status || "";

        return (
          <Chip
            sx={{ m: 0.5, ml: 0, fontWeight: 500, fontSize: 13 }}
            color={
              status === "pending"
                ? "default"
                : status === "approved"
                ? "secondary"
                : "error"
            }
            variant="outlined"
            label={status}
          />
        );
      },
    },

    {
      field: "operatorManager",
      headerName: `${t("common:company")}`,
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => {
        const companyName = isOperatorManager
          ? params.row?.dispatcherCompany?.name
          : params.row?.operatorCompany?.name;

        if (!companyName) {
          return "";
        }

        return (
          <Chip
            label={
              <Stack direction="row" spacing={0.5} alignItems="center">
                <Building06Icon
                  size={16}
                  color="currentColor"
                  variant="stroke"
                />

                <Typography
                  variant="body2"
                  color="textSecondary"
                  noWrap
                  sx={{
                    flex: 1,
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  }}
                >
                  {companyName}
                </Typography>
              </Stack>
            }
          />
        );
      },
    },
    {
      field: "comment",
      headerName: `${t("common:comment")}`,
      headerAlign: "left",
      align: "left",
      width: 500,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.comment} />
      ),
    },
    {
      field: "created_by",
      headerName: "Created By",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.createdBy} />
      ),
    },
    {
      field: "created_at",
      headerName: "Created At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.created_at} />
      ),
    },
    {
      field: "updated_at",
      headerName: "Updated At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.updated_at} />
      ),
    },
    {
      field: "deleted_at",
      headerName: "Deleted At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.deleted_at} />
      ),
    },
  ];

  const renderEditActions = (params: GridRenderCellParams) => {
    const contract: Contract = params.row;
    const pricelistId = contract.pricelistId;
    return (
      <Stack
        direction="row"
        justifyContent="center"
        alignItems="center"
        spacing={2}
      >
        <Stack direction="row" justifyContent="center" alignItems="center">
          {isOperatorManager ? (
            <IconButton
              color="error"
              disabled={contract.status !== "approved"}
              onClick={() =>
                setCancelContractValues({
                  ...cancelContractValues,
                  contractId: contract.id,
                  flow: "Cancel",
                })
              }
            >
              <Tooltip title="Cancel">
                <Cancel01Icon size={18} variant={"stroke"} />
              </Tooltip>
            </IconButton>
          ) : (
            <IconButton
              color="error"
              disabled={contract.status !== "approved"}
              onClick={() =>
                setCancelContractValues({
                  ...cancelContractValues,
                  startDate: contract.startDate || null,
                  endDate: contract.endDate || null,
                  contractId: contract.id,
                  flow: "Suspend",
                })
              }
            >
              <Tooltip title="Suspend">
                <UnavailableIcon size={18} variant={"stroke"} color="inherit" />
              </Tooltip>
            </IconButton>
          )}
        </Stack>
        {!isOperatorManager ? (
          <CeButton
            size="small"
            variant="outlined"
            sx={buttonTextTransform}
            startIcon={<SearchList01Icon size={16} variant={"stroke"} />}
            disabled={contract.status !== "pending"}
            onClick={() => {
              setpricelistReviewValues({
                contractId: contract.id,
                pricelistId,
                flow: "Review",
                vehicleId: contract.vehicleId || null,
              });
            }}
          >
            Review
          </CeButton>
        ) : null}
      </Stack>
    );
  };

  return (
    <>
      <DataGrid
        sx={{
          border: "none",
          paddingTop: 0.5,
        }}
        pagination
        page={gridState.page - 1}
        pageSize={gridState.pageSize}
        rowsPerPageOptions={[20, 40, 60, 80, 100]}
        rowCount={total}
        onPageChange={(newPage) => {
          onPageChange(newPage);
          updateGridStatePart("page", newPage);
        }}
        onPageSizeChange={(newPageSize) => {
          onPageSizeChange(newPageSize);
          updateGridStatePart("pageSize", newPageSize);
        }}
        onColumnVisibilityModelChange={(model) =>
          updateGridStatePart("columnVisibilityModel", model)
        }
        filterModel={gridState.filterModel}
        onFilterModelChange={(model) =>
          updateGridStatePart("filterModel", model)
        }
        sortModel={gridState.sortModel}
        onSortModelChange={(model) => updateGridStatePart("sortModel", model)}
        paginationMode="server"
        components={{
          Toolbar: () => (
            <CeDataGridToolbar
              shouldRenderRefreshButton={shouldRenderRefreshButton}
              shouldRenderAddButton={shouldRenderAddButton}
              addButtonClickHandler={handleOpenContractModal}
              addButtonDescription="Add Contract"
              onRefreshButtonClick={refetchContracts}
            />
          ),
        }}
        columns={columns}
        rows={data}
        disableSelectionOnClick
        initialState={{
          columns: {
            columnVisibilityModel: {
              ...gridState.columnVisibilityModel,
              editActions: shouldRenderEditActionsColumn,
            },
          },
        }}
      />
      <PriceListReviewModal
        pricelistId={pricelistReviewValues.pricelistId}
        handleClose={handleClosePriceListModal}
        isOpen={pricelistReviewValues.flow === "Review"}
        isLoading={isLoading}
        onRespond={handleContractResponse}
      />
      <ContractResponseModal
        title={"test"}
        closeContractDialog={() => setContractResponse(null)}
        isLoading={isLoading}
        contractResponse={contractResponse}
        handleAcceptContract={handleAcceptContract}
        handleRejectContract={handleRejectContract}
      />
      <ContractFormDrawer
        initialFormValues={contractFormValues}
        handleCloseContractModal={handleCloseContractModal}
        handleCreateContract={handleRequestContract}
        vehicles={allVehicles?.data || []}
        partners={allPartners?.data || []}
        isLoading={isRequestContractLoading}
      />
      <CancelOrSuspendContractModal
        initialValues={cancelContractValues}
        handleClose={handleCloseCancelModal}
        handleSuspendContract={suspendContract}
        handleCancelContract={rejectContract}
      />
    </>
  );
};
