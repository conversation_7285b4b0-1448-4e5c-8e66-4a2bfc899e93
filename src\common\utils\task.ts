import {
  CreateTaskDto,
  Task,
  TaskFormFlow,
  TaskFormValues,
  UpdateTaskDto
} from "../types/tasks";

export const turnTaskFormValuesIntoCreateDto = (
  values: TaskFormValues
): CreateTaskDto => {
  return {
    labelId: values.label?.id,
    priority: values.priority!,
    status: values.status!,
    assigneeId: values.assignee?.id!,
    startDate: values.startDate,
    dueDate: values.dueDate!,
    description: values.description!
  };
};

export const turnTaskFormValuesIntoUpdateDto = (
  values: TaskFormValues
): UpdateTaskDto => {
  return {
    taskId: values.taskId!,
    labelId: values.label?.id,
    startDate: values.startDate,
    priority: values.priority!,
    status: values.status!,
    assigneeId: values.assignee?.id!,
    dueDate: values.dueDate!,
    description: values.description!
  };
};

export const turnTaskIntoFormValues = (values: Task): TaskFormValues => {
  return {
    taskId: values.id,
    startDate: values.startDate,
    dueDate: values.dueDate,
    label: values.label || null,
    priority: values.priority,
    status: values.status,
    description: values.description,
    assignee: values.assignee,
    comments: values.comments || [],
    activities: values.activities || [],
    flow: TaskFormFlow.UPDATE,
    assigneeBeingSet: false,
    labelBeingSet: false,
    dueDateBeingSet: false,
    descriptionBeingSet: false,
    priorityBeingSet: false,
    statusBeingSet: false
  };
};
