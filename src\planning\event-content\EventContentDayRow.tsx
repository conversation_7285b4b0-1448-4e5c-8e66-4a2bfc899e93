import { EventContentArg } from "@fullcalendar/core";
import { TickDouble02Icon } from "@hugeicons/react";
import { Box, SxProps, Typography } from "@mui/material";
import { getCurrentUser } from "src/common/api";

import { Bullet } from "src/common/components/custom/Bullet";
import {
  CalendarEventWrapper,
  CalendarEventTypography,
} from "src/common/components/custom/CalendarEventWrapper";
import { HtmlTooltipResource } from "src/common/components/custom/HtmlTooltipResource";
import { STATUS_COLORS } from "src/common/constants";
import { JobStatus } from "src/common/types";

interface EventContentDayRowProps {
  arg: EventContentArg;
  eventsnumber?: number;
  calendarWrapperStyle?: SxProps;
  calendarTypographyStyle?: SxProps;
  bulletStyles?: SxProps;
}

export const EventContentDayRow = (props: EventContentDayRowProps) => {
  const currentUser = getCurrentUser();
  const isOperatorManager = currentUser?.role === 3;

  const title = props.arg.event.title || "";
  const extendedProps = props.arg.event.extendedProps || {};
  const schedule = extendedProps?.schedule || "-";
  const address = extendedProps?.address || "-";
  const city = extendedProps.city || "-";
  const quantity = extendedProps.quantity || 0;
  const hasBeenRead = extendedProps?.hasBeenRead;
  const jobStatus = extendedProps?.jobStatus || "-";

  const { backgroundColor, borderColor, color } = (STATUS_COLORS[
    props.arg.event.extendedProps.jobStatus
  ] || STATUS_COLORS.default) as {
    backgroundColor: string;
    borderColor: string;
    color: string;
  };
  return (
    <HtmlTooltipResource
      placement="bottom-end"
      title={
        <Box>
          <Box
            alignItems="center"
            display="flex"
            justifyContent={"flex-start"}
            mt={0.5}
            gap="5px"
          >
            <Typography>Time:</Typography>
            <Typography>{schedule}</Typography>
          </Box>
          {currentUser?.role === 3 && (
            <Box
              alignItems="center"
              display="flex"
              justifyContent={"flex-start"}
              mt={0.5}
              gap="5px"
            >
              <Typography>Company Name:</Typography>
              <Typography>{title}</Typography>
            </Box>
          )}

          <Box
            alignItems="center"
            display="flex"
            justifyContent={"flex-start"}
            mt={0.5}
            gap="5px"
          >
            <Typography>Adress:</Typography>
            <Typography>{address}</Typography>
          </Box>
          <Box
            alignItems="center"
            display="flex"
            justifyContent={"flex-start"}
            mt={0.5}
            gap="5px"
          >
            <Typography>Amount:</Typography>
            <Typography>{`${quantity}m\u00b3`}</Typography>
          </Box>
        </Box>
      }
    >
      <Box>
        <CalendarEventWrapper
          borderColor={borderColor}
          backgroundColor={backgroundColor}
          color={color}
          eventsnumber={props.eventsnumber}
          sx={props.calendarWrapperStyle}
        >
          <CalendarEventTypography
            eventsnumber={props.eventsnumber}
            sx={{
              lineHeight: "14.3px",
              textOverflow: "ellipsis",
              overflow: "hidden",
              whiteSpace: "nowrap",
              ...props.calendarTypographyStyle,
            }}
          >
            {hasBeenRead && jobStatus === JobStatus.NOT_STARTED ? (
              <TickDouble02Icon
                size={10}
                color="currentColor"
                variant={"solid"}
                style={{
                  padding: 0,
                  marginRight: "1px",
                  transform: "translateY(1px)",
                }}
              />
            ) : null}
            {schedule}
            <Bullet
              sx={{
                mx: "5px",
                ...props.bulletStyles,
              }}
            />
            {isOperatorManager && (
              <>
                {props.arg.event?.title}
                <Bullet
                  sx={{
                    mx: "5px",
                    ...props.bulletStyles,
                  }}
                />
              </>
            )}
            {address}, {city}
            {props.eventsnumber === 2 && <br />}
            <Bullet
              sx={{
                m: props.eventsnumber === 2 ? "0 5px 0 0" : "0 5px",
                ...props.bulletStyles,
              }}
            />
            {`${quantity}m\u00b3`}
          </CalendarEventTypography>
        </CalendarEventWrapper>
      </Box>
    </HtmlTooltipResource>
  );
};
