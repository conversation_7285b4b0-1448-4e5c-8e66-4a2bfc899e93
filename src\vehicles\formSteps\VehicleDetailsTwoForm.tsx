import {
  Autocomplete,
  Checkbox,
  FormControl,
  FormControlLabel,
  InputAdornment,
  Stack,
  TextField,
} from "@mui/material";
import { useFormik } from "formik";
import * as yup from "yup";
import { useTranslation } from "react-i18next";
import {
  BoomUnfoldingSystemType,
  CreateVehicleDto,
  UpdateVehicleDto,
  VehicleFormValues,
  boomUnfoldingSystemTypes,
} from "src/common/types";
import { number, string } from "yup";
import { vehicleValuesRanges } from "src/common/constants";
import { VehicleFormButtons } from "./VehicleFormButtons";
import { SetterOrUpdater } from "recoil";
import { CeTextField } from "src/common/components";

interface VehicleDetailsTwoFormProps {
  initialFormValues: VehicleFormValues;
  activeStep: number;
  steps: string[];
  isLoading: boolean;
  setInitialFormValues: SetterOrUpdater<VehicleFormValues>;
  handleCreateNewVehicle?: (args: CreateVehicleDto) => void;
  handleUpdateVehicle?: (args: UpdateVehicleDto) => void;
  handleBack: () => void;
  handleNext: () => void;
}

export const VehicleDetailsTwoForm: React.FC<VehicleDetailsTwoFormProps> = ({
  initialFormValues,
  activeStep,
  steps,
  isLoading,
  setInitialFormValues,
  handleNext,
  handleCreateNewVehicle,
  handleUpdateVehicle,
  handleBack,
}) => {
  const { t } = useTranslation(["common", "manager", "dispatcher"]);

  const formik = useFormik<VehicleFormValues>({
    initialValues: initialFormValues,
    enableReinitialize: true,
    validationSchema: yup.object({
      availableFlexiblePipeLength80Mm: number()
        .min(
          vehicleValuesRanges["availableFlexiblePipeLength80Mm"].min,
          t("manager:out-of-range")
        )
        .max(
          vehicleValuesRanges["availableFlexiblePipeLength80Mm"].max,
          t("manager:out-of-range")
        )
        .required("required")
        .nullable(),
      availableFlexiblePipeLength90Mm: number()
        .min(
          vehicleValuesRanges["availableFlexiblePipeLength90Mm"].min,
          t("manager:out-of-range")
        )
        .max(
          vehicleValuesRanges["availableFlexiblePipeLength90Mm"].max,
          t("manager:out-of-range")
        )
        .required("required")
        .nullable(),
      availableFlexiblePipeLength100Mm: number()
        .min(
          vehicleValuesRanges["availableFlexiblePipeLength100Mm"].min,
          t("manager:out-of-range")
        )
        .max(
          vehicleValuesRanges["availableFlexiblePipeLength100Mm"].max,
          t("manager:out-of-range")
        )
        .required("required")
        .nullable(),
      availableFlexiblePipeLength120Mm: number()
        .min(
          vehicleValuesRanges["availableFlexiblePipeLength120Mm"].min,
          t("manager:out-of-range")
        )
        .max(
          vehicleValuesRanges["availableFlexiblePipeLength120Mm"].max,
          t("manager:out-of-range")
        )
        .required("required")
        .nullable(),
      maxDownwardReach: number()
        .min(
          vehicleValuesRanges["maxDownwardReach"].min,
          t("manager:out-of-range")
        )
        .max(
          vehicleValuesRanges["maxDownwardReach"].max,
          t("manager:out-of-range")
        )
        .required("required")
        .nullable(),
      numberOfBoomSections: number()
        .min(
          vehicleValuesRanges["numberOfBoomSections"].min,
          t("manager:out-of-range")
        )
        .max(
          vehicleValuesRanges["numberOfBoomSections"].max,
          t("manager:out-of-range")
        )
        .nullable(),
      minUnfoldingHeight: number()
        .min(
          vehicleValuesRanges["minUnfoldingHeight"].min,
          t("manager:out-of-range")
        )
        .max(
          vehicleValuesRanges["minUnfoldingHeight"].max,
          t("manager:out-of-range")
        )
        .nullable(),
      boomRotation: number()
        .min(vehicleValuesRanges["boomRotation"].min, t("manager:out-of-range"))
        .max(vehicleValuesRanges["boomRotation"].max, t("manager:out-of-range"))
        .nullable(),
      frontOutriggerSpan: number()
        .min(
          vehicleValuesRanges["frontOutriggerSpan"].min,
          t("manager:out-of-range")
        )
        .max(
          vehicleValuesRanges["frontOutriggerSpan"].max,
          t("manager:out-of-range")
        )
        .nullable(),
      rearOutriggerSpan: number()
        .min(
          vehicleValuesRanges["rearOutriggerSpan"].min,
          t("manager:out-of-range")
        )
        .max(
          vehicleValuesRanges["rearOutriggerSpan"].max,
          t("manager:out-of-range")
        )
        .nullable(),
      pipeLengthForSecondTechnician: number().required("required").nullable(),
      boomUnfoldingSystem: string().required("required").nullable(),
    }),
    onSubmit: (values) => {
      // Form is valid go next step
      const isLastStep = activeStep === steps.length - 1;

      if (
        isLastStep &&
        initialFormValues.flow === "Create" &&
        handleCreateNewVehicle
      ) {
        const payload: CreateVehicleDto = {
          ...initialFormValues,
          ...values,
          operatorId: initialFormValues.operator?.id!,
        };

        handleCreateNewVehicle(payload);
      }

      if (
        isLastStep &&
        initialFormValues.flow === "Update" &&
        handleUpdateVehicle
      ) {
        const payload: UpdateVehicleDto = {
          ...initialFormValues,
          ...values,
          vehicleId: initialFormValues.vehicleId!,
          operatorId: initialFormValues.operator?.id!,
        };

        handleUpdateVehicle(payload);
      }
    },
  });

  return (
    <Stack
      component="form"
      spacing={2}
      noValidate
      onSubmit={formik.handleSubmit}
    >
      <Stack direction={"row"} spacing={2}>
        <CeTextField
          fullWidth
          id="availableFlexiblePipeLength80Mm"
          name="availableFlexiblePipeLength80Mm"
          label={t("common:available-flexible-pipe-length-80")}
          size="small"
          type="number"
          InputLabelProps={{ shrink: true }}
          value={formik.values.availableFlexiblePipeLength80Mm || ""}
          onChange={formik.handleChange}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                {t("meter-symbol")}
              </InputAdornment>
            ),
          }}
          error={
            formik.touched.availableFlexiblePipeLength80Mm &&
            Boolean(formik.errors.availableFlexiblePipeLength80Mm)
          }
          helperText={
            formik.touched.availableFlexiblePipeLength80Mm &&
            formik.errors.availableFlexiblePipeLength80Mm
          }
          disabled={isLoading}
          required
        />
        <CeTextField
          fullWidth
          id="availableFlexiblePipeLength90Mm"
          name="availableFlexiblePipeLength90Mm"
          label={t("common:available-flexible-pipe-length-90")}
          size="small"
          type="number"
          InputLabelProps={{ shrink: true }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                {t("meter-symbol")}
              </InputAdornment>
            ),
          }}
          value={formik.values.availableFlexiblePipeLength90Mm || ""}
          onChange={formik.handleChange}
          error={
            formik.touched.availableFlexiblePipeLength90Mm &&
            Boolean(formik.errors.availableFlexiblePipeLength90Mm)
          }
          helperText={
            formik.touched.availableFlexiblePipeLength90Mm &&
            formik.errors.availableFlexiblePipeLength90Mm
          }
          disabled={isLoading}
          required
        />
      </Stack>

      <Stack direction={"row"} spacing={2}>
        <CeTextField
          fullWidth
          id="availableFlexiblePipeLength100Mm"
          name="availableFlexiblePipeLength100Mm"
          label={t("common:available-flexible-pipe-length-100")}
          size="small"
          type="number"
          InputLabelProps={{ shrink: true }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                {t("meter-symbol")}
              </InputAdornment>
            ),
          }}
          value={formik.values.availableFlexiblePipeLength100Mm || ""}
          onChange={formik.handleChange}
          error={
            formik.touched.availableFlexiblePipeLength100Mm &&
            Boolean(formik.errors.availableFlexiblePipeLength100Mm)
          }
          helperText={
            formik.touched.availableFlexiblePipeLength100Mm &&
            formik.errors.availableFlexiblePipeLength100Mm
          }
          disabled={isLoading}
          required
        />

        <CeTextField
          fullWidth
          id="availableFlexiblePipeLength120Mm"
          name="availableFlexiblePipeLength120Mm"
          label={t("common:available-flexible-pipe-length-120")}
          size="small"
          type="number"
          InputLabelProps={{ shrink: true }}
          value={formik.values.availableFlexiblePipeLength120Mm || ""}
          onChange={formik.handleChange}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                {t("meter-symbol")}
              </InputAdornment>
            ),
          }}
          error={
            formik.touched.availableFlexiblePipeLength120Mm &&
            Boolean(formik.errors.availableFlexiblePipeLength120Mm)
          }
          helperText={
            formik.touched.availableFlexiblePipeLength120Mm &&
            formik.errors.availableFlexiblePipeLength120Mm
          }
          disabled={isLoading}
          required
        />
      </Stack>

      <Stack direction={"row"} spacing={2}>
        <CeTextField
          fullWidth
          id="numberOfBoomSections"
          name="numberOfBoomSections"
          label={t("number-of-boom-sections")}
          size="small"
          type="number"
          InputLabelProps={{ shrink: true }}
          value={formik.values.numberOfBoomSections || ""}
          onChange={formik.handleChange}
          error={
            formik.touched.numberOfBoomSections &&
            Boolean(formik.errors.numberOfBoomSections)
          }
          helperText={
            formik.touched.numberOfBoomSections &&
            formik.errors.numberOfBoomSections
          }
          disabled={isLoading}
        />
        <CeTextField
          fullWidth
          id="minUnfoldingHeight"
          name="minUnfoldingHeight"
          label={t("min-unfolding-height")}
          size="small"
          type="number"
          InputLabelProps={{ shrink: true }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                {t("meter-symbol")}
              </InputAdornment>
            ),
          }}
          value={formik.values.minUnfoldingHeight || ""}
          onChange={formik.handleChange}
          error={
            formik.touched.minUnfoldingHeight &&
            Boolean(formik.errors.minUnfoldingHeight)
          }
          helperText={
            formik.touched.minUnfoldingHeight &&
            formik.errors.minUnfoldingHeight
          }
          disabled={isLoading}
        />
      </Stack>

      <Stack direction={"row"} spacing={2}>
        <CeTextField
          fullWidth
          id="frontOutriggerSpan"
          name="frontOutriggerSpan"
          label={t("front-outriggers-span")}
          size="small"
          type="number"
          InputLabelProps={{ shrink: true }}
          value={formik.values.frontOutriggerSpan || ""}
          onChange={formik.handleChange}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                {t("meter-symbol")}
              </InputAdornment>
            ),
          }}
          error={
            formik.touched.frontOutriggerSpan &&
            Boolean(formik.errors.frontOutriggerSpan)
          }
          helperText={
            formik.touched.frontOutriggerSpan &&
            formik.errors.frontOutriggerSpan
          }
          disabled={isLoading}
        />
        <CeTextField
          fullWidth
          id="rearOutriggerSpan"
          name="rearOutriggerSpan"
          label={t("rear-outriggers-span")}
          size="small"
          type="number"
          InputLabelProps={{ shrink: true }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                {t("meter-symbol")}
              </InputAdornment>
            ),
          }}
          value={formik.values.rearOutriggerSpan || ""}
          onChange={formik.handleChange}
          error={
            formik.touched.rearOutriggerSpan &&
            Boolean(formik.errors.rearOutriggerSpan)
          }
          helperText={
            formik.touched.rearOutriggerSpan && formik.errors.rearOutriggerSpan
          }
          disabled={isLoading}
        />
      </Stack>

      <Stack direction={"row"} spacing={2}>
        <CeTextField
          fullWidth
          id="frontSideOpening"
          name="frontSideOpening"
          label={t("front-side-opening")}
          size="small"
          type="number"
          InputLabelProps={{ shrink: true }}
          value={formik.values.frontSideOpening || ""}
          onChange={formik.handleChange}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                {t("meter-symbol")}
              </InputAdornment>
            ),
          }}
          error={
            formik.touched.frontSideOpening &&
            Boolean(formik.errors.frontSideOpening)
          }
          helperText={
            formik.touched.frontSideOpening && formik.errors.frontSideOpening
          }
          disabled={isLoading}
        />
        <CeTextField
          fullWidth
          id="rearSideOpening"
          name="rearSideOpening"
          label={t("rear-side-opening")}
          size="small"
          type="number"
          InputLabelProps={{ shrink: true }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                {t("meter-symbol")}
              </InputAdornment>
            ),
          }}
          value={formik.values.rearSideOpening || ""}
          onChange={formik.handleChange}
          error={
            formik.touched.rearSideOpening &&
            Boolean(formik.errors.rearSideOpening)
          }
          helperText={
            formik.touched.rearSideOpening && formik.errors.rearSideOpening
          }
          disabled={isLoading}
        />
      </Stack>

      <Stack direction={"row"} spacing={2}>
        <CeTextField
          fullWidth
          id="frontPressureOnOutrigger"
          name="frontPressureOnOutrigger"
          label={t("front-pressure-on-outrigger")}
          size="small"
          type="number"
          InputLabelProps={{ shrink: true }}
          value={formik.values.frontPressureOnOutrigger || ""}
          onChange={formik.handleChange}
          InputProps={{
            endAdornment: <InputAdornment position="end">kN</InputAdornment>,
          }}
          error={
            formik.touched.frontPressureOnOutrigger &&
            Boolean(formik.errors.frontPressureOnOutrigger)
          }
          helperText={
            formik.touched.frontPressureOnOutrigger &&
            formik.errors.frontPressureOnOutrigger
          }
          disabled={isLoading}
        />
        <CeTextField
          fullWidth
          id="rearPressureOnOutrigger"
          name="rearPressureOnOutrigger"
          label={t("rear-pressure-on-outrigger")}
          size="small"
          type="number"
          InputLabelProps={{ shrink: true }}
          InputProps={{
            endAdornment: <InputAdornment position="end">kN</InputAdornment>,
          }}
          value={formik.values.rearPressureOnOutrigger || ""}
          onChange={formik.handleChange}
          error={
            formik.touched.rearPressureOnOutrigger &&
            Boolean(formik.errors.rearPressureOnOutrigger)
          }
          helperText={
            formik.touched.rearPressureOnOutrigger &&
            formik.errors.rearPressureOnOutrigger
          }
          disabled={isLoading}
        />
      </Stack>

      <Stack direction={"row"} spacing={2}>
        <CeTextField
          fullWidth
          id="invoicingPipesFrom"
          name="invoicingPipesFrom"
          label={t("invoicing-pipes-from")}
          size="small"
          type="number"
          InputLabelProps={{ shrink: true }}
          value={formik.values.invoicingPipesFrom || ""}
          onChange={formik.handleChange}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                {t("meter-symbol")}
              </InputAdornment>
            ),
          }}
          error={
            formik.touched.invoicingPipesFrom &&
            Boolean(formik.errors.invoicingPipesFrom)
          }
          helperText={
            formik.touched.invoicingPipesFrom &&
            formik.errors.invoicingPipesFrom
          }
          disabled={isLoading}
        />
        <CeTextField
          fullWidth
          id="pipeLengthForSecondTechnician"
          name="pipeLengthForSecondTechnician"
          label={t("pipe-length-for-second-technician")}
          size="small"
          type="number"
          InputLabelProps={{ shrink: true }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                {t("meter-symbol")}
              </InputAdornment>
            ),
          }}
          value={formik.values.pipeLengthForSecondTechnician || ""}
          onChange={formik.handleChange}
          error={
            formik.touched.pipeLengthForSecondTechnician &&
            Boolean(formik.errors.pipeLengthForSecondTechnician)
          }
          helperText={
            formik.touched.pipeLengthForSecondTechnician &&
            formik.errors.pipeLengthForSecondTechnician
          }
          required
          disabled={isLoading}
        />
      </Stack>

      <CeTextField
        fullWidth
        id="maxDownwardReach"
        name="maxDownwardReach"
        label={t("max-downward-reach")}
        size="small"
        type="number"
        InputLabelProps={{ shrink: true }}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">{t("meter-symbol")}</InputAdornment>
          ),
        }}
        value={formik.values.maxDownwardReach || ""}
        onChange={formik.handleChange}
        error={
          formik.touched.maxDownwardReach &&
          Boolean(formik.errors.maxDownwardReach)
        }
        helperText={
          formik.touched.maxDownwardReach && formik.errors.maxDownwardReach
        }
        disabled={isLoading}
        required
      />

      <CeTextField
        fullWidth
        id="boomRotation"
        name="boomRotation"
        label={t("boom-rotation")}
        size="small"
        type="number"
        InputLabelProps={{ shrink: true }}
        value={formik.values.boomRotation || ""}
        onChange={formik.handleChange}
        InputProps={{
          endAdornment: <InputAdornment position="end">°</InputAdornment>,
        }}
        error={
          formik.touched.boomRotation && Boolean(formik.errors.boomRotation)
        }
        helperText={formik.touched.boomRotation && formik.errors.boomRotation}
        disabled={isLoading}
      />

      <Autocomplete
        id="boomUnfoldingSystem"
        fullWidth
        value={formik.values.boomUnfoldingSystem || null}
        onChange={(event: any, nextValues: BoomUnfoldingSystemType | null) => {
          formik.setFieldValue("boomUnfoldingSystem", nextValues);
        }}
        onBlur={() => formik.setFieldTouched("boomUnfoldingSystem", true)}
        options={boomUnfoldingSystemTypes}
        filterSelectedOptions
        renderInput={(params) => (
          <CeTextField
            {...params}
            InputLabelProps={{ shrink: true }}
            error={
              formik.touched.boomUnfoldingSystem &&
              Boolean(formik.errors.boomUnfoldingSystem)
            }
            helperText={
              formik.touched.boomUnfoldingSystem &&
              formik.errors.boomUnfoldingSystem
            }
            required
            label={t("boom-unfolding-system")}
            size="small"
          />
        )}
      />

      <FormControl fullWidth sx={{ marginBottom: 2 }}>
        <FormControlLabel
          checked={formik.values.bacExit}
          control={<Checkbox />}
          label={t("bac-exit")}
          name="bacExit"
          onChange={() =>
            formik.setFieldValue("bacExit", !formik.values.bacExit)
          }
        />

        <FormControlLabel
          checked={formik.values.bacExitReverse}
          control={<Checkbox />}
          label={t("bac-exit-reverse")}
          name="bacExitReverse"
          onChange={() =>
            formik.setFieldValue(
              "bacExitReverse",
              !formik.values.bacExitReverse
            )
          }
        />
      </FormControl>

      <VehicleFormButtons
        formik={formik}
        activeStep={activeStep}
        steps={steps}
        handleBack={handleBack}
      />
    </Stack>
  );
};
