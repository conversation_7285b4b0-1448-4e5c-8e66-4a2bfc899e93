import { CommonEntity, PossibleSortDir } from "./common";
import { User } from "./user";
import { VehicleTypes } from "./vehicle";

export interface VehicleType extends CommonEntity {
  id: number;
  name: VehicleTypes | null;
  operatorManagerId?: number | null;
  operatorManager?: User;
}

export interface VehicleTypesWithCount {
  totalCount: number;
  data: VehicleType[];
}

export interface getVehicleTypesDto {
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
  operatorManagerId?: number | null;
  name?: string;
}
export interface DeleteVehicleTypeDto {
  vehicleTypeId: number;
}
export interface CreateVehicleTypeDto
  extends Omit<VehicleType, "operatorManager" | "id"> {
  operatorManagerId: number | null;
}

export interface UpdateVehicleTypeDto extends CreateVehicleTypeDto {
  vehicleTypeId: number;
}
export interface DeleteVehicleTypeModalValues {
  vehicleTypeId?: number;
  vehicleTypeTitle?: string | null;
  flow: VehicleTypeDeleteModalFlow;
}

export interface VehicleTypeFormValues {
  vehicleTypeId: number | null;
  name: VehicleTypes | null;
  operatorManagerId: number | null;
  flow: VehicleTypeModalFlow;
}

export type VehicleTypeDeleteModalFlow = "Delete" | null;
export type VehicleTypeModalFlow = "Create" | "Update" | null;
