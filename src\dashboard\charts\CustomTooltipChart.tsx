import React from 'react';
import { Box, Typography } from '@mui/material';
import { CeChip, CePaper } from 'src/common/components';
import { ArrowDownRight01Icon, ArrowUpRight01Icon } from '@hugeicons/react';

interface CustomTooltipChartProps {
  title: number | null;
  unit?: string;
  label: number;
  status: boolean;
  date: string;
}

const CustomTooltipChart: React.FC<CustomTooltipChartProps> = ({
  title,
  unit,
  label,
  status,
  date
}) => {
  const icon = status ? (
    <ArrowUpRight01Icon size={14} variant={"stroke"} />
) : (
    <ArrowDownRight01Icon size={16} variant={"stroke"} />
);
  return (
    <CePaper
      elevation={1}
      sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Typography
          variant="h5"
          component="div"
          sx={{ fontWeight: 700, fontSize: '20px', mr: 1 }}
        >
          {title}
          <Typography
            component="span"
            sx={{ ml: 0.5, fontSize: '20px', fontWeight: 700 }}
          >
            {unit}
          </Typography>
        </Typography>
        <CeChip
          label={`${label}%`}
          size="small"
          deleteIcon={icon}
          onDelete={() => null}
          status={status ? 'positive' : 'negative'}
        />
      </Box>
      <Typography variant="body2" color="text.secondary">
        {date}
      </Typography>
    </CePaper>
  );
};

export default CustomTooltipChart;
