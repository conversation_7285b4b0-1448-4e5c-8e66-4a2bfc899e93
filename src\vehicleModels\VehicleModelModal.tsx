import { AxiosError } from "axios";
import { UseMutateFunction } from "react-query";
import { MainModal } from "src/common/components";
import { SetterOrUpdater } from "recoil";
import {
  CreateVehicleModelDto,
  UpdateVehicleModelDto,
  VehicleModelFormValues,
  VehicleModels,
} from "src/common/types";
import { Drawer } from "@mui/material";
import { VehicleModalForm } from "./VehicleModelForm";

interface VehicleModelModalProps {
  isLoading: boolean;
  initialFormValues: VehicleModelFormValues;
  handleCloseVehicleModal: () => void;
  handleCreateNewVehicleModel?: UseMutateFunction<
    VehicleModels,
    AxiosError<CreateVehicleModelDto, CreateVehicleModelDto> | Error,
    CreateVehicleModelDto,
    () => void
  >;
  handleUpdateVehicleModel?: UseMutateFunction<
    VehicleModels,
    AxiosError<UpdateVehicleModelDto, UpdateVehicleModelDto> | Error,
    UpdateVehicleModelDto,
    () => void
  >;
}
export const VehicleModelModal: React.FC<VehicleModelModalProps> = ({
  isLoading,
  initialFormValues,
  handleCloseVehicleModal,
  handleCreateNewVehicleModel,
  handleUpdateVehicleModel,
}) => {
  const renderTitle = () => {
    switch (initialFormValues.flow) {
      case "Create":
        return "Create vehicle model";
      case "Update":
        return "Update vehicle model";

      default:
        return "Vehicle";
    }
  };
  return (
    <MainModal
      title={`${initialFormValues.flow || ""} Vehicle Model`}
      isOpen={!!initialFormValues.flow}
      handleClose={handleCloseVehicleModal}
    >
      <VehicleModalForm
        title={renderTitle()}
        initialFormValues={initialFormValues}
        isLoading={isLoading}
        handleClose={handleCloseVehicleModal}
        handleCreateNewVehicleModel={handleCreateNewVehicleModel}
        handleUpdateVehicleModel={handleUpdateVehicleModel}
      />
    </MainModal>
  );
};
