export interface Country {
  value: string;
  label: string;
}

const countries: Country[] = [
  { value: "AD", label: "Andorra" },
  { value: "AE", label: "United Arab Emirates" },
  { value: "AF", label: "Afghanistan" },
  { value: "AI", label: "Anguilla" },
  { value: "AL", label: "Albania" },
  { value: "AM", label: "Armenia" },
  { value: "AO", label: "Angola" },
  { value: "AQ", label: "Antarctica" },
  { value: "AR", label: "Argentina" },
  { value: "AS", label: "American Samoa" },
  { value: "AT", label: "Austria" },
  { value: "AU", label: "Australia" },
  { value: "AW", label: "Aruba" },
  { value: "AX", label: "Aland Islands" },
  { value: "AZ", label: "Azerbaijan" },
  { value: "BA", label: "Bosnia and Herzegovina" },
  { value: "BB", label: "Barbados" },
  { value: "BD", label: "Bangladesh" },
  { value: "BE", label: "Belgium" },
  { value: "BG", label: "Bulgaria" },
  { value: "BH", label: "Bahrain" },
  { value: "BI", label: "Burundi" },
  { value: "BJ", label: "Benin" },
  { value: "BL", label: "Saint Barthélemy" },
  { value: "BM", label: "Bermuda" },
  { value: "BN", label: "Brunei Darussalam" },
  { value: "BO", label: "Bolivia" },
  { value: "BR", label: "Brazil" },
  { value: "BS", label: "Bahamas" },
  { value: "BT", label: "Bhutan" },
  { value: "BV", label: "Bouvet Island" },
  { value: "BW", label: "Botswana" },
  { value: "BY", label: "Belarus" },
  { value: "BZ", label: "Belize" },
  { value: "CA", label: "canada" },
  { value: "CC", label: "cocos-islands" },
  { value: "CD", label: "congo-the-democratic-republic-of-the" },
  { value: "CF", label: "central-african-republic" },
  { value: "CG", label: "congo" },
  { value: "CH", label: "switzerland" },
  { value: "CI", label: "côte-d-ivoire" },
  { value: "CK", label: "cook-islands" },
  { value: "CL", label: "chile" },
  { value: "CM", label: "cameroon" },
  { value: "CN", label: "china" },
  { value: "CO", label: "colombia" },
  { value: "CR", label: "costa-rica" },
  { value: "CU", label: "cuba" },
  { value: "CV", label: "cape-verde" },
  { value: "CW", label: "curaçao" },
  { value: "CX", label: "christmas-island" },
  { value: "CY", label: "cyprus" },
  { value: "CZ", label: "czech-republic" },
  { value: "DE", label: "germany" },
  { value: "DJ", label: "djibouti" },
  { value: "DK", label: "denmark" },
  { value: "DM", label: "dominica" },
  { value: "DO", label: "dominican-republic" },
  { value: "DZ", label: "algeria" },
  { value: "EC", label: "ecuador" },
  { value: "EE", label: "estonia" },
  { value: "EG", label: "egypt" },
  { value: "EH", label: "western-sahara" },
  { value: "ER", label: "eritrea" },
  { value: "ES", label: "spain" },
  { value: "ET", label: "ethiopia" },
  { value: "FI", label: "finland" },
  { value: "FJ", label: "fiji" },
  { value: "FK", label: "falkland-islands-malvinas" },
  { value: "FM", label: "micronesia-federated-states-of" },
  { value: "FO", label: "faroe-islands" },
  { value: "FR", label: "france" },
  { value: "GA", label: "gabon" },
  { value: "GB", label: "united-kingdom" },
  { value: "GD", label: "grenada" },
  { value: "GE", label: "georgia" },
  { value: "GF", label: "french-guiana" },
  { value: "GG", label: "guernsey" },
  { value: "GH", label: "ghana" },
  { value: "GI", label: "gibraltar" },
  { value: "GL", label: "greenland" },
  { value: "GM", label: "gambia" },
  { value: "GN", label: "guinea" },
  { value: "GP", label: "guadeloupe" },
  { value: "GQ", label: "equatorial-guinea" },
  { value: "GR", label: "greece" },
  { value: "GS", label: "south-georgia-and-the-south-sandwich-islands" },
  { value: "GT", label: "guatemala" },
  { value: "GU", label: "guam" },
  { value: "GW", label: "guinea-bissau" },
  { value: "GY", label: "guyana" },
  { value: "HK", label: "hong-kong" },
  { value: "HM", label: "heard-island-and-mcdonald-islands" },
  { value: "HN", label: "honduras" },
  { value: "HR", label: "croatia" },
  { value: "HT", label: "haiti" },
  { value: "HU", label: "hungary" },
  { value: "ID", label: "indonesia" },
  { value: "IE", label: "ireland" },
  { value: "IL", label: "israel" },
  { value: "IM", label: "isle-of-man" },
  { value: "IN", label: "india" },
  { value: "IO", label: "british-indian-ocean-territory" },
  { value: "IQ", label: "iraq" },
  { value: "IR", label: "iran" },
  { value: "IS", label: "iceland" },
  { value: "IT", label: "italy" },
  { value: "JE", label: "jersey" },
  { value: "JM", label: "jamaica" },
  { value: "JO", label: "jordan" },
  { value: "JP", label: "japan" },
  { value: "KE", label: "kenya" },
  { value: "KG", label: "kyrgyzstan" },
  { value: "KH", label: "cambodia" },
  { value: "KI", label: "kiribati" },
  { value: "KM", label: "comoros" },
  { value: "KN", label: "saint-kitts-and-nevis" },
  { value: "KP", label: "korea-democratic-peoples-republic-of" },
  { value: "KR", label: "korea-republic-of" },
  { value: "KW", label: "kuwait" },
  { value: "KY", label: "cayman-islands" },
  { value: "KZ", label: "kazakhstan" },
  { value: "LA", label: "lao-peoples-democratic-republic" },
  { value: "LB", label: "lebanon" },
  { value: "LC", label: "saint-lucia" },
  { value: "LI", label: "liechtenstein" },
  { value: "LK", label: "sri-lanka" },
  { value: "LR", label: "liberia" },
  { value: "LS", label: "lesotho" },
  { value: "LT", label: "lithuania" },
  { value: "LU", label: "luxembourg" },
  { value: "LV", label: "latvia" },
  { value: "LY", label: "libya" },
  { value: "MA", label: "morocco" },
  { value: "MC", label: "monaco" },
  { value: "MD", label: "moldova-republic-of" },
  { value: "ME", label: "montenegro" },
  { value: "MF", label: "saint-martin" },
  { value: "MG", label: "madagascar" },
  { value: "MH", label: "marshall-islands" },
  { value: "MK", label: "north-macedonia" },
  { value: "ML", label: "mali" },
  { value: "MM", label: "myanmar" },
  { value: "MN", label: "mongolia" },
  { value: "MO", label: "lacao" },
  { value: "MP", label: "northern-mariana-islands" },
  { value: "MQ", label: "martinique" },
  { value: "MR", label: "mauritania" },
  { value: "MS", label: "montserrat" },
  { value: "MT", label: "malta" },
  { value: "MU", label: "mauritius" },
  { value: "MV", label: "maldives" },
  { value: "MW", label: "malawi" },
  { value: "MX", label: "mexico" },
  { value: "MY", label: "malaysia" },
  { value: "MZ", label: "mozambique" },
  { value: "NA", label: "namibia" },
  { value: "NC", label: "new-caledonia" },
  { value: "NE", label: "niger" },
  { value: "NF", label: "norfolk-island" },
  { value: "NG", label: "nigeria" },
  { value: "NI", label: "nicaragua" },
  { value: "NL", label: "netherlands" },
  { value: "NO", label: "norway" },
  { value: "NP", label: "nepal" },
  { value: "NR", label: "nauru" },
  { value: "NU", label: "niue" },
  { value: "NZ", label: "new-zealand" },
  { value: "OM", label: "oman" },
  { value: "PA", label: "panama" },
  { value: "PE", label: "peru" },
  { value: "PF", label: "french-polynesia" },
  { value: "PG", label: "papua-new-guinea" },
  { value: "PH", label: "philippines" },
  { value: "PK", label: "pakistan" },
  { value: "PL", label: "poland" },
  { value: "PM", label: "saint-pierre-and-miquelon" },
  { value: "PN", label: "pitcairn" },
  { value: "PR", label: "puerto-rico" },
  { value: "PS", label: "palestinian-territory-occupied" },
  { value: "PT", label: "portugal" },
  { value: "PW", label: "palau" },
  { value: "PY", label: "paraguay" },
  { value: "QA", label: "qatar" },
  { value: "RE", label: "réunion" },
  { value: "RO", label: "romania" },
  { value: "RS", label: "serbia" },
  { value: "RU", label: "russian-federation" },
  { value: "RW", label: "rwanda" },
  { value: "SA", label: "saudi-arabia" },
  { value: "SB", label: "solomon-islands" },
  { value: "SC", label: "seychelles" },
  { value: "SD", label: "sudan" },
  { value: "SE", label: "sweden" },
  { value: "SG", label: "singapore" },
  { value: "SH", label: "saint-helena-ascension-and-tristan-da-cunha" },
  { value: "SI", label: "slovenia" },
  { value: "SJ", label: "svalbard-and-jan-mayen" },
  { value: "SK", label: "slovakia" },
  { value: "SL", label: "sierra-leone" },
  { value: "SM", label: "san-marino" },
  { value: "SN", label: "senegal" },
  { value: "SO", label: "somalia" },
  { value: "SR", label: "suriname" },
  { value: "SS", label: "south-sudan" },
  { value: "ST", label: "sao-tome-and-principe" },
  { value: "SV", label: "el-salvador" },
  { value: "SX", label: "sint-maarten" },
  { value: "SY", label: "syrian-arab-republic" },
  { value: "SZ", label: "eswatini" },
  { value: "TC", label: "turks-and-caicos-islands" },
  { value: "TD", label: "chad" },
  { value: "TF", label: "french-southern-territories" },
  { value: "TG", label: "togo" },
  { value: "TH", label: "thailand" },
  { value: "TJ", label: "tajikistan" },
  { value: "TK", label: "tokelau" },
  { value: "TL", label: "timor-leste" },
  { value: "TM", label: "turkmenistan" },
  { value: "TN", label: "tunisia" },
  { value: "TO", label: "tonga" },
  { value: "TR", label: "turkey" },
  { value: "TT", label: "trinidad-and-tobago" },
  { value: "TV", label: "tuvalu" },
  { value: "TW", label: "taiwan-province-of-china" },
  { value: "TZ", label: "tanzania-united-republic-of" },
  { value: "UA", label: "ukraine" },
  { value: "UG", label: "uganda" },
  { value: "UM", label: "united-states-minor-outlying-islands" },
  { value: "US", label: "united-states" },
  { value: "UY", label: "uruguay" },
  { value: "UZ", label: "uzbekistan" },
  { value: "VA", label: "vatican-city-state" },
  { value: "VC", label: "saint-vincent-and-the-grenadines" },
  { value: "VE", label: "venezuela-bolivarian-republic-of" },
  { value: "VG", label: "virgin-islands-british" },
  { value: "VI", label: "virgin-islands-us" },
  { value: "VN", label: "viet-nam" },
  { value: "VU", label: "vanuatu" },
  { value: "WF", label: "wallis-and-futuna" },
  { value: "WS", label: "samoa" },
  { value: "YE", label: "yemen" },
  { value: "YT", label: "mayotte" },
  { value: "ZA", label: "south-africa" },
  { value: "ZM", label: "zambia" },
  { value: "ZW", label: "zimbabwe" },
];

function formatCountryLabels(countries: Country[]): void {
  for (const country of countries) {
    const words = country.label.split("-");
    for (let i = 0; i < words.length; i++) {
      words[i] = words[i].charAt(0).toUpperCase() + words[i].slice(1);
    }
    country.label = words.join(" ");
  }
}

formatCountryLabels(countries);

export default countries;
