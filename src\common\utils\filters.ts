import { GridFilterItem, GridFilterModel, GridSortModel } from '@mui/x-data-grid'
import { zonedTimeToUtc } from 'date-fns-tz'
import { isValid } from 'date-fns'
import { Expression, ExpressionConditionType, ExpressionOperator } from '../types'
import { v4 as uuid } from 'uuid'

const getConditionType = (datagridFilter: GridFilterModel) => {
  if (!datagridFilter.items?.length || datagridFilter.items.length === 1) {
    return undefined
  }
  if (datagridFilter.items.length > 1 && !datagridFilter.linkOperator) {
    console.log("GOT MULTIPLE DATAGRID FILTERS BUT NO DATAGRID LINK OPERATOR. SETTING LINK OPERATOR TO 'AND'")
    return 'AND'
  }
  return datagridFilter.linkOperator?.toUpperCase() as ExpressionConditionType
}

const getValue = (item: GridFilterItem) => {
  const { columnField, value } = item
  if ((columnField.includes('.createdOn') || columnField === 'patient.lastChartReviewOn') && value && isValid(value)) {
    const localTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    const utcDate = zonedTimeToUtc(value, localTimezone)
    return utcDate.toISOString()
  }
  if (columnField.includes(".createdOn") && value === "") {
    // add other date fields here for each datagrid that we want to send null instead of ''
    // cannot send '' for date type
    return null;
  }
  return value
}

export const turnExpressionsIntoDatagridFilter = (expressions: Expression[]): GridFilterItem[] => {
  const filters = expressions.map((expression) => {
    const category = expression.category?.replace(/"/g, '') || ''
    return {
      columnField: category,
      id: uuid(),
      operatorValue: expression.operator || '',
      value: expression.value,
      conditionType: expression.conditionType,
    }
  })

  return filters
}

export const turnDatagridFilterIntoExpressions = (datagridFilter: GridFilterModel): Expression[] => {
  const expressions: Expression[] = []
  datagridFilter.items?.forEach((item) => {
    const conditionType = getConditionType(datagridFilter)
    const operator = item.operatorValue as ExpressionOperator
    const isValueNeeded =
      operator !== 'isEmpty' &&
      operator !== 'isNotEmpty' &&
      operator !== 'true_or_empty' &&
      operator !== 'false_or_empty' &&
      operator !== 'isInCurrentCalendarYear' &&
      operator !== 'isNotInCurrentCalendarYear'
    const isColumnNumberAndEmpty =
      (operator === '!=' || operator === '<=' || operator === '>=' || operator === '=' || operator === '<' || operator === '>') &&
      item.value === ''

    if (
      (item.value === undefined || isColumnNumberAndEmpty || (Array.isArray(item.value) && !item.value.length)) &&
      isValueNeeded
    ) {
      // if value is needed and the value is not supplied yet, we are not going to add this item to array
      return
    }
    if (!item.columnField) {
      return
    }
    const category = item.columnField.replace(/\w+/g, '"$&"')
    const expression = {
      category,
      operator,
      value: getValue(item),
      conditionType,
    }
    expressions.push(expression)
  })

  return expressions
}




export const wrapEachFieldIntoDoubleQuotes = (sortModel?: GridSortModel) => {
  if (!sortModel) {
    return [];
  }
  const formattedSortModel = sortModel.map((sortModelItem) => {
    return {
      ...sortModelItem,
      field: sortModelItem.field.replace(/\w+/g, '"$&"'),
    };
  });
  return formattedSortModel;
}