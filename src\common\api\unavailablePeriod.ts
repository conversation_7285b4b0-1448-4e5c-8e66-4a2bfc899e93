import axios, { AxiosError } from "axios";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { processApiError, processApiSuccess } from "../utils/errors";
import { CreateUnavailabilityVehicleDto, CreateUnavailablePeriodDto, DeleteUnavailablePeriodDto, getUnavailablePeriodDto, UnavailablePeriod, UnavailablePeriodsWithCount, UpdateUnavailabilityVehicleDto, UpdateUnavailablePeriodDto } from "../types";
import { wrapEachFieldIntoDoubleQuotes } from "../utils";

const backendUrl = process.env.REACT_APP_API_URL;

export const getUnavailablePeriods = async (attr: getUnavailablePeriodDto) => {
    return axios
        .post(`${backendUrl}/unavailable-periods/get`, attr, {
            withCredentials: true,
        })
        .then((response) => response.data);
};
export const useUnavailablePeriods = (
    attrs: getUnavailablePeriodDto,
    enabled: boolean = true
) => {
    const formattedSortModel = wrapEachFieldIntoDoubleQuotes(attrs.sortModel);

    const formattedAttrs = {
        ...attrs,
        sortModel: formattedSortModel,
    };
    return useQuery<UnavailablePeriodsWithCount, AxiosError | Error>(
        ["unavailablePeriods", formattedAttrs],
        () => getUnavailablePeriods(formattedAttrs),
        {
            keepPreviousData: true,
            onError: (err) => processApiError("Unable to fetch unavailable periods", err),
            enabled,
        }
    );
};


export const createUnavailablePeriod = (attrs: CreateUnavailablePeriodDto) => {
    return axios
        .post(`${backendUrl}/unavailable-periods`, attrs, {
            withCredentials: true,
        })
        .then((response) => response.data);
};

export const useCreateUnavailablePeriod = () => {
    const queryClient = useQueryClient();
    return useMutation<UnavailablePeriod, AxiosError | Error, CreateUnavailablePeriodDto, () => void>(
        (a: CreateUnavailablePeriodDto) => createUnavailablePeriod(a),
        {
            onSuccess: () => {
                processApiSuccess("Unavailable Period successfully created!");
                queryClient.invalidateQueries("unavailablePeriods");
            },
            onError: (err) => processApiError("Unable to create unavailable periods", err),
        }
    );
};
export const deleteUnavailablePeriod = (deleteUnavailablePeriodDto: DeleteUnavailablePeriodDto) => {
    const { id } = deleteUnavailablePeriodDto;
    if (!id) {
        throw new Error("the unavailable period ID was not provided");
    }
    return axios
        .delete(`${backendUrl}/unavailable-periods/${id}`, { withCredentials: true })
        .then((response) => response.data);
};

export const useDeleteUnavailablePeriod = () => {
    const queryClient = useQueryClient();
    return useMutation<UnavailablePeriod, AxiosError | Error, DeleteUnavailablePeriodDto, () => void>(
        (deleteUnavailablePeriodDto: DeleteUnavailablePeriodDto) => deleteUnavailablePeriod(deleteUnavailablePeriodDto),
        {
            onSuccess: () => {
                processApiSuccess("Unavailable period successfully deleted!");
                queryClient.invalidateQueries("unavailablePeriods");
            },
            onError: (err) => processApiError("Unable to delete unavailable period", err)
        }
    );
};


export const handleUpdateUnavailablePeriod = (updateUnavailablePeriodArgs: UpdateUnavailablePeriodDto) => {
    const { id, ...unavailablePeriod } = updateUnavailablePeriodArgs;
    if (!id) {
        throw new Error("the unavailable period Id was not provided");
    }
    return axios
        .patch(`${backendUrl}/unavailable-periods/${id}`, unavailablePeriod, {
            withCredentials: true
        })
        .then((response) => response.data);
};

export const useUpdateUnavailablePeriod = () => {
    const queryClient = useQueryClient();
    return useMutation<UnavailablePeriod, AxiosError | Error, UpdateUnavailablePeriodDto, () => void>(
        (updateUnavailablePeriodArgs: UpdateUnavailablePeriodDto) =>
            handleUpdateUnavailablePeriod(updateUnavailablePeriodArgs),
        {
            onSuccess: () => {
                processApiSuccess("Unavailable period successfully updated!");
                queryClient.invalidateQueries("unavailablePeriods");
            },
            onError: (err) => {
                processApiError("Unable to update unavailable period", err);
            }
        }
    );
};

export const createUnavailabilityVehicle = (attrs: CreateUnavailabilityVehicleDto) => {
    return axios
        .post(`${backendUrl}/unavailable-periods/vehicle`, attrs, {
            withCredentials: true,
        })
        .then((response) => response.data);
};

export const useCreateUnavailabilityVehicle = () => {
    const queryClient = useQueryClient();
    return useMutation<UnavailablePeriod, AxiosError | Error, CreateUnavailabilityVehicleDto, () => void>(
        (a: CreateUnavailabilityVehicleDto) => createUnavailabilityVehicle(a),
        {
            onSuccess: () => {
                processApiSuccess("Unavailability Vehicle successfully created!");
                queryClient.invalidateQueries("unavailablePeriods");
                queryClient.invalidateQueries("vehicles");
            },
            onError: (err) => processApiError("Unable to create unavailability vehicle", err),
        }
    );
};

export const handleUnavailabilityVehicle = (updateUnavailabilityVehicleArgs: UpdateUnavailabilityVehicleDto) => {
    const { id, ...unavailablePeriod } = updateUnavailabilityVehicleArgs;
    if (!id) {
        throw new Error("the unavailability period Id was not provided");
    }
    return axios
        .patch(`${backendUrl}/unavailable-periods/${id}/vehicle`, unavailablePeriod, {
            withCredentials: true
        })
        .then((response) => response.data);
};

export const useUpdateUnavailabilityVehicle = () => {
    const queryClient = useQueryClient();
    return useMutation<UnavailablePeriod, AxiosError | Error, UpdateUnavailabilityVehicleDto, () => void>(
        (updateUnavailabilityVehicleArgs: UpdateUnavailabilityVehicleDto) =>
            handleUnavailabilityVehicle(updateUnavailabilityVehicleArgs),
        {
            onSuccess: () => {
                processApiSuccess("Unavailability period successfully updated!");
                queryClient.invalidateQueries("unavailablePeriods");
            },
            onError: (err) => {
                processApiError("Unable to update unavailability period", err);
            }
        }
    );
};