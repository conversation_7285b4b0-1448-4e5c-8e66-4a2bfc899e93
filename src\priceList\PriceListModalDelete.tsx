import { Box, Typography } from "@mui/material";
import { DeleteModal } from "src/common/components";
import {
  PriceListDeleteFlowEnum,
  PriceListModalDeleteFlow,
} from "src/common/types/priceList";
import { useTranslation } from "react-i18next";

interface PriceListContainerDeleteModalProps {
  flow: PriceListModalDeleteFlow;
  priceListTitle?: string;
  isLoading: boolean;
  handleClosePriceListModalDelete: () => void;
  handleSubmit: () => void;
}
export const PriceListContainerDeleteModal: React.FC<
  PriceListContainerDeleteModalProps
> = ({
  flow,
  priceListTitle,
  isLoading,
  handleClosePriceListModalDelete,
  handleSubmit,
}) => {
  const { t } = useTranslation(["common", "dispatcher"]);

  const DeleteModalHelperText = () => (
    <Box>
      <Typography sx={{ marginBottom: 1 }}>
        {t("delete-pricelist-message", {
          priceListTitle: priceListTitle || "unknown pricelist",
          entityType: "pricelist",
        })}
      </Typography>
    </Box>
  );

  return (
    <DeleteModal
      isOpen={flow === PriceListDeleteFlowEnum.PRICELIST_DELETE}
      isLoading={isLoading}
      title={t("delete-price-list")}
      helperText={DeleteModalHelperText()}
      handleSubmit={handleSubmit}
      handleClose={handleClosePriceListModalDelete}
    />
  );
};
