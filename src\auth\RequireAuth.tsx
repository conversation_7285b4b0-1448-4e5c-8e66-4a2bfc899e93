import { Navigate, useLocation } from "react-router-dom";
import { isAuthenticated } from "src/common/api/auth";

export const RequireAuth: React.FC = ({ children }) => {
  const { isAuth } = isAuthenticated();

  const location = useLocation();

  if (!isAuth) {
    // Redirect them to the /auth/signin page, but save the current location they were
    // trying to go to when they were redirected. This allows us to send them
    // along to that page after they /auth/signin, which is a nicer user experience
    // than dropping them off on the home page.
    return <Navigate to="/auth/signin" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};
