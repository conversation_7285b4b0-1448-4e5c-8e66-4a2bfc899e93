import { useMutation, useQuery, useQueryClient } from "react-query";
import { processApiError, processApiSuccess } from "../utils/errors";
import axios, { AxiosError } from "axios";
import {
  CreateJobLocationDto,
  getJobLocationDto,
  Job,
  JobLocationWithCount,
  Reservation,
  SecurityValidationDto,
  UpdateJobDto,
  UpdateReportDto,
} from "../types";
import { wrapEachFieldIntoDoubleQuotes } from "../utils";

const backendUrl = process.env.REACT_APP_API_URL;

// GET ONE pending reservation job
export const getPendingReservationJob = async (operatorId?: number) => {
  if (!operatorId) {
    throw new Error("the operator ID was not provided");
  }
  return axios
    .get(`${backendUrl}/reservation/pendingReservation/${operatorId}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const usePendingReservationJob = (
  operatorId?: number,
  enabled: boolean = true
) => {
  return useQuery<Reservation, AxiosError | Error>(
    ["pendingReservation", operatorId],
    () => getPendingReservationJob(operatorId),
    {
      onError: (err) =>
        processApiError("Unable to fetch pending reservation", err),
      enabled,
    }
  );
};

// GET ONE upcoing reservation job
export const getUpcomingReservationJob = async (operatorId?: number) => {
  if (!operatorId) {
    throw new Error("the operator ID was not provided");
  }
  return axios
    .get(`${backendUrl}/reservation/nextUpcomingReservation/${operatorId}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const useUpcomingReservationJob = (
  operatorId?: number,
  enabled: boolean = true
) => {
  return useQuery<Reservation, AxiosError | Error>(
    ["upcomingReservation", operatorId],
    () => getUpcomingReservationJob(operatorId),
    {
      onError: (err) =>
        processApiError("Unable to fetch upcoming reservation", err),
      enabled,
    }
  );
};

// UPDATE job BY ID
export const handleUpdateJob = (updateJobArgs: UpdateJobDto) => {
  const { jobId, ...job } = updateJobArgs;
  if (!jobId) {
    throw new Error("the job ID was not provided");
  }
  return axios
    .patch(`${backendUrl}/job/${jobId}`, job, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useUpdateJob = () => {
  const queryClient = useQueryClient();
  return useMutation<Job, AxiosError | Error, UpdateJobDto, () => void>(
    (updateJobArgs: UpdateJobDto) => handleUpdateJob(updateJobArgs),
    {
      onSuccess: (response) => {
        queryClient.invalidateQueries("reservation");
        queryClient.invalidateQueries("reservations");
        queryClient.invalidateQueries("vehiclesWithReservations");

        if (response.progress === 100) {
          processApiSuccess("Job successfully completed!");
        }
      },
      onError: (err) => {
        processApiError("Unable to update job", err);
      },
    }
  );
};

// UPDATE reservation BY ID
export const handleUpdateSecurityValidation = (
  updateJobArgs: SecurityValidationDto
) => {
  const {
    jobId,
    electricalRiskComment,
    isAccessCompliance,
    accessComplianceComment,
    accessComplianceFile,
    isParkingCompliance,
    parkingComplianceComment,
    parkingComplianceFile,
    terrainStabilityComment,
    terrainStabilityFile,
    isTerrainStability,
    electricalRiskFile,
    isElectricalRisk,
    electricalRiskKey,
    accessComplianceKey,
    parkingComplianceKey,
    terrainStabilityKey
  } = updateJobArgs;
  if (!jobId) {
    throw new Error("the job ID was not provided");
  }

  const formData = new FormData();
  formData.append("jobId", jobId.toString());
  formData.append("electricalRiskComment", electricalRiskComment || "");
  formData.append("isAccessCompliance", String(isAccessCompliance));
  formData.append("isElectricalRisk", String(isElectricalRisk));

  if (electricalRiskFile) {
    formData.append("electricalRiskFile", electricalRiskFile);
  }

  if (accessComplianceFile) {
    formData.append("accessComplianceFile", accessComplianceFile);
  }
  formData.append("accessComplianceComment", accessComplianceComment || "");
  formData.append("isParkingCompliance", String(isParkingCompliance));
  if (parkingComplianceFile) {
    formData.append("parkingComplianceFile", parkingComplianceFile);
  }
  formData.append("parkingComplianceComment", parkingComplianceComment || "");
  formData.append("isTerrainStability", String(isTerrainStability));
  if (terrainStabilityFile) {
    formData.append("terrainStabilityFile", terrainStabilityFile);
  }
  formData.append("terrainStabilityComment", terrainStabilityComment || "");

  formData.append("electricalRiskKey", electricalRiskKey || "");
  formData.append("accessComplianceKey", accessComplianceKey || "");
  formData.append("parkingComplianceKey", parkingComplianceKey || "");
  formData.append("terrainStabilityKey", terrainStabilityKey || "");


  return axios
    .put(`${backendUrl}/job/security-validation`, formData, {
      withCredentials: true,
      headers: {
        "Content-type": "multipart/form-data",
      },
    })
    .then((response) => response.data);
};

export const useUpdateSecurityValidation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    Job,
    AxiosError | Error,
    SecurityValidationDto,
    () => void
  >(
    (updateJobArgs: SecurityValidationDto) =>
      handleUpdateSecurityValidation(updateJobArgs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("reservation");
        queryClient.invalidateQueries("reservations");
      },
      onError: (err) => {
        processApiError("Unable to update job security validation", err);
      },
    }
  );
};

// UPDATE reservation BY ID
export const handleUpdateReport = (updateJobArgs: UpdateReportDto) => {
  const { jobId, ...job } = updateJobArgs;
  if (!jobId) {
    throw new Error("the job ID was not provided");
  }
  return axios
    .put(`${backendUrl}/job/report`, updateJobArgs, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useUpdateReport = () => {
  const queryClient = useQueryClient();
  return useMutation<Job, AxiosError | Error, UpdateReportDto, () => void>(
    (updateJobArgs: UpdateReportDto) => handleUpdateReport(updateJobArgs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("reservation");
        queryClient.invalidateQueries("reservations");
      },
      onError: (err) => {
        processApiError("Unable to update job report", err);
      },
    }
  );
};

export const handleCreateJobLocation = (createJobLocationArgs: CreateJobLocationDto) => {
  const { jobId } = createJobLocationArgs;
  if (!jobId) {
    throw new Error("The job ID was not provided");
  }
  return axios
    .post(`${backendUrl}/job-locations`, createJobLocationArgs, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const useCreateJobLocation = () => {
  const queryClient = useQueryClient();
  return useMutation<CreateJobLocationDto, AxiosError | Error, CreateJobLocationDto, () => void>(
    (createJobLocationArgs: CreateJobLocationDto) => handleCreateJobLocation(createJobLocationArgs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("jobLocation");
        queryClient.invalidateQueries("jobLocations");
      },
      onError: (err) => {
        processApiError("Unable to create job location", err);
      },
    }
  );
};

export const getLocation = async (attr: getJobLocationDto) => {
  return axios
  .post(`${backendUrl}/job-locations/get`, attr, {
    withCredentials: true,
  })
  .then((response) => response.data);
};

export const useLocation = (
  attr: getJobLocationDto,
  enabled: boolean = true
) => {
    const formattedSortModel = wrapEachFieldIntoDoubleQuotes(attr.sortModel);
    const formattedAttrs = { ...attr, sortModel: formattedSortModel };
  return useQuery<JobLocationWithCount, AxiosError | Error>(
    ["job-location", formattedAttrs],
    () => getLocation(formattedAttrs),
    {
      onError: (err) => processApiError("Unable to fetch job location", err),
      enabled,
      refetchInterval: 5 * 60 * 1000,   
      refetchIntervalInBackground: true,    
    }
  );
};