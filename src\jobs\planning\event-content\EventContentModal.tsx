import { EventClickArg } from "@fullcalendar/core";
import { Close } from "@mui/icons-material";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import LocationOnIcon from "@mui/icons-material/LocationOn";

import { Button, Chip, DialogActions, Stack, useTheme } from "@mui/material";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import { useTranslation } from "react-i18next";
import { FC } from "react";
import { MainModal } from "src/common/components";
import { useNavigate } from "react-router-dom";
import { buttonTextTransform } from "src/common/components/custom/customCss";
import { useMarkAsRead } from "src/common/api";

type Props = {
  open: boolean;
  onClose: () => void;
  arg: EventClickArg;
};

const EventContentModal: FC<Props> = (props) => {
  const { open, onClose } = props;
  const { title, id } = props.arg.event;
  const theme = useTheme();
  const { schedule, quantity, address, hasBeenRead } = props.arg.event.extendedProps;
  const { t } = useTranslation(["operator"]);
  const navigate = useNavigate();
  const reservationId = Number(id);

  const {
    mutateAsync: handleMarkAsRead,
    isLoading: isMarkAsReadLoading,
    isSuccess: isMarkAsReadSuccess
  } = useMarkAsRead();


  return (
    <MainModal
      title={"Reservation Details"}
      isOpen={!!open}
      handleClose={onClose}
    >
      <Stack spacing={2} sx={{ minWidth: "300px" }}>
        <Box>
          <Stack spacing={1} direction={"column"}>
            <Typography fontWeight="bold">
              {title} ({quantity} m³)
            </Typography>

            <Typography sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <AccessTimeIcon fontSize="small" /> <span>{schedule}</span>
            </Typography>

            <Typography sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <LocationOnIcon fontSize="small" />
              {address}
            </Typography>
          </Stack>
        </Box>
      </Stack>

      <DialogActions sx={{ marginTop: 4 }}>
        <Button
          color="primary"
          fullWidth
          variant="contained"
          onClick={() => { 
            if(!hasBeenRead){
              handleMarkAsRead({reservationId})
            }
            navigate(`/job-details/${id}`);
          }}
          sx={buttonTextTransform}
        >
          {t("view-details")}
        </Button>
      </DialogActions>
    </MainModal>
  );
};

export default EventContentModal;
