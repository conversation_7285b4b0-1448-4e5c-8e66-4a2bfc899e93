import { Al<PERSON>, Box, Divider, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { DeleteModal } from "src/common/components";
import {
  DeleteOperatorDto,
  OperatorFormValues,
  OperatorModalDeleteFlow,
} from "src/common/types";

interface OperatorModalDeleteProps {
  flow: OperatorModalDeleteFlow;
  operatorTitle?: string;
  isLoading: boolean;
  operatorId?: number;
  handleCloseOperatorModalDelete: () => void;
  handleDeleteOperator: (args: DeleteOperatorDto) => void;
}
export const OperatorModalDelete: React.FC<OperatorModalDeleteProps> = ({
  flow,
  operatorTitle,
  operatorId,
  isLoading,
  handleCloseOperatorModalDelete,
  handleDeleteOperator,
}) => {
  const { t } = useTranslation("common");

  const onDeleteOperator = () => {
    if (operatorId) {
      handleDeleteOperator({ operatorId });
    }
  };

  const DeleteModalHelperText = () => (
    <Box>
      <Typography sx={{ marginBottom: 1 }}>
        {t("delete-operator-message", {
          operatorTitle: operatorTitle || "unknown operator",
        })}
      </Typography>
    </Box>
  );

  return (
    <DeleteModal
      isLoading={isLoading}
      title="Delete operator"
      helperText={DeleteModalHelperText()}
      isOpen={flow === "Delete"}
      handleSubmit={() => onDeleteOperator()}
      handleClose={handleCloseOperatorModalDelete}
    />
  );
};
