import { format } from "date-fns";
import {
  UnavailablePeriod,
  UnavailablePeriodFlow,
  UnavailablePeriodFormValues,
} from "../types";
import areIntervalsOverlapping from "date-fns/areIntervalsOverlapping";

export const turnUnavailablePeriodIntoFormValues = (
  unavailablePeriod: UnavailablePeriod,
  flow: UnavailablePeriodFlow
): UnavailablePeriodFormValues => {
  const payload: UnavailablePeriodFormValues = {
    ...unavailablePeriod,
    flow,
    from: unavailablePeriod.from,
    to: unavailablePeriod.to,
    operatorId: unavailablePeriod.operatorId || null,
    vehicleIds:
      unavailablePeriod.vehicles?.[0]?.id != null
        ? [unavailablePeriod.vehicles[0].id!]
        : null,
  };

  return payload;
};

export const formatDateRange = (from: Date, to: Date): string => {
  const sameMonth = from.getMonth() === to.getMonth();
  const sameYear = from.getFullYear() === to.getFullYear();

  if (sameMonth && sameYear) {
    return `${format(from, "MMM do")} - ${format(to, "do yyyy")}`;
  } else if (sameYear) {
    return `${format(from, "MMM do")} - ${format(to, "MMM do yyyy")}`;
  } else {
    return `${format(from, "MMM do yyyy")} - ${format(to, "MMM do yyyy")}`;
  }
};

export const checkVehicleAvailability = (
  dateFrom: Date | string | null,
  dateTo: Date | string | null,
  unavailableVehiclePeriods: UnavailablePeriod[] | null | undefined
): boolean => {
  if (
    !dateFrom ||
    !dateTo ||
    !unavailableVehiclePeriods ||
    unavailableVehiclePeriods.length === 0
  ) {
    return true;
  }

  const requested = {
    start: new Date(dateFrom),
    end: new Date(dateTo),
  };

  const itOverLaps = unavailableVehiclePeriods.some((p) =>
    areIntervalsOverlapping(
      requested,
      { start: new Date(p.from), end: new Date(p.to) },
      { inclusive: true }
    )
  );

  return !itOverLaps;
};

export const noonTime = (date: Date | string) => {
  const day = new Date(date);
  day.setHours(12, 0, 0, 0);
  return day.toISOString();
};
