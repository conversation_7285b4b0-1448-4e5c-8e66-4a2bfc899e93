import { FC, useCallback, useEffect, useState } from "react";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import queryString from "query-string";

import { useTranslation } from "react-i18next";

import {
  Stack,
  IconButton,
  Typography,
  Chip,
  useTheme,
  Tooltip,
  MenuItem,
  Autocomplete,
} from "@mui/material";
import { useRecoilState } from "recoil";

import {
  DataGrid,
  GridColDef,
  GridFilterModel,
  GridRenderCellParams,
  GridRowParams,
  GridSortModel,
} from "@mui/x-data-grid";
import { stringAvatar } from "src/common/utils/avatar";

import {
  JOB_STEPS,
  RESERVATION_CANCEL_DEFAULT,
  RESERVATION_DELETE_DEFAULT,
  RESERVATION_EDIT_DEFAULT,
  RESERVATION_FORM_VALUES,
  STATUS_COLORS,
} from "src/common/constants";

import {
  ColorTypes,
  getTranslatedJobStatusOptions,
  JobStatus,
  Reservation,
  ReservationFormValues,
  ReservationQueryFilters,
  Role,
} from "src/common/types";
import {
  getCurrentUser,
  useCreateNewReservation,
  useDeleteReservation,
  useUpdateReservation,
} from "src/common/api";
import {
  reservationCancelValuesState,
  reservationDeleteValuesState,
  reservationEditValuesState,
  reservationFormValuesState,
  reservationFullFormValuesState,
} from "src/common/state";
import { addMinutes, format, parseISO } from "date-fns";
import { ReservationModal } from "../ReservationModal";
import { ReservationModalDelete } from "../ReservationModalDelete";
import { useNavigate, useLocation } from "react-router-dom";
import { enGB } from "date-fns/locale";
import { getVehicleUniqueId } from "src/common/utils/vehicle";
import { useUpdateJob } from "src/common/api/job";
import { VehicleOutlined } from "src/images/Icons";
import { ReservationCancelWithCommentModal } from "../ReservationCancelWithCommentModal";
import { Delete01Icon, Edit02Icon, UnavailableIcon } from "@hugeicons/react";
import { CeAvatar, CeTextField } from "src/common/components";
import { CeDataGridToolbar } from "src/common/components/custom/company/CeDataGridToolbar";
import { GridColumnTypography } from "src/common/components/custom/company/GridColumnTypography";
import {
  GridStateSnapshot,
  UpdateGridStatePart,
} from "src/common/utils/gridState";
import { useCompanies, useCompany } from "src/common/api/company";
import { Company } from "src/common/types/company";
import { CustomDropDownActionButtons } from "src/common/components/custom/CustomDropDownActionButtons";
import GenerateInvoiceModal from "../GenerateInvoiceModal";
import { useCreateBulkInvoiceReservation } from "src/common/api/invoices";
import { processApiWarning } from "src/common/utils/errors";
import { ReservationEditModal } from "../ReservationEditModal";
import {
  determineCancelationFee,
  turnReservationIntoFormValues,
} from "src/common/utils";

interface ReservationsDatagridProps {
  data: Reservation[];
  isFetchingReservations: boolean;
  refetchReservations: () => void;
  shouldRenderRefreshButton: boolean;
  shouldRenderAddButton: boolean;
  shouldRenderEditActionsColumn: boolean;
  page: number;
  pageSize: number;
  total: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
  selectedCompany: Company | null;
  setSelectedCompany: (company: Company | null) => void;
  isServerDriven: boolean;
  handleSortModelChange?: (sortModel: GridSortModel) => void;
  onDatagridFiltersChange?: (datagridFilter: GridFilterModel) => void;
  gridState: GridStateSnapshot;
  updateGridStatePart: UpdateGridStatePart;
}

export const ReservationsDatagrid: FC<ReservationsDatagridProps> = ({
  data,
  shouldRenderRefreshButton,
  shouldRenderAddButton,
  isFetchingReservations,
  refetchReservations,
  page,
  pageSize,
  total,
  onPageChange,
  onPageSizeChange,
  selectedCompany,
  setSelectedCompany,
  isServerDriven,
  handleSortModelChange,
  onDatagridFiltersChange,
  gridState,
  updateGridStatePart,
}) => {
  const navigate = useNavigate();
  const currentUser = getCurrentUser();
  const theme = useTheme();
  const { t } = useTranslation(["manager", "common", "dispatcher"]);
  const {
    mutate: handleUpdateJob,
    isLoading: isUpdatingJob,
    isSuccess: isJobUpdated,
  } = useUpdateJob();
  const jobStatusOptions = getTranslatedJobStatusOptions(t);
  const {
    mutate: handleDeleteReservation,
    isLoading: isDeletingReservation,
    isSuccess: isDeleteReservationSuccess,
  } = useDeleteReservation();

  const { mutateAsync: handleCreateBulkInvoiceReservation } =
    useCreateBulkInvoiceReservation();

  const isManager = currentUser?.role === Role.OEPRATOR_MANAGER;
  const { data: allCompanies } = useCompanies(
    { expressions: [], sortModel: [], limit: 1000, offset: 0 },
    isManager
  );
  const [reservationFormValues, setReservationFormValues] = useRecoilState(
    reservationFormValuesState
  );

  const [reservationDeleteValues, setReservationDeleteValues] = useRecoilState(
    reservationDeleteValuesState
  );
  const [reservationCancelValues, setReservationCancelValues] = useRecoilState(
    reservationCancelValuesState
  );
  const [reservation, setReservation] = useState<Reservation | null>(null);

  const {
    data: companyData,
    isLoading: isCompanyLoading,
    isSuccess: isCompanySuccess,
  } = useCompany(
    reservation?.operator?.companyId,
    Boolean(reservation?.operator?.companyId)
  );

  const [reservationEditValues, setReservationEditValues] = useRecoilState(
    reservationEditValuesState
  );
  const [reservationFullFormValues, setReservationFullFormValues] =
    useRecoilState(reservationFullFormValuesState);

  const [selectedReservationIds, setSelectedReservationIds] = useState<
    number[]
  >([]);

  const [openBulkInvoiceModal, setOpenBulkInvoiceModal] =
    useState<boolean>(false);

  const companies = allCompanies?.data || [];

  const isLoading =
    isDeletingReservation || isFetchingReservations || isUpdatingJob;

  const createBulkInvoice = async () => {
    if (!selectedCompany) {
      processApiWarning("No company selected");
      return;
    }
    const createReservationInvoice = await handleCreateBulkInvoiceReservation({
      reservationIds: selectedReservationIds,
      dispatcherCompanyId: selectedCompany.id,
    });
    if (createReservationInvoice) {
      navigate("/finance/invoices");
    }
  };
  const handleCloseReservationModal = () => {
    if (!isLoading) {
      setReservationFormValues(RESERVATION_FORM_VALUES);
    }
  };

  const handleRowClick = (params: any) => {
    const reservationId = params.row.id;
    navigate(`/reservation/${reservationId}`);
  };

  const handleSubmitReservationModal = (values: ReservationFormValues) => {
    const reservationQuery: ReservationQueryFilters = {
      dateFrom: values.dateFrom
        ? new Date(values.dateFrom).toISOString()
        : null,
      dateTo: values.dateTo ? new Date(values.dateTo).toISOString() : null,
      siteAddress: values.siteAddress || null,
      city: values.city || null,
      plz: values.plz || null,
      lat: values.location?.coordinates[0] || null,
      lng: values.location?.coordinates[1] || null,
      vehicleId: values.vehicle?.id.toString() || null,
    };
    const queryParams = queryString.stringify(reservationQuery);

    setReservationFormValues(RESERVATION_FORM_VALUES);

    navigate(`/reservation?${queryParams}`);
  };

  const handleOpenReservationEditModal = (reservation: Reservation) => {
    setReservationEditValues({
      reservationId: reservation.id,
      flow: "Edit",
      dateFrom: reservation?.dateFrom || "",
      dateTo: reservation?.dateTo || "",
      siteAddress: reservation?.siteAddress || "",
      city: reservation?.city || "",
      plz: reservation?.plz || null,
      vehicle: reservation?.vehicle || null,
    });
  };
  const handleCloseReservationEditModal = () => {
    setReservationEditValues(RESERVATION_EDIT_DEFAULT);
  };
  const handleEditReservation = (values: ReservationFormValues) => {
    if (reservation) {
      const reservationQuery: ReservationQueryFilters = {
        dateFrom: values.dateFrom
          ? new Date(values.dateFrom).toISOString()
          : null,
        dateTo: values.dateTo ? new Date(values.dateTo).toISOString() : null,
        siteAddress: values.siteAddress || null,
        city: values.city || null,
        plz: values.plz || null,
        lat: values.location?.coordinates[0] || null,
        lng: values.location?.coordinates[1] || null,
        vehicleId: reservation.vehicle?.id.toString() || null,
        editMode: true,
      };
      const queryParams = queryString.stringify(reservationQuery);

      const reservationFormData = turnReservationIntoFormValues(reservation);
      setReservationFullFormValues(reservationFormData);
      navigate(`/reservation?${queryParams}`);
      handleCloseReservationEditModal();
    }
  };

  const handleCloseReservationModalDelete = () => {
    if (!isLoading) {
      setReservationDeleteValues(RESERVATION_DELETE_DEFAULT);
    }
  };
  const handleOpenReservationModalCancel = (reservation: Reservation) => {
    setReservation(reservation);

    const cancelationFee = determineCancelationFee(
      reservation?.dateFrom!,
      companyData?.settings.cancellation?.cancellationWindow || "",
      companyData?.settings.cancellation?.reducedCancellationWindow || "",
      reservation?.pricelist?.cancellationFee || 0,
      reservation?.pricelist?.dayCancellationFee || 0
    );
    setReservationCancelValues({
      reservationId: reservation.id,
      jobId: reservation.job?.id ? reservation.job?.id : null,
      reservationTitle: reservation.orderNumber,
      cancelationFee: cancelationFee,
      flow: "Cancel",
    });
  };

  const handleCloseReservationModalCancel = () => {
    if (!isLoading) {
      setReservationCancelValues(RESERVATION_CANCEL_DEFAULT);
    }
  };

  const handleCancelReservation = (comment: string) => {
    handleUpdateJob({
      status: JobStatus.CANCELLED,
      jobId: Number(reservationCancelValues.jobId),
      reservationId: Number(reservationCancelValues.reservationId),
      comments: comment,
    });
  };

  useEffect(() => {
    if (isDeleteReservationSuccess) {
      setReservationDeleteValues(RESERVATION_DELETE_DEFAULT);
    }
    if (isJobUpdated) {
      setReservationCancelValues(RESERVATION_CANCEL_DEFAULT);
    }
  }, [
    isDeleteReservationSuccess,
    isJobUpdated,
    setReservationDeleteValues,
    setReservationCancelValues,
  ]);

  const columns: GridColDef[] = [
    {
      field: "editActions",
      headerName: `${t("common:edit")}`,
      sortable: false,
      renderCell: (params: GridRenderCellParams<string>) =>
        renderEditActions(params),
      filterable: false,
    },
    {
      field: "id",
      headerName: "Id",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 50,
      sortable: false,
      filterable: false,
      renderCell: (params) => <GridColumnTypography value={params.row.id} />,
    },
    {
      field: "reservation.dateFrom",
      headerName: `${t("common:date")}`,
      type: "date",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => {
        const dateFrom = params.row.dateFrom;

        const formattedDate = format(parseISO(dateFrom), "dd/MM/yyyy", {
          locale: enGB,
        });

        return <GridColumnTypography value={formattedDate} />;
      },
    },
    {
      field: "created_at",
      headerName: "Created At",
      type: "string",
      headerAlign: "left",
      align: "left",
      sortable: false,
      filterable: false,
      width: 200,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.created_at} />
      ),
    },
    {
      field: "reservation.dateTo",
      headerName: `${t("common:time")}`,
      type: "dateTime",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => {
        const dateFrom = params.row.dateFrom;
        const dateTo = params.row.dateTo;

        const parsedFrom = parseISO(dateFrom);
        const parsedTo = parseISO(dateTo);

        const formattedFrom = format(parsedFrom, "HH:mm");
        const formattedTo = format(parsedTo, "HH:mm");

        return (
          <GridColumnTypography
            typographyStyles={{ ml: 0 }}
            value={`${formattedFrom} - ${formattedTo}`}
          >
            <AccessTimeIcon
              sx={{ marginRight: 1 }}
              color="primary"
              fontSize="small"
            />
          </GridColumnTypography>
        );
      },
    },
    {
      field: "job.status",
      headerName: `${t("common:status")}`,
      type: "singleSelect",
      headerAlign: "left",
      align: "left",
      width: 150,
      valueOptions: jobStatusOptions,
      renderCell: (params) => {
        const status = params.row?.job?.status;
        if (!status) {
          return "N/A";
        }
        const findStatus = JOB_STEPS.find((step) => step.name === status);
        const statusStyles: ColorTypes | {} = findStatus?.name
          ? STATUS_COLORS[findStatus.name]
          : {};
        const value = findStatus?.label ? t(findStatus.label) : "";
        return (
          <Chip
            sx={{
              color:
                status === JobStatus.NOT_STARTED
                  ? STATUS_COLORS.NOT_STARTED.color
                  : theme.palette.primary.contrastText,
              fontSize: "13px",
            }}
            component="span"
            color={
              Object.keys(statusStyles).length > 0
                ? (statusStyles as ColorTypes).chipColor
                : "default"
            }
            size="small"
            label={value}
          />
        );
      },
    },
    {
      field: "reservation.hasBeenRead",
      headerName: `${t("common:has-been-read")}`,
      type: "singleSelect",
      headerAlign: "left",
      valueOptions: [
        { value: true, label: t("common:yes") },
        { value: false, label: t("common:no") },
      ],
      align: "left",
      width: 200,
      renderCell: (params) => {
        const hasBeenRead = params.row.hasBeenRead || "";
        return (
          <GridColumnTypography
            value={hasBeenRead ? t("common:yes") : t("common:no")}
          />
        );
      },
    },
    {
      field: "reservation.vehicleUniqueId",
      headerName: `${t("common:vehicle-name")}`,
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => {
        if (!params.row.vehicle) {
          return "";
        }
        const vehicle = params.row?.vehicle;
        let vehicleUniqueId = "Unknown Vehicle";

        if (!params.row?.vehicleUniqueId) {
          vehicleUniqueId = getVehicleUniqueId(vehicle);
        } else {
          vehicleUniqueId = params.row.vehicleUniqueId;
        }

        return (
          <GridColumnTypography value={vehicleUniqueId}>
            <VehicleOutlined color={theme.palette.primary.main} />
          </GridColumnTypography>
        );
      },
    },
    {
      field: "operator.firstName",
      headerName: `${t("common:operator")}`,
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => {
        if (!params.row?.operator) {
          return "";
        }

        const firstName = params.row?.operator?.name || "";
        const lastName = params.row?.operator?.surname || "";
        const fullName = `${firstName} ${lastName}`;

        return (
          <>
            {fullName && (
              <GridColumnTypography value={fullName}>
                <CeAvatar size="medium" {...stringAvatar(fullName)} />
              </GridColumnTypography>
            )}
          </>
        );
      },
    },
    {
      field: "reservation.siteAddress",
      headerName: `${t("common:address")}`,
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => {
        const address = params.row.siteAddress || "";
        return <GridColumnTypography value={address} />;
      },
    },
    {
      field: "clientDetailsCompanyName",
      headerName: `${t("common:client-company")}`,
      type: "string",
      headerAlign: "left",
      align: "left",
      sortable: false,
      width: 200,
      renderCell: (params) => {
        const company = params.row.clientDetails?.companyName || "";
        return <GridColumnTypography value={company} />;
      },
    },
    {
      field: "dispatcherCompany.name",
      headerName: `${t("common:dispatcher-company")}`,
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => {
        const company = params.row.dispatcher?.company?.name || "";
        return <GridColumnTypography value={company} />;
      },
    },
    {
      field: "operatorCompany.name",
      headerName: `${t("common:operator-company")}`,
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => {
        const company = params.row.manager?.company?.name || "";
        return <GridColumnTypography value={company} />;
      },
    },
    {
      field: "job.amountOfConcrete",
      headerName: `${t("common:amount-of-concrete")}`,
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => {
        const amountOfConcrete = params.row.job?.amountOfConcrete || "";
        return <GridColumnTypography value={amountOfConcrete} />;
      },
    },
    {
      field: "job.flowRate",
      headerName: `${t("common:flow-rate")}`,
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => {
        const flowRate = params.row.job?.flowRate || "";
        return <GridColumnTypography value={flowRate} />;
      },
    },
    {
      field: "reservation.totalSum",
      headerName: `${t("common:sum")}`,
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 100,
      renderCell: (params) => {
        const isCompleted = params.row.job?.status === JobStatus.COMPLETE;
        const totalSum =
          isCompleted &&
          params.row.totalSum !== undefined &&
          params.row.totalSum !== null
            ? params.row.totalSum + " €"
            : "-";
        return <GridColumnTypography value={totalSum} />;
      },
    },
    {
      field: "reservation.invoiced",
      headerName: `${t("common:invoice")}`,
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 150,
      renderCell: (params) => {
        const invoiceNumber =
          params.row.invoiced && params.row.invoiceNumber
            ? params.row.invoiceNumber
            : "-";
        return (
          <Tooltip title={invoiceNumber}>
            <Typography variant="caption" ml={1} fontSize={13}>
              {invoiceNumber}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      field: "billed",
      headerName: `${t("common:billed")}`,
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => {
        const billed = params.row.billed || "";
        return <GridColumnTypography value={billed} />;
      },
    },
    {
      field: "job.comments",
      headerName: `${t("common:comment")}`,
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 700,
      renderCell: (params) => {
        const comments = params.row.job?.comments || "";
        return <GridColumnTypography value={comments} />;
      },
    },
    {
      field: "created_by",
      headerName: "Created By",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 125,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.created_by} />
      ),
    },
    {
      field: "updated_at",
      headerName: "Updated At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.updated_at} />
      ),
    },
    {
      field: "deleted_at",
      headerName: "Deleted At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.deleted_at} />
      ),
    },
  ];

  const renderEditActions = (params: GridRenderCellParams) => {
    const reservation: Reservation = params.row;
    const canEditReservation =
      reservation.job?.status !== JobStatus.NOT_STARTED &&
      reservation.created_by === currentUser?.id;

    return (
      <Stack
        direction="row"
        justifyContent="center"
        alignItems="center"
        spacing={0.5}
      >
        <IconButton
          aria-label="update reservation"
          disabled={canEditReservation}
          size="small"
          onClick={(event) => {
            event.stopPropagation();
            setReservation(reservation);
            handleOpenReservationEditModal(reservation);
          }}
        >
          <Edit02Icon size={16} variant={"stroke"} />
        </IconButton>
        <IconButton
          color="error"
          aria-label="update reservation"
          disabled={reservation?.job?.status === "CANCELLED"}
          size="small"
          onClick={(event) => {
            event.preventDefault();
            event.stopPropagation();
            handleOpenReservationModalCancel(reservation);
          }}
        >
          <UnavailableIcon size={17} variant={"stroke"} />
        </IconButton>

        <IconButton
          aria-label={t("common:delete-reservation")}
          disabled={isLoading}
          color="error"
          size="small"
          onClick={(event) => {
            event.preventDefault();
            event.stopPropagation();
            setReservationDeleteValues({
              reservationId: reservation.id,
              reservationTitle: "Reservation",
              flow: "Delete",
            });
          }}
        >
          <Delete01Icon size={16} variant={"stroke"} />
        </IconButton>
      </Stack>
    );
  };

  const onFilterChange = useCallback(
    (filterModel: GridFilterModel) => {
      if (onDatagridFiltersChange) {
        onDatagridFiltersChange(filterModel);
        updateGridStatePart("filterModel", filterModel);
      }
    },
    [onDatagridFiltersChange]
  );

  const onSortChange = useCallback(
    (sortModel: GridSortModel) => {
      if (handleSortModelChange) {
        handleSortModelChange(sortModel);
        updateGridStatePart("sortModel", sortModel);
      }
    },
    [handleSortModelChange]
  );

  return (
    <>
      <DataGrid
        sx={{
          border: "none",
          paddingTop: 0.5,
        }}
        onRowClick={handleRowClick}
        pagination
        page={gridState.page - 1}
        pageSize={gridState.pageSize}
        rowsPerPageOptions={[20, 40, 60, 80, 100]}
        rowCount={total}
        isRowSelectable={(params: GridRowParams) => {
          if (
            params.row.job?.status !== JobStatus.COMPLETE ||
            params.row.invoiced
          ) {
            return false;
          }

          return true;
        }}
        columnVisibilityModel={gridState.columnVisibilityModel}
        onColumnVisibilityModelChange={(model) =>
          updateGridStatePart("columnVisibilityModel", model)
        }
        filterModel={gridState.filterModel}
        sortingMode={isServerDriven ? "server" : "client"}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        onSortModelChange={isServerDriven ? onSortChange : undefined}
        filterMode={isServerDriven ? "server" : "client"}
        onFilterModelChange={isServerDriven ? onFilterChange : undefined}
        sortModel={gridState.sortModel}
        components={{
          Toolbar: () => (
            <CeDataGridToolbar
              shouldRenderRefreshButton={shouldRenderRefreshButton}
              shouldRenderAddButton={shouldRenderAddButton}
              onRefreshButtonClick={refetchReservations}
              addButtonClickHandler={() => {
                setReservationFormValues({
                  ...reservationFormValues,
                  dateFrom: new Date().toString(),
                  dateTo: addMinutes(new Date(), 180).toString(),
                  flow: "Add",
                });
              }}
              addButtonDescription={t("add-reservation")}
              toolBarSxProps={{ alignItems: "center" }}
            >
              <Stack
                flex={2}
                direction="row"
                justifyContent="space-between"
                px={1}
              >
                {isManager ? (
                  <Autocomplete
                    options={companies}
                    value={selectedCompany || null}
                    getOptionLabel={(option) => option.name || ""}
                    isOptionEqualToValue={(option, value) =>
                      option.id === value.id
                    }
                    onChange={(_, newValue) => setSelectedCompany(newValue)}
                    clearOnEscape
                    renderInput={(params) => (
                      <CeTextField
                        {...params}
                        size="small"
                        label="Filter by company"
                        InputLabelProps={{ shrink: true }}
                      />
                    )}
                    sx={{ width: 250, ml: 1 }}
                  />
                ) : null}
                {selectedReservationIds.length > 0 ? (
                  <CustomDropDownActionButtons
                    buttonDescription="Actions"
                    buttonSize="small"
                    buttonIconSize={16}
                    sx={{ alignSelf: "center" }}
                  >
                    <MenuItem onClick={() => setOpenBulkInvoiceModal(true)}>
                      Generate
                    </MenuItem>
                  </CustomDropDownActionButtons>
                ) : null}
              </Stack>
            </CeDataGridToolbar>
          ),
        }}
        paginationMode="server"
        columns={columns}
        rows={data}
        checkboxSelection={!!selectedCompany?.id}
        selectionModel={selectedReservationIds}
        onSelectionModelChange={(newSelection) => {
          setSelectedReservationIds(newSelection as number[]);
        }}
      />
      <GenerateInvoiceModal
        shouldModalOpen={openBulkInvoiceModal}
        handleCloseInvoiceModal={() => setOpenBulkInvoiceModal(false)}
        invoicedCompanyName={selectedCompany?.name || ""}
        onInvoiceGenerate={createBulkInvoice}
        reservationIdsArray={selectedReservationIds}
      />

      <ReservationModal
        initialFormValues={reservationFormValues}
        isLoading={isLoading}
        handleSubmitReservationModal={(values) =>
          handleSubmitReservationModal(values)
        }
        handleCloseReservationModal={handleCloseReservationModal}
      />
      <ReservationCancelWithCommentModal
        onSubmit={handleCancelReservation}
        isLoading={isLoading}
        cancelationFee={reservationCancelValues.cancelationFee}
        flow={reservationCancelValues.flow}
        reservationTitle={reservationCancelValues.reservationTitle}
        handleCloseReservationModalCancel={handleCloseReservationModalCancel}
      />
      <ReservationModalDelete
        flow={reservationDeleteValues.flow}
        isLoading={isLoading}
        reservationTitle={reservationDeleteValues.reservationTitle}
        reservationId={reservationDeleteValues.reservationId}
        handleCloseReservationModalDelete={handleCloseReservationModalDelete}
        handleDeleteReservation={handleDeleteReservation}
      />
      <ReservationEditModal
        initialFormValues={reservationEditValues}
        handleEditReservation={handleEditReservation}
        flow={reservationEditValues.flow}
        handleCloseReservationEditModal={handleCloseReservationEditModal}
        fullUpdate
        isLoading={false}
      />
    </>
  );
};
