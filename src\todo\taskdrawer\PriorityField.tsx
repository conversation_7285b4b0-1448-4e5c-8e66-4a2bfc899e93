import React from "react";
import {
  MenuItem,
  ListItemIcon,
  Typography,
  Box,
  IconButton,
  useTheme
} from "@mui/material";
import {
  ArrowDown01Icon,
  ArrowUp01Icon,
  Edit02Icon,
  Menu09Icon
} from "@hugeicons/react";
import { Priority } from "src/common/types/tasks";
import { CeTextField } from "src/common/components";

interface PriorityFieldProps {
  formik: {
    values: {
      priority: Priority | null;
    };
    setFieldValue: (field: string, value: any) => void;
    handleChange: (e: React.ChangeEvent<any>) => void;
    touched: {
      priority?: boolean;
    };
    errors: {
      priority?: string;
    };
    setFieldTouched: (
      field: string,
      touched: boolean,
      shouldValidate?: boolean
    ) => void;
  };
}

const PriorityField: React.FC<PriorityFieldProps> = ({ formik }) => {
  const theme = useTheme();
  const priorityData = [
    {
      priority: Priority.HIGH,
      icon: <ArrowUp01Icon size={20} color={theme.palette.error.main} />,
      color: theme.palette.error.main
    },
    {
      priority: Priority.MEDIUM,
      icon: <Menu09Icon size={20} color={theme.palette.warning.main} />,
      color: theme.palette.warning.main
    },
    {
      priority: Priority.LOW,
      icon: <ArrowDown01Icon size={20} color={theme.palette.secondary.main} />,
      color: theme.palette.secondary.main
    }
  ];

  const renderIcon = () =>
    priorityData.find(
      (priorityObject) => priorityObject.priority === formik.values.priority
    )?.icon;

  const priorityDataFields = priorityData.map((priorityObject, key) => (
    <MenuItem key={key} value={priorityObject.priority}>
      <ListItemIcon color={priorityObject.color}>
        {priorityObject.icon}
      </ListItemIcon>
      <Typography variant="body1">{priorityObject.priority}</Typography>
    </MenuItem>
  ));

  if (!formik.values.priority) {
    return (
      <CeTextField
        select
        name="priority"
        value={formik.values.priority || ""}
        onChange={formik.handleChange}
        variant="outlined"
        size="small"
        fullWidth
        onBlur={() => formik.setFieldTouched("priority", true)}
        error={!!formik.errors.priority && formik.touched.priority}
        helperText={formik.touched.priority && formik.errors.priority}
      >
        {priorityDataFields}
      </CeTextField>
    );
  }
  return (
    <Box alignSelf="center" display="flex" alignItems="center" gap={1}>
      <>
        {renderIcon()}
        <Typography variant="body1" fontSize={16} letterSpacing={0.15}>
          {formik.values.priority}
        </Typography>
      </>
      <IconButton
        size="small"
        onClick={() => formik.setFieldValue("priority", "")}
        sx={{ ml: 1 }}
      >
        <Edit02Icon size={16} variant="stroke" />
      </IconButton>
    </Box>
  );
};

export default PriorityField;
