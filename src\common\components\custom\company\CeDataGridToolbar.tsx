import { Icon<PERSON><PERSON><PERSON>, <PERSON>ack, Divider, SxProps } from "@mui/material";
import { CeButton } from "./CeButton";
import {
  GridToolbar<PERSON>ontainer,
  GridToolbarColumnsButton,
  GridToolbarDensitySelector,
  GridToolbarExport,
  GridToolbarFilterButton,
} from "@mui/x-data-grid";
import {
  Layout3ColumnIcon,
  Layout3RowIcon,
  FilterIcon,
  Download04Icon,
  RefreshIcon,
  Add01Icon,
} from "@hugeicons/react";
import { buttonTextTransform } from "../customCss";
import { ReactNode } from "react";

interface CeDataGridToolbarProps {
  shouldRenderRefreshButton?: boolean;
  shouldRenderAddButton?: boolean;
  onRefreshButtonClick?: () => void;
  addButtonClickHandler?: () => void;
  addButtonDescription?: string;
  children?: ReactNode;
  toolBarSxProps?: SxProps;
}

export const CeDataGridToolbar = ({
  shouldRenderRefreshButton,
  onRefreshButtonClick,
  shouldRenderAddButton,
  addButtonClickHandler,
  addButtonDescription,
  children,
  toolBarSxProps,
}: CeDataGridToolbarProps) => {
  return (
    <GridToolbarContainer>
      <Stack
        direction="row"
        justifyContent="space-between"
        sx={{
          marginBottom: 1,
          marginTop: 0.5,
          marginRight: 1,
          flex: 1,
          ...toolBarSxProps,
        }}
      >
        <Stack direction="row" justifyContent="space-between">
          {shouldRenderRefreshButton && (
            <IconButton
              onClick={onRefreshButtonClick}
              color="primary"
              aria-label="Refresh rows"
            >
              <RefreshIcon size={16} color="currentColor" variant={"solid"} />
            </IconButton>
          )}
          <Divider
            orientation="vertical"
            sx={{ marginRight: 2, marginLeft: 1 }}
          />
          <GridToolbarColumnsButton
            startIcon={
              <Layout3ColumnIcon
                size={16}
                color="currentColor"
                variant={"solid"}
              />
            }
            sx={buttonTextTransform}
          />
          <GridToolbarFilterButton
            componentsProps={{
              button: {
                startIcon: (
                  <FilterIcon
                    size={16}
                    color={"currentColor"}
                    variant={"solid"}
                  />
                ),
              },
            }}
            sx={buttonTextTransform}
          />
          <GridToolbarDensitySelector
            startIcon={
              <Layout3RowIcon
                size={16}
                color="currentColor"
                variant={"solid"}
              />
            }
            sx={buttonTextTransform}
          />
          <GridToolbarExport
            startIcon={
              <Download04Icon
                size={16}
                color="currentColor"
                variant={"solid"}
              />
            }
            sx={buttonTextTransform}
          />
        </Stack>
        {children}
        {shouldRenderAddButton ? (
          <CeButton
            variant="contained"
            startIcon={
              <Add01Icon size={16} color={"currentColor"} variant={"solid"} />
            }
            size="small"
            color="primary"
            onClick={addButtonClickHandler}
            sx={{ textWrap: "nowrap" }}
          >
            {addButtonDescription}
          </CeButton>
        ) : null}
      </Stack>
    </GridToolbarContainer>
  );
};
