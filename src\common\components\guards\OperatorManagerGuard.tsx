import type { FC, ReactNode } from "react";
import { isAuthenticated } from "src/common/api/auth";
import { Role } from "src/common/types";

interface OperatorManagerGuardProps {
  children: ReactNode;
}
export const OperatorManagerGuard: FC<OperatorManagerGuardProps> = ({
  children,
}) => {
  const { role } = isAuthenticated();

  if (role === Role.OEPRATOR_MANAGER) {
    return <>{children}</>;
  }

  return null;
};
