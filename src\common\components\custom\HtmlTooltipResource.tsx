import { Tooltip, TooltipProps, styled, tooltipClasses } from "@mui/material";

export const HtmlTooltipResource = styled(
  ({ className, ...props }: TooltipProps) => (
    <Tooltip
      placement="bottom-start"
      sx={{
        backgroundColor: "transparent",
        fontSize: "12px",
        letterSpacing: "0.4px",
        color: props.color,
        ...props.sx
      }}
      {...props}
      classes={{ popper: className }}
    />
  )
)(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    maxWidth: "100%",
    minWidth: "100%",
    backgroundColor: theme.palette.background.paper,
    padding: "12px",
    boxShadow: "0px 2px 5px 0px rgba(0, 0, 0, 0.08)",
    borderRadius: "8px",
    color: theme.palette.text.primary,
    fontSize: "14px",
    fontWeight: "400",
    lineHeight: "20.02px",
    letterSpacing: "0.17px"
  }
}));
