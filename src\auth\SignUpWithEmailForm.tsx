import { Stack, Typography, CardActions, CardContent } from "@mui/material";
import { useFormik } from "formik";
import * as yup from "yup";
import { useNavigate } from "react-router-dom";
import { CeCard, CeButton, CeTextField } from "src/common/components";

interface SignUpWithEmailFormProps {
  // handleSignUp: (attrs: CreateOrganizationAndUserDto) => void
  isLoading: boolean;
}
export const SignUpWithEmailForm: React.FC<SignUpWithEmailFormProps> = ({
  isLoading,
}) => {
  const navigate = useNavigate();

  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
      firstName: "",
      lastName: "",
      title: "",
    },
    validationSchema: yup.object({
      title: yup.string().required("Organization name is required"),
      email: yup
        .string()
        .email("Enter a valid email")
        .required("Email is required"),
      password: yup
        .string()
        .min(5, "Invalid password")
        .required("Password is required"),
      firstName: yup
        .string()
        .min(1, "Invalid first name")
        .required("First name is required"),
      lastName: yup
        .string()
        .min(1, "Invalid last name")
        .required("Last name is required"),
    }),
    onSubmit: () => {},
  });

  return (
    <CeCard sx={{ padding: 2 }}>
      <CardContent>
        <Stack
          component="form"
          sx={{ width: "30ch" }}
          spacing={2}
          noValidate
          onSubmit={formik.handleSubmit}
        >
          <Stack>
            <Typography
              variant="h5"
              textAlign="center"
              sx={{ marginTop: 0, marginBottom: 0 }}
            >
              Sign up my organization
            </Typography>
            <Typography
              textAlign="center"
              color="text.secondary"
              sx={{ marginBottom: 2, fontSize: "14px", marginTop: "5px" }}
            >
              Join our primary care network for free!
            </Typography>
          </Stack>
          <CeTextField
            fullWidth
            id="title"
            name="title"
            label="Organization name"
            size="small"
            value={formik.values.title}
            onChange={formik.handleChange}
            error={formik.touched.title && Boolean(formik.errors.title)}
            helperText={formik.touched.title && formik.errors.title}
            disabled={isLoading}
            required
          />
          <CeTextField
            fullWidth
            id="firstName"
            name="firstName"
            label="First name"
            size="small"
            value={formik.values.firstName}
            onChange={formik.handleChange}
            error={formik.touched.firstName && Boolean(formik.errors.firstName)}
            helperText={formik.touched.firstName && formik.errors.firstName}
            disabled={isLoading}
            required
          />
          <CeTextField
            fullWidth
            id="lastName"
            name="lastName"
            label="Last name"
            size="small"
            value={formik.values.lastName}
            onChange={formik.handleChange}
            error={formik.touched.lastName && Boolean(formik.errors.lastName)}
            helperText={formik.touched.lastName && formik.errors.lastName}
            disabled={isLoading}
            required
          />
          <CeTextField
            fullWidth
            id="email"
            name="email"
            label="Email"
            type="email"
            size="small"
            value={formik.values.email}
            onChange={formik.handleChange}
            error={formik.touched.email && Boolean(formik.errors.email)}
            helperText={formik.touched.email && formik.errors.email}
            disabled={isLoading}
            required
          />
          <CeTextField
            fullWidth
            id="password"
            name="password"
            label="Password"
            type="password"
            size="small"
            value={formik.values.password}
            onChange={formik.handleChange}
            error={formik.touched.password && Boolean(formik.errors.password)}
            helperText={formik.touched.password && formik.errors.password}
            disabled={isLoading}
            required
          />
          <CeButton
            color="primary"
            variant="contained"
            fullWidth
            type="submit"
            disabled={isLoading}
          >
            Sign up
          </CeButton>
        </Stack>
      </CardContent>
      <CardActions sx={{ justifyContent: "space-between" }}>
        <CeButton
          variant="text"
          size="small"
          onClick={() => navigate("/auth/signin")}
        >
          Sign in
        </CeButton>
      </CardActions>
    </CeCard>
  );
};
