import { Stack } from "@mui/material";
import { CeButton, MainModal } from "src/common/components";

interface GenerateInvoiceModalProps {
  shouldModalOpen: boolean;
  reservationIdsArray: number[];
  invoicedCompanyName: string | null;
  handleCloseInvoiceModal: () => void;
  onInvoiceGenerate: () => void;
}

const GenerateInvoiceModal = ({
  shouldModalOpen,
  reservationIdsArray,
  invoicedCompanyName,
  handleCloseInvoiceModal,
  onInvoiceGenerate,
}: GenerateInvoiceModalProps) => {
  const displayModalHelperText = `You are generating an invoice for ${
    reservationIdsArray.length
  } ${
    reservationIdsArray.length > 1 ? "reservations" : "reservation"
  } for ${invoicedCompanyName}.`;

  return (
    <MainModal
      title="Generate Invoice"
      isOpen={shouldModalOpen}
      handleClose={handleCloseInvoiceModal}
      maxWidth="lg"
      helperText={displayModalHelperText}
      shouldRenderDialogActions
      actionsChildren={
        <Stack direction="row" alignItems="center">
          <CeButton
            onClick={handleCloseInvoiceModal}
            size="medium"
            variant="text"
          >
            Cancel
          </CeButton>
          <CeButton onClick={onInvoiceGenerate} size="medium">
            Generate Invoice
          </CeButton>
        </Stack>
      }
    />
  );
};

export default GenerateInvoiceModal;
