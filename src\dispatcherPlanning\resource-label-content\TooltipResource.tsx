import React from "react";
import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";

interface TooltipResourceProps {
    phoneNumber?: string | null;
    contactEmail?: string | null;
    companyName?: string | null;
}
const TooltipResource: React.FC<TooltipResourceProps> = ({ phoneNumber, contactEmail, companyName }) => {
     const { t } = useTranslation("common");
  return (
    <Box>
      <Box
        alignItems="center"
        display="flex"
        justifyContent={"flex-start"}
        mt={0.5}
        gap="5px"
      >
        <Typography>{t('common:phone-number')}:</Typography>
        <Typography>{phoneNumber || ''}</Typography>
      </Box>

      <Box
        alignItems="center"
        display="flex"
        justifyContent={"flex-start"}
        mt={0.5}
        gap="5px"
      >
        <Typography>{t('common:email')}:</Typography>
        <Typography>{contactEmail || ''}</Typography>
      </Box>

      <Box
        alignItems="center"
        display="flex"
        justifyContent={"flex-start"}
        mt={0.5}
        gap="5px"
      >
        <Typography>{t('common:company-name')}:</Typography>
        <Typography>{companyName || ''}</Typography>
      </Box>
    </Box>
  );
};

export default TooltipResource;
