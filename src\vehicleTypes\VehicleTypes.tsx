import { useWindowSize } from "@react-hook/window-size";
import { useState } from "react";
import { getCurrentUser, useVehicleTypes } from "src/common/api";
import { VehicleTypesDatagrid } from "./datagrid/VehicleTypesDatagrid";
import { CePaper } from "src/common/components";

export const VehicleTypes = () => {
  const currentUser = getCurrentUser();

  const [, height] = useWindowSize();
  const [pageState, setPageState] = useState<{
    page: number;
    pageSize: number;
  }>({
    page: 1,
    pageSize: 20
  });

  const {
    data: allVehicleTypes,
    isLoading: isLoadingVehicleTypes,
    refetch: refetchVehicleTypes
  } = useVehicleTypes(
    {
      operatorManagerId: currentUser?.id,
      limit: pageState.pageSize,
      offset: (pageState.page - 1) * pageState.pageSize
    },
    Boolean(currentUser?.id)
  );

  const handlePageChange = (newPage: number) => {
    setPageState((old) => ({
      ...old,
      page: newPage + 1
    }));
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageState((old) => ({
      ...old,
      pageSize: newPageSize
    }));
  };

  return (
    <CePaper sx={{ height: `${height - 100}px`, p: 2 }}>
      <VehicleTypesDatagrid
        data={allVehicleTypes?.data || []}
        isFetchingVehicleTypes={isLoadingVehicleTypes}
        refetchVehicleTypes={refetchVehicleTypes}
        shouldRenderRefreshButton
        page={pageState.page}
        pageSize={pageState.pageSize}
        total={allVehicleTypes?.totalCount || 0}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />
    </CePaper>
  );
};
