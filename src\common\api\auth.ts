import axios, { AxiosError } from "axios";
import { decodeToken, isExpired } from "react-jwt"; // Make sure to import the appropriate functions from react-jwt
import { useMutation, useQuery } from "react-query";
import { processApiError, processApiSuccess } from "../utils/errors";
import {
  ForgotPasswordDto,
  ResetPasswordDto,
  SignInUserWithEmailDto,
  User,
  UserProfile,
  UserWithToken,
} from "../types";

const backendUrl = process.env.REACT_APP_API_URL;

// Function to set the access token to local storage
function setUserToLocalStorage(user: UserWithToken): void {
  localStorage.setItem("current_user", JSON.stringify(user));
}

// Function to check if the user is authenticated
export const isAuthenticated = () => {
  const userWithToken: UserWithToken | null = getCurrentUser();

  if (!userWithToken) {
    return {
      isAuth: false,
      role: 1,
    };
  }
  const isMyTokenExpired = isExpired(userWithToken.token);

  const isAuth = userWithToken && !isMyTokenExpired;

  const role = userWithToken.role;

  return {
    isAuth,
    role,
  };
};

// Function to remove the access token from local storage
function removeAccessTokenFromLocalStorage(): void {
  localStorage.removeItem("current_user");
}

export const getCurrentUser = () => {
  const userStringified = localStorage.getItem("current_user") || "";
  const userWithToken: UserWithToken | null = userStringified
    ? JSON.parse(userStringified)
    : null;

  return userWithToken;
};

export const handleSignInWithEmailAndPassword = (
  signInArgs: SignInUserWithEmailDto
) => {
  return axios
    .post(`${backendUrl}/auth/login`, signInArgs, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useSignInWithEmailAndPassword = () => {
  return useMutation<
    { access_token: string; user: User },
    AxiosError | Error,
    SignInUserWithEmailDto,
    () => void
  >(
    (signInArgs: SignInUserWithEmailDto) =>
      handleSignInWithEmailAndPassword(signInArgs),
    {
      onError: (err) => processApiError("Unable to sign in", err),
      onSuccess: (response) => {
        const userWithToken: UserWithToken = {
          ...response.user,
          token: response.access_token,
        };
        setUserToLocalStorage(userWithToken);
      },
    }
  );
};

export const handleResetPassword = (resetPasswordArgs: ResetPasswordDto) => {
  return axios
    .post(`${backendUrl}/users/reset-password`, resetPasswordArgs, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useResetPassword = () => {
  return useMutation<
    { message: string },
    AxiosError | Error,
    ResetPasswordDto,
    () => void
  >(
    (resetPasswordArgs: ResetPasswordDto) =>
      handleResetPassword(resetPasswordArgs),
    {
      onSuccess: (response) => {
        processApiSuccess(response.message);
      },
      onError: (err) => processApiError("Unable to reset password", err),
    }
  );
};

export const handleForgotPassword = (forgotPasswordArgs: ForgotPasswordDto) => {
  return axios
    .post(`${backendUrl}/users/forgot-password`, forgotPasswordArgs, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useForgotPassword = () => {
  return useMutation<
    { message: string },
    AxiosError | Error,
    ForgotPasswordDto,
    () => void
  >(
    (forgotPasswordArgs: ForgotPasswordDto) =>
      handleForgotPassword(forgotPasswordArgs),
    {
      onSuccess: (response) => {
        processApiSuccess(response.message);
      },
      onError: (err) => processApiError("Unable to use forgot password", err),
    }
  );
};

export const handleLogoutUser = () => {
  return axios
    .post(`${backendUrl}/auth/logout`, null, { withCredentials: true })
    .then((response) => response.data);
};
export const useLogoutUser = () => {
  return useMutation<{ status: string }, AxiosError | Error>(
    () => handleLogoutUser(),
    {
      onSuccess: () => {
        removeAccessTokenFromLocalStorage();
        window.location.reload(); // using browser native features instead of react router to get a full page refresh to pick up any new deployments
      },
      onError: (err) => processApiError("Unable to logout", err),
    }
  );
};
