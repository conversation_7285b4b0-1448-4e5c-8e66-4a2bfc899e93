import { GridSortModel } from "@mui/x-data-grid";
import { CommonEntity, PossibleSortDir } from "./common";
import { Contract } from "./contract";
import { Expression } from "./filters";
import { Partner } from "./partners";
import { VehicleModels } from "./vehicleModels";

export interface PriceList extends CommonEntity {
  id: number;
  title: string;
  packageFlatFee: number | null;
  pricePerMeterPumped: number | null;
  additionalHour: number | null;
  cleaningFee: number | null;
  pricePerMeterOfFlexiblePipeLength80Mm: number | null;
  pricePerMeterOfFlexiblePipeLength90Mm: number | null;
  pricePerMeterOfFlexiblePipeLength100Mm: number | null;
  pricePerMeterOfRigidPipeLength120Mm: number | null;
  supplyOfTheChemicalSlushie: number | null;
  barbotine: number | null;
  extraCementBagPrice: number | null;
  packageFlatFeeDuration?: number;
  pipeInvoicingStartsFrom?: number;
  pumpTiers?: PumpTier[];
  transportRates?: TransportRate[];
  secondTechnicianHourFee?: number;
  containerId?: number;
  vehicleId?: number;
  vehicleSize?: VehicleModels;
  variants?: PriceList[];
  contracts: Contract[];
  vehicleTypeId?: number;
  dayContractFee?: number | null;
  dayContractDuration?: number | null;
  dayContractOvertimeRate?: number | null;
  packageFlatFeeWeekend?: number | null;
  additionalHourWeekend?: number | null;
  packageFlatFeeNight?: number | null;
  additionalHourNight?: number | null;
  minimumChargeFlatFee?: number | null;
  minimumM3Charged?: number | null;
  dayCancellationFee?: number | null;
  cancellationFee?: number | null;
  secondTechnicianHourFeeWeekend?: number | null;
  secondTechnicianHourFeeNight?: number | null;
  isContract?: boolean;
}

export interface PriceListFormValues {
  priceListId: number | null;
  title: string | null;
  packageFlatFee: number | null;
  packageFlatFeeDuration: number | null;
  pricePerMeterPumped: number | null;
  additionalHour: number | null;
  cleaningFee: number | null;
  pipeInvoicingStartsFrom?: number | null;
  pricePerMeterOfFlexiblePipeLength80Mm: number | null;
  pricePerMeterOfFlexiblePipeLength90Mm: number | null;
  pricePerMeterOfFlexiblePipeLength100Mm: number | null;
  pricePerMeterOfRigidPipeLength120Mm: number | null;
  supplyOfTheChemicalSlushie: number | null;
  barbotine: number | null;
  extraCementBagPrice: number | null;
  secondTechnicianHourFee?: number | null;
  pumpTiers: PumpTier[];
  transportRates: TransportRate[];
  vehicleId: number | null;
  containerId?: number | null;
  vehicleTypeId: number | null;
  flow: PriceListModalFlow;
  pumpingFeeOptions?: PumpingFeeOptions;
  dayContractFee?: number | null;
  dayContractDuration?: number | null;
  dayContractOvertimeRate?: number | null;
  packageFlatFeeWeekend?: number | null;
  additionalHourWeekend?: number | null;
  packageFlatFeeNight?: number | null;
  additionalHourNight?: number | null;
  minimumChargeFlatFee?: number | null;
  minimumM3Charged?: number | null;
  dayCancellationFee?: number | null;
  cancellationFee?: number | null;
  secondTechnicianHourFeeWeekend?: number | null;
  secondTechnicianHourFeeNight?: number | null;
  isWeekendContract: boolean;
  isNightContract: boolean;
  contractCancellationPeriod?: string | null;
  contractLateCancellationPeriod?: string | null;
}

export interface PriceListWithCount {
  totalCount: number;
  data: PriceList[];
}

export interface getPriceListsDto {
  expressions: Expression[];
  sortModel: GridSortModel;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
  excludeVariants?: boolean;
  parentId?: number;
  relations?: string[];
}

export interface DeletePriceListModalValues {
  priceListId?: number;
  priceListTitle?: string;
  flow: PriceListModalDeleteFlow;
}

export interface ReviewPriceListModalValues {
  contractId?: number;
  pricelistId: number | null;
  vehicleId: number | null;
  flow: PriceListModalReviewFlow;
}

export interface CreatePriceListDto {
  title: string | null;
  packageFlatFee: number | null;
  packageFlatFeeDuration?: number | null;
  pricePerMeterPumped: number | null;
  additionalHour: number | null;
  cleaningFee: number | null;
  pipeInvoicingStartsFrom?: number | null;
  pricePerMeterOfFlexiblePipeLength80Mm: number | null;
  pricePerMeterOfFlexiblePipeLength90Mm: number | null;
  pricePerMeterOfFlexiblePipeLength100Mm: number | null;
  pricePerMeterOfRigidPipeLength120Mm: number | null;
  supplyOfTheChemicalSlushie: number | null;
  barbotine: number | null;
  extraCementBagPrice: number | null;
  secondTechnicianHourFee?: number | null;
  vehicleId?: number | null;
  pumpTiers: PumpTier[];
  transportRates: TransportRate[];
  containerId?: number | null;
  vehicleTypeId: number | null;
  dayContractFee?: number | null;
  dayContractDuration?: number | null;
  dayContractOvertimeRate?: number | null;
  packageFlatFeeWeekend?: number | null;
  additionalHourWeekend?: number | null;
  packageFlatFeeNight?: number | null;
  additionalHourNight?: number | null;
  minimumChargeFlatFee?: number | null;
  minimumM3Charged?: number | null;
  dayCancellationFee?: number | null;
  cancellationFee?: number | null;
  secondTechnicianHourFeeWeekend?: number | null;
  secondTechnicianHourFeeNight?: number | null;
  contractCancellationPeriod?: string | null;
  contractLateCancellationPeriod?: string | null;
}

export interface PumpingFeeOptions {
  tierPricing: boolean;
  pricePerMeter: boolean;
}

export interface PumpTier {
  name: string;
  price: number | null;
  minimum: number | null;
  maximum: number | null;
}

export interface TransportRate {
  to: number | null;
  from: number | null;
  name: string;
  tariff: number | null;
}

export interface UpdatePriceListDto
  extends Omit<CreatePriceListDto, "password"> {
  priceListId: number;
}

export interface DeletePriceListDto {
  priceListId: number;
}
export enum PriceListDeleteFlowEnum {
  VARIANT_DELETE = "Variant Delete",
  PRICELIST_DELETE = "Pricelist Delete",
}
export type PriceListModalFlow = PricelistTypeEnum | null;
export type PriceListModalDeleteFlow = PriceListDeleteFlowEnum | null;
export type PriceListModalReviewFlow = "Review" | null;

export enum PricelistTypeEnum {
  CREATE_PARENT = "CREATE_PARENT",
  UPDATE_PARENT = "UPDATE_PARENT",
  CREATE_VARIANT = "CREATE_VARIANT",
  UDPATE_VARIANT = "UPDATE_VARIANT",
  CREATE_DEFAULT = "CREATE_DEFAULT",
  UPDATE_DEFAULT = "UPDATE_DEFAULT",
}

export interface ContainerPricelists extends CommonEntity {
  id: number;
  title: string;
  isPublic?: boolean;
  isDefault: boolean;
  companyId: number;
  partners: Partner[];
  pricelists: PriceList[];
}
export interface ExtendedContainerPricelistsState extends ContainerPricelists {
  flow?: "Update" | null;
}

export interface ContainersWithCount {
  totalCount: number;
  data: ContainerPricelists[];
}

export interface GetContainersDto {
  sortModel: GridSortModel;
  expressions: Expression[];
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
}

export interface CreateContainerDto {
  title: string;
  isPublic?: boolean;
  isDefault?: boolean;
  partnerIds?: number[] | [];
  copyFromContainerId?: number | null;
}
export interface UpdateContainerDto extends Partial<CreateContainerDto> {
  id: number;
}
export interface DeleteContainerDto {
  id: number;
}

export enum ContainerFormFlow {
  UPDATE = "Update",
  CREATE = "Create",
}
export interface ContainerModalFormValues {
  title: string | null;
  isPublic?: boolean;
  partnerIds?: number[] | [];
  id?: number | null;
  isDefault: boolean;
  flow: ContainerFormFlow | null;
  partners: Partner[];
  copyFromContainer: ContainerPricelists | null;
}
