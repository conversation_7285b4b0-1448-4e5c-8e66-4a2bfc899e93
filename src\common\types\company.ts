import { GridSortModel } from "@mui/x-data-grid";
import { Country } from "../utils/countries";
import { CommonEntity, PossibleSortDir } from "./common";
import { CompanySettings } from "./companySettings";
import { CompanyType } from "./manager";
import { Task } from "./tasks";
import { Status, UserEnumDisplay } from "./user";
import { Expression } from "./filters";

export interface Company extends CommonEntity {
  id: number;
  name: string | null;
  type: CompanyType;
  contactEmail?: string | null;
  billingEmail?: string | null;
  planningEmail?: string | null;
  phoneNumber?: string | null;
  address?: string | null;
  secondaryAddress?: string | null;
  country?: string | null;
  city?: string | null;
  zipCode?: number;
  status: Status | null;
  vatNumber?: string | null;
  taxes?: Tax[];
  settings: CompanySettings;
  tasks?: Task[];
}

export interface CompanyWithCount {
  totalCount: number;
  data: Company[];
}

export interface CompanyFormValues {
  companyId?: number;
  name: string;
  type: CompanyType;
  contactEmail: string;
  billingEmail: string;
  planningEmail: string;
  phoneNumber: string | null;
  address: string;
  secondaryAddress: string;
  country: Country | null;
  city: string;
  zipCode: number | null;
  vatNumber: string;
  taxes: Tax[];
}

export interface CreateCompanyDto {
  name: string | null;
  type: string | null;
  contactEmail: string | null;
  billingEmail: string | null;
  planningEmail: string | null;
  phoneNumber: string | null;
  address: string | null;
  secondaryAddress: string | null;
  country: string | null;
  city: string | null;
  zipCode: number | null;
  vatNumber: string | null;
  taxes: Tax[];
}

export enum CompanyCategory {
  OPERATOR = 1,
  DISPATCHER = 2,
}

export interface UpdateCompanyDto extends CreateCompanyDto {
  companyId: number;
}

export enum CompanyCategoryTypes {
  ID = "company.id",
  CATEGORY = "company.category",
  CONTACT_EMAIL = "company.contactEmail",
}

export interface GetCompanyDto {
  sortModel: GridSortModel;
  expressions: Expression[];
  relations?: string[];
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
}

export interface Tax {
  name: string | null;
  percentage: number | null;
  isDefault?: boolean;
}

export interface TaxDto {
  name: string;
  percentage: number;
}
