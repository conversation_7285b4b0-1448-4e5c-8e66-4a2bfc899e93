import { VehicleTypeFormValues, VehicleTypeModalFlow, VehicleType } from "../types/vehicleType";

export const turnVehicleTypeIntoFormValues = (
  vehicleType: VehicleType,
  flow: VehicleTypeModalFlow
): VehicleTypeFormValues => {
  const payload: VehicleTypeFormValues = {
      ...vehicleType,
      flow,
      vehicleTypeId: vehicleType.id,
      name: vehicleType.name || null,
      operatorManagerId: vehicleType.operatorManagerId || null,
  };

  return payload;
};
