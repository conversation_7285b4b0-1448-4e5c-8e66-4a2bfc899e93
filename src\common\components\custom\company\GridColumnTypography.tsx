import { ReactNode } from "react";
import { Box, SxProps, Typography } from "@mui/material";
interface ColumnProps {
  value: string;
  children?: ReactNode;
  typographyStyles?: SxProps;
}

export const GridColumnTypography = ({
  value,
  children,
  typographyStyles
}: ColumnProps) => {
  return (
    <Box alignItems="center" display="flex" width="100%">
      <Box alignItems="center" display="flex" mr={1} width="100%">
        {children}
        <Typography
          ml={1}
          fontSize={13}
          sx={{
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            ...typographyStyles
          }}
          variant="caption"
        >
          {value || "-"}
        </Typography>
      </Box>
    </Box>
  );
};
