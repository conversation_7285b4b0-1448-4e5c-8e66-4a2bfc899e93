import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON>ack,
  Typography
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { buttonTextTransform } from "src/common/components/custom/customCss";
import { CeCard } from "src/common/components";

export const EmailSent: React.FC = () => {
  const { t } = useTranslation("common");
  const navigate = useNavigate();

  return (
    <Grid
      container
      direction="column"
      justifyContent="center"
      alignItems="center"
    >
      <CeCard sx={{ minWidth: 275, padding: 2 }}>
        <CardContent>
          <Stack sx={{ width: "30ch" }}>
            <Typography
              variant="h5"
              textAlign="center"
              sx={{ marginTop: 0, marginBottom: 0 }}
            >
              {t("email-sent")}
            </Typography>
            <Typography
              textAlign="center"
              color="text.secondary"
              sx={{ marginBottom: 2, fontSize: "14px", marginTop: "5px" }}
            >
              {t("check-inbox")}
            </Typography>
          </Stack>
        </CardContent>

        <CardActions sx={{ justifyContent: "space-between" }}>
          <Button
            sx={buttonTextTransform}
            size="small"
            onClick={() => navigate("/auth/signin")}
          >
            {t("sign-in")}
          </Button>
        </CardActions>
      </CeCard>
    </Grid>
  );
};
