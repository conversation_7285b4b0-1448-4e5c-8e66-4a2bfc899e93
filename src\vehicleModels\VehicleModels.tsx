import { useWindowSize } from "@react-hook/window-size";
import { useState } from "react";
import { getCurrentUser, useVehicleModels } from "src/common/api";
import { VehicleModelDatagrid } from "./datagrid/VehicleModelDatagrid";
import { CePaper } from "src/common/components";
import { Expression } from "src/common/types";
import { GridFilterModel, GridSortModel } from "@mui/x-data-grid";
import { turnDatagridFilterIntoExpressions } from "src/common/utils";
import usePersistentGridState from "src/common/utils/gridState";
import { useLocation } from "react-router-dom";

export const VehicleModels = () => {
  const location = useLocation();
  const localStorageKey = location.pathname.slice(1);

  const currentUser = getCurrentUser();

  const [, height] = useWindowSize();

  const [gridState, updateGridStatePart] = usePersistentGridState(
    localStorageKey,
    1,
    20
  );
  const expression = turnDatagridFilterIntoExpressions(gridState.filterModel);
  const [expressions, setExpressions] = useState<Expression[]>(expression);
  const [sortingModelOptions, setSortingModelOptions] = useState<GridSortModel>(
    gridState.sortModel
  );
  const {
    data: allVehicleModels,
    isLoading: isLoadingVehicleModels,
    refetch: refetchVehicleModels,
  } = useVehicleModels(
    {
      operatorManagerId: currentUser?.id,
      limit: gridState.pageSize,
      expressions,
      sortModel: sortingModelOptions,
      offset: (gridState.page - 1) * gridState.pageSize,
    },
    Boolean(currentUser?.id)
  );

  const handlePageChange = (newPage: number) => {
    updateGridStatePart("page", newPage + 1);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    updateGridStatePart("pageSize", newPageSize);
  };

  const handleSortModelChange = (sortModel: GridSortModel) => {
    setSortingModelOptions(sortModel);
  };

  const onDatagridFiltersChange = (filterModel: GridFilterModel) => {
    const expressions = turnDatagridFilterIntoExpressions(filterModel);
    setExpressions(expressions);
  };

  return (
    <CePaper sx={{ height: `${height - 100}px`, p: 2 }}>
      <VehicleModelDatagrid
        data={allVehicleModels?.data || []}
        isFetchingVehicleModels={isLoadingVehicleModels}
        refetchVehicleModels={refetchVehicleModels}
        shouldRenderRefreshButton
        shouldRenderAddButton
        gridState={gridState}
        updateGridStatePart={updateGridStatePart}
        total={allVehicleModels?.totalCount || 0}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        isServerDriven
        handleSortModelChange={handleSortModelChange}
        onDatagridFiltersChange={onDatagridFiltersChange}
      />
    </CePaper>
  );
};
