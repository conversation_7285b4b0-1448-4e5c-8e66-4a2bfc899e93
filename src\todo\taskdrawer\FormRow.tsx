import { Box, Stack, Typography } from "@mui/material";
import { ReactNode } from "react";

interface FormRowProps {
  label: string;
  children?: ReactNode;
  childrenFlex?: string;
}
export const FormRow = ({ label, children, childrenFlex }: FormRowProps) => {
  return (
    <Stack
      direction="row"
      gap={2}
      alignItems="flex-start"
      justifyContent="flex-start"
    >
      <Typography
        variant="body1"
        fontWeight={400}
        fontSize={16}
        letterSpacing={0.15}
        color="text.primary"
        flex="0 0 35%"
        minHeight={42}
        flexShrink={0}
      >
        {label}
      </Typography>
      <Box flex={childrenFlex || "0 0 45%"} position="relative" flexShrink={0}>
        {children}
      </Box>
      <Box flexGrow={1} flexShrink={1} />
    </Stack>
  );
};
