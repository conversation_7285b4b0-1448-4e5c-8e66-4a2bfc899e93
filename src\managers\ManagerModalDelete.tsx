import { Al<PERSON>, Box, Divider, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { DeleteModal } from "src/common/components";
import { DeleteManagerDto, ManagerModalDeleteFlow } from "src/common/types";

interface ManagerModalDeleteProps {
  flow: ManagerModalDeleteFlow;
  managerTitle?: string;
  isLoading: boolean;
  managerId?: number;
  handleCloseUserModalDelete: () => void;
  handleDeleteUser: (args: DeleteManagerDto) => void;
}
export const ManagerModalDelete: React.FC<ManagerModalDeleteProps> = ({
  flow,
  managerTitle,
  managerId,
  isLoading,
  handleCloseUserModalDelete,
  handleDeleteUser,
}) => {
  const { t } = useTranslation(["common", "dispatcher"]);

  const onDeleteUser = () => {
    if (managerId) {
      handleDeleteUser({ managerId });
    }
  };

  const DeleteModalHelperText = () => (
    <Box>
      <Typography sx={{ marginBottom: 1 }}>
        {t("delete-manager-message", {
          managerTitle: managerTitle || "unknown manager",
        })}
      </Typography>
    </Box>
  );

  return (
    <DeleteModal
      isLoading={isLoading}
      title="Delete manager"
      helperText={DeleteModalHelperText()}
      isOpen={flow === "Delete"}
      handleSubmit={() => onDeleteUser()}
      handleClose={handleCloseUserModalDelete}
    />
  );
};
