import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  CreatePartnerDto,
  DeletePartnerDto,
  GetPartnersDto,
  Partner,
  PartnerWithCount,
} from "../types/partners";
import { wrapEachFieldIntoDoubleQuotes } from "../utils";
import { processApiError, processApiSuccess } from "../utils/errors";
import axios, { AxiosError } from "axios";
const backendUrl = process.env.REACT_APP_API_URL;

export const getPartners = async (attr: GetPartnersDto) => {
  return axios
    .post(`${backendUrl}/partners/get`, attr, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const usePartners = (
  queryParams: GetPartnersDto,
  enabled: boolean = true
) => {
  const formattedSortModel = wrapEachFieldIntoDoubleQuotes(
    queryParams.sortModel
  );
  const formattedAttrs = { ...queryParams, sortModel: formattedSortModel };
  return useQuery<PartnerWithCount, AxiosError | Error>(
    ["partners", formattedAttrs],
    () => getPartners(formattedAttrs),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch partners", err),
      enabled,
    }
  );
};

export const createNewPartners = ({ operatorCompanyIds }: CreatePartnerDto) => {
  return axios
    .post(
      `${backendUrl}/partners/create-many`,
      { operatorCompanyIds },
      {
        withCredentials: true,
      }
    )
    .then((response) => response.data);
};

export const useCreateNewPartners = () => {
  const queryClient = useQueryClient();
  return useMutation<Partner, AxiosError | Error, CreatePartnerDto, () => void>(
    ({ operatorCompanyIds }: CreatePartnerDto) =>
      createNewPartners({ operatorCompanyIds }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("partner");
        queryClient.invalidateQueries("partners");
      },
      onError: (err) => processApiError("Unable to create partner", err),
    }
  );
};

export const deletePartner = ({ partnerId }: DeletePartnerDto) => {
  if (!partnerId) {
    throw new Error("the partner ID was not provided");
  }
  return axios
    .delete(`${backendUrl}/partners/${partnerId}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useDeletePartner = () => {
  const queryClient = useQueryClient();
  return useMutation<Partner, AxiosError | Error, DeletePartnerDto, () => void>(
    ({ partnerId }: DeletePartnerDto) => deletePartner({ partnerId }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("partner");
        queryClient.invalidateQueries("partners");
      },
      onError: (err) => processApiError("Unable to delete partner", err),
    }
  );
};
