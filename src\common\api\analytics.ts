import axios, { AxiosError } from "axios";
import { useQuery } from "react-query";
import { processApiError } from "../utils/errors";
import { 
    AverageJobCompletionTime,
    AverageM3PerHour,
    AverageM3PerHourCompany,
    AverageM3PerJob, 
    AverageReservationsPerDispatcher, 
    FrequentlyUsedVehicles, 
    GetAnalyticstDto, 
    HighestExpensesPerPumpingCompany, 
    MostActiveContractorsResponse, 
    TotalExpensesPerMonth, 
    TotalReservationsPerMonth 
} from "../types/analytics";

const backendUrl = process.env.REACT_APP_API_URL;

export const getAverageM3PerJob = async (attr: GetAnalyticstDto) => {
    return axios
        .get(`${backendUrl}/analytics/average-m3-per-job`, {
            withCredentials: true,
            params: attr
        })
        .then((response) => response.data);
};

export const useAverageM3PerJob = (attrs: GetAnalyticstDto, enabled: boolean = true) => {
    return useQuery<AverageM3PerJob, AxiosError | Error>(
        ["AverageM3PerJob", attrs],
        () => getAverageM3PerJob(attrs),
        {
            keepPreviousData: true,
            onError: (err) => processApiError("Unable to fetch Average M3 Per Job", err),
            enabled
        }
    );
};

export const getAverageM3PerHourCompany = async (attr: GetAnalyticstDto) => {
    return axios
        .get(`${backendUrl}/analytics/average-m3-per-hour-company`, {
            withCredentials: true,
            params: attr
        })
        .then((response) => response.data);
};

export const useAverageM3PerHourCompany = (attrs: GetAnalyticstDto, enabled: boolean = true) => {
    return useQuery<AverageM3PerHourCompany, AxiosError | Error>(
        ["AverageM3PerHourCompany", attrs],
        () => getAverageM3PerHourCompany(attrs),
        {
            keepPreviousData: true,
            onError: (err) => processApiError("Unable to fetch Average M3 Per Hour Company", err),
            enabled
        }
    );
};

export const getAverageM3PerHour = async (attr: GetAnalyticstDto) => {
    return axios
        .get(`${backendUrl}/analytics/average-m3-per-hour`, {
            withCredentials: true,
            params: attr
        })
        .then((response) => response.data);
};

export const useAverageM3PerHour = (attrs: GetAnalyticstDto, enabled: boolean = true) => {
    return useQuery<AverageM3PerHour, AxiosError | Error>(
        ["AverageM3PerHour", attrs],
        () => getAverageM3PerHour(attrs),
        {
            keepPreviousData: true,
            onError: (err) => processApiError("Unable to fetch Average M3 Per Hour", err),
            enabled
        }
    );
};

export const getAverageJobCompletionTime = async (attr: GetAnalyticstDto) => {
    return axios
        .get(`${backendUrl}/analytics/average-job-completion-time`, {
            withCredentials: true,
            params: attr
        })
        .then((response) => response.data);
};

export const useAverageJobCompletionTime = (attrs: GetAnalyticstDto, enabled: boolean = true) => {
    return useQuery<AverageJobCompletionTime, AxiosError | Error>(
        ["AverageJobCompletionTime", attrs],
        () => getAverageJobCompletionTime(attrs),
        {
            keepPreviousData: true,
            onError: (err) => processApiError("Unable to fetch Average Job Completion Time", err),
            enabled
        }
    );
};

export const getAverageReservationsPerDispatcher = async (attr: GetAnalyticstDto) => {
    return axios
        .get(`${backendUrl}/analytics/average-reservations-per-dispatcher`, {
            withCredentials: true,
            params: attr
        })
        .then((response) => response.data);
};

export const useAverageReservationsPerDispatcher = (attrs: GetAnalyticstDto, enabled: boolean = true) => {
    return useQuery<AverageReservationsPerDispatcher, AxiosError | Error>(
        ["AverageReservationsPerDispatcher", attrs],
        () => getAverageReservationsPerDispatcher(attrs),
        {
            keepPreviousData: true,
            onError: (err) => processApiError("Unable to fetch Average Reservations Per Dispatcher", err),
            enabled
        }
    );
};

export const getTotalExpensesPerMonth = async (attr: GetAnalyticstDto) => {
    return axios
        .get(`${backendUrl}/analytics/total-expenses-per-month`, {
            withCredentials: true,
            params: attr
        })
        .then((response) => response.data);
};

export const useTotalExpensesPerMonth = (attrs: GetAnalyticstDto, enabled: boolean = true) => {
    return useQuery<TotalExpensesPerMonth, AxiosError | Error>(
        ["TotalExpensesPerMonth", attrs],
        () => getTotalExpensesPerMonth(attrs),
        {
            keepPreviousData: true,
            onError: (err) => processApiError("Unable to fetch Total Expenses Per Month", err),
            enabled
        }
    );
};

export const getFrequentlyUsedVehicles = async (attr: GetAnalyticstDto) => {
    return axios
        .get(`${backendUrl}/analytics/frequently-used-vehicles`, {
            withCredentials: true,
            params: attr
        })
        .then((response) => response.data);
};

export const useFrequentlyUsedVehicles = (attrs: GetAnalyticstDto, enabled: boolean = true) => {
    return useQuery<FrequentlyUsedVehicles, AxiosError | Error>(
        ["FrequentlyUsedVehicles", attrs],
        () => getFrequentlyUsedVehicles(attrs),
        {
            keepPreviousData: true,
            onError: (err) => processApiError("Unable to fetch Frequently Used Vehicles", err),
            enabled
        }
    );
};

export const getMostActiveContractors = async (attr: GetAnalyticstDto) => {
    return axios
        .get(`${backendUrl}/analytics/most-active-contractors`, {
            withCredentials: true,
            params: attr
        })
        .then((response) => response.data);
};

export const useMostActiveContractors = (attrs: GetAnalyticstDto, enabled: boolean = true) => {
    return useQuery<MostActiveContractorsResponse, AxiosError | Error>(
        ["MostActiveContractors", attrs],
        () => getMostActiveContractors(attrs),
        {
            keepPreviousData: true,
            onError: (err) => processApiError("Unable to fetch Most Active Contractors", err),
            enabled
        }
    );
};

export const getHighestExpensesPerPumpingCompany = async (attr: GetAnalyticstDto) => {
    return axios
        .get(`${backendUrl}/analytics/highest-expenses-per-pumping-company`, {
            withCredentials: true,
            params: attr
        })
        .then((response) => response.data);
};

export const useHighestExpensesPerPumpingCompany = (attrs: GetAnalyticstDto, enabled: boolean = true) => {
    return useQuery<HighestExpensesPerPumpingCompany, AxiosError | Error>(
        ["HighestExpensesPerPumpingCompany", attrs],
        () => getHighestExpensesPerPumpingCompany(attrs),
        {
            keepPreviousData: true,
            onError: (err) => processApiError("Unable to fetch Highest Expenses Per Pumping Company", err),
            enabled
        }
    );
};

export const getTotalReservationsPerMonth = async (attr: GetAnalyticstDto) => {
    return axios
        .get(`${backendUrl}/analytics/total-reservations-per-month`, {
            withCredentials: true,
            params: attr
        })
        .then((response) => response.data);
};

export const useTotalReservationsPerMonth = (attrs: GetAnalyticstDto, enabled: boolean = true) => {

    return useQuery<TotalReservationsPerMonth, AxiosError | Error>(
        ["TotalReservationsPerMonth", attrs],
        () => getTotalReservationsPerMonth(attrs),
        {
            keepPreviousData: true,
            onError: (err) => processApiError("Unable to fetch Total Reservations Per Month", err),
            enabled
        }
    );
};
