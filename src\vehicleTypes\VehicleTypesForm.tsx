import { Autocomplete, DialogActions, Stack, Typography } from "@mui/material";
import { SetterOrUpdater } from "recoil";
import { useTranslation } from "react-i18next";
import {
  CreateVehicleTypeDto,
  UpdateVehicleTypeDto,
  VehicleTypeFormValues,
  vehicleTypes,
  VehicleTypes,
} from "src/common/types";
import { useFormik } from "formik";
import * as yup from "yup";
import { getCurrentUser } from "src/common/api";
import { CeButton, CeTextField } from "src/common/components";

interface VehicleTypesFormProps {
  isLoading: boolean;
  initialFormValues: VehicleTypeFormValues;
  setInitialFormValues: SetterOrUpdater<VehicleTypeFormValues>;
  handleClose: () => void;
  handleCreateNewVehicleType?: (args: CreateVehicleTypeDto) => void;
  handleUpdateVehicleType?: (args: UpdateVehicleTypeDto) => void;
}

export const VehicleTypesForm: React.FC<VehicleTypesFormProps> = ({
  handleClose,
  isLoading,
  handleCreateNewVehicleType,
  handleUpdateVehicleType,
  setInitialFormValues,
  initialFormValues,
}) => {
  const { t } = useTranslation(["manager", "dispatcher", "common"]);
  const currentUser = getCurrentUser();

  const formik = useFormik<VehicleTypeFormValues>({
    initialValues: initialFormValues,
    enableReinitialize: true,
    validationSchema: yup.object({
      name: yup.string().required("Type is required").nullable(),
    }),
    onSubmit: (values) => {
      if (initialFormValues.flow === "Create" && handleCreateNewVehicleType) {
        const payload: CreateVehicleTypeDto = {
          ...initialFormValues,
          ...values,
          operatorManagerId: currentUser?.id!,
        };

        handleCreateNewVehicleType(payload);
      }
      if (initialFormValues.flow === "Update" && handleUpdateVehicleType) {
        const payload: UpdateVehicleTypeDto = {
          ...initialFormValues,
          ...values,
          vehicleTypeId: initialFormValues.vehicleTypeId!,
          operatorManagerId: currentUser?.id!,
        };

        handleUpdateVehicleType(payload);
      }
    },
  });
  return (
    <Stack
      component="form"
      spacing={2}
      noValidate
      sx={{ width: "400px" }}
      onSubmit={formik.handleSubmit}
    >
      <Autocomplete
        id="name"
        fullWidth
        value={formik.values.name || null}
        onChange={(event: any, nextValues: VehicleTypes | null) => {
          formik.setFieldValue("name", nextValues);
        }}
        onBlur={() => formik.setFieldTouched("name", true)}
        options={vehicleTypes}
        filterSelectedOptions
        renderInput={(params) => (
          <CeTextField
            {...params}
            InputLabelProps={{ shrink: true }}
            error={formik.touched.name && Boolean(formik.errors.name)}
            helperText={formik.touched.name && formik.errors.name}
            required
            label={t("common:type")}
            size="small"
          />
        )}
      />
      <DialogActions
        sx={{
          display: "flex",
          flexDirection: "column",
          padding: "0",
          gap: 1.5,
        }}
      >
        <CeButton
          type="submit"
          disabled={isLoading}
          variant="contained"
          color="primary"
          fullWidth
        >
          <Typography
            sx={{
              fontSize: "14px",
              letterSpacing: "0.4px",
            }}
          >
            {t(
              `common:${
                formik.values.flow === "Create" ? "create" : "update"
              }-vehicle-type`
            )}
          </Typography>
        </CeButton>
        <CeButton
          variant="text"
          onClick={handleClose}
          disabled={isLoading}
          fullWidth
        >
          {t("common:cancel")}
        </CeButton>
      </DialogActions>
    </Stack>
  );
};
