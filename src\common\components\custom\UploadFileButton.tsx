import styled from "@emotion/styled";
import { Box, Button, Typography } from "@mui/material";
import React, { ReactNode } from "react";
import { useTranslation } from "react-i18next";
import { buttonTextTransform } from "./customCss";

const VisuallyHiddenInput = styled("input")({
  clip: "rect(0 0 0 0)",
  clipPath: "inset(50%)",
  height: 1,
  overflow: "hidden",
  position: "absolute",
  bottom: 0,
  left: 0,
  whiteSpace: "nowrap",
  width: 1,
});

interface UploadButtonProps {
  onChange: (event: React.SyntheticEvent) => void;
  icon: ReactNode;
  documentName: string;
}

const UploadFileButton = ({
  onChange,
  icon,
  documentName,
}: UploadButtonProps) => {
  const { t, i18n } = useTranslation(["common", "dispatcher"]);

  return (
    <Button
      component="label"
      variant="outlined"
      color="primary"
      size="medium"
      sx={{
        color: "primary",
        borderRadius: "8px",
        display: "flex",
        justifyContent: "space-between",
        flexDirection: "column",
        width: "156px",
        height: "130px",
        padding: "15px",
        textAlign: "center",
        ...buttonTextTransform,
      }}
    >
      <Typography color="text.primary" fontSize="14px">
        {documentName}
      </Typography>
      <Box display="flex" gap={0.8}>
        {icon}
        <Typography fontSize="13px" fontWeight="bold">
          {t("common:upload")}
        </Typography>
      </Box>
      <VisuallyHiddenInput
        type="file"
        accept=".jpg,.jpeg"
        onChange={onChange}
      />
    </Button>
  );
};

export default UploadFileButton;
