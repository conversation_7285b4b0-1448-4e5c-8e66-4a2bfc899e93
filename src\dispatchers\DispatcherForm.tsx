import { <PERSON><PERSON>ple<PERSON>, But<PERSON>, DialogActions, Stack } from "@mui/material";
import { useFormik } from "formik";
import * as yup from "yup";
import {
  CreateDispatcherDto,
  DispatcherFormValues,
  UpdateDispatcherDto,
  UserEnumDisplay,
} from "src/common/types";
import { userRolesDisplay, userStatusDisplay } from "src/common/constants";
import { useTranslation } from "react-i18next";
import {
  turnDispatcherFormValuesIntoCreateDto,
  turnDispatcherFormValuesIntoUpdateDto,
} from "src/common/utils";
import {
  CeMuiPhoneNumber,
  CeButton,
  CeTextField,
} from "src/common/components/";

interface DispatcherFormProps {
  isLoading: boolean;
  handleClose: () => void;
  handleCreateNewDispatcher?: (args: CreateDispatcherDto) => void;
  handleUpdateDispatcher?: (args: UpdateDispatcherDto) => void;
  initialFormValues: DispatcherFormValues;
}

export const DispatcherForm: React.FC<DispatcherFormProps> = ({
  handleClose,
  isLoading,
  handleCreateNewDispatcher,
  handleUpdateDispatcher,
  initialFormValues,
}) => {
  const { t } = useTranslation(["common", "dispatcher"]);

  const formik = useFormik<DispatcherFormValues>({
    initialValues: initialFormValues,
    validationSchema: yup.object({
      firstName: yup.string().required("First name is required"),
      lastName: yup.string().required("Last name is required"),
      email: yup
        .string()
        .email("Enter a valid email")
        .required("Email is required"),
      status: yup.object().required("Status is required").nullable(),
      role: yup.object().required("Role is required").nullable(),
    }),
    onSubmit: (values) => {
      if (initialFormValues.flow === "Create" && handleCreateNewDispatcher) {
        const payload: CreateDispatcherDto =
          turnDispatcherFormValuesIntoCreateDto(values);

        handleCreateNewDispatcher(payload);
      }
      if (initialFormValues.flow === "Update" && handleUpdateDispatcher) {
        const payload: UpdateDispatcherDto =
          turnDispatcherFormValuesIntoUpdateDto(values);

        handleUpdateDispatcher(payload);
      }
    },
  });
  return (
    <Stack
      component="form"
      spacing={2}
      noValidate
      onSubmit={formik.handleSubmit}
      sx={{ width: "500px" }}
    >
      <Stack direction={"row"} spacing={2}>
        <CeTextField
          fullWidth
          id="name"
          name="firstName"
          label={t("first-name")}
          size="small"
          value={formik.values.firstName}
          onChange={formik.handleChange}
          InputLabelProps={{ shrink: true }}
          error={formik.touched.firstName && Boolean(formik.errors.firstName)}
          helperText={formik.touched.firstName && formik.errors.firstName}
          disabled={isLoading}
          required
        />
        <CeTextField
          fullWidth
          id="surname"
          name="lastName"
          label={t("last-name")}
          size="small"
          value={formik.values.lastName}
          onChange={formik.handleChange}
          InputLabelProps={{ shrink: true }}
          error={formik.touched.lastName && Boolean(formik.errors.lastName)}
          helperText={formik.touched.lastName && formik.errors.lastName}
          disabled={isLoading}
          required
        />
      </Stack>

      {initialFormValues.flow === "Create" ? (
        <Stack direction={"row"} spacing={2}>
          <CeTextField
            fullWidth
            id="email"
            name="email"
            label={t("email")}
            type="email"
            size="small"
            value={formik.values.email}
            onChange={formik.handleChange}
            InputLabelProps={{ shrink: true }}
            error={formik.touched.email && Boolean(formik.errors.email)}
            helperText={formik.touched.email && formik.errors.email}
            disabled={isLoading}
            required
          />
        </Stack>
      ) : (
        <>
          <CeTextField
            fullWidth
            id="email"
            name="email"
            label={t("email")}
            type="email"
            size="small"
            value={formik.values.email}
            onChange={formik.handleChange}
            InputLabelProps={{ shrink: true }}
            error={formik.touched.email && Boolean(formik.errors.email)}
            helperText={formik.touched.email && formik.errors.email}
            disabled={isLoading}
            required
          />
        </>
      )}

      <CeMuiPhoneNumber
        fullWidth
        defaultCountry={"be"}
        id="phoneNumber"
        name="phoneNumber"
        label={t("phone-number")}
        size="small"
        InputLabelProps={{ shrink: true }}
        value={formik.values.phoneNumber}
        onChange={(value) => formik.setFieldValue("phoneNumber", value)}
        onBlur={() => formik.setFieldTouched("phoneNumber", true)}
        error={formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)}
        helperText={formik.touched.phoneNumber && formik.errors.phoneNumber}
        disabled={isLoading}
        variant="outlined"
      />

      <Stack direction={"row"} spacing={2}>
        <Autocomplete
          id="role"
          fullWidth
          value={formik.values.role || null}
          onChange={(event: any, nextValues: UserEnumDisplay | null) => {
            formik.setFieldValue("role", nextValues);
          }}
          onBlur={() => formik.setFieldTouched("role", true)}
          options={userRolesDisplay.filter(
            (role) => role.visible && role.title === "Dispatcher"
          )}
          isOptionEqualToValue={(
            option: UserEnumDisplay,
            value: UserEnumDisplay
          ) => option.id === value.id}
          getOptionLabel={(role) => role.title}
          filterSelectedOptions
          renderInput={(params) => (
            <CeTextField
              {...params}
              InputLabelProps={{ shrink: true }}
              error={formik.touched.role && Boolean(formik.errors.role)}
              helperText={formik.touched.role && formik.errors.role}
              required
              label={t("role")}
              size="small"
            />
          )}
        />

        <Autocomplete
          id="status"
          fullWidth
          value={formik.values.status || null}
          onChange={(event: any, nextValues: UserEnumDisplay | null) => {
            formik.setFieldValue("status", nextValues);
          }}
          onBlur={() => formik.setFieldTouched("status", true)}
          options={userStatusDisplay.filter((st) => st.visible)}
          isOptionEqualToValue={(
            option: UserEnumDisplay,
            value: UserEnumDisplay
          ) => option.id === value.id}
          getOptionLabel={(status) => status.title}
          filterSelectedOptions
          renderInput={(params) => (
            <CeTextField
              {...params}
              InputLabelProps={{ shrink: true }}
              error={formik.touched.status && Boolean(formik.errors.status)}
              helperText={formik.touched.status && formik.errors.status}
              required
              label={t("common:status")}
              size="small"
            />
          )}
        />
      </Stack>

      <DialogActions>
        <CeButton variant="text" onClick={handleClose} disabled={isLoading}>
          {t("cancel")}
        </CeButton>
        <CeButton type="submit" disabled={isLoading}>
          {t("submit")}
        </CeButton>
      </DialogActions>
    </Stack>
  );
};
