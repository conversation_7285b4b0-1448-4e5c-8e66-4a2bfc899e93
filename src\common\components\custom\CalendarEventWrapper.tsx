import { Box, styled, SxProps, Theme, Typography } from "@mui/material";
import { format, formatDistanceToNow, isBefore, subHours } from "date-fns";
import { ReactNode } from "react";

interface EventWrapperProps {
  borderColor: string;
  backgroundColor: string;
  color: string;
  eventsnumber?: number;
  wrapperheight?: {
    height: string;
    mt: string;
    mb: string;
  };
  children: ReactNode;
  sx?: SxProps<Theme>;
  className?: string;
}
interface RowEventTypographyProps
  extends Pick<EventWrapperProps, "eventsnumber"> {
  sx?: SxProps<Theme>;
  children: ReactNode;
  className?: string;
}

const CustomEventWrapper = styled(Box)<EventWrapperProps>(
  ({ borderColor, backgroundColor, color, eventsnumber, wrapperheight }) => ({
    display: "flex",
    justifyContent: "flex-start",
    flexDirection: `${eventsnumber && eventsnumber === 1 ? "column" : "row"}`,
    alignItems: `${eventsnumber && eventsnumber > 1 ? "center" : "flex-start"}`,
    flexWrap: `${eventsnumber && eventsnumber >= 3 ? "nowrap" : "wrap"}`,
    gap: `${!eventsnumber ? "10px" : eventsnumber > 1 ? "2px" : "5px"}`,

    color,
    boxShadow: "0px 1px 3px 0px rgba(0, 0, 0, 0.12)",
    border: `solid ${borderColor} 1px`,
    backgroundColor: backgroundColor,
    padding: `${eventsnumber && eventsnumber > 3 ? "1px" : "4px"}`,
    flex: "1 0 0",
    borderRadius: "8px",
    marginLeft: `${!eventsnumber ? "10px" : "0px"} `,
    marginTop: wrapperheight?.mt,
    marginBottom: wrapperheight?.mb,
    height: `${wrapperheight?.height} !important`,
    flexGrow: 1
  })
);

export const CalendarEventWrapper = ({
  eventsnumber,
  borderColor,
  color,
  backgroundColor,
  children,
  sx,
  className
}: EventWrapperProps) => {
  const style = (eventsnumber: number | undefined) => {
    if (!eventsnumber) {
      return { height: "auto", mt: "0px", mb: "0px" };
    }

    if (eventsnumber === 1) {
      return { height: "60px", mt: "5px", mb: "0px" };
    }
    if (eventsnumber === 2) {
      return { height: "31px", mt: "2px", mb: "0px" };
    }
    if (eventsnumber === 3) {
      return { height: "19px", mt: "1px", mb: "1px" };
    }
    if (eventsnumber >= 4) {
      return { height: "12px", mt: "3px", mb: "0px" };
    }
  };
  const wrapperheight = style(eventsnumber);
  return (
    <CustomEventWrapper
      borderColor={borderColor}
      color={color}
      backgroundColor={backgroundColor}
      wrapperheight={wrapperheight}
      eventsnumber={eventsnumber}
      className={className}
      sx={sx}
    >
      {children}
    </CustomEventWrapper>
  );
};

const RowEventTypography = styled(Typography)<RowEventTypographyProps>(
  ({ eventsnumber }) => ({
    fontSize: `${!eventsnumber ? "14px" : "10px"} `,
    letterSpacing: `${!eventsnumber ? "0.17px" : "0.4px"}`,
    fontWeight: "400",
    textTransform: "capitalize",
    lineHeight: `${!eventsnumber ? "20px" : "10px"}`
  })
);

export const CalendarEventTypography = ({
  eventsnumber,
  children,
  sx,
  className
}: RowEventTypographyProps) => {
  return (
    <RowEventTypography
      className={className}
      sx={sx}
      eventsnumber={eventsnumber}
    >
      {children}
    </RowEventTypography>
  );
};

export const LastLineWrapper = styled(Box)({
  display: "flex",
  width: "100%",
  flexBasis: "100%",
  alignItems: "center",
  gap: "2px"
});

export const formatDateToDayMonthYear = (date: string) => {
  const activityDate = new Date(date);
  const now = new Date();
  const twentyFourHoursAgo = subHours(now, 24);
  if (isBefore(twentyFourHoursAgo, activityDate)) {
    return formatDistanceToNow(activityDate, { addSuffix: true });
  } else {
    return format(activityDate, "dd MMMM, yyyy");
  }
};
