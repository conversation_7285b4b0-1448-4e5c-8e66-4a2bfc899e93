import { Box, Chip, Grid, Typography } from '@mui/material';
import React, { useMemo } from 'react'
import { useTranslation } from 'react-i18next';
import { calculateElapsedTime, Job } from 'src/common/types';
import { formatDateWithoutTimezoneForDisplaying } from 'src/common/utils/formatDate';

interface JobDetailsProps {
    jobDetails: Job | undefined;
    orderNumber: string
}

export const JobDetails: React.FC<JobDetailsProps> = ({
    jobDetails,
    orderNumber
}) => {
      const { t } = useTranslation("common");
       const elapsedTime = useMemo(() => {
          return calculateElapsedTime(jobDetails?.jobEvents!, t);
        }, [jobDetails?.jobEvents, t]);
  return (
    <Box sx={{ padding: 3, paddingTop: 1 }}>
        <Typography variant="h6" sx={{ fontWeight: "bold", marginBottom: 2 }}>
          {t("job")}
        </Typography>
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("start-date")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                {jobDetails?.start ? (
                  <Chip
                    label={formatDateWithoutTimezoneForDisplaying(
                        jobDetails.start
                    )}
                    color="primary"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
                ) : (
                  "-"
                )}
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("end-date")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                {jobDetails?.end ? (
                  <Chip
                    label={formatDateWithoutTimezoneForDisplaying(
                        jobDetails.end
                    )}
                    color="primary"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
                ) : (
                  "-"
                )}
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("pumped-volume")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Chip
                  label={
                    jobDetails?.amountOfConcrete
                      ? jobDetails.amountOfConcrete + " m³"
                      : "-"
                  }
                  color="primary"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
                {jobDetails?.report?.amountOfConcrete ? (
                  <Chip
                    label={jobDetails?.report.amountOfConcrete + " m³"}
                    color="success"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
                ) : null}
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("flexible-pipe-length-80mm")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Chip
                  label={
                    jobDetails?.flexiblePipeLength80Mm
                      ? jobDetails.flexiblePipeLength80Mm + " m"
                      : "-"
                  }
                  color="primary"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
                {jobDetails?.report?.flexiblePipeLength80Mm ? (
                  <Chip
                    label={jobDetails.report.flexiblePipeLength80Mm + " m"}
                    color="success"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
                ) : null}
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("flexible-pipe-length-90mm")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Chip
                  label={
                    jobDetails?.flexiblePipeLength90Mm
                      ? jobDetails.flexiblePipeLength90Mm + " m"
                      : "-"
                  }
                  color="primary"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
                {jobDetails?.report?.flexiblePipeLength90Mm ? (
                  <Chip
                    label={jobDetails.report.flexiblePipeLength90Mm + " m"}
                    color="success"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
                ) : null}
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("rigid-pipe-length-100mm")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Chip
                  label={
                    jobDetails?.rigidPipeLength100Mm
                      ? jobDetails.rigidPipeLength100Mm + " m"
                      : "-"
                  }
                  color="primary"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
                {jobDetails?.report?.rigidPipeLength100Mm ? (
                  <Chip
                    label={jobDetails.report.rigidPipeLength100Mm + " m"}
                    color="success"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
                ) : null}
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("rigid-pipe-length-120mm")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Chip
                  label={
                    jobDetails?.rigidPipeLength120Mm
                      ? jobDetails.rigidPipeLength120Mm + " m"
                      : "-"
                  }
                  color="primary"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
                {jobDetails?.report?.rigidPipeLength120Mm ? (
                  <Chip
                    label={jobDetails.report.rigidPipeLength120Mm + " m"}
                    color="success"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
                ) : null}
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("common:order-number")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Typography variant="body2">{orderNumber}</Typography>
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("cleanup-in-concrete-plant")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              {jobDetails?.cleaning ? (
                <Chip
                  label={t("common:yes")}
                  color="success"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
              ) : (
                <Chip
                  label={t("common:no")}
                  color="error"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
              )}
            </Grid>
          </Grid>
        <Grid container>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("delay")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            {jobDetails?.reasonForDelay ? (
              <>
                <Chip
                  label={elapsedTime}
                  color="error"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
                <Typography variant="body2">{jobDetails.reasonForDelay}</Typography>
              </>
            ) : (
              <Chip
                label={t("common:no")}
                color="success"
                size="small"
                sx={{ fontSize: "12px", fontWeight: "bold" }}
              />
            )}
          </Grid>
        </Grid>
        </Box>
      </Box>
  )
}

