import { atom } from "recoil";
import { DeleteUnavailablePeriodValues, UnavailablePeriodFormValues } from "../types";
import { UNAVAILABLE_PERIOD_DELETE_DEFAULT, UNAVAILABLE_PERIOD_FORM_VALUES } from "../constants";

export const unavailablePeriodFormValuesState = atom<UnavailablePeriodFormValues>({
  key: "unavailablePeriodFormValuesState",
  default: UNAVAILABLE_PERIOD_FORM_VALUES,
});

export const unavailablePeriodDeleteValuesState = atom<DeleteUnavailablePeriodValues>({
  key: "unavailablePeriodDeleteValuesState",
  default: UNAVAILABLE_PERIOD_DELETE_DEFAULT,
});
