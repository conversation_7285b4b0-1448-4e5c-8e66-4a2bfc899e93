import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { Box, TextField, TextFieldProps } from '@mui/material'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { GridFilterInputValueProps } from '@mui/x-data-grid-pro'
import { isValid } from 'date-fns'
import CalendarTodayIcon from '@mui/icons-material/CalendarToday'

export const CustomDatePicker = (props: GridFilterInputValueProps) => {
  const { item, applyValue } = props
  const { value, ...others } = item

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ width: '186px' }}>
        <DatePicker
          label='Value'
          value={value || null}
          onChange={(date) => {
            if (isValid(date)) {
              const dateStr: string = date.toLocaleDateString()
              const yearDigitsCount = dateStr.split('/')[2]
              if (yearDigitsCount.length === 4) {
                applyValue({ value: dateStr, ...others })
              }
            }
          }}
          components={{
            OpenPickerIcon: () => <CalendarTodayIcon />,
          }}
          renderInput={(params: TextFieldProps) => (
            <TextField
              {...params}
              size='small'
              variant='standard'
              fullWidth
              sx={{
                '& .MuiInputBase-input': { paddingTop: 0.5, paddingX: 0.5 },
                '& .MuiSvgIcon-root': { width: '15px', height: '15px', color: 'black', mr: 0.5 },
              }}
              InputLabelProps={{ shrink: true }}
            />
          )}
        />
      </Box>
    </LocalizationProvider>
  )
}
