import { Typography } from '@mui/material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import React, { useCallback } from 'react'
import { useTranslation } from 'react-i18next';
import { CeDataGridToolbar } from 'src/common/components/custom/company/CeDataGridToolbar';
import { GridColumnTypography } from 'src/common/components/custom/company/GridColumnTypography';
import { MostActiveContractor } from 'src/common/types/analytics';

interface ActiveContractorsDatagridProps {
  data: MostActiveContractor[];
  isFetchingActiveContractors: boolean;
  refetchActiveContractors: () => void;
  shouldRenderRefreshButton: boolean;
  page: number;
  pageSize: number;
  total: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
}

const ActiveContractorsDatagrid: React.FC<ActiveContractorsDatagridProps> = ({
  data,
  shouldRenderRefreshButton,
  isFetchingActiveContractors,
  refetchActiveContractors,
  page,
  pageSize,
  total,
  onPageChange,
  onPageSizeChange
}) => {
  const { t } = useTranslation(["dispatcher"]);
  const columns: GridColDef[] = [
    {
      field: "operatorCompanyName",
      headerName: t("company"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => <GridColumnTypography value={params.row.operatorCompanyName} />,
      editable: true
    },
    {
      field: "reservationCount",
      headerName: t("no-of-reservations"),
      type: "string",
      headerAlign: "right",
      align: "right",
      width: 200,
      renderCell: (params) => (
        <Typography sx={{ textAlign: 'right', fontSize:'14px' }}>
          {params.row.reservationCount || 0}
        </Typography>
      ),
      editable: true
    },
    {
      field: "totalSum",
      headerName: t("total-invoiced"),
      type: "string",
      headerAlign: "right",
      align: "right",
      width: 200,
      renderCell: (params) => (
        <Typography sx={{ textAlign: 'right', fontSize:'14px' }}>
          {params.row.totalSum ? `${params.row.totalSum} €` : "0 €"}
        </Typography>
      ),
      editable: true
    },
  ]
  const handleProcessRowUpdateError = useCallback((error: Error) => {
    console.error(error.message);
  }, []);



  return (
    <>
      <DataGrid
        sx={{
          border: "none",
          paddingTop: 0.5
        }}
        pagination
        page={page - 1}
        pageSize={pageSize}
        rowsPerPageOptions={[20, 40, 60, 80, 100]}
        rowCount={total}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        paginationMode="server"
        editMode="row"
        experimentalFeatures={{ newEditingApi: true }}
        onProcessRowUpdateError={handleProcessRowUpdateError}
        components={{
          Toolbar: () => (
            <CeDataGridToolbar
              shouldRenderRefreshButton={shouldRenderRefreshButton}
              onRefreshButtonClick={refetchActiveContractors}
            />
          )
        }}
        columns={columns}
        rows={data}
        getRowId={(row) => row.operatorCompanyId}
        disableSelectionOnClick
        initialState={{
          columns: {
            columnVisibilityModel: {
              id: false,
              operatorCompanyId: false,
            }
          },
          sorting: {
            sortModel: [{ field: "name", sort: "asc" }]
          }
        }}
      />
    </>
  )
}
export default ActiveContractorsDatagrid;