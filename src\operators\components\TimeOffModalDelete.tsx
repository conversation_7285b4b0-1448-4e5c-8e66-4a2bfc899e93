import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { DeleteModal } from "src/common/components";
import { DeleteUnavailablePeriodDto, UnavailablePeriodDeleteFlow } from "src/common/types";

interface TimeOffModalDeleteProps {
  flow: UnavailablePeriodDeleteFlow;
  timeOffTitle?: string;
  isLoading: boolean;
  id?: number;
  handleCloseUnavailablePeriodModalDelete: () => void;
  handleDeleteUnavailablePeriod: (args: DeleteUnavailablePeriodDto) => void;
}
export const TimeOffModalDelete: React.FC<TimeOffModalDeleteProps> = ({
  flow,
  timeOffTitle,
  id,
  isLoading,
  handleCloseUnavailablePeriodModalDelete,
  handleDeleteUnavailablePeriod,
}) => {
  const { t } = useTranslation("common");

  const onDeleteUnavailablePeriod = () => {
    if (id) {
      handleDeleteUnavailablePeriod({ id });
    }
  };

  const DeleteModalHelperText = () => (
    <Box>
      <Typography sx={{ marginBottom: 1 }}>
      {t("delete-unavailable-period-message", {
          timeOffTitle: timeOffTitle || "unknown time off",
        })}
      </Typography>
    </Box>
  );

  return (
    <DeleteModal
      isLoading={isLoading}
      title="Delete Unavailable Period"
      helperText={DeleteModalHelperText()}
      isOpen={flow === "Delete"}
      handleSubmit={() => onDeleteUnavailablePeriod()}
      handleClose={handleCloseUnavailablePeriodModalDelete}
    />
  );
};
