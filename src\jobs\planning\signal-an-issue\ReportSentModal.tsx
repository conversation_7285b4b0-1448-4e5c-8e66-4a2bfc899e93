import { Button, Typography } from "@mui/material";
import Box from "@mui/material/Box";

import { useTranslation } from "react-i18next";
import { FC } from "react";
import { buttonTextTransform } from "src/common/components/custom/customCss";
import { CePaper } from "src/common/components";

type Props = {
  dispatcher?: boolean;
};

const ReportSentModal: FC<Props> = (props) => {
  const { dispatcher } = props;
  const { t } = useTranslation(["operator", "common"]);

  const styles = {
    border: "Primary",
    height: 324,
    padding: 3,
    margin: 2
  };

  return (
    <>
      <CePaper sx={styles} variant="outlined">
        <Typography textAlign="center" variant="h5">
          {t("report-sent-title")}
        </Typography>
        {dispatcher ? (
          <Typography textAlign="center" variant="subtitle1">
            {t("report-sent-description")}
          </Typography>
        ) : (
          <Typography textAlign="center" variant="subtitle1">
            {t("report-sent-manager-description")}
          </Typography>
        )}
        <Typography textAlign="center" variant="subtitle1">
          {t("job-interrupted")}
        </Typography>
      </CePaper>

      <Box
        display="flex"
        justifyContent="center"
        sx={{ marginTop: 1, marginBottom: 1 }}
      >
        <Button
          color="primary"
          type="submit"
          variant="contained"
          sx={buttonTextTransform}
          // onClick={() => router.push('/operator/planning')}
        >
          {t("common:ok")}
        </Button>
      </Box>
    </>
  );
};

export default ReportSentModal;
