import React from "react";
import { styled } from "@mui/material/styles";
import Button, { ButtonProps } from "@mui/material/Button";
import { lightThemeOptions, darkThemeOptions } from "../../../../app/Theme";
import { ElevationLevel, elevationStyles } from "../customCss";

type ButtonColor =
  | "primary"
  | "secondary"
  | "success"
  | "error"
  | "info"
  | "warning";

const isPaletteColor = (
  palette: any
): palette is {
  light: string;
  dark: string;
  contrastText: string;
  main: string;
} => {
  return (
    palette &&
    typeof palette.main === "string" &&
    typeof palette.light === "string" &&
    typeof palette.dark === "string" &&
    typeof palette.contrastText === "string"
  );
};

const StyledButton = styled(Button)<
  ButtonProps & { elevation?: ElevationLevel }
>(({ theme, color = "primary", variant = "contained", elevation = 5 }) => {
  const mode = theme?.palette?.mode || "light";

  const buttonTheme = mode === "dark" ? darkThemeOptions : lightThemeOptions;

  // Safeguard against undefined palette
  const validColor = (color === "inherit" ? "primary" : color) as ButtonColor;
  const palette =
    buttonTheme?.palette?.[validColor] || buttonTheme?.palette?.primary;

  if (!palette || !isPaletteColor(palette)) {
    // Fallback style in case the palette is undefined
    return {
      display: "flex",
      alignItems: "center",
      textTransform: "none",
      borderRadius: "8px",
      backgroundColor: "#ccc",
      color: "#000"
    };
  }

  const styles = {
    contained: {
      backgroundColor: palette.main,
      color: palette.contrastText,
      boxShadow: elevationStyles[elevation],
      "&:hover": {
        backgroundColor: palette.main
      },
      "&:active": {
        backgroundColor: palette.main
      }
    },
    outlined: {
      backgroundColor: "transparent",
      color: palette.main,
      border: `1px solid rgba(0, 87, 178, 0.50)`,
      "&:hover": {
        borderColor: palette.main,
        color: palette.main
      },
      "&:active": {
        borderColor: palette.main,
        color: palette.main
      }
    },
    text: {
      backgroundColor: "transparent",
      color: palette.main,
      "&:hover": {
        color: palette.main
      },
      "&:active": {
        color: palette.main
      }
    }
  };

  return {
    ...styles[variant],
    display: "flex",
    alignItems: "center",
    textTransform: "none",
    borderRadius: "8px",
    fontWeight: "bold"
  };
});

export const CeButton = (props: ButtonProps) => {
  return <StyledButton {...props} />;
};
