import { atom } from "recoil";
import {
  DEFAULT_CLIENT_DELETE_VALUES,
  DEFAULT_CLIENT_FORM_VALUES
} from "../constants/client";
import { ClientDeleteModalValues, ClientFormValues } from "../types/client";

export const clientFormValuesState = atom<ClientFormValues>({
  key: "clientFormValuesState",
  default: DEFAULT_CLIENT_FORM_VALUES
});

export const clientDeleteModalState = atom<ClientDeleteModalValues>({
  key: "clientDeleteModalState",
  default: DEFAULT_CLIENT_DELETE_VALUES
});
