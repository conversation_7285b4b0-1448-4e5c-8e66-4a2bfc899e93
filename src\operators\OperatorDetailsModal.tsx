import { Cancel01Icon } from '@hugeicons/react';
import { Tab<PERSON>ontex<PERSON>, <PERSON>b<PERSON>ist, TabPanel } from '@mui/lab';
import { Box, Drawer, IconButton, Tab, Typography } from '@mui/material';
import React, { useState } from 'react';
import { OperatorFormValues } from 'src/common/types';
import PersonalInfo, { PersonalInfoField } from './components/PersonalInfo';
import TimeOff from './components/TimeOff';
import { useUnavailablePeriods } from 'src/common/api';
import { useTranslation } from 'react-i18next';

interface OperatorDetailsModalProps {
  handleCloseOperatorDetailsModal: () => void;
  isLoading: boolean;
  initialFormValues: OperatorFormValues;
}

const OperatorDetailsModal: React.FC<OperatorDetailsModalProps> = ({
  isLoading,
  initialFormValues,
  handleCloseOperatorDetailsModal,
}) => {
  const { t } = useTranslation("common");
  const [tabValue, setTabValue] = useState('personalInfo');

  const handleChangeTab = (event: React.SyntheticEvent, newValue: string) => {
    setTabValue(newValue);
  };

  const personalInfoFields: PersonalInfoField[] = [
    { label: t('first-name'), value: initialFormValues.firstName },
    { label: t('last-name'), value: initialFormValues.lastName },
    { label: t('email'), value: initialFormValues.email },
    { label: t('country'), value: initialFormValues.country },
    { label: t('phone-number'), value: initialFormValues.phoneNumber },
    { label: t('role'), value: initialFormValues.role?.title! },
    { label: t('status'), value: initialFormValues.status?.title! },
  ];

  const {
    data: allUnavailablePeriod
  } = useUnavailablePeriods(
    {
      limit: 1000,
      offset: 0,
      expressions: [
        {
          operator: "=",
          value: initialFormValues.operatorId,
          category: `"operator"."id"`,
          conditionType: "AND",
        }
      ],
      sortModel: [
        {
          sort: "desc",
          field: "unavailable_period.id",
        },
      ],
      relations: ["operator"],
    },
    Boolean(initialFormValues.operatorId)
  );

  return (
    <Drawer
      anchor="right"
      open={initialFormValues.flow === 'Details'}
      onClose={handleCloseOperatorDetailsModal}
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 2,
        '& .MuiDrawer-paper': {
          boxSizing: 'border-box',
          width: 500,
        },
        '& .MuiBackdrop-root': {
          backgroundColor: 'transparent',
        },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            px:3,
            py:2
          }}
        >
          <Typography variant="h6" component="div" sx={{textTransform:'capitalize'}}>
            {initialFormValues.firstName} {initialFormValues.lastName}
          </Typography>
          <IconButton onClick={handleCloseOperatorDetailsModal} edge="end" aria-label="close">
            <Cancel01Icon variant='solid' size={20}/>
          </IconButton>
        </Box>

        <TabContext value={tabValue}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-start', px: 2, }}>
            <TabList onChange={handleChangeTab} aria-label="Operator Details">
              <Tab sx={{ textTransform: 'none' }} label={t("personalInfo")} value="personalInfo" />
              <Tab sx={{ textTransform: 'none' }} label={t("timeOff")} value="timeOff" />
            </TabList>
          </Box>

          <TabPanel value="personalInfo" sx={{ p:0, overflowY: 'scroll' }}>
            <PersonalInfo fields={personalInfoFields} />
          </TabPanel>

          <TabPanel value="timeOff" sx={{ p:0, overflowY: 'scroll' }}>
            <TimeOff 
                unavailablePeriods={allUnavailablePeriod?.data}
                operatorId={initialFormValues.operatorId} />
          </TabPanel>
        </TabContext>
      </Box>
    </Drawer>
  );
};

export default OperatorDetailsModal;
