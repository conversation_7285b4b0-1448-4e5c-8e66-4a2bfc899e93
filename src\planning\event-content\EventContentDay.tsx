import { EventContentArg } from "@fullcalendar/core";
import {
  Box,
  Chip,
  CircularProgress,
  CircularProgressProps,
  Tooltip,
  tooltipClasses,
  TooltipProps,
  Typography,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { useTranslation } from "react-i18next";
import { FC } from "react";
import { JOB_STEPS, STATUS_COLORS } from "src/common/constants";
import { EventContentDayRow } from "./EventContentDayRow";
import {
  CalendarEventTypography,
  CalendarEventWrapper,
} from "src/common/components/custom/CalendarEventWrapper";
import { HtmlTooltipResource } from "src/common/components/custom/HtmlTooltipResource";
import { TickDouble02Icon } from "@hugeicons/react";
import { JobStatus } from "src/common/types";

type Props = {
  arg: EventContentArg;
  eventsNumber?: number;
};

const EventContentDay: FC<Props> = ({ arg, eventsNumber }) => {
  const title = arg.event.title || "";
  const extendedProps = arg.event.extendedProps || {};
  const address = extendedProps?.address || "-";
  const city = extendedProps.city || "-";
  const fullSchedule = extendedProps?.schedule || "-";
  const jobStatus = extendedProps?.jobStatus || "-";
  const quantity = extendedProps?.quantity || "-";
  const hasBeenRead = extendedProps?.hasBeenRead;
  const flowRate = extendedProps?.flowRate || "-";

  const { t } = useTranslation(["manager", "dispatcher", "common"]);

  const { backgroundColor, borderColor, color } = (STATUS_COLORS[jobStatus] ||
    STATUS_COLORS.default) as {
    backgroundColor: string;
    borderColor: string;
    color: string;
  };

  const getStatusData = (
    jobStatus: string
  ): { statusText: string; statusPercentage: number } => {
    const step = JOB_STEPS.find((step) => step.name === jobStatus);
    let statusPercentage = 0;

    if (jobStatus === "UNAVAILABLE") {
      return { statusText: t("common:unavailable"), statusPercentage: 0 };
    } else if (step) {
      step.progress == null
        ? (statusPercentage = 0)
        : (statusPercentage = step.progress);
      return { statusText: t(`${step.label}`), statusPercentage };
    } else {
      return { statusText: "common:unknown-status", statusPercentage: 0 };
    }
  };

  const statusData = getStatusData(jobStatus);

  const HtmlTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: "#f5f5f9",
      color: "rgba(0, 0, 0, 0.87)",
      maxWidth: 220,
      fontSize: theme.typography.pxToRem(12),
      border: "1px solid #dadde9",
    },
  }));

  const CircularProgressWithLabel = (
    props: CircularProgressProps & { value: number; status: string }
  ) => {
    const status = props.status;
    if (
      [
        t("common:complete"),
        t("common:cancelled"),
        t("common:not-started"),
        t("common:unavailable"),
      ].includes(t(status))
    ) {
      return <></>;
    } else {
      return (
        <Box sx={{ position: "relative", display: "inline-flex" }}>
          <CircularProgress
            size={32}
            sx={{
              color: (theme) =>
                theme.palette.grey[theme.palette.mode === "light" ? 200 : 800],
            }}
            thickness={4}
            variant="determinate"
            {...props}
            value={100}
          />
          <CircularProgress
            size={32}
            variant="determinate"
            {...props}
            sx={{ position: "absolute", left: 0 }}
          />
          <Box
            sx={{
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              position: "absolute",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Typography
              color="primary"
              component="div"
              fontSize={10}
              variant="caption"
            >{`${Math.round(props.value)}%`}</Typography>
          </Box>
        </Box>
      );
    }
  };

  const StatusTooltipContent = (
    <Box alignItems="center" display="flex">
      <Box alignItems="center" display="flex">
        <Box alignItems="center" display="flex" mr={1}>
          <CircularProgressWithLabel
            color="secondary"
            status={statusData.statusText}
            value={statusData.statusPercentage}
          />
        </Box>
        <Chip label={statusData.statusText} />
      </Box>
    </Box>
  );

  window.dispatchEvent(new Event("resize"));
  const nonGenericTypographyStyle = {
    lineHeight: "14.3px",
    textOverflow: "ellipsis",
    overflow: "hidden",
    whiteSpace: "nowrap",
    width: "100%",
    flex: "1",
  };
  if (eventsNumber && eventsNumber > 1) {
    return (
      <EventContentDayRow
        arg={arg}
        eventsnumber={eventsNumber}
        bulletStyles={{ pt: "4px" }}
      />
    );
  }
  return (
    <HtmlTooltipResource
      placement="bottom-end"
      title={
        <Box>
          <Box
            alignItems="center"
            display="flex"
            justifyContent={"flex-start"}
            mt={0.5}
            gap="5px"
          >
            <Typography noWrap>Time:</Typography>
            <Typography noWrap>{arg.event.extendedProps.schedule}</Typography>
          </Box>
          <Box
            alignItems="center"
            display="flex"
            justifyContent={"flex-start"}
            mt={0.5}
            gap="5px"
          >
            <Typography noWrap>Company Name:</Typography>
            <Typography noWrap>{title}</Typography>
          </Box>
          <Box
            alignItems="center"
            display="flex"
            justifyContent={"flex-start"}
            mt={0.5}
            gap="5px"
          >
            <Typography noWrap>Adress:</Typography>
            <Typography noWrap>{address},{city}</Typography>
          </Box>
          <Box
            alignItems="center"
            display="flex"
            justifyContent={"flex-start"}
            mt={0.5}
            gap="5px"
          >
            <Typography noWrap>Amount:</Typography>
            <Typography
              noWrap
            >{`${quantity}m\u00b3 ${""} (${flowRate} m\u00b3/h)`}</Typography>
          </Box>
        </Box>
      }
    >
      <Box>
        <CalendarEventWrapper
          borderColor={borderColor}
          backgroundColor={backgroundColor}
          color={color}
          eventsnumber={eventsNumber}
        >
          <Chip
            sx={{
              color: color,
              backgroundColor: "transparent",
              height: "auto",
              "& span": {
                px: "0px !important",
              },
            }}
            label={
              <Box alignItems="center" display="flex">
                {hasBeenRead && jobStatus === JobStatus.NOT_STARTED ? (
                  <TickDouble02Icon
                    size={14}
                    color="currentColor"
                    variant={"solid"}
                  />
                ) : null}
                <CalendarEventTypography
                  eventsnumber={eventsNumber}
                  sx={{
                    flex: "0.5",
                  }}
                >
                  {fullSchedule}
                </CalendarEventTypography>
                {![
                  t("common:complete"),
                  t("common:cancelled"),
                  t("common:not-started"),
                  t("common:unavailable"),
                ].includes(t(statusData.statusText)) && (
                  <HtmlTooltip title={<>{StatusTooltipContent}</>}>
                    <Box border="solid white 4px" borderRadius="50%" ml={1}>
                      <Box
                        border={`solid ${backgroundColor} 4px`}
                        borderRadius="50%"
                      >
                        {""}
                      </Box>
                    </Box>
                  </HtmlTooltip>
                )}
              </Box>
            }
          />

          <CalendarEventTypography
            eventsnumber={eventsNumber}
            sx={nonGenericTypographyStyle}
          >
            {title}
          </CalendarEventTypography>
          <CalendarEventTypography
            eventsnumber={eventsNumber}
            sx={nonGenericTypographyStyle}
          >
            {`${quantity} m\u00b3 ${""} (${flowRate} m\u00b3/h)`}
          </CalendarEventTypography>
        </CalendarEventWrapper>
      </Box>
    </HtmlTooltipResource>
  );
};

export default EventContentDay;
