import { AxiosError } from "axios";
import { UseMutateFunction } from "react-query";
import { MainModal } from "src/common/components";
import {
  CreateManagerDto,
  ManagerFormValues,
  UpdateManagerDto,
  Manager,
} from "src/common/types";
import { ManagerForm } from "./ManagerForm";

interface ManagerModalProps {
  isLoading: boolean;
  initialFormValues: ManagerFormValues;
  handleCloseManagerModal: () => void;
  handleCreateNewManager?: UseMutateFunction<
    Manager,
    AxiosError<CreateManagerDto, CreateManagerDto> | Error,
    CreateManagerDto,
    () => void
  >;
  handleUpdateManager?: UseMutateFunction<
    Manager,
    AxiosError<UpdateManagerDto, UpdateManagerDto> | Error,
    UpdateManagerDto,
    () => void
  >;
}
export const ManagerModal: React.FC<ManagerModalProps> = ({
  isLoading,
  initialFormValues,
  handleCloseManagerModal,
  handleCreateNewManager,
  handleUpdateManager,
}) => {
  return (
    <MainModal
      title={`${initialFormValues.flow || ""} Manager`}
      isOpen={!!initialFormValues.flow}
      handleClose={handleCloseManagerModal}
    >
      <ManagerForm
        initialFormValues={initialFormValues}
        isLoading={isLoading}
        handleCreateNewManager={handleCreateNewManager}
        handleUpdateManager={handleUpdateManager}
        handleClose={handleCloseManagerModal}
      />
    </MainModal>
  );
};
