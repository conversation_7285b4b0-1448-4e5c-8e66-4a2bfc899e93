import { Autocomplete, DialogActions, Stack } from "@mui/material";
import { useFormik } from "formik";
import * as yup from "yup";
import {
  CompanyType,
  CreateManagerDto,
  ManagerFormValues,
  UpdateManagerDto,
  UserEnumDisplay,
} from "src/common/types";
import { userRolesDisplay, userStatusDisplay } from "src/common/constants";
import {
  turnManagerFormValuesIntoCreateDto,
  turnManagerFormValuesIntoUpdateDto,
} from "src/common/utils";
import countries from "src/common/utils/countries";
import { useTranslation } from "react-i18next";
import { CeTextField, CeButton, CeMuiPhoneNumber } from "src/common/components";

interface ManagerFormProps {
  isLoading: boolean;
  handleClose: () => void;
  handleCreateNewManager?: (args: CreateManagerDto) => void;
  handleUpdateManager?: (args: UpdateManagerDto) => void;
  initialFormValues: ManagerFormValues;
}

export const ManagerForm: React.FC<ManagerFormProps> = ({
  handleClose,
  isLoading,
  handleCreateNewManager,
  handleUpdateManager,
  initialFormValues,
}) => {
  const { t } = useTranslation(["common"]);

  const formik = useFormik<ManagerFormValues>({
    initialValues: initialFormValues,
    validationSchema: yup.object({
      firstName: yup.string().required("First name is required"),
      lastName: yup.string().required("Last name is required"),
      email: yup
        .string()
        .email("Enter a valid email")
        .required("Email is required"),
      companyName: yup.string().required("Company name is required"),
      companyAddress: yup.string().required("Company Address is required"),
      status: yup.object().required("Status is required"),
      role: yup.object().required("Role is required").nullable(),
      country: yup.string().required("Country is required"),
    }),
    onSubmit: (values) => {
      if (initialFormValues.flow === "Create" && handleCreateNewManager) {
        const payload: CreateManagerDto =
          turnManagerFormValuesIntoCreateDto(values);
        handleCreateNewManager(payload);
      }
      if (initialFormValues.flow === "Update" && handleUpdateManager) {
        const payload: UpdateManagerDto =
          turnManagerFormValuesIntoUpdateDto(values);

        handleUpdateManager(payload);
      }
    },
  });

  const findCountryByValue = (value: string) => {
    return countries.find((country) => country.value === value);
  };

  return (
    <Stack
      component="form"
      spacing={2}
      noValidate
      onSubmit={formik.handleSubmit}
      sx={{ width: "500px" }}
    >
      <Stack direction={"row"} spacing={2}>
        <CeTextField
          fullWidth
          id="name"
          name="firstName"
          label="First name"
          size="small"
          value={formik.values.firstName}
          onChange={formik.handleChange}
          InputLabelProps={{ shrink: true }}
          error={formik.touched.firstName && Boolean(formik.errors.firstName)}
          helperText={formik.touched.firstName && formik.errors.firstName}
          disabled={isLoading}
          required
        />
        <CeTextField
          fullWidth
          id="surname"
          name="lastName"
          label="Last name"
          size="small"
          value={formik.values.lastName}
          onChange={formik.handleChange}
          InputLabelProps={{ shrink: true }}
          error={formik.touched.lastName && Boolean(formik.errors.lastName)}
          helperText={formik.touched.lastName && formik.errors.lastName}
          disabled={isLoading}
          required
        />
      </Stack>

      {initialFormValues.flow === "Create" ? (
        <Stack direction={"row"} spacing={2}>
          <CeTextField
            fullWidth
            id="email"
            name="email"
            label="Email"
            type="email"
            size="small"
            value={formik.values.email}
            onChange={formik.handleChange}
            InputLabelProps={{ shrink: true }}
            error={formik.touched.email && Boolean(formik.errors.email)}
            helperText={formik.touched.email && formik.errors.email}
            disabled={isLoading}
            required
          />
        </Stack>
      ) : (
        <>
          <CeTextField
            fullWidth
            id="email"
            name="email"
            label="Email"
            type="email"
            size="small"
            value={formik.values.email}
            onChange={formik.handleChange}
            InputLabelProps={{ shrink: true }}
            error={formik.touched.email && Boolean(formik.errors.email)}
            helperText={formik.touched.email && formik.errors.email}
            disabled={isLoading}
            required
          />
        </>
      )}

      <Autocomplete
        fullWidth
        id="country"
        size="small"
        value={findCountryByValue(formik.values.country!)}
        onChange={(_, nextValue) => {
          formik.setFieldValue("country", nextValue?.value || null);
        }}
        options={countries}
        getOptionLabel={(option) => option.label}
        renderInput={(params) => (
          <CeTextField
            {...params}
            InputLabelProps={{ shrink: true }}
            error={formik.touched.country && Boolean(formik.errors.country)}
            helperText={formik.touched.country && formik.errors.country}
            disabled={isLoading}
            required
            label={t("common:country")}
            size="small"
          />
        )}
      />
      <Autocomplete
        id="companyType"
        fullWidth
        value={formik.values.companyType || null}
        onChange={(event: any, newValue: CompanyType | null) => {
          formik.setFieldValue("companyType", newValue || null);
        }}
        options={Object.values(CompanyType)}
        getOptionLabel={(option) => option}
        renderInput={(params) => (
          <CeTextField
            {...params}
            label="Company Type"
            size="small"
            InputLabelProps={{ shrink: true }}
            error={
              formik.touched.companyType && Boolean(formik.errors.companyType)
            }
            helperText={formik.touched.companyType && formik.errors.companyType}
            required
          />
        )}
      />
      <Stack direction={"row"} spacing={2}>
        <CeTextField
          fullWidth
          id="companyName"
          name="companyName"
          label="Company Name"
          size="small"
          value={formik.values.companyName}
          InputLabelProps={{ shrink: true }}
          onChange={formik.handleChange}
          error={
            formik.touched.companyName && Boolean(formik.errors.companyName)
          }
          helperText={formik.touched.companyName && formik.errors.companyName}
          disabled={isLoading}
          required
        />
        <CeTextField
          fullWidth
          id="companyAddress"
          name="companyAddress"
          label="Company Address"
          size="small"
          value={formik.values.companyAddress}
          onChange={formik.handleChange}
          InputLabelProps={{ shrink: true }}
          error={
            formik.touched.companyAddress &&
            Boolean(formik.errors.companyAddress)
          }
          helperText={
            formik.touched.companyAddress && formik.errors.companyAddress
          }
          disabled={isLoading}
          required
        />
      </Stack>
      <Stack direction={"row"} spacing={2}>
        <CeMuiPhoneNumber
          fullWidth
          defaultCountry={"be"}
          id="phoneNumber"
          name="phoneNumber"
          label="Phone number"
          size="small"
          InputLabelProps={{ shrink: true }}
          value={formik.values.phoneNumber}
          onChange={(value) => formik.setFieldValue("phoneNumber", value)}
          onBlur={() => formik.setFieldTouched("phoneNumber", true)}
          error={
            formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)
          }
          helperText={formik.touched.phoneNumber && formik.errors.phoneNumber}
          disabled={isLoading}
          variant="outlined"
        />
        <CeTextField
          fullWidth
          id="vatNumber"
          name="vatNumber"
          label="VAT Number"
          size="small"
          InputLabelProps={{ shrink: true }}
          value={formik.values.vatNumber}
          onChange={formik.handleChange}
          error={formik.touched.vatNumber && Boolean(formik.errors.vatNumber)}
          helperText={formik.touched.vatNumber && formik.errors.vatNumber}
          disabled={isLoading}
        />
      </Stack>

      <Stack direction={"row"} spacing={2}>
        <Autocomplete
          id="role"
          fullWidth
          value={formik.values.role || null}
          onChange={(event: any, nextValues: UserEnumDisplay | null) => {
            formik.setFieldValue("role", nextValues);
          }}
          onBlur={() => formik.setFieldTouched("role", true)}
          options={userRolesDisplay.filter(
            (role) =>
              role.visible &&
              (role.title === "Dispatcher Manager" ||
                role.title === "Operator Manager")
          )}
          isOptionEqualToValue={(
            option: UserEnumDisplay,
            value: UserEnumDisplay
          ) => option.id === value.id}
          getOptionLabel={(role) => role.title}
          filterSelectedOptions
          renderInput={(params) => (
            <CeTextField
              {...params}
              InputLabelProps={{ shrink: true }}
              error={formik.touched.role && Boolean(formik.errors.role)}
              helperText={formik.touched.role && formik.errors.role}
              required
              label="Role"
              size="small"
            />
          )}
        />

        <Autocomplete
          id="status"
          fullWidth
          value={formik.values.status || null}
          onChange={(event: any, nextValues: UserEnumDisplay | null) => {
            formik.setFieldValue("status", nextValues);
          }}
          onBlur={() => formik.setFieldTouched("status", true)}
          options={userStatusDisplay.filter((st) => st.visible)}
          isOptionEqualToValue={(
            option: UserEnumDisplay,
            value: UserEnumDisplay
          ) => option.id === value.id}
          getOptionLabel={(status) => status.title}
          filterSelectedOptions
          renderInput={(params) => (
            <CeTextField
              {...params}
              InputLabelProps={{ shrink: true }}
              error={formik.touched.status && Boolean(formik.errors.status)}
              helperText={formik.touched.status && formik.errors.status}
              required
              label="Status"
              size="small"
            />
          )}
        />
      </Stack>

      <DialogActions>
        <CeButton variant="text" onClick={handleClose} disabled={isLoading}>
          Cancel
        </CeButton>
        <CeButton type="submit" disabled={isLoading}>
          Submit
        </CeButton>
      </DialogActions>
    </Stack>
  );
};
