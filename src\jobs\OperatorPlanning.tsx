import { getCurrentUser } from "src/common/api";
import { useWindowSize } from "@react-hook/window-size";
import { Box } from "@mui/material";
import { Planning } from "./planning/Planning";

export const OperatorPlanning = () => {
  const [, height] = useWindowSize();
  const currentUser = getCurrentUser();

  return (
    <Box sx={{background: (theme) => theme.palette.background.paper, p:1}}>
      <Planning />
    </Box>
  );
};
