import { Box, Chip, Grid, Typography, useMediaQuery, useTheme } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { JOB_STEPS, STATUS_COLORS } from 'src/common/constants';
import { getFormattedDatewithLocale } from 'src/common/utils/formatDate';

interface OrderInformationProps {
    orderInfoProps: {
        created: string;
        expected: string;
        status: string;
        orderNumber: string;
        dispatcherPhoneNumber: string;
        operatorPhoneNumber: string;
    };
}

const OrderInformation: React.FC<OrderInformationProps> = ({ orderInfoProps }) => {
    const { t, i18n } = useTranslation(["common"]);
    const { created, expected, status, orderNumber, dispatcherPhoneNumber, operatorPhoneNumber } = orderInfoProps;
    const findStatus = JOB_STEPS.find((step) => step.name === status);
    const statusStyles = findStatus?.name ? STATUS_COLORS[findStatus.name] : {};
    const statusLabel = findStatus?.label ? t(findStatus.label) : "";
    const selectedLanguage = i18n.language;

    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

    const formattedCurrentDate = getFormattedDatewithLocale(
        selectedLanguage,
        created,
        "dd MMM yyyy hh:mm a"
    );
    const formattedExpectedDate = getFormattedDatewithLocale(
        selectedLanguage,
        expected,
        "dd MMM yyyy hh:mm a"
    );

    return (
        <Box sx={{ padding: isMobile ? 2 : 3 }}>
            <Typography variant="h6" sx={{ fontWeight: "bold", marginBottom: 2 }}>
                {t("order-information")}
            </Typography>
            <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.disabled" sx={{ mb: 1 }}>
                        {t("date-created")}
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                        {formattedCurrentDate || '-'}
                    </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.disabled" sx={{ mb: 1 }}>
                        {t("expected-date")}
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                        {formattedExpectedDate || '-'}
                    </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.disabled" sx={{ mb: 1 }}>
                        {t("order-number")}
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                        {orderNumber}
                    </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.disabled" sx={{ mb: 1 }}>
                        {t("current-status")}
                    </Typography>
                    <Chip
                        size="medium"
                        label={statusLabel}
                        sx={{
                            ...statusStyles,
                            fontWeight: 700,
                        }}
                    />
                </Grid>
                <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.disabled" sx={{ mb: 1 }}>
                        {t("tel-dispatcher")}
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                        <a href={`tel:${dispatcherPhoneNumber}`} style={{ textDecoration: 'none', cursor: 'pointer', color: 'inherit' }}>
                            {dispatcherPhoneNumber}
                        </a>
                    </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.disabled" sx={{ mb: 1 }}>
                        {t("tel-operator")}
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                        <a href={`tel:${operatorPhoneNumber}`} style={{ textDecoration: 'none', cursor: 'pointer', color: 'inherit'  }}>
                            {operatorPhoneNumber}
                        </a>
                    </Typography>
                </Grid>
            </Grid>
        </Box>
    );
};

export default OrderInformation;