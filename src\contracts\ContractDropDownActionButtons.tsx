import { MenuItem, Typography } from "@mui/material";
import { CustomDropDownActionButtons } from "src/common/components/custom/CustomDropDownActionButtons";
import ClearIcon from "@mui/icons-material/Clear";
import CheckIcon from "@mui/icons-material/Check";
import { ContractResponse } from "src/common/types";

interface DropDownActionButtonsProps {
  onRespond: (value: ContractResponse) => void;
}
export const ContractDropDownActionButtons = ({
  onRespond,
}: DropDownActionButtonsProps) => {
  const onMenuItemClick = (value: ContractResponse) => {
    onRespond(value);
  };
  return (
    <CustomDropDownActionButtons buttonDescription="Respond">
      <MenuItem
        onClick={() => {
          onMenuItemClick(ContractResponse.ACCEPT);
        }}
        disableRipple
      >
        <Typography
          sx={{ display: "flex", alignItems: "center" }}
          variant="button"
        >
          <CheckIcon />
          Accept
        </Typography>
      </MenuItem>
      <MenuItem
        onClick={() => {
          onMenuItemClick(ContractResponse.REJECT);
        }}
        disableRipple
      >
        <Typography
          sx={{
            display: "flex",
            alignItems: "center",
            color: (theme) => theme.palette.error.main,
          }}
          variant="button"
        >
          <ClearIcon
            sx={{
              color: (theme) => `${theme.palette.error.main} !important`,
            }}
          />
          Reject
        </Typography>
      </MenuItem>
    </CustomDropDownActionButtons>
  );
};

export default ContractDropDownActionButtons;
