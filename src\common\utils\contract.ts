import { ContractFormValues, CreateContractDto } from "../types";
import { turnPriceListFormValuesIntoCreateDto } from "./priceList";
import { noonTime } from "./unavailablePeriod";

export const turnContractFormValuesIntoCreateDto = (
  formValues: ContractFormValues
): CreateContractDto => {
  const pricelistDto = turnPriceListFormValuesIntoCreateDto(
    formValues.pricelist
  );
  return {
    pricelist: {
      ...pricelistDto,
      contractCancellationPeriod:
        formValues.pricelist.contractCancellationPeriod,
      contractLateCancellationPeriod:
        formValues.pricelist.contractLateCancellationPeriod,
    },
    dispatcherCompanyId: formValues.partner?.dispatcherCompanyId!,
    comment: formValues.comment || "",
    vehicleId: formValues.vehicle?.id!,
    startDate: formValues.startDate!.toISOString(),
    endDate: formValues.endDate!.toISOString(),
  };
};
