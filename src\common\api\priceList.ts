import axios, { AxiosError } from "axios";
import {
  CreatePriceListDto,
  DeletePriceListDto,
  PriceList,
  UpdatePriceListDto,
  PriceListWithCount,
  getPriceListsDto,
  GetContainersDto,
  ContainersWithCount,
  DeleteContainerDto,
  ContainerPricelists,
  UpdateContainerDto,
  CreateContainerDto,
} from "../types/priceList";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { processApiError, processApiSuccess } from "../utils/errors";
import { wrapEachFieldIntoDoubleQuotes } from "../utils";

const backendUrl = process.env.REACT_APP_API_URL;

export const getPriceList = async (priceListId?: number | null) => {
  if (!priceListId) {
    throw new Error("the pricelist ID was not provided");
  }
  return axios
    .get(`${backendUrl}/pricelist/globally/${priceListId}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const usePriceList = (
  priceListId?: number | null,
  enabled: boolean = true
) => {
  return useQuery<PriceList, AxiosError | Error>(
    ["pricelist", priceListId],
    () => getPriceList(priceListId),
    {
      onError: (err) => processApiError("Unable to fetch pricelist", err),
      enabled,
    }
  );
};

export const getPriceListById = async (priceListId?: number | null) => {
  if (!priceListId) {
    throw new Error("the pricelist ID was not provided");
  }
  return axios
    .get(`${backendUrl}/pricelist/${priceListId}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const usePriceListById = (
  priceListId?: number | null,
  enabled: boolean = true
) => {
  return useQuery<PriceList, AxiosError | Error>(
    ["pricelistById", priceListId],
    () => getPriceListById(priceListId),
    {
      onError: (err) => processApiError("Unable to fetch pricelist by id", err),
      enabled,
    }
  );
};

export const getPriceLists = async (attr: getPriceListsDto) => {
  return axios
    .post(`${backendUrl}/pricelist/get`, attr, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const usePriceLists = (
  attrs: getPriceListsDto,
  enabled: boolean = true
) => {
  const formattedSortModel = wrapEachFieldIntoDoubleQuotes(attrs.sortModel);
  const formattedAttrs = { ...attrs, sortModel: formattedSortModel };
  return useQuery<PriceListWithCount, AxiosError | Error>(
    ["pricelists", formattedAttrs],
    () => getPriceLists(formattedAttrs),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch pricelists", err),
      enabled,
    }
  );
};

export const createNewPriceList = (attrs: CreatePriceListDto) => {
  return axios
    .post(`${backendUrl}/pricelist`, attrs, { withCredentials: true })
    .then((response) => response.data);
};

export const useCreateNewPriceList = () => {
  const queryClient = useQueryClient();
  return useMutation<
    PriceList,
    AxiosError | Error,
    CreatePriceListDto,
    () => void
  >((a: CreatePriceListDto) => createNewPriceList(a), {
    onSuccess: () => {
      queryClient.invalidateQueries("pricelist");
      queryClient.invalidateQueries("pricelists");
      queryClient.invalidateQueries("pricelistById");
      queryClient.invalidateQueries("vehicleTypes");
    },
    onError: (err) => processApiError("Unable to create pricelist", err),
  });
};

export const handleUpdatePriceList = (
  updatePriceListArgs: UpdatePriceListDto
) => {
  const { priceListId, ...priceList } = updatePriceListArgs;
  if (!priceListId) {
    throw new Error("the pricelist ID was not provided");
  }
  return axios
    .patch(`${backendUrl}/pricelist/${priceListId}`, priceList, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useUpdatePriceList = () => {
  const queryClient = useQueryClient();
  return useMutation<
    PriceList,
    AxiosError | Error,
    UpdatePriceListDto,
    () => void
  >(
    (updatePriceListArgs: UpdatePriceListDto) =>
      handleUpdatePriceList(updatePriceListArgs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("pricelist");
        queryClient.invalidateQueries("pricelists");
        queryClient.invalidateQueries("pricelistById");
        queryClient.invalidateQueries("vehicleTypes");
      },
      onError: (err) => {
        processApiError("Unable to update pricelist", err);
      },
    }
  );
};

export const deletePriceList = (deletePriceListDto: DeletePriceListDto) => {
  const { priceListId } = deletePriceListDto;
  if (!priceListId) {
    throw new Error("the pricelist ID was not provided");
  }
  return axios
    .delete(`${backendUrl}/pricelist/${priceListId}`, { withCredentials: true })
    .then((response) => response.data);
};

export const useDeletePriceList = () => {
  const queryClient = useQueryClient();
  return useMutation<
    PriceList,
    AxiosError | Error,
    DeletePriceListDto,
    () => void
  >(
    (deletePriceListDto: DeletePriceListDto) =>
      deletePriceList(deletePriceListDto),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("pricelist");
        queryClient.invalidateQueries("pricelists");
        queryClient.invalidateQueries("pricelistById");
      },
      onError: (err) => processApiError("Unable to delete pricelist", err),
    }
  );
};

export const getPriceListContainers = async (attr: GetContainersDto) => {
  return axios
    .post(`${backendUrl}/pricelist/containers/get`, attr, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const usePriceListsContainers = (
  attrs: GetContainersDto,
  enabled: boolean = true
) => {
  const formattedSortModel = wrapEachFieldIntoDoubleQuotes(attrs.sortModel);
  const formattedAttrs = { ...attrs, sortModel: formattedSortModel };
  return useQuery<ContainersWithCount, AxiosError | Error>(
    ["containers", formattedAttrs],
    () => getPriceListContainers(formattedAttrs),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch containers", err),
      enabled,
    }
  );
};

export const handleCreateContainer = (attrs: CreateContainerDto) => {
  const { title } = attrs;
  if (!title) {
    throw new Error(" container title was not provided");
  }
  return axios
    .post(`${backendUrl}/pricelist/containers/`, attrs, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useCreateContainer = () => {
  const queryClient = useQueryClient();
  return useMutation<
    ContainerPricelists,
    AxiosError | Error,
    CreateContainerDto,
    () => void
  >((attrs: CreateContainerDto) => handleCreateContainer(attrs), {
    onSuccess: () => {
      queryClient.invalidateQueries("containers");
    },
    onError: (err) => {
      processApiError("Unable to create container", err);
    },
  });
};

export const handleUpdateContainer = (
  updateContainerArgs: UpdateContainerDto
) => {
  const { id, ...container } = updateContainerArgs;
  if (!id) {
    throw new Error("the container ID was not provided");
  }
  return axios
    .patch(`${backendUrl}/pricelist/containers/${id}`, container, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useUpdateContainer = () => {
  const queryClient = useQueryClient();
  return useMutation<
    ContainerPricelists,
    AxiosError | Error,
    UpdateContainerDto,
    () => void
  >(
    (updateContainerArgs: UpdateContainerDto) =>
      handleUpdateContainer(updateContainerArgs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("containers");
      },
      onError: (err) => {
        processApiError("Unable to update container", err);
      },
    }
  );
};

export const deleteContainer = (deleteContainerDto: DeleteContainerDto) => {
  const { id } = deleteContainerDto;
  if (!id) {
    throw new Error("the container ID was not provided");
  }
  return axios
    .delete(`${backendUrl}/pricelist/containers/${id}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useDeleteContainer = () => {
  const queryClient = useQueryClient();
  return useMutation<
    ContainerPricelists,
    AxiosError | Error,
    DeleteContainerDto,
    () => void
  >(
    (deleteContainerDto: DeleteContainerDto) =>
      deleteContainer(deleteContainerDto),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("containers");
      },
      onError: (err) => processApiError("Unable to delete container", err),
    }
  );
};

export const handleDefaultContainer = (
  updateContainerArgs: UpdateContainerDto
) => {
  const storedUser = localStorage.getItem("current_user");
  if (!storedUser) {
    throw new Error("User is not authenticated.");
  }
  const user = JSON.parse(storedUser);
  const token = user.token;
  if (!token) {
    throw new Error("Authentication token is missing.");
  }
  const { id } = updateContainerArgs;
  if (!id) {
    throw new Error("The container ID was not provided.");
  }
  return axios
    .patch(
      `${backendUrl}/pricelist/containers/${id}/default`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        withCredentials: true,
      }
    )
    .then((response) => response.data);
};

export const useUpdateDefaultContainer = () => {
  const queryClient = useQueryClient();
  return useMutation<
    ContainerPricelists,
    AxiosError | Error,
    UpdateContainerDto,
    () => void
  >(
    (updateContainerArgs: UpdateContainerDto) =>
      handleDefaultContainer(updateContainerArgs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("containers");
      },
      onError: (err) => {
        processApiError("Unable to update container", err);
      },
    }
  );
};
