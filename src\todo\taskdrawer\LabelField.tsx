import React, { useRef } from "react";
import {
  <PERSON>,
  Chip,
  IconButton,
  Popover,
  Stack,
  Typography,
  useTheme
} from "@mui/material";
import { Tick01Icon, Edit02Icon, Tag01Icon } from "@hugeicons/react";
import { <PERSON><PERSON><PERSON>on, CeTextField } from "src/common/components";
import {
  CreateTaskLabelDto,
  LabelColors,
  TaskLabel,
  UpdateTaskLabelDto
} from "src/common/types/labels";
import { Role } from "src/common/types";
import { getCurrentUser } from "src/common/api";
import { useRecoilState } from "recoil";
import { labelValuesState } from "src/common/state/label";
import { LABEL_STATE_DEFAULT } from "src/common/constants/label";
import { useTranslation } from "react-i18next";

interface PartialFormikProps {
  values: {
    label: TaskLabel | null;
    labelBeingSet: boolean;
  };
  setFieldValue: (field: string, value: any) => void;
  touched: {
    label?: boolean;
  };
  errors: {
    label?: string;
  };
  handleChange: (e: React.ChangeEvent<any>) => void;
  setFieldTouched: (
    field: string,
    touched: boolean,
    shouldValidate?: boolean
  ) => void;
}

interface LabelFieldProps {
  formik: PartialFormikProps;
  handleCreateLabel: (labelData: CreateTaskLabelDto) => void;
  handleUpdateLabel: (labelData: UpdateTaskLabelDto) => void;
  labels: TaskLabel[] | [];
}

const LabelField = ({
  formik,
  labels,
  handleCreateLabel,
  handleUpdateLabel
}: LabelFieldProps) => {
  const { t } = useTranslation("common");

  const [labelState, setLabelState] = useRecoilState(labelValuesState);

  const theme = useTheme();
  const currentUser = getCurrentUser();
  const boxRef = useRef<HTMLDivElement | null>(null);

  const isManager =
    currentUser?.role === Role.DISPATCHER_MANAGER ||
    currentUser?.role === Role.OEPRATOR_MANAGER;

  const isLabelBeingSet = formik.values.labelBeingSet;
  const existingLabel = labels.find((label) => label.name === labelState.name);

  const taskLabelColors = labels.map((label) => label.color);
  const colorOptions = Object.entries(LabelColors).filter(
    ([, color]) => !taskLabelColors.includes(color)
  );

  const renderLabelContent = () => {
    if (!formik.values.label?.name && !isLabelBeingSet) {
      return (
        <>
          <CeButton
            variant="text"
            sx={{ fontWeight: 700, size: 15, letterSpacing: 0.46 }}
            startIcon={<Tag01Icon />}
            onClick={() => formik.setFieldValue("labelBeingSet", true)}
          >
            {t("add-label")}
          </CeButton>
        </>
      );
    }
    if (formik.values.label) {
      return (
        <Chip
          size="medium"
          sx={{
            backgroundColor: formik.values.label.color,
            color: theme.palette.primary.contrastText,
            p: 0.5,
            borderRadius: "100px",
            fontWeight: 400,
            fontSize: 13,
            letterSpacing: 0.16,
            ":hover": {
              cursor: "pointer"
            }
          }}
          label={formik.values.label.name}
          onClick={() => formik.setFieldValue("labelBeingSet", true)}
        />
      );
    }

    return null;
  };

  return (
    <Box>
      {renderLabelContent()}
      <Box
        visibility="hidden"
        position="absolute"
        ref={boxRef}
        width="100%"
        left={0}
        top={18}
      >
        ""
      </Box>
      <Popover
        open={isLabelBeingSet}
        anchorEl={boxRef.current}
        onClose={() => formik.setFieldValue("labelBeingSet", false)}
        sx={{ px: 1.5, py: 2 }}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left"
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left"
        }}
        PaperProps={{
          sx: {
            width: boxRef.current ? boxRef.current.offsetWidth : "auto",
            px: 1.5,
            py: 2
          }
        }}
      >
        <Stack
          direction="row"
          flexWrap="wrap"
          gap={1}
          justifyContent="flex-start"
        >
          {!labels.length && (
            <Typography color="active" fontStyle="italic">
              {t("no-labels-available")}
            </Typography>
          )}

          {labels.map((label, index) => (
            <Chip
              key={index}
              size="medium"
              onClick={() => {
                formik.setFieldValue("label", label);
                formik.setFieldValue("labelId", label.id);
                formik.setFieldValue("labelBeingSet", false);
              }}
              label={
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between"
                >
                  {label.name}
                  <IconButton
                    sx={{ visibility: "hidden" }}
                    onClick={(e) => {
                      e.stopPropagation();
                      setLabelState({
                        color: label.color,
                        labelId: label.id,
                        name: "",
                        flow: "Update"
                      });
                    }}
                  >
                    <Edit02Icon
                      size={14}
                      color={theme.palette.primary.contrastText}
                    />
                  </IconButton>
                </Box>
              }
              sx={{
                backgroundColor: label.color,
                color: theme.palette.primary.contrastText,
                p: 0.5,
                borderRadius: "100px",
                fontWeight: 400,
                fontSize: 13,
                position: "relative",
                letterSpacing: 0.16,
                ":hover": {
                  backgroundColor: label.color,
                  color: theme.palette.primary.contrastText,
                  cursor: "pointer",
                  "& button": {
                    visibility: isManager ? "visible" : "hidden"
                  }
                }
              }}
            />
          ))}
        </Stack>

        {isManager && (
          <>
            {!labelState.flow && labels.length < 6 && (
              <CeButton
                variant="text"
                sx={{ fontWeight: 700, size: 15, letterSpacing: 0.46, mt: 2 }}
                startIcon={<Tag01Icon />}
                onClick={() => setLabelState({ ...labelState, flow: "Create" })}
              >
                {t("add-new-label")}
              </CeButton>
            )}

            {!!labelState.flow && (
              <>
                <CeTextField
                  name="label"
                  value={labelState.name || ""}
                  onChange={(event) =>
                    setLabelState((prevLabel) => ({
                      ...prevLabel,
                      name: event.target.value
                    }))
                  }
                  size="small"
                  sx={{ mt: 1 }}
                  fullWidth
                />

                <Box
                  sx={{
                    mt: 2,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-start",
                    gap: 2.5,
                    ml: 1
                  }}
                >
                  {colorOptions.map(([key, value]) => (
                    <Box
                      role="button"
                      key={key}
                      onClick={() =>
                        setLabelState((prevLabel) => ({
                          ...prevLabel,
                          color: value
                        }))
                      }
                      sx={{
                        border:
                          labelState.color === value
                            ? `3px solid ${theme.palette.primary.light}`
                            : "1px solid transparent",
                        width: 30,
                        height: 30,
                        backgroundColor: value,
                        borderRadius: "4px",
                        ":hover": {
                          cursor: "pointer"
                        }
                      }}
                    />
                  ))}

                  <IconButton
                    disabled={
                      !labelState.color || !labelState.name || !!existingLabel
                    }
                    onClick={() => {
                      if (labelState.color && labelState.name) {
                        if (labelState.labelId) {
                          handleUpdateLabel({
                            color: labelState.color,
                            name: labelState.name,
                            labelId: labelState.labelId
                          });
                          setLabelState(LABEL_STATE_DEFAULT);
                          return;
                        }

                        handleCreateLabel({
                          color: labelState.color,
                          name: labelState.name
                        });
                      }
                      setLabelState(LABEL_STATE_DEFAULT);
                    }}
                  >
                    <Tick01Icon
                      size={25}
                      color="currentColor"
                      variant="solid"
                    />
                  </IconButton>
                </Box>
              </>
            )}

            {!!existingLabel && (
              <Typography color="error" variant="caption">
                {t("label-already-exists")}
              </Typography>
            )}
          </>
        )}
      </Popover>
    </Box>
  );
};

export default LabelField;
