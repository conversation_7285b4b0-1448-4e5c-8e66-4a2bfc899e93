import { GridDensity } from "@mui/x-data-grid";
import { useCallback, useState } from "react";

export interface GridStateSnapshot {
  columnVisibilityModel: Record<string, boolean>;
  filterModel: { items: any[] };
  sortModel: any[];
  page: number;
  pageSize: number;
  density: { value: GridDensity };
}

export type UpdateGridStatePart = <K extends keyof GridStateSnapshot>(
  key: K,
  value: GridStateSnapshot[K]
) => void;

const usePersistentGridState = (
  localStorageKey: string,
  defaultPage: number = 0,
  defaultPageSize: number = 20,
  initialSortModel?: any[]
): [GridStateSnapshot, UpdateGridStatePart] => {
  const [gridState, setGridState] = useState<GridStateSnapshot>(() => {
    const saved = localStorage.getItem(`${localStorageKey}GridState`);
    return saved
      ? JSON.parse(saved)
      : {
          columnVisibilityModel: {
            id: false,
            updated_at: false,
            deleted_at: false,
            created_by: false,
            created_at: false,
          },
          filterModel: { items: [] },
          sortModel: initialSortModel,
          page: defaultPage,
          pageSize: defaultPageSize,
          density: { value: "standard" as GridDensity },
        };
  });

  const updateGridStatePart = useCallback<UpdateGridStatePart>((key, value) => {
    setGridState((prev) => {
      const updated = { ...prev, [key]: value };
      localStorage.setItem(
        `${localStorageKey}GridState`,
        JSON.stringify(updated)
      );
      return updated;
    });
  }, []);

  return [gridState, updateGridStatePart];
};

export default usePersistentGridState;
