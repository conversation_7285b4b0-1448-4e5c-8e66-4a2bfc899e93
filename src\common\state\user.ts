import { atom } from "recoil";
import {
  DeleteManagerModalValues,
  SignInUserWithEmailDto,
  ManagerFormValues,
  UserSettingsFormValues,
} from "../types";
import {
  SIGN_IN_DEFAULT,
  MANAGER_DELETE_DEFAULT,
  MANAGER_FORM_VALUES_DEFAULT,
  SETTINGS_USER_DEFAULT,
} from "../constants";

export const ManagerFormValuesState = atom<ManagerFormValues>({
  key: "managerFormValuesState",
  default: MANAGER_FORM_VALUES_DEFAULT,
});

export const settingsUserFormValuesState = atom<UserSettingsFormValues>({
  key: "settingsUserFormValuesState",
  default: SETTINGS_USER_DEFAULT,
});

export const managerDeleteValuesState = atom<DeleteManagerModalValues>({
  key: "managerDeleteValuesState",
  default: MANAGER_DELETE_DEFAULT,
});

export const signInState = atom<SignInUserWithEmailDto>({
  key: "signInStrate",
  default: SIGN_IN_DEFAULT,
});

export const passwordResetTokenState = atom<string>({
  key: "passwordResetTokenState",
  default: "",
});
