import React from "react";
import { Box, Chip, Grid, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { Job, Reservation } from "src/common/types";
import { ReactComponent as Checkmark } from "src/images/Checkmark.svg";
import {
  FlashIcon,
  CarParking01Icon,
  ParkingAreaCircleIcon,
  AutoConversationsIcon,
} from "@hugeicons/react";
import { useFile } from "src/common/api/reservation";

interface MediaProps {
  job: Job | undefined;
}

const Media: React.FC<MediaProps> = ({ job }) => {
  const { t } = useTranslation("common");

  const electricalRiskFile = useFile(
    job?.electricalRiskKey!,
    Boolean(job?.electricalRiskKey)
  );
  const accessComplianceFile = useFile(
    job?.accessComplianceKey!,
    Boolean(job?.accessComplianceKey)
  );
  const parkingComplianceFile = useFile(
    job?.parkingComplianceKey!,
    Bo<PERSON>an(job?.parkingComplianceKey)
  );
  const terrainStabilityFile = useFile(
    job?.terrainStabilityKey!,
    Bo<PERSON>an(job?.terrainStabilityKey)
  );

  return (
    <>
      <Typography variant="h6" sx={{ fontWeight: "bold", marginBottom: 2 }}>
        {t("media")}
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={6}>
          {electricalRiskFile.data ? (
            <Box
              component="img"
              src={electricalRiskFile.data}
              alt="Uploaded photo"
              sx={{ width: "100%", borderRadius: 1 }}
            />
          ) : (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ width: "100%", textAlign: "center", padding: 2 }}
            >
              {t("no-uploads")}
            </Typography>
          )}
          <Box display="flex" justifyContent="center" mt={1}>
            <Chip
              icon={
                <FlashIcon size={16} color="currentColor" variant="solid" />
              }
              label={t("electrical-risk")}
              color="primary"
              size="small"
              sx={{ padding: 1 }}
            />
          </Box>
          {job?.electricalRiskKey && (
            <Box display="flex" justifyContent="center" mt={1}>
              <Chip icon={<Checkmark />} label="Secured" color="default" />
            </Box>
          )}
        </Grid>

        <Grid item xs={6}>
          {accessComplianceFile.data ? (
            <Box
              component="img"
              src={accessComplianceFile.data}
              alt="Uploaded photo"
              sx={{ width: "100%", borderRadius: 1 }}
            />
          ) : (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ width: "100%", textAlign: "center", padding: 2 }}
            >
              {t("no-uploads")}
            </Typography>
          )}
          <Box display="flex" justifyContent="center" mt={1}>
            <Chip
              icon={
                <CarParking01Icon
                  size={16}
                  color="currentColor"
                  variant="solid"
                />
              }
              label={t("access-compliance")}
              color="primary"
              size="small"
              sx={{ padding: 1 }}
            />
          </Box>
          {job?.accessComplianceKey && (
            <Box display="flex" justifyContent="center" mt={1}>
              <Chip icon={<Checkmark />} label="Secured" color="default" />
            </Box>
          )}
        </Grid>

        <Grid item xs={6}>
          {parkingComplianceFile.data ? (
            <Box
              component="img"
              src={parkingComplianceFile.data}
              alt="Uploaded photo"
              sx={{ width: "100%", borderRadius: 1 }}
            />
          ) : (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ width: "100%", textAlign: "center", padding: 2 }}
            >
              {t("no-uploads")}
            </Typography>
          )}
          <Box display="flex" justifyContent="center" mt={1}>
            <Chip
              icon={
                <ParkingAreaCircleIcon
                  size={16}
                  color="currentColor"
                  variant="solid"
                />
              }
              label={t("parking-compliance")}
              color="primary"
              size="small"
              sx={{ padding: 1 }}
            />
          </Box>
          {job?.parkingComplianceKey && (
            <Box display="flex" justifyContent="center" mt={1}>
              <Chip icon={<Checkmark />} label="Secured" color="default" />
            </Box>
          )}
        </Grid>

        <Grid item xs={6}>
          {terrainStabilityFile.data ? (
            <Box
              component="img"
              src={terrainStabilityFile.data}
              alt="Uploaded photo"
              sx={{ width: "100%", borderRadius: 1 }}
            />
          ) : (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ width: "100%", textAlign: "center", padding: 2 }}
            >
              {t("no-uploads")}
            </Typography>
          )}
          <Box display="flex" justifyContent="center" mt={1}>
            <Chip
              icon={
                <AutoConversationsIcon
                  size={16}
                  color="currentColor"
                  variant="solid"
                />
              }
              label={t("terrain-stability")}
              color="primary"
              size="small"
              sx={{ padding: 1 }}
            />
          </Box>
          {job?.terrainStabilityKey && (
            <Box display="flex" justifyContent="center" mt={1}>
              <Chip icon={<Checkmark />} label="Secured" color="default" />
            </Box>
          )}
        </Grid>
      </Grid>
    </>
  );
};

export default Media;
