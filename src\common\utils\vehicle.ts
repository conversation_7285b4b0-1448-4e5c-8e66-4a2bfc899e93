import { vehicleTypeCodeMap } from "../constants";
import {
  CreateVehicleDto,
  FilterVehiclesDto,
  FilterVehiclesFormValues,
  Manager,
  Reservation,
  SearchVehicleType,
  User,
  Vehicle,
  VehicleFormValues,
  VehicleModalFlow,
  VehicleTypeEnum,
} from "../types";
import { Company } from "../types/company";

export const turnVehicleIntoFormValues = (
  vehicle: Vehicle,
  flow: VehicleModalFlow
): VehicleFormValues => {
  const payload: VehicleFormValues = {
    ...vehicle,
    vehicleId: vehicle.id,
    operator: vehicle.operator || null,
    type: vehicle.type || null,
    vehicleBrand: vehicle.vehicleBrand || "",
    brandModel: vehicle.brandModel || "",
    typeOfMotorization: vehicle.typeOfMotorization || null,
    licensePlateNumber: vehicle.licensePlateNumber || "",
    weight: vehicle.weight || null,
    height: vehicle.height || null,
    length: vehicle.length || null,
    width: vehicle.width || null,
    maxVerticalReach: vehicle.maxVerticalReach || null,
    maxHorizontalReach: vehicle.maxHorizontalReach || null,
    hasStrangler: vehicle.hasStrangler,
    invoicingPipesFrom: vehicle.invoicingPipesFrom || null,
    pipeLengthForSecondTechnician:
      vehicle.pipeLengthForSecondTechnician || null,
    endHoseLength: vehicle.endHoseLength || null,
    maxFlowRate: vehicle.maxFlowRate || null,
    maxConcretePressure: vehicle.maxConcretePressure || null,
    availableFlexiblePipeLength80Mm:
      vehicle.availableFlexiblePipeLength80Mm || null,
    availableFlexiblePipeLength90Mm:
      vehicle.availableFlexiblePipeLength90Mm || null,
    availableFlexiblePipeLength100Mm:
      vehicle.availableFlexiblePipeLength80Mm || null,
    availableFlexiblePipeLength120Mm:
      vehicle.availableFlexiblePipeLength90Mm || null,
    availableRigidPipeLength: vehicle.availableRigidPipeLength || null,
    maxDownwardReach: vehicle.maxDownwardReach || null,
    numberOfBoomSections: vehicle.numberOfBoomSections || null,
    minUnfoldingHeight: vehicle.minUnfoldingHeight || null,
    boomRotation: vehicle.boomRotation || null,
    frontSideOpening: vehicle.frontSideOpening || null,
    rearSideOpening: vehicle.rearSideOpening || null,
    frontOutriggerSpan: vehicle.frontOutriggerSpan || null,
    rearOutriggerSpan: vehicle.rearOutriggerSpan || null,
    frontPressureOnOutrigger: vehicle.frontPressureOnOutrigger || null,
    rearPressureOnOutrigger: vehicle.rearPressureOnOutrigger || null,
    boomUnfoldingSystem: vehicle.boomUnfoldingSystem || null,
    boomSize: vehicle.boomSize || null,
    bacExit: vehicle.bacExit || false,
    bacExitReverse: vehicle.bacExitReverse || false,
    siteAddress: vehicle.siteAddress || "",
    city: vehicle.city || "",
    plz: vehicle.plz!,
    country: vehicle.country || "",
    location: vehicle.location || { type: "", coordinates: [0, 0] },
    completedJobs: vehicle.completedJobs || null,
    flow,
  };

  return payload;
};

export function turnVehicleFormValuesIntoCreateVehicleDto(
  formValues: VehicleFormValues
): CreateVehicleDto {
  return {
    operatorId: formValues.operator?.id!,
    type: formValues.type,
    vehicleBrand: formValues.vehicleBrand,
    brandModel: formValues.brandModel,
    typeOfMotorization: formValues.typeOfMotorization,
    licensePlateNumber: formValues.licensePlateNumber!,
    weight: formValues.weight,
    height: formValues.height,
    length: formValues.length,
    width: formValues.width,
    maxVerticalReach: formValues.maxVerticalReach,
    maxHorizontalReach: formValues.maxHorizontalReach,
    hasStrangler: formValues.hasStrangler,
    invoicingPipesFrom: formValues.invoicingPipesFrom,
    pipeLengthForSecondTechnician: formValues.pipeLengthForSecondTechnician,
    endHoseLength: formValues.endHoseLength,
    maxFlowRate: formValues.maxFlowRate,
    maxConcretePressure: formValues.maxConcretePressure,
    availableFlexiblePipeLength80Mm: formValues.availableFlexiblePipeLength80Mm,
    availableFlexiblePipeLength90Mm: formValues.availableFlexiblePipeLength90Mm,
    availableFlexiblePipeLength100Mm:
      formValues.availableFlexiblePipeLength100Mm,
    availableFlexiblePipeLength120Mm:
      formValues.availableFlexiblePipeLength120Mm,
    availableRigidPipeLength: formValues.availableRigidPipeLength,
    maxDownwardReach: formValues.maxDownwardReach,
    numberOfBoomSections: formValues.numberOfBoomSections,
    minUnfoldingHeight: formValues.minUnfoldingHeight,
    boomRotation: formValues.boomRotation,
    frontOutriggerSpan: formValues.frontOutriggerSpan,
    rearOutriggerSpan: formValues.rearOutriggerSpan,
    frontPressureOnOutrigger: formValues.frontPressureOnOutrigger,
    rearPressureOnOutrigger: formValues.rearPressureOnOutrigger,
    frontSideOpening: formValues.frontSideOpening,
    rearSideOpening: formValues.rearSideOpening,
    boomUnfoldingSystem: formValues.boomUnfoldingSystem,
    bacExit: formValues.bacExit,
    bacExitReverse: formValues.bacExitReverse,
    siteAddress: formValues.siteAddress,
    city: formValues.city,
    plz: formValues.plz,
    country: formValues.country,
    location: formValues.location,
    uniqueIdentificationNumber: formValues.uniqueIdentificationNumber,
    completedJobs: formValues.completedJobs,
    boomSize: formValues.boomSize,
  };
}

export const turnFilterVehicleFormValuesIntoDto = (
  values: FilterVehiclesFormValues
): FilterVehiclesDto => {
  const pipeDiameter = values.pipeDiameter;

  return {
    type: values.type,
    boomSize: values.boomSize || null,
    coordinates: values.coordinates || null,
    radius: values.radius,
    availableFlexiblePipeLength80Mm:
      pipeDiameter === "80mm" ? values.requiredPipes : null,
    availableFlexiblePipeLength90Mm:
      pipeDiameter === "90mm" ? values.requiredPipes : null,
    availableFlexiblePipeLength100Mm:
      pipeDiameter === "100mm" ? values.requiredPipes : null,
    availableFlexiblePipeLength120Mm:
      pipeDiameter === "120mm" ? values.requiredPipes : null,
    frontOutriggerSpan: values.frontOutriggerSpan || null,
    rearOutriggerSpan: values.rearOutriggerSpan || null,
    dateFrom: values.dateFrom ? new Date(values.dateFrom) : null,
    dateTo: values.dateTo ? new Date(values.dateTo) : null,
    siteAddress: values.siteAddress || "",
  };
};

export const getVehiclesFromReservations = (reservations: Reservation[]) => {
  const vehicles: Vehicle[] = [];

  reservations.forEach((res) => {
    if (res.vehicle) {
      vehicles.push(res.vehicle);
    }
  });

  return vehicles;
};

export const getVehicleUniqueId = (vehicle: Vehicle): string => {
  const vehicleManager = vehicle.manager as unknown as User;
  return `${vehicleTypeCodeMap[vehicle.type as VehicleTypeEnum] || ""} ${
    vehicleManager?.country || ""
  } ${vehicle?.boomSize || ""} ${vehicleManager?.company?.name || ""}`;
};

export const collectOperatorCompanyIdsAttr = (
  searchType: SearchVehicleType,
  contractedCompanyIds: Company[],
  favoritedCompanyIds: Company[],
  partneredCompanyIds: Company[]
) => {
  switch (searchType) {
    case "Contracted":
      return contractedCompanyIds.map((cm) => cm.id) || [];
    case "Favorited":
      return favoritedCompanyIds.map((fm) => fm.id) || [];
    case "Partnered":
      return partneredCompanyIds.map((pm) => pm.id) || [];
    default:
      return [];
  }
};
