import axios, { AxiosError } from "axios";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { processApiError } from "../utils/errors";
import { CreateVehicleTypeDto, DeleteVehicleTypeDto, getVehicleTypesDto, UpdateVehicleTypeDto, VehicleType, VehicleTypesWithCount } from "../types/vehicleType";

const backendUrl = process.env.REACT_APP_API_URL;

export const getVehicleTypes = async (attr: getVehicleTypesDto) => {
    return axios
        .get(`${backendUrl}/vehicle-type`, {
            withCredentials: true,
            params: attr
        })
        .then((response) => response.data);
};

export const useVehicleTypes = (attrs: getVehicleTypesDto, enabled: boolean = true) => {

    return useQuery<VehicleTypesWithCount, AxiosError | Error>(
        ["vehicleTypes", attrs],
        () => getVehicleTypes(attrs),
        {
            keepPreviousData: true,
            onError: (err) => processApiError("Unable to fetch vehicle types", err),
            enabled
        }
    );
};

export const getVehicleTypeById = async (vehicleTypeId: number | null) => {
    if (!vehicleTypeId) {
        throw new Error("the vehicleTypeId ID was not provided");
    }
    return axios
        .get(`${backendUrl}/vehicle-type/${vehicleTypeId}`, { withCredentials: true })
        .then((response) => response.data);
};

export const useVehicleType = (
    vehicleId: number | null,
    enabled: boolean = true
) => {
    return useQuery<VehicleType, AxiosError | Error>(
        ["vehicleType", vehicleId],
        () => getVehicleTypeById(vehicleId),
        {
            onError: (err) => processApiError("Unable to fetch vehicle Type by id", err),
            enabled
        }
    );
};
export const deleteVehicleType = (deleteVehicleDto: DeleteVehicleTypeDto) => {
    const { vehicleTypeId } = deleteVehicleDto;
    console.log(vehicleTypeId);
    
    if (!vehicleTypeId) {
      throw new Error("the vehicle Type ID was not provided");
    }
    return axios
      .delete(`${backendUrl}/vehicle-type/${vehicleTypeId}`, { withCredentials: true })
      .then((response) => response.data);
};
export const useDeleteVehicleType = () => {
    const queryClient = useQueryClient();
    return useMutation<VehicleType, AxiosError | Error, DeleteVehicleTypeDto, () => void>(
      (deleteVehicleTypeDto: DeleteVehicleTypeDto) => deleteVehicleType(deleteVehicleTypeDto),
      {
        onSuccess: () => {
          queryClient.invalidateQueries("vehicleType");
          queryClient.invalidateQueries("vehicleTypes");
        },
        onError: (err) => processApiError("Unable to delete vehicle Type", err)
      }
    );
  };

  export const createNewVehicleType = (attrs: CreateVehicleTypeDto) => {
    return axios
        .post(`${backendUrl}/vehicle-type/`, attrs, { withCredentials: true })
        .then((response) => response.data);
};
export const useCreateNewVehicleType = () => {
    const queryClient = useQueryClient();
    return useMutation<VehicleType, AxiosError | Error, CreateVehicleTypeDto, () => void>(
        (a: CreateVehicleTypeDto) => createNewVehicleType(a),
        {
            onSuccess: (newVehicle) => {
                queryClient.invalidateQueries("vehicleType");
                queryClient.invalidateQueries("vehicleTypes");
            },
            onError: (err) => processApiError("Unable to create vehicle Type", err)
        }
    );
};
export const handleUpdateVehicleType = (updateVehicleArgs: UpdateVehicleTypeDto) => {
    const { vehicleTypeId, ...vehicle } = updateVehicleArgs;
    if (!vehicleTypeId) {
        throw new Error("the vehicle Type ID was not provided");
    }
    return axios
        .patch(`${backendUrl}/vehicle-type/${vehicleTypeId}`, vehicle, {
            withCredentials: true
        })
        .then((response) => response.data);
};
export const useUpdateVehicleType = () => {
    const queryClient = useQueryClient();
    return useMutation<VehicleType, AxiosError | Error, UpdateVehicleTypeDto, () => void>(
      (updateVehicleTypeArgs: UpdateVehicleTypeDto) =>
        handleUpdateVehicleType(updateVehicleTypeArgs),
      {
        onSuccess: () => {
          queryClient.invalidateQueries("vehicleType");
          queryClient.invalidateQueries("vehicleTypes");
        },
        onError: (err) => {
          processApiError("Unable to update vehicle Type", err);
        }
      }
    );
};