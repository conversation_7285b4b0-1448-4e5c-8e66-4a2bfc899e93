import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { DeleteModal } from "src/common/components";
import {
  DeleteVehicleModelDto,
  VehicleModelDeleteModalFlow,
  VehicleModelFormValues,
} from "src/common/types";

interface ReservationModalDeleteProps {
  flow: VehicleModelDeleteModalFlow;
  vehicleModelTitle?: string;
  isLoading: boolean;
  vehicleModelId?: number;
  handleCloseVehicleModalDelete: () => void;
  handleDeleteVehicleModel: (args: DeleteVehicleModelDto) => void;
}
export const VehicleModelDeleteModal: React.FC<ReservationModalDeleteProps> = ({
  flow,
  vehicleModelTitle,
  vehicleModelId,
  isLoading,
  handleCloseVehicleModalDelete,
  handleDeleteVehicleModel,
}) => {
  const { t } = useTranslation("common");

  const onDeleteVehicle = () => {
    if (vehicleModelId) {
      handleDeleteVehicleModel({ vehicleModelId });
    }
  };

  const DeleteModalHelperText = () => (
    <Typography sx={{ marginBottom: 1 }}>
      {t("delete-vehicle-model-message", {
        vehicleModelTitle: vehicleModelTitle || "unknown vehicle",
      })}
    </Typography>
  );

  return (
    <DeleteModal
      isLoading={isLoading}
      title={t("common:delete-vehicle-model")}
      helperText={DeleteModalHelperText()}
      isOpen={flow === "Delete"}
      handleSubmit={() => onDeleteVehicle()}
      handleClose={handleCloseVehicleModalDelete}
    />
  );
};
