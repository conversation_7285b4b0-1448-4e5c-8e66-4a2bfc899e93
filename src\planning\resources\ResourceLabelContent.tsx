import { ResourceLabelContentArg } from "@fullcalendar/resource";

import { useTranslation } from "react-i18next";

import {
  Box,
  Chip,
  IconButton,
  Paper,
  Stack,
  Tooltip,
  Typography,
  useTheme,
} from "@mui/material";
import { FC } from "react";
import { Manager, Operator, Vehicle } from "src/common/types";
import { VehicleOutlined } from "src/images/Icons";
import { Building06Icon, IdIcon } from "@hugeicons/react";
import { getTruncatedName } from "src/common/utils/operator";
import { HtmlTooltipResource } from "src/common/components/custom/HtmlTooltipResource";
import { CePaper } from "src/common/components";

type ResourceLabelContentProps = {
  arg: ResourceLabelContentArg;
  handleVehicleInformationModal: (vehicleId: number) => void;
};

const ResourceLabelContent: FC<ResourceLabelContentProps> = (props) => {
  const { t } = useTranslation("dispatcher");
  const theme = useTheme();
  const { arg, handleVehicleInformationModal } = props;
  const vehicle = arg.resource.extendedProps as {
    _id: string;
    vehicleId: number;
    operator: Operator | null;
    manager: Manager | null;
  } & Vehicle;
  const operatorFullName = vehicle.operator
    ? `${vehicle.operator?.name} ${vehicle.operator?.surname}`
    : "None";
  return (
    <CePaper
      sx={{
        p: 1,
        width: "100%",
      }}
    >
      <Box mb="5px">
        <HtmlTooltipResource
          title={<Typography>{arg.resource.extendedProps.name}</Typography>}
        >
          <Box
            sx={{
              alignItems: "center",
              display: "flex",
              justifyContent: "flex-start",
              px: "9px",
            }}
          >
            <VehicleOutlined
              width="16px"
              height="16px"
              color={theme.palette.primary.main}
            />
            <Box
              ml={1}
              flex="1"
              sx={{
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
              }}
            >
              <Typography
                fontSize={12}
                fontWeight={700}
                color={theme.palette.text.primary}
                letterSpacing={0.1}
                lineHeight="10px"
              >
                {arg.resource.extendedProps.name}
              </Typography>
            </Box>
          </Box>
        </HtmlTooltipResource>
      </Box>
      <Box
        display="flex"
        alignItems="center"
        justifyContent={"space-between"}
        sx={{ gap: "8px" }}
      >
        <HtmlTooltipResource
          title={
            <Stack>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "flex-start",
                  gap: 1,
                }}
              >
                <Typography>{t("common:name")}:</Typography>
                <Typography>{operatorFullName}</Typography>
              </Box>
              <Box>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-start",
                    gap: 1,
                  }}
                >
                  <Typography>{t("common:phone-number")}:</Typography>
                  <Typography>
                    {vehicle?.operator?.phoneNumber || ""}
                  </Typography>
                </Box>
              </Box>
            </Stack>
          }
          sx={{
            flexShrink: "0",
          }}
        >
          <Chip
            size="small"
            sx={{
              padding: "4px",
              borderRadius: "100px",
              display: "flex",
              alignItems: "center",
              backgroundColor: "rgba(0, 0, 0, 0.03)",
              "& span": { width: "84px", pr: "0" },
            }}
            icon={
              <IdIcon size={16} color={"currentColor"} variant={"stroke"} />
            }
            label={
              <Typography
                sx={{
                  fontWeight: "500",
                  fontSize: "12px",
                  letterSpacing: "0.16px",
                  lineHeight: "11px",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                }}
              >
                {operatorFullName}
              </Typography>
            }
          />
        </HtmlTooltipResource>
        <HtmlTooltipResource
          sx={{ flexShrink: "0" }}
          title={
            <Box>
              <Box
                alignItems="center"
                display="flex"
                justifyContent={"flex-start"}
                mt={0.5}
                gap="5px"
              >
                <Typography>Phone:</Typography>
                <Typography>{vehicle.manager?.phoneNumber}</Typography>
              </Box>

              <Box
                alignItems="center"
                display="flex"
                justifyContent={"flex-start"}
                mt={0.5}
                gap="5px"
              >
                <Typography>Email:</Typography>
                <Typography>{vehicle.manager?.email}</Typography>
              </Box>

              <Box
                alignItems="center"
                display="flex"
                justifyContent={"flex-start"}
                mt={0.5}
                gap="5px"
              >
                <Typography>Company:</Typography>
                <Typography>{vehicle.manager?.company.name}</Typography>
              </Box>
            </Box>
          }
        >
          <Chip
            size="small"
            sx={{
              padding: "4px",
              borderRadius: "100px",
              display: "flex",
              alignItems: "center",
              backgroundColor: "rgba(0, 0, 0, 0.03)",
              color: theme.palette.text.primary,
              "& span": { width: "84px", pr: "0" },
            }}
            icon={
              <Building06Icon
                size={16}
                color={"currentColor"}
                variant={"stroke"}
              />
            }
            label={
              <Typography
                sx={{
                  fontWeight: "500",
                  fontSize: "12px",
                  letterSpacing: "0.16px",
                  lineHeight: "19.92px",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                }}
                variant="caption"
              >
                {vehicle.manager?.company.name}
              </Typography>
            }
          />
        </HtmlTooltipResource>
      </Box>
    </CePaper>
  );
};

export default ResourceLabelContent;
