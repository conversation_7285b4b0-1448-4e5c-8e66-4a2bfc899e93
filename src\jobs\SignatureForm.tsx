import { But<PERSON>, <PERSON>alogA<PERSON>, FormControl, Stack } from "@mui/material";
import { useFormik } from "formik";
import * as yup from "yup";
import { useTranslation } from "react-i18next";
import { FC, useRef } from "react";
import SignatureCanvas from "react-signature-canvas";
import { string } from "yup";
import { buttonTextTransform } from "src/common/components/custom/customCss";
import { CePaper } from "src/common/components";

type SignatureFormProps = {
  isLoading: boolean;
  handleUpdateSignature: (values: SignatureFormValues) => void;
};

export type SignatureFormValues = {
  signature: string;
};

const SignatureForm: FC<SignatureFormProps> = (props) => {
  const { handleUpdateSignature, isLoading } = props;
  const { t } = useTranslation(["common", "operator"]);
  const signatureCanvasRef = useRef<any>(null);

  const initialValues = {
    signature: "",
  };

  const formik = useFormik<SignatureFormValues>({
    initialValues: initialValues,
    validationSchema: yup.object({
      signature: string().required("required"),
    }),
    onSubmit: (values) => {},
  });

  return (
    <Stack
      component="form"
      spacing={2}
      padding={2}
      marginTop={2}
      noValidate
      onSubmit={formik.handleSubmit}
    >
      <FormControl fullWidth sx={{ marginBottom: 2 }}>
        <CePaper
          sx={{
            width: 300,
            m: "16px auto 0",
          }}
        >
          <SignatureCanvas
            ref={signatureCanvasRef}
            canvasProps={{ width: 300, height: 200 }}
            onEnd={() => {
              formik.setFieldValue(
                "signature",
                signatureCanvasRef.current?.toDataURL()
              );
            }}
          />
        </CePaper>
      </FormControl>

      <DialogActions>
        <Button
          fullWidth
          disabled={isLoading}
          color="primary"
          variant="contained"
          sx={{ m: 2, ...buttonTextTransform }}
          onClick={() => {
            signatureCanvasRef.current.clear();
          }}
        >
          {t("common:clear")}
        </Button>
      </DialogActions>
    </Stack>
  );
};

export default SignatureForm;
