import { FormikValues } from "formik";
import { EVehicleType, Vehicle, VehicleTypes } from "../types";
import {
  CreatePriceListDto,
  PriceList,
  PriceListFormValues,
  PriceListModalFlow,
  PricelistTypeEnum,
  PumpingFeeOptions,
  UpdatePriceListDto,
} from "../types/priceList";

export const turnPriceListFormValuesIntoCreateDto = (
  values: PriceListFormValues
): CreatePriceListDto => {
  const payload = {
    title: values.title || null,
    packageFlatFee: values.packageFlatFee || null,
    packageFlatFeeDuration: values.packageFlatFeeDuration || null,
    pricePerMeterPumped: values.pricePerMeterPumped || null,
    additionalHour: values.additionalHour || null,
    cleaningFee: values.cleaningFee || null,
    pipeInvoicingStartsFrom: values.pipeInvoicingStartsFrom || null,
    pricePerMeterOfFlexiblePipeLength80Mm:
      values.pricePerMeterOfFlexiblePipeLength80Mm || null,
    pricePerMeterOfFlexiblePipeLength90Mm:
      values.pricePerMeterOfFlexiblePipeLength90Mm || null,
    pricePerMeterOfFlexiblePipeLength100Mm:
      values.pricePerMeterOfFlexiblePipeLength100Mm || null,
    pricePerMeterOfRigidPipeLength120Mm:
      values.pricePerMeterOfRigidPipeLength120Mm || null,
    supplyOfTheChemicalSlushie: values.supplyOfTheChemicalSlushie || null,
    barbotine: values.barbotine || null,
    extraCementBagPrice: values.extraCementBagPrice || null,
    pumpTiers: values.pumpTiers || [],
    vehicleId: values.vehicleId || null,
    containerId: values.containerId || null,
    transportRates: values.transportRates || [],
    vehicleTypeId: values.vehicleTypeId || null,
    secondTechnicianHourFee: values.secondTechnicianHourFee || null,
    packageFlatFeeWeekend: values.packageFlatFeeWeekend || null,
    additionalHourWeekend: values.additionalHourWeekend || null,
    packageFlatFeeNight: values.packageFlatFeeNight || null,
    additionalHourNight: values.additionalHourNight || null,
    minimumChargeFlatFee: values.minimumChargeFlatFee || null,
    minimumM3Charged: values.minimumM3Charged || null,
    dayCancellationFee: values.dayCancellationFee || null,
    cancellationFee: values.cancellationFee || null,
    secondTechnicianHourFeeWeekend:
      values.secondTechnicianHourFeeWeekend || null,
    secondTechnicianHourFeeNight: values.secondTechnicianHourFeeNight || null,
    dayContractFee: values.dayContractFee || null,
    dayContractDuration: values.dayContractDuration || null,
    dayContractOvertimeRate: values.dayContractOvertimeRate || null,
  };

  return payload;
};

export const turnPriceListFormValuesIntoUpdateDto = (
  values: PriceListFormValues
): UpdatePriceListDto => {
  const payload: UpdatePriceListDto = {
    priceListId: values.priceListId!,
    title: values.title || null,
    packageFlatFee: values.packageFlatFee || null,
    packageFlatFeeDuration: values.packageFlatFeeDuration || null,
    pricePerMeterPumped: values.pricePerMeterPumped || null,
    additionalHour: values.additionalHour || null,
    cleaningFee: values.cleaningFee || null,
    pipeInvoicingStartsFrom: values.pipeInvoicingStartsFrom || null,
    pricePerMeterOfFlexiblePipeLength80Mm:
      values.pricePerMeterOfFlexiblePipeLength80Mm || null,
    pricePerMeterOfFlexiblePipeLength90Mm:
      values.pricePerMeterOfFlexiblePipeLength90Mm || null,
    pricePerMeterOfFlexiblePipeLength100Mm:
      values.pricePerMeterOfFlexiblePipeLength100Mm || null,
    pricePerMeterOfRigidPipeLength120Mm:
      values.pricePerMeterOfRigidPipeLength120Mm || null,
    supplyOfTheChemicalSlushie: values.supplyOfTheChemicalSlushie || null,
    barbotine: values.barbotine || null,
    extraCementBagPrice: values.extraCementBagPrice || null,
    pumpTiers: values.pumpTiers || [],
    transportRates: values.transportRates || [],
    vehicleId: values.vehicleId || null,
    containerId: values.containerId || null,
    vehicleTypeId: values.vehicleTypeId || null,
    secondTechnicianHourFee: values.secondTechnicianHourFee || null,
    packageFlatFeeWeekend: values.packageFlatFeeWeekend || null,
    additionalHourWeekend: values.additionalHourWeekend || null,
    packageFlatFeeNight: values.packageFlatFeeNight || null,
    additionalHourNight: values.additionalHourNight || null,
    minimumChargeFlatFee: values.minimumChargeFlatFee || null,
    minimumM3Charged: values.minimumM3Charged || null,
    dayCancellationFee: values.dayCancellationFee || null,
    cancellationFee: values.cancellationFee || null,
    secondTechnicianHourFeeWeekend:
      values.secondTechnicianHourFeeWeekend || null,
    secondTechnicianHourFeeNight: values.secondTechnicianHourFeeNight || null,
  };

  return payload;
};

const priceListPayload = (priceList: PriceList) => {
  const payload = {
    ...priceList,
    priceListId: priceList.id,
    title: priceList.title || null,
    packageFlatFee: priceList.packageFlatFee || null,
    packageFlatFeeDuration: priceList.packageFlatFeeDuration || null,
    pricePerMeterPumped: priceList.pricePerMeterPumped || null,
    additionalHour: priceList.additionalHour || null,
    cleaningFee: priceList.cleaningFee || null,
    pipeInvoicingStartsFrom: priceList.pipeInvoicingStartsFrom || null,
    pricePerMeterOfFlexiblePipeLength80Mm:
      priceList.pricePerMeterOfFlexiblePipeLength80Mm || null,
    pricePerMeterOfFlexiblePipeLength90Mm:
      priceList.pricePerMeterOfFlexiblePipeLength90Mm || null,
    pricePerMeterOfFlexiblePipeLength100Mm:
      priceList.pricePerMeterOfFlexiblePipeLength100Mm || null,
    pricePerMeterOfRigidPipeLength120Mm:
      priceList.pricePerMeterOfRigidPipeLength120Mm || null,
    supplyOfTheChemicalSlushie: priceList.supplyOfTheChemicalSlushie || null,
    barbotine: priceList.barbotine || null,
    extraCementBagPrice: priceList.extraCementBagPrice || null,
    pumpTiers: priceList.pumpTiers || [],
    transportRates: priceList.transportRates || [],
    secondTechnicianHourFee: priceList.secondTechnicianHourFee || null,
    vehicleId: priceList.vehicleId || null,
    vehicleTypeId: priceList.vehicleTypeId || null,
    containerId: priceList.containerId || null,
    packageFlatFeeWeekend: priceList.packageFlatFeeWeekend || null,
    additionalHourWeekend: priceList.additionalHourWeekend || null,
    packageFlatFeeNight: priceList.packageFlatFeeNight || null,
    additionalHourNight: priceList.additionalHourNight || null,
    minimumChargeFlatFee: priceList.minimumChargeFlatFee || null,
    minimumM3Charged: priceList.minimumM3Charged || null,
    dayCancellationFee: priceList.dayCancellationFee || null,
    cancellationFee: priceList.cancellationFee || null,
    secondTechnicianHourFeeWeekend:
      priceList.secondTechnicianHourFeeWeekend || null,
    secondTechnicianHourFeeNight:
      priceList.secondTechnicianHourFeeNight || null,
  };
  return payload;
};

export const turnPriceListIntoFormValues = (
  priceList: PriceList,
  flow: PriceListModalFlow
): PriceListFormValues => {
  const priceListFormValues = priceListPayload(priceList);
  const payload: PriceListFormValues = {
    ...priceListFormValues,

    isNightContract: Boolean(priceList.packageFlatFeeNight),
    isWeekendContract: Boolean(priceList.packageFlatFeeWeekend),
    flow,
  };
  return payload;
};

export const turnPriceListRowIntoIntoUpdateDto = (
  priceListRow: PriceList
): UpdatePriceListDto => {
  const payload: UpdatePriceListDto = priceListPayload(priceListRow);
  return payload;
};

export const getInitialPumpFeeOption = (
  flow: PriceListModalFlow,
  initialFormValues: PriceListFormValues
): PumpingFeeOptions => {
  if (PricelistTypeEnum.UDPATE_VARIANT || PricelistTypeEnum.UPDATE_DEFAULT) {
    return {
      tierPricing: Boolean(!initialFormValues.pricePerMeterPumped),
      pricePerMeter: Boolean(initialFormValues.pricePerMeterPumped),
    };
  }

  return { tierPricing: true, pricePerMeter: false };
};

export const isPumpOrCityPump = (
  vehicles: Vehicle[],
  vehicleId: number | null
) => {
  const chosenVehicle = vehicles.find((vehicle) => vehicle.id === vehicleId);
  return (
    chosenVehicle?.type === EVehicleType.Pump ||
    chosenVehicle?.type === EVehicleType.CityPump
  );
};

export const updateCurrentMinAndPrevMax = async (
  event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  currentMinField: string,
  prevMaxField: string,
  index: number,
  array: Array<number | any>,
  setFieldValue: FormikValues["setFieldValue"]
) => {
  const value = parseInt(event.target.value, 10);
  const intValue = index === 0 ? Math.max(value, 0) : Math.max(value, 1);

  await setFieldValue(currentMinField, intValue ?? null);

  const previousTierIndex = index - 1;
  if (array[previousTierIndex]) {
    const previousMaxValue = intValue > 1 ? intValue - 1 : null;
    await setFieldValue(prevMaxField, previousMaxValue);
  }
};

export const updateCurrentMaxAndNextMin = async (
  event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  currentMaxField: string,
  nextMinField: string,
  index: number,
  array: Array<number | any>,
  setFieldValue: FormikValues["setFieldValue"]
) => {
  const value = parseInt(event.target.value, 10);
  const intValue = Math.max(value, 1);

  await setFieldValue(currentMaxField, intValue || null);

  const nextTierIndex = index + 1;
  if (array[nextTierIndex]) {
    const nextMinimumValue = intValue ? intValue + 1 : null;
    await setFieldValue(nextMinField, nextMinimumValue);
  }
};

export const getTierHelperText = (
  tierIndex: number,
  minValue: number | null,
  maxValue: number | null
) => {
  if (minValue && maxValue && minValue >= maxValue) return null;
  if (tierIndex === 0) {
    if (maxValue) {
      return `0 m³ - ${maxValue} m³`;
    }
    return null;
  }
  if (minValue && maxValue) {
    return `${minValue} m³ - ${maxValue} m³`;
  }
  return null;
};

export const getPackageFlatHelperText = (
  flatFeeDuration: number | null,
  withinFlatFeeDuration: boolean
) => {
  if (flatFeeDuration != null && flatFeeDuration >= 1) {
    if (withinFlatFeeDuration) {
      return `${flatFeeDuration} hours`;
    }
    return `beyond ${flatFeeDuration} hours`;
  }
  return null;
};
