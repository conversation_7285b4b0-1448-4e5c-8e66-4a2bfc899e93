import { useWindowSize } from "@react-hook/window-size";
import { usePriceLists } from "src/common/api/priceList";
import { useState } from "react";
import { useLocation, useParams } from "react-router-dom";
import { PriceListDatagrid } from "../datagrid/PriceListDatagrid";
import { CePaper } from "src/common/components";
import usePersistentGridState from "src/common/utils/gridState";

export const PriceListDetails = () => {
  const [, height] = useWindowSize();
  const { id } = useParams<{ id: string }>();
  const containerId = Number(id);
  const location = useLocation();
  const localStorageKey = location.pathname.slice(1);
  const [gridState, updateGridStatePart] = usePersistentGridState(
    localStorageKey,
    1,
    20,
  );
  const {
    data: allPriceLists,
    isLoading: isLoadingPriceLists,
    refetch: refetchPricelists,
  } = usePriceLists(
    {
      expressions: [],
      sortModel: [],
      limit: gridState.pageSize,
      offset: (gridState.page - 1) * gridState.pageSize,
      parentId: containerId,
    },
    true
  );

  const handlePageChange = (newPage: number) => {
    updateGridStatePart("page", newPage + 1);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    updateGridStatePart("pageSize", newPageSize);
  };

  return (
    <CePaper sx={{ height: `${height - 100}px`, p: 2 }}>
      <PriceListDatagrid
        data={allPriceLists?.data || []}
        isFetchingPriceLists={isLoadingPriceLists}
        refetchPriceLists={refetchPricelists}
        shouldRenderRefreshButton
        shouldRenderAddButton
        shouldRenderEditActionsColumn
        gridState={gridState}
        total={allPriceLists?.totalCount || 0}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        updateGridStatePart={updateGridStatePart}
      />
    </CePaper>
  );
};
