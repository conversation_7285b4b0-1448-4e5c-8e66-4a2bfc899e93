import { atom } from "recoil";
import { PossibleTheme } from "../types";

const localStorageKey = "current_theme";

export const themeState = atom<PossibleTheme>({
  key: "themeState",
  default: "light",
  effects: [
    // just one effect to set selected theme into local storage
    ({ setSelf, onSet }) => {
      const savedValue = localStorage.getItem(localStorageKey);
      if (savedValue != null) {
        setSelf(JSON.parse(savedValue));
      }

      onSet((newValue, _, isReset) => {
        isReset
          ? localStorage.removeItem(localStorageKey)
          : localStorage.setItem(localStorageKey, JSON.stringify(newValue));
      });
    },
  ],
});
