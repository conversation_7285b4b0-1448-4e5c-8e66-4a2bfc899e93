import { Box, Typography, Button } from "@mui/material";
import { FC } from "react";
import { useTranslation } from "react-i18next";
import usePagination from "@mui/material/usePagination";
import { ArrowLeft01Icon, ArrowRight01Icon } from "@hugeicons/react";
import { CeButton } from "./company/CeButton";

interface VehicleListProps {
  page: number;
  total: number;
  handleChangePage: (event: React.ChangeEvent<unknown>, value: number) => void;
}

export const VehicleListPagination: FC<VehicleListProps> = ({
  page,
  total,
  handleChangePage,
}) => {
  const { t } = useTranslation("common");
  const { items } = usePagination({
    count: total,
    page,
    onChange: handleChangePage,
  });

  const isFirstPage = page === 1;
  const isLastPage = page === total;

  const handlePageChange = (
    event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    if (value > 0 && value <= total) {
      handleChangePage(event, value);
    }
  };

  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "center",
        marginTop: 4,
      }}
    >
      {items.map(({ page, type, selected }, index) => {
        let children = null;

        if (type === "start-ellipsis" || type === "end-ellipsis") {
          children = "…";
        } else if (type === "page") {
          children = (
            <CeButton
              key={index}
              type="button"
              variant={selected ? "contained" : "text"}
              size="medium"
              sx={{ minWidth: 31 }}
              onClick={(e) => handlePageChange(e, page ?? 0)}
            >
              {page}
            </CeButton>
          );
        } else if (type === "previous") {
          children = (
            <CeButton
              key={index}
              type="button"
              variant="outlined"
              size="medium"
              onClick={(e) => handlePageChange(e, page ?? 0)}
              startIcon={
                <ArrowLeft01Icon
                  fontSize={24}
                  variant="stroke"
                  color="currentColor"
                />
              }
              disabled={isFirstPage}
            >
              <Typography textTransform={"none"} variant="button">
                {t("back")}
              </Typography>
            </CeButton>
          );
        } else if (type === "next") {
          children = (
            <CeButton
              key={index}
              type="button"
              variant="outlined"
              size="medium"
              onClick={(e) => handlePageChange(e, page ?? 1)}
              endIcon={
                <ArrowRight01Icon
                  fontSize={24}
                  variant="stroke"
                  color="currentColor"
                />
              }
              disabled={isLastPage}
            >
              <Typography textTransform={"none"} variant="button">
                {t("next")}
              </Typography>
            </CeButton>
          );
        }

        return (
          <Box key={index} sx={{ marginLeft: 1 }}>
            {children}
          </Box>
        );
      })}
    </Box>
  );
};
