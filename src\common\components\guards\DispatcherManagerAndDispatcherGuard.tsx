import type { FC, ReactNode } from "react";
import { isAuthenticated } from "src/common/api/auth";
import { Role } from "src/common/types";

interface DispatcherManagerAndDispatcherGuardProps {
  children: ReactNode;
}
export const DispatcherManagerAndDispatcherGuard: FC<
  DispatcherManagerAndDispatcherGuardProps
> = ({ children }) => {
  const { role } = isAuthenticated();

  if (role === Role.DISPATCHER_MANAGER || role === Role.DISPATCHER) {
    return <>{children}</>;
  }

  return null;
};
