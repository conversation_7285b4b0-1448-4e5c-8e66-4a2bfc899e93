import { atom } from "recoil";
import { DeleteVehicleTypeModalValues, VehicleTypeFormValues } from "../types/vehicleType";
import { VEHICLE_TYPE_DELETE_DEFAULT, VEHICLE_TYPE_FORM_VALUES } from "../constants/vehicleType";

export const vehicleTypeFormValuesState = atom<VehicleTypeFormValues>({
  key: "vehicleTypeFormValuesState",
  default: VEHICLE_TYPE_FORM_VALUES,
});
  
export const vehicleTypeDeleteValuesState = atom<DeleteVehicleTypeModalValues>({
    key: "vehicleTypeDeleteValuesState",
    default: VEHICLE_TYPE_DELETE_DEFAULT,
});