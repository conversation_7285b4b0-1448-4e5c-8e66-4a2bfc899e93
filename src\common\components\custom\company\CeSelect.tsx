import {
  styled,
  Select,
  SelectProps,
  SelectChangeEvent,
  OutlinedInput,
} from "@mui/material";

interface CeSelectProps<T> extends SelectProps<T> {
  onChange?: (event: SelectChangeEvent<T>, child: React.ReactNode) => void;
}

const StyledOutlinedInput = styled(OutlinedInput)(({ theme }) => ({
  borderRadius: "8px",
  "& fieldset": {
    borderWidth: "2px",
    borderColor: `${theme.palette.action} !important`,
  },
  "&:hover fieldset": {
    borderColor: `${theme.palette.action} !important`,
  },
  "&.Mui-focused fieldset": {
    borderColor: `${theme.palette.action} !important`,
  },
  "&.Mui-disabled fieldset": {
    borderColor:
      theme.palette.mode === "light"
        ? theme.palette.action.disabled
        : theme.palette.action.disabled,
  },
}));

export const CeSelect = <T extends string>({
  className,
  sx,
  onChange,
  ...props
}: CeSelectProps<T>) => {
  return (
    <Select
      labelId={props.labelId}
      input={<StyledOutlinedInput label={props.label} />}
      value={props.value}
      onChange={(event) => {
        if (onChange) {
          onChange(event as SelectChangeEvent<T>, null);
        }
      }}
      sx={sx}
      {...props}
    >
      {props.children}
    </Select>
  );
};
