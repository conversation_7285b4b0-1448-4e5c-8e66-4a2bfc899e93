import axios, { AxiosError } from "axios";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { GetUsersDto, UpdateUserDto, User, UsersWithCount } from "../types";
import { processApiError, processApiSuccess } from "../utils/errors";
import { wrapEachFieldIntoDoubleQuotes } from "../utils";

const backendUrl = process.env.REACT_APP_API_URL;

// GET ONE User
export const getUser = async (userId?: number) => {
  if (!userId) {
    throw new Error("the user ID was not provided");
  }
  return axios
    .get(`${backendUrl}/users/${userId}`, { withCredentials: true })
    .then((response) => response.data);
};
export const useUser = (userId?: number, enabled: boolean = true) => {
  return useQuery<User, AxiosError | Error>(
    ["user", userId],
    () => getUser(userId),
    {
      onError: (err) => processApiError("Unable to fetch user", err),
      enabled,
    }
  );
};

// GET MANY Users
export const getUsers = async (attr: GetUsersDto) => {
  return axios
    .post(`${backendUrl}/users/get`, attr, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const useUsers = (attrs: GetUsersDto, enabled: boolean = true) => {
  const formattedSortModel = wrapEachFieldIntoDoubleQuotes(attrs.sortModel);
  const formattedAttrs = { ...attrs, sortModel: formattedSortModel };
  return useQuery<UsersWithCount, AxiosError | Error>(
    ["users", formattedAttrs],
    () => getUsers(formattedAttrs),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch users", err),
      enabled,
    }
  );
};

export const handleUpdateUser = (updateUserDto: UpdateUserDto) => {
  const { userId, ...user } = updateUserDto;
  if (!userId) {
    throw new Error("the user ID was not provided");
  }
  return axios
    .patch(`${backendUrl}/users/${userId}`, user, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useUpdateUser = () => {
  const queryClient = useQueryClient();
  return useMutation<User, AxiosError | Error, UpdateUserDto, () => void>(
    (updateUserArgs: UpdateUserDto) => handleUpdateUser(updateUserArgs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("user");
        queryClient.invalidateQueries("users");
        processApiSuccess("User successfully updated");
      },
      onError: (err) => {
        processApiError("Unable to update manager", err);
      },
    }
  );
};

export const getAllDispatchers = async (attr: GetUsersDto) => {
  return axios
    .get(`${backendUrl}/users/dispatchers`, {
      withCredentials: true,
      params: attr,
    })
    .then((response) => response.data);
};

export const useDispatchers = (attrs: GetUsersDto, enabled: boolean = true) => {
  return useQuery<UsersWithCount, AxiosError | Error>(
    ["dispatchers", attrs],
    () => getAllDispatchers(attrs),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch dispatchers", err),
      enabled,
    }
  );
};
