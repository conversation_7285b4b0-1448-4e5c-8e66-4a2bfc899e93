import { CancelContractValues, ContractFormValues } from "../types";
import { PRICELIST_FORM_VALUES_DEFAULT } from "./priceList";

export const CONTRACT_DEFAULT_FORM_VALUES: ContractFormValues = {
  partner: null,
  pricelist: {
    ...PRICELIST_FORM_VALUES_DEFAULT,
    useCancellationFee: false,
    useLateCancellationFee: false,
    standardFeeAsDayContractFee: false,
    lateFeeAsDayContractFee: false,
  },
  flow: null,
  status: null,
  vehicle: null,
  startDate: null,
  endDate: null,
};

export const CANCEL_CONTRACT_DEFAULT_FORM_VALUES: CancelContractValues = {
  startDate: null,
  endDate: null,
  flow: null,
  contractId: null,
  comment: null,
};
