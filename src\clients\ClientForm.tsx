import { DialogActions, Grid, Stack, Typography } from "@mui/material";
import { getIn, useFormik } from "formik";
import { EMAIL_REGEX_VALIDATION } from "src/common/constants";
import { ClientFormValues } from "src/common/types/client";
import { ClientProps } from "./ClientActionModal";
import { turnClientDetailsIntoDto } from "src/common/utils";
import { DEFAULT_CLIENT_FORM_VALUES } from "src/common/constants/client";
import * as yup from "yup";
import { useTranslation } from "react-i18next";
import {
  CeTextField,
  CeButton,
  CeMuiPhoneNumber,
} from "src/common/components/";

interface ClientFormTypes extends ClientProps {
  handleCloseClientActionModal: () => void;
}
export const ClientForm = ({
  initialFormValues,
  handleCreateClient,
  handleUpdateClient,
  setClientFormValues,
  handleCloseClientActionModal,
  isLoading,
}: ClientFormTypes) => {
  const { t } = useTranslation(["common", "dispatcher", "manager"]);
  const formik = useFormik<ClientFormValues>({
    enableReinitialize: true,
    initialValues: initialFormValues,
    validationSchema: yup.object({
      name: yup
        .string()
        .nullable()
        .max(100, "client-name-exceeds")
        .required("Required"),
      lastName: yup
        .string()
        .nullable()
        .max(100, "client-lastName-exceeds")
        .required("Required"),
      email: yup
        .string()
        .email()
        .nullable()
        .max(100, "client-email-exceeds")
        .required("Required")
        .matches(EMAIL_REGEX_VALIDATION, "common:invalid-email-format"),
      phoneNumber: yup
        .string()
        .nullable()
        .max(100, "client-phone-number-exceeds")
        .required("Required"),
      companyName: yup
        .string()
        .nullable()
        .max(100, "client-company-name-exceeds")
        .required("Required"),
      companyVatNumber: yup
        .string()
        .nullable()
        .max(100, "client-companyVat-number-exceeds")
        .required("Required"),
    }),

    onSubmit: async (values) => {
      const formValues = { ...values };

      const payload = turnClientDetailsIntoDto(formValues);
      if (formValues.flow === "Create") {
        await handleCreateClient(payload);
      }
      if (formValues.flow === "Update" && formValues.id) {
        const updatePayload = { ...payload, id: formValues.id };
        await handleUpdateClient(updatePayload);
      }
      setClientFormValues(DEFAULT_CLIENT_FORM_VALUES);
    },
  });

  return (
    <Stack component="form" noValidate onSubmit={formik.handleSubmit}>
      <Grid
        container
        gap={2}
        justifyContent="center"
        sx={{ m: "10px 0" }}
        width="100%"
      >
        <Grid item xs={5}>
          <CeTextField
            fullWidth
            id="name"
            name="name"
            label={t("common:first-name")}
            size="small"
            value={formik.values?.name || ""}
            onChange={formik.handleChange}
            InputLabelProps={{ shrink: true }}
            error={Boolean(
              getIn(formik.touched, "name") && getIn(formik.errors, "name")
            )}
            helperText={
              getIn(formik.touched, "name") && getIn(formik.errors, "name")
            }
            disabled={isLoading}
            required
            variant="outlined"
          />
        </Grid>
        <Grid item xs={5}>
          <CeTextField
            fullWidth
            id="lastName"
            name="lastName"
            label={t("common:last-name")}
            size="small"
            value={formik.values?.lastName || ""}
            onChange={formik.handleChange}
            InputLabelProps={{ shrink: true }}
            error={Boolean(
              getIn(formik.touched, "lastName") &&
                getIn(formik.errors, "lastName")
            )}
            helperText={
              getIn(formik.touched, "lastName") &&
              getIn(formik.errors, "lastName")
            }
            disabled={isLoading}
            required
          />
        </Grid>

        <Grid item xs={5}>
          <CeTextField
            fullWidth
            id="email"
            name="email"
            label={t("common:email")}
            type="email"
            size="small"
            value={formik.values?.email || ""}
            onChange={formik.handleChange}
            InputLabelProps={{ shrink: true }}
            error={Boolean(
              getIn(formik.touched, "email") && getIn(formik.errors, "email")
            )}
            helperText={
              getIn(formik.touched, "email") && getIn(formik.errors, "email")
            }
            disabled={isLoading}
            required
          />
        </Grid>

        <Grid item xs={5}>
          <CeMuiPhoneNumber
            fullWidth
            defaultCountry={"be"}
            id="phoneNumber"
            name="phoneNumber"
            label={t("common:phone-number")}
            size="small"
            required
            InputLabelProps={{ shrink: true }}
            value={formik.values?.phoneNumber || ""}
            error={Boolean(
              getIn(formik.touched, "phoneNumber") &&
                getIn(formik.errors, "phoneNumber")
            )}
            helperText={
              getIn(formik.touched, "phoneNumber") &&
              getIn(formik.errors, "phoneNumber")
            }
            onChange={(value) => formik.setFieldValue("phoneNumber", value)}
            onBlur={() => formik.setFieldTouched("phoneNumber", true)}
            disabled={isLoading}
            variant="outlined"
          />
        </Grid>

        <Grid item xs={5}>
          <CeTextField
            fullWidth
            id="companyName"
            name="companyName"
            label={t("common:company-name")}
            size="small"
            required
            value={formik.values?.companyName || ""}
            InputLabelProps={{ shrink: true, size: "small" }}
            onChange={formik.handleChange}
            error={Boolean(
              getIn(formik.touched, "companyName") &&
                getIn(formik.errors, "companyName")
            )}
            helperText={
              getIn(formik.touched, "companyName") &&
              getIn(formik.errors, "companyName")
            }
            disabled={isLoading}
          />
        </Grid>

        <Grid item xs={5}>
          <CeTextField
            fullWidth
            id="companyVatNumber"
            name="companyVatNumber"
            label={t("common:company-vat-number")}
            size="small"
            required
            error={Boolean(
              getIn(formik.touched, "companyVatNumber") &&
                getIn(formik.errors, "companyVatNumber")
            )}
            helperText={
              getIn(formik.touched, "companyVatNumber") &&
              getIn(formik.errors, "companyVatNumber")
            }
            value={formik.values.companyVatNumber || ""}
            onChange={formik.handleChange}
            InputLabelProps={{ shrink: true }}
            disabled={isLoading}
          />
        </Grid>
        <Grid item xs={5} />
        <Grid item xs={5} display="flex" justifyContent="flex-end" gap={"10px"}>
          <DialogActions>
            <CeButton
              type="submit"
              variant="contained"
              color="primary"
              size="large"
              disabled={isLoading}
            >
              <Typography
                sx={{
                  fontSize: "16px",
                  letterSpacing: "0.4px",
                }}
              >
                {t(
                  `common:${
                    formik.values.flow === "Create" ? "create" : "update"
                  }-client`
                )}
              </Typography>
            </CeButton>
            <CeButton
              variant="text"
              color="primary"
              size="large"
              onClick={handleCloseClientActionModal}
              disabled={isLoading}
            >
              <Typography
                sx={{
                  fontSize: "16px",
                  letterSpacing: "0.4px",
                }}
              >
                {t("common:cancel")}
              </Typography>
            </CeButton>
          </DialogActions>
        </Grid>
      </Grid>
    </Stack>
  );
};
