import axios, { AxiosError } from "axios";
import {
  Create<PERSON>anagerDto,
  Delete<PERSON><PERSON>ger<PERSON><PERSON>,
  Manager,
  UpdateManagerDto,
} from "../types";
import { useMutation, useQueryClient } from "react-query";
import { processApiError, processApiSuccess } from "../utils/errors";

const backendUrl = process.env.REACT_APP_API_URL;

export const createNewManager = (attrs: CreateManagerDto) => {
  return axios
    .post(`${backendUrl}/users`, attrs, { withCredentials: true })
    .then((response) => response.data);
};

export const useCreateNewManager = () => {
  const queryClient = useQueryClient();
  return useMutation<Manager, AxiosError | Error, CreateManagerDto, () => void>(
    (a: CreateManagerDto) => createNewManager(a),
    {
      onSuccess: (newManager) => {
        queryClient.invalidateQueries("user");
        queryClient.invalidateQueries("users");

        // maybe in the future we can include email invitations..
        processApiSuccess(`Invite email sent to ${newManager?.email}`);
      },
      onError: (err) => processApiError("Unable to create manager", err),
    }
  );
};

export const handleUpdateManager = (updateManagerArgs: UpdateManagerDto) => {
  const { managerId, ...manager } = updateManagerArgs;
  if (!managerId) {
    throw new Error("the manager ID was not provided");
  }
  return axios
    .patch(`${backendUrl}/users/${managerId}`, manager, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useUpdateManager = () => {
  const queryClient = useQueryClient();
  return useMutation<Manager, AxiosError | Error, UpdateManagerDto, () => void>(
    (updateManagerArgs: UpdateManagerDto) =>
      handleUpdateManager(updateManagerArgs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("user");
        queryClient.invalidateQueries("users");
      },
      onError: (err) => {
        processApiError("Unable to update manager", err);
      },
    }
  );
};

export const deleteManager = (deleteManagerDto: DeleteManagerDto) => {
  const { managerId } = deleteManagerDto;
  if (!managerId) {
    throw new Error("the manager ID was not provided");
  }
  return axios
    .delete(`${backendUrl}/users/${managerId}`, { withCredentials: true })
    .then((response) => response.data);
};

export const useDeleteManager = () => {
  const queryClient = useQueryClient();
  return useMutation<Manager, AxiosError | Error, DeleteManagerDto, () => void>(
    (deleteManagerDto: DeleteManagerDto) => deleteManager(deleteManagerDto),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("user");
        queryClient.invalidateQueries("users");
      },
      onError: (err) => processApiError("Unable to delete manager", err),
    }
  );
};
