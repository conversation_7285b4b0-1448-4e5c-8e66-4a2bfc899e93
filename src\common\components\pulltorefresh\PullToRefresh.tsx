import React, { useState, useRef, useCallback } from "react";
import { CircularProgress, Box } from "@mui/material";

interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  children: React.ReactNode;
}

export const PullToRefresh: React.FC<PullToRefreshProps> = ({ onRefresh, children }) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const startY = useRef<number | null>(null);
  const threshold = 50;

  const handleTouchStart = (e: React.TouchEvent) => {
    if (window.scrollY === 0) {
      startY.current = e.touches[0].clientY;
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (startY.current !== null && e.touches[0].clientY - startY.current > threshold) {
      if (!isRefreshing) {
        setIsRefreshing(true);
      }
    }
  };

  const handleTouchEnd = useCallback(async () => {
    if (isRefreshing) {
      await onRefresh();
      setIsRefreshing(false);
    }
    startY.current = null;
  }, [isRefreshing, onRefresh]);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (window.scrollY === 0 && e.button === 0) {
      startY.current = e.clientY;
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (startY.current !== null && e.buttons === 1 && e.clientY - startY.current > threshold) {
      if (!isRefreshing) {
        setIsRefreshing(true);
      }
    }
  };

  const handleMouseUp = useCallback(async () => {
    if (isRefreshing) {
      await onRefresh();
      setIsRefreshing(false);
    }
    startY.current = null;
  }, [isRefreshing, onRefresh]);

  return (
    <Box
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      sx={{ position: "relative", overflow: "hidden", userSelect: "none" }}
    >
      {isRefreshing && (
        <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", py: 2 }}>
          <CircularProgress size={24} />
        </Box>
      )}
      {children}
    </Box>
  );
};
