import { Button, DialogActions } from "@mui/material";
import { format, parseISO } from "date-fns";
import { MainModal } from "src/common/components";
import { JobFormValues } from "src/common/types";
import { useTranslation } from "react-i18next";
import { convertDecimalHoursToTime } from "src/common/utils/formatDate";
import { buttonTextTransform } from "src/common/components/custom/customCss";

interface EditWorkTimeModalProps {
  onHandleOverwriteTime: (endTimeValue: string) => void;
  handleCloseModal: () => void;
  isModalOpen: boolean;
  dateFrom: string;
  jobInfo: JobFormValues | undefined;
}

export const EditWorkTimeModal = ({
  handleCloseModal,
  isModalOpen,
  onHandleOverwriteTime,
  dateFrom,
  jobInfo,
}: EditWorkTimeModalProps) => {
  const { t, i18n } = useTranslation(["manager,common"]);

  const calcjobTimeByFlowRate = () => {
    const startDate = parseISO(dateFrom);
    if (jobInfo?.amountOfConcrete && jobInfo?.flowRate) {
      const jobDuration = jobInfo.amountOfConcrete / jobInfo.flowRate;
      const hoursToAdd = jobDuration * 60 * 60 * 1000;
      const endDate = new Date(startDate.getTime() + hoursToAdd).toISOString();
      return { endDate, jobDuration };
    }
  };

  const jobTimeByFlowRate = calcjobTimeByFlowRate();

  const endTime =
    jobTimeByFlowRate?.endDate &&
    format(parseISO(jobTimeByFlowRate?.endDate), "HH:mm");

  const timeAndMinutes = convertDecimalHoursToTime(
    jobTimeByFlowRate?.jobDuration
  );

  const onConfirmModal = () => {
    if (jobTimeByFlowRate?.endDate) {
      onHandleOverwriteTime(jobTimeByFlowRate.endDate);
    }
    handleCloseModal();
  };
  return (
    <MainModal
      title={t("common:confirm-action-needed")}
      helperText={t("common:overwrite-job-hours", {
        duration: timeAndMinutes,
        time: endTime,
      })}
      isOpen={isModalOpen}
      handleClose={handleCloseModal}
      maxWidth="xs"
      titlePosition="center"
      helperTextProps={{ textAlign: "center", px: "15px" }}
    >
      <DialogActions
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "stretch",
          gap: "10px",
          "& > button": {
            py: "7px",
            mx: "0 !important",
            borderRadius: "8px",
          },
        }}
      >
        <Button
          onClick={onConfirmModal}
          variant="contained"
          sx={buttonTextTransform}
        >
          {t("common:yes")}
        </Button>
        <Button
          onClick={handleCloseModal}
          variant="text"
          sx={{ mx: "0", ...buttonTextTransform }}
        >
          {t("common:no")}
        </Button>
      </DialogActions>
    </MainModal>
  );
};
