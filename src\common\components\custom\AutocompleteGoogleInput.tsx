import LocationOnIcon from "@mui/icons-material/LocationOn";
import Autocomplete from "@mui/material/Autocomplete";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import { TextField } from "@mui/material";
import Typography from "@mui/material/Typography";
import { debounce } from "@mui/material/utils";
import parse from "autosuggest-highlight/parse";
import { useField } from "formik";
import { FC, useEffect, useMemo, useRef, useState } from "react";
import { CeTextField } from "./company/CeTextField";
import { apiWarningMessage } from "src/common/utils/reservationDetails";

const apiKey = process.env.REACT_APP_GM_API_KEY;
apiWarningMessage(apiKey);

const loadScript = (src: string, position: HTMLElement | null, id: string) => {
  if (!position) {
    return;
  }

  const script = document.createElement("script");
  script.setAttribute("async", "");
  script.setAttribute("id", id);
  script.src = src;
  position.appendChild(script);
};

const autocompleteService = { current: null };

interface MainTextMatchedSubstrings {
  offset: number;
  length: number;
}
interface StructuredFormatting {
  main_text: string;
  secondary_text: string;
  main_text_matched_substrings?: readonly MainTextMatchedSubstrings[];
}
interface PlaceType {
  description: string;
  structured_formatting: StructuredFormatting;
  place_id: string;
}

interface AddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

interface PlaceDetails {
  address_components: AddressComponent[];
  html_attributions: any[];
  geometry: {
    location: {
      lat: () => number;
      lng: () => number;
    };
  };
}

interface Props {
  id: string;
  label: string;
  name: string;
  existingInputValue?: string | null;
  onAddressChange?: (data: {
    countryCode: string;
    postalCode?: string;
    siteAddress: string;
    city: string;
    lat: number | null;
    lng: number | null;
  }) => void;
  variant: "outlined";
  disabled?: boolean;
}

declare const google: any;

const AutocompleteInput: FC<Props> = (props) => {
  const {
    id,
    label,
    name,
    existingInputValue,
    onAddressChange,
    variant,
    disabled = false,
  } = props;
  const [field, meta, helpers] = useField(name);
  const [value, setValue] = useState<PlaceType | null>(null);
  const [inputValue, setInputValue] = useState("");
  const [options, setOptions] = useState<readonly PlaceType[]>([]);
  const loaded = useRef(false);
  const [placesService, setPlacesService] = useState<any>(null);

  if (typeof window !== "undefined" && !loaded.current) {
    if (!document.querySelector("#google-maps")) {
      loadScript(
        `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places,geometry`,
        document.querySelector("head"),
        "google-maps"
      );
    }

    loaded.current = true;
  }

  const fetch = useMemo(
    () =>
      debounce(
        (
          request: { input: string },
          callback: (results?: readonly PlaceType[]) => void
        ) => {
          (autocompleteService.current as any).getPlacePredictions(
            request,
            callback
          );
        },
        400
      ),
    []
  );

  useEffect(() => {
    let active = true;

    if (
      !autocompleteService.current &&
      (window as any).google &&
      (window as any).google.maps &&
      (window as any).google.maps.places
    ) {
      autocompleteService.current = new (
        window as any
      ).google.maps.places.AutocompleteService();

      setPlacesService(
        new google.maps.places.PlacesService(document.createElement("div"))
      );
    }
    if (!autocompleteService.current) {
      return undefined;
    }

    if (inputValue === "") {
      setOptions(value ? [value] : []);
      return undefined;
    }

    fetch({ input: inputValue }, (results?: readonly PlaceType[]) => {
      if (active) {
        let newOptions: readonly PlaceType[] = [];

        if (value) {
          newOptions = [value];
        }

        if (results) {
          newOptions = [...newOptions, ...results];
        }

        setOptions(newOptions);
      }
    });

    return () => {
      active = false;
    };
  }, [value, inputValue, fetch]);

  useEffect(() => {
    if (existingInputValue) {
      setValue({
        description: existingInputValue,
        structured_formatting: {
          main_text: "",
          secondary_text: "",
        },
        place_id: "",
      });
      setInputValue(existingInputValue);
    }
  }, [existingInputValue]);

  return (
    <Autocomplete
      fullWidth
      autoComplete
      filterSelectedOptions
      includeInputInList
      filterOptions={(x) => x}
      getOptionLabel={(option) =>
        typeof option === "string" ? option : option.description
      }
      id={id}
      options={options}
      renderInput={(params) => {
        const { inputProps, ...restParams } = params;
        return (
          <CeTextField
            variant={variant}
            {...restParams}
            onBlur={field.onBlur}
            fullWidth
            size="small"
            helperText={meta.touched && meta.error}
            error={meta.touched && Boolean(meta.error)}
            label={label}
            inputProps={{
              ...inputProps,
            }}
          />
        );
      }}
      renderOption={(props, option) => {
        const matches =
          option.structured_formatting.main_text_matched_substrings || [];

        const parts = parse(
          option.structured_formatting.main_text,
          matches.map((match: any) => [
            match.offset,
            match.offset + match.length,
          ])
        );

        return (
          <li {...props}>
            <Grid container alignItems="center">
              <Grid item sx={{ display: "flex", width: 44 }}>
                <LocationOnIcon sx={{ color: "text.secondary" }} />
              </Grid>
              <Grid
                item
                sx={{ width: "calc(100% - 44px)", wordWrap: "break-word" }}
              >
                {parts.map((part: any, index: number) => (
                  <Box
                    key={index}
                    component="span"
                    sx={{ fontWeight: part.highlight ? "bold" : "regular" }}
                  >
                    {part.text}
                  </Box>
                ))}
                <Typography color="text.secondary" variant="body2">
                  {option.structured_formatting.secondary_text}
                </Typography>
              </Grid>
            </Grid>
          </li>
        );
      }}
      value={value}
      onChange={(event: any, newValue: PlaceType | null) => {
        helpers.setValue(newValue?.description || "");
        setValue(newValue);
        if (!newValue && onAddressChange) {
          helpers.setValue("");
          onAddressChange({
            countryCode: "",
            postalCode: "",
            siteAddress: "",
            city: "",
            lat: null,
            lng: null,
          });
          return;
        }

        if (newValue && onAddressChange) {
          const request = {
            placeId: newValue.place_id,
            fields: ["address_components", "geometry"],
          };

          const service =
            placesService ||
            new google.maps.places.PlacesService(document.createElement("div"));
          service.getDetails(request, (place: PlaceDetails, status: string) => {
            if (status === google.maps.places.PlacesServiceStatus.OK) {
              const countryComponent = place.address_components?.find(
                (component: AddressComponent) =>
                  component.types.includes("country")
              );
              const postalCodeComponent = place.address_components?.find(
                (component: AddressComponent) =>
                  component.types.includes("postal_code")
              );
              const cityComponent = place.address_components?.find(
                (component: AddressComponent) =>
                  component.types.includes("locality")
              );
              const streetNameComponent = place.address_components?.find(
                (component: AddressComponent) =>
                  component.types.includes("route")
              );
              const streetNumberComponent = place.address_components?.find(
                (component: AddressComponent) =>
                  component.types.includes("street_number")
              );

              if (!streetNumberComponent) {
                helpers.setError("Street number is required.");
                return;
              } else {
                helpers.setError("");
              }

              if (countryComponent) {
                const countryCode = countryComponent.short_name;
                const postalCode = postalCodeComponent
                  ? postalCodeComponent.long_name
                  : undefined;
                const city = cityComponent ? cityComponent.long_name : "";
                const streetName = streetNameComponent
                  ? streetNameComponent.long_name
                  : "";
                const streetNumber = streetNumberComponent
                  ? streetNumberComponent.long_name
                  : "";
                const siteAddress = `${streetName} ${streetNumber}`.trim();
                const lat = place.geometry.location.lat();
                const lng = place.geometry.location.lng();
                onAddressChange({
                  countryCode,
                  postalCode,
                  city,
                  lat,
                  lng,
                  siteAddress,
                });
              }
            }
          });
        }
      }}
      onInputChange={(event, newInputValue) => {
        setInputValue(newInputValue);
      }}
      disabled={disabled}
    />
  );
};

export default AutocompleteInput;
