import React, { CSSProperties, FC } from 'react'
import Typography from '@mui/material/Typography'
import Tooltip from '@mui/material/Tooltip'
import { TypographyVariant } from '@mui/material'

interface EllipsisTypographyProps {
  text: string
  maxCharacters: number
  variant: TypographyVariant
  customSx?: CSSProperties
}

const EllipsisTypography: FC<EllipsisTypographyProps> = ({ text, maxCharacters, variant, customSx }) => {
  const truncatedText = text.length > maxCharacters ? `${text.slice(0, maxCharacters)}...` : text

  return (
    <Tooltip title={text.length > maxCharacters ? text : ''}>
      <Typography variant={variant} noWrap sx={customSx}>
        {truncatedText}
      </Typography>
    </Tooltip>
  )
}

export default EllipsisTypography
