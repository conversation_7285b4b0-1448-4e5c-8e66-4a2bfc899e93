import {
  Box,
  IconButton,
  Tooltip,
  useTheme,
  ListItem,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import { FavouriteIcon } from "@hugeicons/react";
import { Building06Icon, Calendar03Icon } from "@hugeicons/react";
import { Clock01Icon } from "@hugeicons/react";
import { Location01Icon } from "@hugeicons/react";
import { useTranslation } from "react-i18next";
import { VehicleOutlined } from "src/images/Icons";
import { Operator, User } from "src/common/types";
import { CeCard } from "src/common/components";

interface ReservationInfoCardProps {
  formattedDate: string;
  formattedTime: string;
  siteAddress: string;
  operatorManager?: Operator | null;
  isFavorited: boolean;
  handleFavoriteCompany: (managerId: number) => void;
  handleVehicleInformationModal: () => void;
  vehicleUniqueName: string;
}

const ReservationInfoCard = ({
  handleVehicleInformationModal,
  handleFavoriteCompany,
  formattedDate,
  formattedTime,
  siteAddress,
  operatorManager,
  isFavorited,
  vehicleUniqueName,
}: ReservationInfoCardProps) => {
  const { t } = useTranslation(["common", "dispatcher"]);
  const theme = useTheme();
  const manager = operatorManager as unknown as User;
  const company = operatorManager as unknown as User;
  const companyName = company?.company?.name;

  return (
    <CeCard
      sx={{
        padding: 2,
      }}
    >
      <Box>
        <Box display="flex" alignItems="center" gap={2}>
          <Box flex="1">
            <ListItem
              dense
              secondaryAction={
                <Tooltip title={t("common:vehicle-details")}>
                  <IconButton
                    size="medium"
                    sx={{
                      p: 0,
                      pl: 1,
                      color: `${theme.palette.primary.main} !important`,
                    }}
                    onClick={handleVehicleInformationModal}
                  >
                    <InfoIcon />
                  </IconButton>
                </Tooltip>
              }
            >
              <ListItemIcon>
                <VehicleOutlined
                  width="24px"
                  height="24px"
                  stroke="1px"
                  color={theme.palette.primary.main}
                />
              </ListItemIcon>
              <ListItemText
                sx={{
                  textTransform: "capitalize",
                }}
                primary={vehicleUniqueName}
                secondary={t("common:vehicle")}
              />
            </ListItem>
          </Box>

          <Box flex="1">
            <ListItem dense>
              <ListItemIcon>
                <Calendar03Icon
                  size={24}
                  color={theme.palette.primary.main}
                  variant={"stroke"}
                />
              </ListItemIcon>
              <ListItemText
                primary={formattedDate}
                secondary={t("common:date")}
              />
            </ListItem>
          </Box>
        </Box>

        <Box display="flex" alignItems="center" gap={2}>
          <ListItem dense>
            <ListItemIcon>
              <Clock01Icon
                size={24}
                color={theme.palette.primary.main}
                variant={"stroke"}
              />
            </ListItemIcon>
            <ListItemText
              primary={formattedTime}
              secondary={t("common:time")}
            />
          </ListItem>

          <ListItem dense>
            <ListItemIcon>
              <Location01Icon
                size={24}
                color={theme.palette.primary.main}
                variant={"stroke"}
              />
            </ListItemIcon>
            <ListItemText
              primary={siteAddress}
              secondary={t("common:address")}
            />
          </ListItem>
        </Box>

        <Box display="flex" alignItems="center" gap={2} width="50%">
          <ListItem
            dense
            secondaryAction={
              <IconButton
                onClick={() => {
                  if (manager.companyId) {
                    handleFavoriteCompany(manager.companyId);
                  }
                }}
              >
                {isFavorited ? (
                  <FavouriteIcon size={24} color={"error"} variant={"solid"} />
                ) : (
                  <FavouriteIcon
                    size={24}
                    color={"active"}
                    variant={"stroke"}
                  />
                )}
              </IconButton>
            }
          >
            <ListItemIcon>
              <Building06Icon
                size={24}
                color={theme.palette.primary.main}
                variant="stroke"
              />
            </ListItemIcon>
            <ListItemText
              primary={companyName}
              secondary={t("common:company")}
            />
          </ListItem>
        </Box>
      </Box>
    </CeCard>
  );
};

export default ReservationInfoCard;
