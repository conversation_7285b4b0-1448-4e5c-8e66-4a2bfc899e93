import { InformationCircleIcon } from "@hugeicons/react";
import { Box, Grid, IconButton, Typography, useTheme } from "@mui/material";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Reservation } from "src/common/types";
import { getVehicleUniqueId } from "src/common/utils";
import { VehicleInformationModalShort } from "./VehicleInformationModalShort";

interface CharteredEquipmentProps {
  reservation: Reservation | undefined;
}

const CharteredEquipment: React.FC<CharteredEquipmentProps> = ({
  reservation,
}) => {
  const { t } = useTranslation("common");
  const theme = useTheme();
  const [openVehicleInfoModal, setOpenVehicleInfoModal] = useState(false);

  const handleCloseModal = () => {
    setOpenVehicleInfoModal(false);
  };

  const renderVehicleUniqueName = (reservation?: Reservation) => {
    if (reservation?.vehicleUniqueId) {
      return (
        <Typography variant="body2">{reservation.vehicleUniqueId}</Typography>
      );
    } else {
      return (
        <Typography variant="body2">
          {reservation?.vehicle ? getVehicleUniqueId(reservation.vehicle) : "-"}
        </Typography>
      );
    }
  };

  return (
    <>
      <Box sx={{ padding: 3, paddingTop: 1 }}>
        <Typography variant="h6" sx={{ fontWeight: "bold", marginBottom: 2 }}>
          {t("chartered-equipment")}
        </Typography>
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("vehicle-name")}
            </Typography>
          </Grid>
          <Grid
            item
            xs={6}
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              gap: 1,
            }}
          >
            {renderVehicleUniqueName(reservation)}
            <IconButton
              color="primary"
              sx={{ p: 0 }}
              onClick={(event: React.SyntheticEvent) => {
                event.stopPropagation();
                setOpenVehicleInfoModal(true);
              }}
            >
              <InformationCircleIcon
                size="22px"
                stroke={theme.palette.primary.main}
              />
            </IconButton>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("vehicle-brand")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.vehicle?.vehicleBrand
                ? reservation.vehicle.vehicleBrand
                : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("brand-model")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.vehicle?.brandModel
                ? reservation.vehicle.brandModel
                : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("license-plate-number")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.vehicle?.licensePlateNumber
                ? reservation.vehicle.licensePlateNumber
                : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("type")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.vehicle?.type ? reservation.vehicle.type : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("common:max-flow-rate")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.vehicle?.maxFlowRate
                ? reservation.vehicle.maxFlowRate
                : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("delivery-pressure")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.vehicle?.maxConcretePressure
                ? reservation.vehicle.maxConcretePressure
                : "-"}
            </Typography>
          </Grid>
        </Grid>
      </Box>

      {reservation?.vehicle && (
        <VehicleInformationModalShort
          vehicle={reservation.vehicle}
          open={openVehicleInfoModal}
          onClose={handleCloseModal}
        />
      )}
    </>
  );
};

export default CharteredEquipment;
