import axios, { AxiosError } from "axios";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  CancelContractDto,
  Contract,
  ContractsWithCount,
  CreateContractDto,
  GetContractDto,
  GetContractedPriceList,
  RespondToContractPayload,
} from "../types";
import { processApiError } from "../utils/errors";
import { wrapEachFieldIntoDoubleQuotes } from "../utils";

const backendUrl = process.env.REACT_APP_API_URL;

export const getContracts = async (attr: GetContractDto) => {
  return axios
    .post(`${backendUrl}/contracts/get`, attr, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useGetContracts = (
  attrs: GetContractDto,
  enabled: boolean = true
) => {
  const formattedSortModel = wrapEachFieldIntoDoubleQuotes(attrs.sortModel);
  const formattedAttrs = { ...attrs, sortModel: formattedSortModel };
  return useQuery<ContractsWithCount, AxiosError | Error>(
    ["contracts", formattedAttrs],
    () => getContracts(formattedAttrs),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch contracts", err),
      enabled,
    }
  );
};

export const getContractedPriceList = async (attr: GetContractedPriceList) => {
  const { dispatcherCompanyId, operatorCompanyId } = attr;

  if (!operatorCompanyId) {
    throw new Error("Operator Company Id was not provided");
  }
  if (!dispatcherCompanyId) {
    throw new Error("Dispatcher Company Id was not provided");
  }

  return axios
    .post(`${backendUrl}/contracts/find-one-with-variants`, attr, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useGetContractedPriceList = (
  attrs: GetContractedPriceList,
  enabled: boolean = true
) => {
  return useQuery<Contract, AxiosError | Error>(
    ["contractedPriceList", attrs],
    () => getContractedPriceList(attrs),
    {
      keepPreviousData: true,
      onError: (err) =>
        processApiError("Unable to fetch contracted price list", err),
      enabled,
    }
  );
};

export const getPendingContractsCount = async (
  dispatcherCompanyId: number
): Promise<number> => {
  const response = await axios.get(
    `${backendUrl}/contracts/${dispatcherCompanyId}/pending`,
    {
      withCredentials: true,
    }
  );
  return response.data;
};

export const useGetPendingContractsCount = (
  dispatcherCompanyId: number,
  enabled: boolean
) => {
  return useQuery<number, Error>(
    ["pendingContracts", dispatcherCompanyId],
    () => getPendingContractsCount(dispatcherCompanyId),
    {
      keepPreviousData: true,
      onError: (err) =>
        processApiError("Unable to fetch pending contracts number", err),
      enabled,
    }
  );
};

export const requestContract = (attrs: CreateContractDto) => {
  return axios
    .post(`${backendUrl}/contracts`, attrs, { withCredentials: true })
    .then((response) => response.data);
};

export const useRequestContract = () => {
  const queryClient = useQueryClient();
  return useMutation<
    Contract,
    AxiosError | Error,
    CreateContractDto,
    () => void
  >((a: CreateContractDto) => requestContract(a), {
    onSuccess: () => {
      queryClient.invalidateQueries("contracts");
      queryClient.invalidateQueries("vehicles");
    },
    onError: (err) => processApiError("Unable to request contract", err),
  });
};

export const acceptContract = (
  id: number,
  payload: RespondToContractPayload
) => {
  if (!id) {
    throw new Error("contract ID was not provided");
  }

  return axios
    .post(`${backendUrl}/contracts/${id}/accept`, payload, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useAcceptContract = () => {
  const queryClient = useQueryClient();

  return useMutation<
    Contract,
    AxiosError | Error,
    { id: number; payload: RespondToContractPayload }
  >(({ id, payload }) => acceptContract(id, payload), {
    onSuccess: () => {
      queryClient.invalidateQueries("contracts");
      queryClient.invalidateQueries("vehicles");
      queryClient.invalidateQueries("pendingContracts");
    },
    onError: (err) => processApiError("Unable to accept contract", err),
  });
};

export const rejectContract = (
  id: number,
  payload: RespondToContractPayload
) => {
  if (!id) {
    throw new Error("contract ID was not provided");
  }

  return axios
    .post(`${backendUrl}/contracts/${id}/reject`, payload, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const useRejectContract = () => {
  const queryClient = useQueryClient();

  return useMutation<
    Contract,
    AxiosError | Error,
    { id: number; payload: RespondToContractPayload }
  >(({ id, payload }) => rejectContract(id, payload), {
    onSuccess: () => {
      queryClient.invalidateQueries("contracts");
      queryClient.invalidateQueries("vehicles");
      queryClient.invalidateQueries("pendingContracts");
    },
    onError: (err) => processApiError("Unable to reject contract", err),
  });
};

export const addSuspensionPeriod = async (
  payload: CancelContractDto
): Promise<Contract> => {
  const { contractId, suspensionStart, suspensionEnd } = payload;

  if (!contractId) {
    throw new Error("Contract ID was not provided");
  }
  if (!suspensionStart || !suspensionEnd) {
    throw new Error("Suspension start/end date was not provided");
  }

  return axios
    .post(
      `${backendUrl}/contracts/${contractId}/suspension-period`,
      { suspensionStart, suspensionEnd },
      { withCredentials: true }
    )
    .then((res) => res.data);
};

export const useAddSuspensionPeriod = () => {
  const queryClient = useQueryClient();

  return useMutation<Contract, AxiosError | Error, CancelContractDto>(
    (payload) => addSuspensionPeriod(payload),
    {
      onSuccess: () => queryClient.invalidateQueries("contracts"),
      onError: (err) => processApiError("Unable to add suspension period", err),
    }
  );
};
