import React from "react";
import { MainModal } from "src/common/components";
import {
  ContainerModalFormValues,
  ContainerPricelists,
  CreateContainerDto,
  UpdateContainerDto,
} from "src/common/types/priceList";
import ContainerForm from "./ContainerForm";
import { useTranslation } from "react-i18next";
import { Partner } from "src/common/types/partners";

interface ContainerFormProps {
  initialFormValues: ContainerModalFormValues;
  onUpdateContainer: (values: UpdateContainerDto) => void;
  onCreateContainer: (values: CreateContainerDto) => void;
  handleCloseModal: () => void;
  partners: Partner[];
  existingContainers: ContainerPricelists[];
}

const ContainerModal = ({
  initialFormValues,
  onUpdateContainer,
  onCreateContainer,
  handleCloseModal,
  partners,
  existingContainers,
}: ContainerFormProps) => {
  const { t } = useTranslation("common");

  return (
    <MainModal
      isOpen={!!initialFormValues.flow}
      title={
        initialFormValues.flow === "Create"
          ? t("add-pricelist")
          : t("update-pricelist")
      }
      handleClose={handleCloseModal}
    >
      <ContainerForm
        initialFormValues={initialFormValues}
        handleCloseModal={handleCloseModal}
        onUpdateContainer={onUpdateContainer}
        onCreateContainer={onCreateContainer}
        partners={partners}
        existingContainers={existingContainers}
      />
    </MainModal>
  );
};

export default ContainerModal;
