import { Autocomplete, DialogActions, Stack } from "@mui/material";
import { isBefore } from "date-fns";
import enGb from "date-fns/locale/en-GB";

import { LocalizationProvider, DateTimePicker } from "@mui/x-date-pickers";

import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import * as yup from "yup";
import { useTranslation } from "react-i18next";

import { FormikProvider, useFormik } from "formik";
import {
  ReservationFormValues,
  Role,
  Vehicle,
  getVehiclesDto,
} from "src/common/types";
import { getCurrentUser, useVehicles } from "src/common/api";
import { CeTextField, CeButton } from "src/common/components";
import { useLocation } from "react-router-dom";
import AutocompleteInput from "src/common/components/custom/AutocompleteGoogleInput";
interface ReservationFormProps {
  isLoading: boolean;
  initialFormValues: ReservationFormValues;
  handleClose: () => void;
  handleSubmit: (values: ReservationFormValues) => void;
}

export const ReservationForm: React.FC<ReservationFormProps> = ({
  handleClose,
  handleSubmit,
  isLoading,
  initialFormValues,
}) => {
  const currentUser = getCurrentUser();
  const isOperatorManager = currentUser?.role === Role.OEPRATOR_MANAGER;

  const location = useLocation();
  const isPlanningPage =
    location.pathname === "/dispatcher-planning" ||
    location.pathname === "/planning";

  const { t } = useTranslation(["common", "manager", "dispatcher"]);

  const formik = useFormik<ReservationFormValues>({
    initialValues: initialFormValues,
    validationSchema: yup.object({
      vehicle: yup
        .object()
        .nullable()
        .required("Vehicle is required")
        .test(
          "has-assignedPricelist",
          t("common:vehicle-missing-pricelist"),
          (value) => {
            return !!(
              (value && value.assignedPricelist != null) ||
              initialFormValues.hasAssignedPricelist
            );
          }
        ),
      siteAddress: yup
        .string()
        .required("Address is required")
        .matches(/\d+/, "Street number is required"),
      city: yup.string(),
      plz: yup.number(),
      location: yup.object().nullable(),
      dateFrom: yup
        .date()
        .required("required")
        .typeError("invalid-date")
        .test(
          "isDateFromInPast",
          `${t("common:cannot-select-past-date-from")}`,
          function (value) {
            if (!value) {
              return false;
            }
            const currentTime = new Date();
            const fiveMinutesAgo = new Date(
              currentTime.getTime() - 5 * 60 * 1000
            );
            return !isBefore(value, fiveMinutesAgo);
          }
        )
        .test("dateFrom", `${t("common:date-from-error")}`, function (value) {
          if (!value) {
            return false;
          }
          const { dateTo } = this.parent;
          return value < dateTo;
        }),
      dateTo: yup
        .date()
        .required("required")
        .typeError("invalid-date")
        .test(
          "isDateToInPast",
          `${t("common:cannot-select-past-date-to")}`,
          function (value) {
            if (!value) {
              return false;
            }
            const currentTime = new Date();
            const fiveMinutesAgo = new Date(
              currentTime.getTime() - 5 * 60 * 1000
            );
            return !isBefore(value, fiveMinutesAgo);
          }
        )
        .test("dateTo", `${t("common:date-to-error")}`, function (value) {
          if (!value) {
            return false;
          }
          const { dateFrom } = this.parent;
          return value > dateFrom;
        }),
    }),
    onSubmit: (values) => {
      if (initialFormValues.flow === "Add") {
        handleSubmit(values);
      }
    },
  });

  const attrs: getVehiclesDto = {
    operatorManagerId: currentUser?.id,
    dateFrom: formik.values.dateFrom
      ? new Date(formik.values.dateFrom)
      : undefined,
    dateTo: formik.values.dateTo ? new Date(formik.values.dateTo) : undefined,
  };

  const { data: AllVehicles } = useVehicles(
    attrs,
    Boolean(isOperatorManager && !isPlanningPage)
  );

  const vehicles = AllVehicles?.data || [];

  return (
    <FormikProvider value={formik}>
      <Stack
        component="form"
        spacing={2}
        noValidate
        onSubmit={formik.handleSubmit}
        sx={{ width: "300px" }}
      >
        <AutocompleteInput
          id="siteAddress"
          name="siteAddress"
          label={t("common:site-address")}
          variant="outlined"
          onAddressChange={({ city, postalCode, lat, lng, siteAddress }) => {
            formik.setFieldValue("siteAddress", siteAddress);
            formik.setFieldValue("location", {
              coordinates: [lat, lng],
            });
            formik.setFieldValue("plz", postalCode);
            formik.setFieldValue("city", city);
          }}
          disabled={isLoading}
        />

        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={enGb}>
          <DateTimePicker
            minutesStep={5}
            renderInput={(props: any) => (
              <CeTextField
                {...props}
                size="small"
                onBlur={() => formik.setFieldTouched("dateFrom", true)}
                error={!!formik.errors.dateFrom && formik.touched.dateFrom}
                helperText={formik.touched.dateFrom && formik.errors.dateFrom}
                required
              />
            )}
            label={t("date-from")}
            value={formik.values.dateFrom}
            onChange={(newValue) => {
              formik.setFieldValue("dateFrom", newValue);
            }}
          />
        </LocalizationProvider>
        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={enGb}>
          <DateTimePicker
            minutesStep={5}
            renderInput={(props: any) => (
              <CeTextField
                {...props}
                size="small"
                error={!!formik.errors.dateTo && formik.touched.dateTo}
                helperText={formik.touched.dateTo && formik.errors.dateTo}
                onBlur={() => formik.setFieldTouched("dateTo", true)}
                required
              />
            )}
            label={t("date-to")}
            value={formik.values.dateTo}
            onChange={(newValue) => {
              formik.setFieldValue("dateTo", newValue);
            }}
          />
        </LocalizationProvider>

        {!isPlanningPage && isOperatorManager ? (
          <Autocomplete
            id="vehicle"
            fullWidth
            value={formik.values.vehicle}
            onChange={(event: any, nextValues: Vehicle | null) => {
              formik.setFieldValue("vehicle", nextValues);
            }}
            onBlur={() => formik.setFieldTouched("vehicle", true)}
            isOptionEqualToValue={(option: Vehicle, value: Vehicle) =>
              option.id === value.id
            }
            getOptionLabel={(vehicle: Vehicle) =>
              `${vehicle.type || ""}-${vehicle.boomSize || ""}` || ""
            }
            options={vehicles}
            filterSelectedOptions
            disabled={isLoading}
            renderInput={(params) => (
              <CeTextField
                {...params}
                InputLabelProps={{ shrink: true }}
                label={t("common:vehicle")}
                size="small"
                error={!!formik.errors.vehicle && formik.touched.vehicle}
                helperText={formik.touched.vehicle && formik.errors.vehicle}
              />
            )}
          />
        ) : (
          <CeTextField
            fullWidth
            id="vehicleName"
            label={t("vehicle-name")}
            size="small"
            InputLabelProps={{ shrink: true }}
            value={initialFormValues.vehicle?.name}
            disabled
            error={!!formik.errors.vehicle && formik.touched.vehicle}
            helperText={formik.touched.vehicle && formik.errors.vehicle}
          />
        )}
        <DialogActions>
          <CeButton onClick={handleClose} disabled={isLoading} variant="text">
            {t("cancel")}
          </CeButton>
          <CeButton type="submit" disabled={isLoading}>
            {t("submit")}
          </CeButton>
        </DialogActions>
      </Stack>
    </FormikProvider>
  );
};
