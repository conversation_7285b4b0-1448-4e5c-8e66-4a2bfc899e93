import {
  Avatar,
  Box,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Tooltip,
  Typography,
} from "@mui/material";
import { CeButton } from "src/common/components";
import { useTranslation } from "react-i18next";
import { useWindowSize } from "@react-hook/window-size";
import { PriceListDatagrid } from "./datagrid/PriceListDatagrid";
import {
  useCreateContainer,
  useDeleteContainer,
  usePriceLists,
  usePriceListsContainers,
  useUpdateContainer,
  useUpdateDefaultContainer,
} from "src/common/api/priceList";
import { useEffect, useState } from "react";
import { CePaper } from "src/common/components";
import {
  Add01Icon,
  Delete01Icon,
  Edit02Icon,
  FileEuroIcon,
  FileValidationIcon,
} from "@hugeicons/react";
import { getCurrentUser } from "src/common/api";
import { useQueryParam, NumberParam } from "use-query-params";
import {
  ContainerFormFlow,
  ExtendedContainerPricelistsState,
  PriceListDeleteFlowEnum,
} from "src/common/types/priceList";
import { useRecoilState } from "recoil";
import {
  containerFormValuesState,
  contractFormValuesState,
  priceListDeleteValuesState,
  priceListFormValuesState,
} from "src/common/state";
import {
  CONTAINER_FORM_DEFAULT_VALUES,
  PRICELIST_DELETE_DEFAULT,
} from "src/common/constants/priceList";

import { useLocation, useNavigate } from "react-router-dom";
import { useTheme } from "@mui/material";
import ContainerModal from "./ContainerModal";
import { PriceListContainerDeleteModal } from "./PriceListModalDelete";
import { usePartners } from "src/common/api/partner";
import DefaultContainerConfirmModal from "./DefaultContainerConfirmModal";
import usePersistentGridState from "src/common/utils/gridState";

export const PriceLists = () => {
  const { t } = useTranslation(["dispatcher", "common"]);
  const navigate = useNavigate();
  const [, height] = useWindowSize();
  const currentUser = getCurrentUser();
  const location = useLocation();
  const localStorageKey = location.pathname.slice(1);
  const [containerId, setContainerId] = useQueryParam("id", NumberParam);
  const theme = useTheme();
  const [priceListFormValues, setPriceListFormValues] = useRecoilState(
    priceListFormValuesState
  );
  const [containerFormValues, setContainerFormValues] = useRecoilState(
    containerFormValuesState
  );

  const [priceListDeleteValues, setPriceListDeleteValues] = useRecoilState(
    priceListDeleteValuesState
  );

  const [contractFormValues, setContractFormValues] = useRecoilState(
    contractFormValuesState
  );
  const {
    mutate: handleUpdateContainer,
    isSuccess: isUpdateContainerSuccess,
    isLoading: isUpdatingContainer,
  } = useUpdateContainer();

  const {
    mutate: handleCreateContainer,
    isSuccess: isCreateContainerSuccess,
    isLoading: isCreatingContainer,
  } = useCreateContainer();
  const {
    mutate: handleDeleteContainer,
    isSuccess: isDeleteContainerSuccess,
    isLoading: isDeletingContainer,
  } = useDeleteContainer();

  const [selectedContainer, setSelectedContainer] =
    useState<ExtendedContainerPricelistsState | null>(null);
    
    const [gridState, updateGridStatePart] = usePersistentGridState(
      localStorageKey,
      1,
      20,
    );
    
  const {
    data: containers,
    isLoading: isLoadingContainers,
    refetch: refetchContainers,
  } = usePriceListsContainers(
    {
      expressions: [],
      sortModel: [],
      limit: gridState.pageSize,
      offset: (gridState.page - 1) * gridState.pageSize,
    },
    Boolean(currentUser?.id)
  );
  const allContainers = containers?.data || [];

  const {
    data: pricelists,
    isLoading: isLoadingPriceLists,
    refetch: refetchPricelists,
  } = usePriceLists(
    {
      expressions: [
        {
          category: '"pricelist"."containerId"',
          operator: "=",
          value: selectedContainer?.id,
          conditionType: "AND",
        },
      ],
      sortModel: [],
      limit: gridState.pageSize,
      offset: (gridState.page - 1) * gridState.pageSize,
    },
    Boolean(selectedContainer?.id)
  );
  const allPricelists = pricelists?.data || [];

  const {
    data: allPartners,
    isLoading: isLoadingPartners,
    refetch: refetchPartners,
  } = usePartners(
    { expressions: [], sortModel: [] },
    Boolean(containerFormValues?.flow)
  );

  const {
    mutate: updateToDefaultContainer,
    isLoading: isDefaultContainerLoading,
  } = useUpdateDefaultContainer();

  const partners = allPartners?.data || [];
  const isLoading =
    isLoadingContainers ||
    isUpdatingContainer ||
    isDeletingContainer ||
    isCreatingContainer;

  useEffect(() => {
    if (isCreateContainerSuccess) {
      setContainerFormValues(CONTAINER_FORM_DEFAULT_VALUES);
    }
  }, [isCreateContainerSuccess, setContainerFormValues]);

  useEffect(() => {
    if (isUpdateContainerSuccess) {
      setContainerFormValues(CONTAINER_FORM_DEFAULT_VALUES);
    }
  }, [isUpdateContainerSuccess, setContainerFormValues]);

  useEffect(() => {
    if (isDeleteContainerSuccess) {
      setPriceListDeleteValues(PRICELIST_DELETE_DEFAULT);
    }
  }, [isDeleteContainerSuccess, setPriceListDeleteValues]);

  const handlePageChange = (newPage: number) => {
    updateGridStatePart("page", newPage + 1);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    updateGridStatePart("pageSize", newPageSize);
  };

  const handleCloseDefaultContainerModal = () => {
    if (!isDefaultContainerLoading) {
      setSelectedContainer(null);
    }
  };

  const onDefaultContainerSet = () => {
    if (selectedContainer?.id) {
      updateToDefaultContainer({ id: selectedContainer.id });
      handleCloseDefaultContainerModal();
    }
  };

  const handleCloseContainerModal = () => {
    if (!isLoading) {
      setContainerFormValues(CONTAINER_FORM_DEFAULT_VALUES);
    }
  };

  const handleClosePriceListModalDelete = () => {
    if (!isLoading) {
      setPriceListDeleteValues(PRICELIST_DELETE_DEFAULT);
    }
  };

  const onContainerDelete = () => {
    const id = priceListDeleteValues?.priceListId;
    if (id) {
      handleDeleteContainer({ id });
    }
  };

  const refetchPricelistsData = () => {
    if (selectedContainer?.id) {
      refetchPricelists();
    }
  };
  return (
    <Box sx={{ height: `${height - 100}px` }}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={3}>
          <CePaper
            sx={{
              height: `${height - 100}px`,
              overflowY: "auto",
              padding: 0,
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: 2,
              }}
            >
              <Typography variant="body1" component="div">
                Pricelists
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  gap: 1,
                  alignItems: "center",
                }}
              >
                {selectedContainer?.id ? (
                  <>
                    {selectedContainer?.isDefault ? null : (
                      <IconButton
                        sx={{ alignSelf: "flex-end" }}
                        onClick={() =>
                          setSelectedContainer({
                            ...selectedContainer,
                            flow: "Update",
                          })
                        }
                      >
                        <FileValidationIcon
                          variant="solid"
                          color={theme.palette.primary.dark}
                        />
                      </IconButton>
                    )}
                    <Tooltip title={t("common:update-pricelist")}>
                      <CeButton
                        variant="text"
                        size="small"
                        disabled={isLoading}
                        onClick={() => {
                          const containerToUpdate = allContainers.find(
                            (container) =>
                              container.id === selectedContainer?.id
                          );
                          if (containerToUpdate) {
                            setContainerFormValues({
                              ...containerToUpdate,
                              flow: ContainerFormFlow.UPDATE,
                              isDefault: containerToUpdate.isDefault || false,
                              copyFromContainer: null,
                            });
                          }
                        }}
                        sx={{
                          minWidth: "20px",
                          pl: 0.5,
                        }}
                      >
                        <Edit02Icon
                          size={20}
                          color={"currentColor"}
                          variant="solid"
                        />
                      </CeButton>
                    </Tooltip>
                  </>
                ) : null}
                <Tooltip title={t("common:add-pricelist")}>
                  <CeButton
                    variant="text"
                    size="small"
                    disabled={isLoading}
                    onClick={() => {
                      setContainerFormValues({
                        ...CONTAINER_FORM_DEFAULT_VALUES,
                        flow: ContainerFormFlow.CREATE,
                      });
                    }}
                    sx={{
                      minWidth: "20px",
                    }}
                  >
                    <Add01Icon
                      size={20}
                      color={"currentColor"}
                      variant="solid"
                    />
                  </CeButton>
                </Tooltip>
              </Box>
            </Box>
            <Divider sx={{ mx: 1.5 }} />
            <List
              dense={true}
              sx={{ overflowY: "auto", maxHeight: "calc(100vh - 258px)", p: 1 }}
            >
              {allContainers.map((container) => {
                return (
                  <ListItem
                    key={container.id}
                    sx={{
                      m: 0,
                      p: 0,
                      pt: 1,
                      pb: 1,
                      borderRadius: 2,
                      backgroundColor:
                        selectedContainer?.id === container.id
                          ? "#f3f3f3"
                          : null,
                    }}
                    onClick={() => {
                      setSelectedContainer(container);
                      setContainerId(container.id);

                      if (selectedContainer?.id === container.id) {
                        setSelectedContainer(null);
                        setContainerId(undefined);
                        refetchPricelists();
                      }
                    }}
                    secondaryAction={
                      <IconButton
                        disabled={isLoading}
                        edge="end"
                        aria-label="delete"
                        onClick={() => {
                          setPriceListDeleteValues({
                            priceListId: container.id,
                            priceListTitle: container.title || "",
                            flow: PriceListDeleteFlowEnum.PRICELIST_DELETE,
                          });
                        }}
                      >
                        <Delete01Icon
                          size={18}
                          variant={"stroke"}
                          color={theme.palette.error.main}
                        />
                      </IconButton>
                    }
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ height: "35px", width: "35px", ml: 1 }}>
                        <FileEuroIcon
                          style={{ fill: "none" }}
                          size={20}
                          color={"currentColor"}
                          variant={"stroke"}
                          type="rounded"
                          opacity={0.8}
                        />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={container.title}
                      sx={{ lineHeight: 0.7 }}
                      secondary={
                        container.isDefault ? (
                          <Typography color="text.secondary" variant="caption">
                            Default
                          </Typography>
                        ) : null
                      }
                    />
                  </ListItem>
                );
              })}
            </List>
          </CePaper>
        </Grid>
        <Grid item xs={12} md={9}>
          <CePaper
            sx={{ height: `${height - 100}px`, overflowY: "auto", p: 2 }}
          >
            <PriceListDatagrid
              data={allPricelists}
              isFetchingPriceLists={isLoadingPriceLists}
              refetchPriceLists={refetchPricelistsData}
              shouldRenderRefreshButton
              shouldRenderAddButton={Boolean(selectedContainer?.id)}
              shouldRenderEditActionsColumn={Boolean(selectedContainer?.id)}
              gridState={gridState}
              total={containers?.totalCount || 0}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              updateGridStatePart={updateGridStatePart}
            />
          </CePaper>
        </Grid>
      </Grid>
      <ContainerModal
        initialFormValues={containerFormValues}
        onUpdateContainer={handleUpdateContainer}
        onCreateContainer={handleCreateContainer}
        handleCloseModal={handleCloseContainerModal}
        existingContainers={allContainers}
        partners={partners}
      />
      <DefaultContainerConfirmModal
        onConfirm={onDefaultContainerSet}
        flow={selectedContainer?.flow!}
        closeModal={handleCloseDefaultContainerModal}
      />
      <PriceListContainerDeleteModal
        flow={priceListDeleteValues.flow}
        isLoading={isLoading}
        priceListTitle={priceListDeleteValues.priceListTitle}
        handleClosePriceListModalDelete={handleClosePriceListModalDelete}
        handleSubmit={onContainerDelete}
      />
    </Box>
  );
};
