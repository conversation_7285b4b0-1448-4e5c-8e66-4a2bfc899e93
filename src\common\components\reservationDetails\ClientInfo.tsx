import React, { useMemo } from "react";
import { <PERSON>, Chip, Divider, Grid, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { stringAvatar } from "src/common/utils/avatar";
import { calculateElapsedTime, Reservation } from "src/common/types";
import { formatDateWithoutTimezoneForDisplaying } from "src/common/utils/formatDate";
import { getVehicleUniqueId } from "src/common/utils";
import { CeAvatar, CePaper } from "..";

interface ClientInfoProps {
  reservation: Reservation | undefined;
}

const ClientInfo: React.FC<ClientInfoProps> = ({ reservation }) => {
  const { t } = useTranslation("common");

  const renderVehicleUniqueName = (reservation?: Reservation) => {
    if (reservation?.vehicleUniqueId) {
      return (
        <Typography variant="body2">{reservation.vehicleUniqueId}</Typography>
      );
    } else {
      return (
        <Typography variant="body2">
          {reservation?.vehicle ? getVehicleUniqueId(reservation.vehicle) : "-"}
        </Typography>
      );
    }
  };

  const elapsedTime = useMemo(() => {
    return calculateElapsedTime(reservation?.job?.jobEvents!, t);
  }, [reservation?.job?.jobEvents, t]);

  return (
    <CePaper>
      <Box sx={{ padding: 3 }}>
        <Typography variant="h6" sx={{ marginBottom: 2, fontWeight: "bold" }}>
          {t("client-info")}
        </Typography>
        <Box display="flex" alignItems="center" sx={{ marginBottom: 1 }}>
          <Box display="flex" alignSelf="flex-start">
            <CeAvatar
              size="medium"
              {...stringAvatar(
                `${reservation?.clientDetails?.name} ${reservation?.clientDetails?.lastName}`
              )}
              sx={{ marginRight: 2 }}
            />
          </Box>
          <Box>
            <Typography variant="body2" sx={{ marginBottom: 1 }}>
              {reservation?.clientDetails?.name}{" "}
              {reservation?.clientDetails?.lastName}
            </Typography>
            <Typography
              variant="body2"
              color="text.disabled"
              sx={{ marginBottom: 1 }}
            >
              {reservation?.clientDetails?.email}
            </Typography>
            <Typography variant="body2" sx={{ marginBottom: 1 }}>
              {reservation?.clientDetails?.phoneNumber
                ? reservation.clientDetails.phoneNumber
                : "-"}
            </Typography>
            <Typography variant="body2" sx={{ marginBottom: 1 }}>
              {reservation?.clientDetails?.companyName
                ? reservation.clientDetails.companyName
                : "-"}
            </Typography>
          </Box>
        </Box>
      </Box>
      <Divider sx={{ marginBottom: 2 }} />
      <Box sx={{ padding: 3, paddingTop: 1 }}>
        <Typography variant="h6" sx={{ marginBottom: 2, fontWeight: "bold" }}>
          {t("worksite")}
        </Typography>
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("worksite-name")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">{"-"}</Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("city")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.siteAddress ? reservation.siteAddress : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("terrain")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.job?.terrainStability
                ? reservation.job.terrainStability
                : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("job-type")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.job?.jobType ? reservation.job.jobType : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("ciaw")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.job?.ciaw ? reservation.job.ciaw : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("weight-restriction")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.job?.authorizedWeight
                ? reservation.job.authorizedWeight + " tons"
                : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("height-restriction")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.job?.heightRestriction
                ? reservation.job.heightRestriction + " meters"
                : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("power-line")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Chip
              label={reservation?.job?.isElectricalRisk ? "Yes" : "No"}
              color="primary"
              size="small"
              sx={{ fontSize: "12px", fontWeight: "bold" }}
            />
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("voltage")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.job?.voltage ? reservation.job.voltage : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("local-admin-authorization")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Chip
              label={
                reservation?.localAdministrationAuthorizationKey ? "Yes" : "No"
              }
              color="primary"
              size="small"
              sx={{ fontSize: "12px", fontWeight: "bold" }}
            />
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("traffic-plan")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">{"-"}</Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("parking-permit")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Chip
              label={reservation?.job?.parkingComplianceKey ? "Yes" : "No"}
              color="primary"
              size="small"
              sx={{ fontSize: "12px", fontWeight: "bold" }}
            />
          </Grid>
        </Grid>
      </Box>
      <Divider sx={{ marginBottom: 2, marginTop: 2 }} />
      <Box sx={{ padding: 3, paddingTop: 1 }}>
        <Typography variant="h6" sx={{ fontWeight: "bold", marginBottom: 2 }}>
          {t("chartered-equipment")}
        </Typography>
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("vehicle-name")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            {renderVehicleUniqueName(reservation)}
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("vehicle-brand")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.vehicle?.vehicleBrand
                ? reservation.vehicle.vehicleBrand
                : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("brand-model")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.vehicle?.brandModel
                ? reservation.vehicle.brandModel
                : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("license-plate-number")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.vehicle?.licensePlateNumber
                ? reservation.vehicle.licensePlateNumber
                : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("type")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.vehicle?.type ? reservation.vehicle.type : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("common:max-flow-rate")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.vehicle?.maxFlowRate
                ? reservation.vehicle.maxFlowRate
                : "-"}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color="text.disabled">
              {t("delivery-pressure")}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2">
              {reservation?.vehicle?.maxConcretePressure
                ? reservation.vehicle.maxConcretePressure
                : "-"}
            </Typography>
          </Grid>
        </Grid>
      </Box>

      <Divider sx={{ marginBottom: 2, marginTop: 2 }} />

      <Box sx={{ padding: 3, paddingTop: 1 }}>
        <Typography variant="h6" sx={{ fontWeight: "bold", marginBottom: 2 }}>
          {t("job")}
        </Typography>
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("start-date")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                {reservation?.job?.start ? (
                  <Chip
                    label={formatDateWithoutTimezoneForDisplaying(
                      reservation.job.start
                    )}
                    color="primary"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
                ) : (
                  "-"
                )}
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("end-date")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                {reservation?.job?.end ? (
                  <Chip
                    label={formatDateWithoutTimezoneForDisplaying(
                      reservation.job.end
                    )}
                    color="primary"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
                ) : (
                  "-"
                )}
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("pumped-volume")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Chip
                  label={
                    reservation?.job?.amountOfConcrete
                      ? reservation.job.amountOfConcrete + " m³"
                      : "-"
                  }
                  color="primary"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
                {reservation?.job?.report?.amountOfConcrete ? (
                  <Chip
                    label={reservation.job?.report.amountOfConcrete + " m³"}
                    color="success"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
                ) : null}
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("flow-rate")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Chip
                  label={
                    reservation?.job?.flowRate
                      ? reservation.job.flowRate + " m³/h"
                      : "-"
                  }
                  color="primary"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("flexible-pipe-length-80mm")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Chip
                  label={
                    reservation?.job?.flexiblePipeLength80Mm
                      ? reservation.job.flexiblePipeLength80Mm + " m"
                      : "-"
                  }
                  color="primary"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
                {reservation?.job?.report?.flexiblePipeLength80Mm ? (
                  <Chip
                    label={reservation.job.report.flexiblePipeLength80Mm + " m"}
                    color="success"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
                ) : null}
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("flexible-pipe-length-90mm")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Chip
                  label={
                    reservation?.job?.flexiblePipeLength90Mm
                      ? reservation.job.flexiblePipeLength90Mm + " m"
                      : "-"
                  }
                  color="primary"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
                {reservation?.job?.report?.flexiblePipeLength90Mm ? (
                  <Chip
                    label={reservation.job.report.flexiblePipeLength90Mm + " m"}
                    color="success"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
                ) : null}
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("rigid-pipe-length-100mm")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Chip
                  label={
                    reservation?.job?.rigidPipeLength100Mm
                      ? reservation.job.rigidPipeLength100Mm + " m"
                      : "-"
                  }
                  color="primary"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
                {reservation?.job?.report?.rigidPipeLength100Mm ? (
                  <Chip
                    label={reservation.job.report.rigidPipeLength100Mm + " m"}
                    color="success"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
                ) : null}
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("rigid-pipe-length-120mm")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Chip
                  label={
                    reservation?.job?.rigidPipeLength120Mm
                      ? reservation.job.rigidPipeLength120Mm + " m"
                      : "-"
                  }
                  color="primary"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
                {reservation?.job?.report?.rigidPipeLength120Mm ? (
                  <Chip
                    label={reservation.job.report.rigidPipeLength120Mm + " m"}
                    color="success"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
                ) : null}
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("common:order-number")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Typography variant="body2">{reservation?.id}</Typography>
              </Box>
            </Grid>
          </Grid>
          <Grid container alignItems="center">
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("cleanup-in-concrete-plant")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              {reservation?.job?.cleaning ? (
                <Chip
                  label={t("common:yes")}
                  color="success"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
              ) : (
                <Chip
                  label={t("common:no")}
                  color="error"
                  size="small"
                  sx={{ fontSize: "12px", fontWeight: "bold" }}
                />
              )}
            </Grid>
          </Grid>
          <Grid container>
            <Grid item xs={6}>
              <Typography variant="body2" color="text.disabled">
                {t("delay")}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              {reservation?.job?.reasonForDelay ? (
                 <>
                 <Chip
                   label={elapsedTime}
                   color="error"
                   size="small"
                   sx={{ fontSize: "12px", fontWeight: "bold" }}
                 />
                 <Typography variant="body2">{reservation.job.reasonForDelay}</Typography>
               </>
              ) : (
                  <Chip
                    label={t("common:no")}
                    color="success"
                    size="small"
                    sx={{ fontSize: "12px", fontWeight: "bold" }}
                  />
              )}
            </Grid>
          </Grid>
        </Box>
      </Box>
    </CePaper>
  );
};

export default ClientInfo;
