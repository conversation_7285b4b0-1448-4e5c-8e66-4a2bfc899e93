import { SxProps, Typography } from "@mui/material";

interface BulletProps {
  sx?: SxProps;
}

export const Bullet = ({ sx }: BulletProps) => {
  return (
    <Typography
      noWrap
      sx={{
        fontSize: "16px !important",
        fontStyle: "normal !important",
        fontWeight: 400,
        letterSpacing: "0.17px !important",
        lineHeight: "5px !important",
        height: "10px",
        pt: "4px",
        alignSelf: "center !important",
        flexShrink: 0,
        display: "inline-block",
        ...sx
      }}
    >
      •
    </Typography>
  );
};
