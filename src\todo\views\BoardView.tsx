import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON>,
  Grid,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Typography,
  useTheme,
} from "@mui/material";
import { CeAvatar, CeCard } from "src/common/components";
import { Task, TaskStatus, UpdateTaskDto } from "src/common/types/tasks";
import { format } from "date-fns";
import { stringAvatar } from "src/common/utils/avatar";
import { Comment02Icon, DragDropHorizontalIcon } from "@hugeicons/react";
import { ConnectableElement, DndProvider, useDrag, useDrop } from "react-dnd";
import { useRef, useState } from "react";
import { HTML5Backend } from "react-dnd-html5-backend";

interface TaskCardProps {
  task: Task;
  onCardClick: (task: Task) => void;
}
const TaskCard = ({ task, onCardClick }: TaskCardProps) => {
  const theme = useTheme();

  const [, drag] = useDrag(() => ({
    type: "task",
    item: { task },
  }));

  const dateFrom = task.startDate
    ? format(new Date(task.startDate), "dd MMM, yy")
    : task.created_at
    ? format(new Date(task.created_at), "dd MMM, yy")
    : "";
  task.created_at && format(new Date(task.created_at), "dd MMM, yy");
  const dueDate = format(new Date(task.dueDate), "dd MMM, yy");

  return (
    <CeCard
      className="task-card"
      ref={drag}
      role="button"
      elevation={1}
      sx={{
        mb: 2,
        cursor: "pointer",
        "&:hover .drag-indicator": {
          visibility: "visible !important",
        },
      }}
      onClick={() => onCardClick(task)}
    >
      <CardContent>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Typography
            variant="body2"
            fontWeight={400}
            fontSize={14}
            letterSpacing={0.17}
            mb={1.25}
          >
            Task {task.id}
          </Typography>
          <IconButton
            ref={drag}
            className="drag-indicator"
            sx={{ cursor: "grab" }}
          >
            <DragDropHorizontalIcon size={18} />
          </IconButton>
        </Stack>
        <Box sx={{ display: "flex", gap: 1, mb: 1 }}>
          {task.label && (
            <Chip
              label={task.label?.name || ""}
              size="small"
              sx={{
                px: 0.5,
                py: 0.375,
                fontWeight: 400,
                fontSize: 13,
                letterSpacing: 0.16,
                backgroundColor: task.label?.color,
                color: theme.palette.primary.contrastText,
              }}
            />
          )}

          <Chip
            label={task.priority}
            sx={{
              px: 0.5,
              py: 0.375,
              fontWeight: 400,
              fontSize: 13,
              letterSpacing: 0.16,
              backgroundColor:
                task.priority === "High"
                  ? theme.palette.error.main
                  : task.priority === "Medium"
                  ? theme.palette.warning.main
                  : theme.palette.secondary.main,
              color: theme.palette.primary.contrastText,
            }}
            size="small"
          />
        </Box>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-start",
            gap: 1.5,
          }}
        >
          <CeAvatar
            size="small"
            {...stringAvatar(
              `${task.assignee?.firstName} ${task.assignee?.lastName}`
            )}
          />
          <Typography variant="body2">
            {dateFrom} - {dueDate}
          </Typography>
          {!!task.comments?.length && (
            <Box display="flex" alignItems="center" gap={0.5}>
              <Typography variant="body2">{task.comments?.length}</Typography>
              <Comment02Icon size={20} />
            </Box>
          )}
        </Box>
      </CardContent>
    </CeCard>
  );
};

const DroppableColumn = ({
  tasks,
  status,
  moveTask,
  onBoardCardClick,
}: {
  tasks: Task[];
  status: TaskStatus;
  moveTask: (task: Task, newStatus: TaskStatus, targetIndex: number) => void;
  onBoardCardClick: (task: Task) => void;
}) => {
  const [, drop] = useDrop(() => ({
    accept: "task",
    hover: (item: { task: Task; targetIndex?: number }, monitor) => {
      const clientOffset = monitor.getClientOffset();

      if (!clientOffset) return;

      const taskElements = document.querySelectorAll(
        `[data-status="${status}"] .task-card`
      );

      let targetIndex = tasks.length; // Default to end of the array
      taskElements.forEach((taskElement, index) => {
        const { top, bottom } = taskElement.getBoundingClientRect();
        if (clientOffset.y >= top && clientOffset.y <= bottom) {
          targetIndex = index;
        }
      });

      item.targetIndex = targetIndex;
    },
    drop: (item: { task: Task; targetIndex?: number }) => {
      const targetIndex = item.targetIndex ?? tasks.length;
      moveTask(item.task, status, targetIndex);
    },
  }));

  return (
    <Box
      ref={drop}
      data-status={status}
      sx={{
        minHeight: 100,
        border: tasks?.length === 0 ? "1px dashed lightgray" : "none",
        borderRadius: 4,
      }}
    >
      {tasks?.length > 0 ? (
        tasks.map((task) => (
          <TaskCard key={task.id} task={task} onCardClick={onBoardCardClick} />
        ))
      ) : (
        <Typography
          variant="body2"
          align="center"
          color="textSecondary"
          sx={{ mt: 2 }}
        >
          No current "{status}" tasks
        </Typography>
      )}
    </Box>
  );
};

interface BoardViewProps {
  taskData: Task[];
  onBoardCardClick: (task: Task) => void;
  onTaskDrag: (itemData: UpdateTaskDto) => void;
}

const BoardView = ({
  taskData,
  onBoardCardClick,
  onTaskDrag,
}: BoardViewProps) => {
  const [groupedTasks, setGroupedTasks] = useState(() =>
    taskData.reduce((acc, task) => {
      acc[task.status] = acc[task.status] || [];
      acc[task.status].push(task);
      return acc;
    }, {} as Record<TaskStatus, Task[]>)
  );

  const moveTask = (task: Task, newStatus: TaskStatus, targetIndex: number) => {
    setGroupedTasks((prev) => {
      const updated = { ...prev };

      for (const status in updated) {
        updated[status as TaskStatus] = updated[status as TaskStatus].filter(
          (t) => t.id !== task.id
        );
      }

      const updatedTask = { ...task, status: newStatus };
      updated[newStatus] = updated[newStatus] || [];

      if (task.status === newStatus) {
        const tasksInStatus = updated[newStatus];
        tasksInStatus.splice(targetIndex, 0, updatedTask);
      } else {
        updated[newStatus].splice(targetIndex, 0, updatedTask);
      }

      onTaskDrag({ taskId: updatedTask.id, status: updatedTask.status });

      return updated;
    });
  };
  return (
    <DndProvider backend={HTML5Backend}>
      <Box sx={{ p: 4 }}>
        <Grid container spacing={2}>
          {(
            [TaskStatus.TODO, TaskStatus.DOING, TaskStatus.DONE] as TaskStatus[]
          ).map((status) => (
            <Grid key={status} item xs={12} md={4} p={1.25}>
              <Box display="flex" alignItems={"center"} mb={2}>
                <Typography
                  variant="h6"
                  fontWeight={500}
                  fontSize={20}
                  letterSpacing={0.15}
                  alignSelf="flex-end"
                >
                  {status}
                </Typography>
                <Typography
                  variant="body1"
                  fontWeight={700}
                  fontSize={16}
                  letterSpacing={0.15}
                  ml={2}
                >
                  {groupedTasks[status]?.length || 0}
                </Typography>
              </Box>

              <Box
                sx={{
                  minHeight: 100,
                  border: !groupedTasks[status]?.length
                    ? "1px dashed lightgray"
                    : null,
                  borderRadius: 4,
                }}
              >
                <DroppableColumn
                  tasks={groupedTasks[status] || []}
                  status={status}
                  moveTask={moveTask}
                  onBoardCardClick={onBoardCardClick}
                />
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>
    </DndProvider>
  );
};

export default BoardView;
