import { Cancel01Icon, Tick02Icon } from '@hugeicons/react';
import { TabContext, Tab<PERSON>ist, TabPanel } from '@mui/lab';
import { Box, Drawer, IconButton, Tab, Typography, useTheme } from '@mui/material';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { VehicleFormValues } from 'src/common/types';
import { getUserFullName } from 'src/common/utils/user';
import VehicleInfo, { VehicleInfoField } from './components/VehicleInfo';
import { useUnavailablePeriods } from 'src/common/api';
import TimeOff from 'src/operators/components/TimeOff';

interface VehicleDetailsModalProps {
  handleCloseVehicleDetailsModal: () => void;
  isLoading: boolean;
  initialFormValues: VehicleFormValues;
}

const VehicleDetailsModal: React.FC<VehicleDetailsModalProps> = ({
  initialFormValues,
  handleCloseVehicleDetailsModal,
}) => {
  const { t } = useTranslation("common");
  const [tabValue, setTabValue] = useState('vehicleInfo');
  const theme = useTheme();

  const handleChangeTab = (event: React.SyntheticEvent, newValue: string) => {
    setTabValue(newValue);
  };

  const vehicleSpecifications: VehicleInfoField[] = [
    { label: t('type'), value: initialFormValues.type },
    { label: t('vehicle-boom-size'), value: initialFormValues.boomSize },
    { label: t('vehicle-brand'), value: initialFormValues.vehicleBrand },
    { label: t('brand-model'), value: initialFormValues.brandModel },
    { label: t('type-of-motorisation'), value: initialFormValues.typeOfMotorization },
    { label: t('license-plate-number'), value: initialFormValues.licensePlateNumber },
    { label: t('weight'), value: initialFormValues.weight + t("meters")},
    { label: t('width'), value: initialFormValues.width + t("meters")},
    { label: t('height'), value: initialFormValues.height + t("meters")},
    { label: t('length'), value: initialFormValues.length + t("meters")},
    { label: t('front-outriggers-span'), value: initialFormValues.frontOutriggerSpan + t("meters")},
    { label: t('rear-outriggers-span'), value: initialFormValues.rearOutriggerSpan + t("meters")},
    { label: t('front-side-opening'), value: initialFormValues.frontSideOpening + t("meters")},
    { label: t('rear-side-opening'), value: initialFormValues.rearSideOpening + t("meters")},
    { label: t('front-pressure-on-outrigger'), value: initialFormValues.frontPressureOnOutrigger! + ' kN'},
    { label: t('rear-pressure-on-outrigger'), value: initialFormValues.rearPressureOnOutrigger! + ' kN'},
    { label: t('operator'), value: getUserFullName(initialFormValues.operator?.firstName!, initialFormValues.operator?.lastName!) },
    { label: t('site-address'), value: initialFormValues.siteAddress},
    {
      label: t('bac-exit'),
      value: initialFormValues.bacExit
        ? <Tick02Icon size={18} color={theme.palette.success.main} variant="solid" />
        : <Cancel01Icon size={18} color={theme.palette.error.main} variant="solid" />
    },
    {
      label: t('bac-exit-reverse'),
      value: initialFormValues.bacExitReverse
        ? <Tick02Icon size={18} color={theme.palette.success.main} variant="solid" />
        : <Cancel01Icon size={18} color={theme.palette.error.main} variant="solid" />
    },
    {
      label: t('strangler'),
      value: initialFormValues.hasStrangler
        ? <Tick02Icon size={18} color={theme.palette.success.main} variant="solid" />
        : <Cancel01Icon size={18} color={theme.palette.error.main} variant="solid" />
    },
  ];
  const boomSpecifications: VehicleInfoField[] = [
    { label: t('maxVerticalReach'), value: initialFormValues.maxVerticalReach + t("meters")},
    { label: t('maxHorizontalReach'), value: initialFormValues.maxHorizontalReach + t("meters")},
    { label: t('minUnfoldingHeight'), value: initialFormValues.minUnfoldingHeight + t("meters")},
    { label: t('maxDownwardReach'), value: initialFormValues.maxDownwardReach + t("meters")},
    { label: t('number-of-boom-sections'), value: initialFormValues.numberOfBoomSections },
    { label: t('boom-rotation'), value: initialFormValues.boomRotation + t("degrees")},
    { label: t('boom-unfolding-system'), value: initialFormValues.boomUnfoldingSystem },
    { label: t('endHoseLength'), value: initialFormValues.endHoseLength + t("meters")},
  ];
  const pumpSpecifications: VehicleInfoField[] = [
    { label: t('maximum-flow-rate-rod-end'), value: initialFormValues.maxFlowRate + ' m/h'},
    { label: t('maximum-concrete-pressure-rod-end'), value: initialFormValues.maxConcretePressure + t("bars")},
  ];
  const pipes: VehicleInfoField[] = [
    { label: t('flexible-pipe-80'), value: initialFormValues.availableFlexiblePipeLength80Mm + t("meters")},
    { label: t('flexible-pipe-90'), value: initialFormValues.availableFlexiblePipeLength90Mm + t("meters")},
    { label: t('rigid-pipe-100'), value: initialFormValues.availableFlexiblePipeLength100Mm + t("meters")},
    { label: t('rigid-pipe-120'), value: initialFormValues.availableFlexiblePipeLength120Mm + t("meters")},
    { label: t('invoicing-pipes-from'), value: initialFormValues.invoicingPipesFrom + t("meters")},
  ];

   const {
      data: allUnavailablePeriod
    } = useUnavailablePeriods(
      {
        limit: 1000,
        offset: 0,
        expressions: [
          {
            operator: "=",
            value: initialFormValues.vehicleId,
            category: `"vehicles"."id"`,
            conditionType: "AND",
          }
        ],
        sortModel: [],
        relations: ["vehicles"],
      },
      Boolean(initialFormValues.vehicleId)
    );

  return (
    <Drawer
      anchor="right"
      open={initialFormValues.flow === 'Details'}
      onClose={handleCloseVehicleDetailsModal}
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 2,
        '& .MuiDrawer-paper': {
          boxSizing: 'border-box',
          width: 500,
        },
        '& .MuiBackdrop-root': {
          backgroundColor: 'transparent',
        },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            px:3,
            py:2
          }}
        >
          <Typography variant="h6" component="div" sx={{textTransform:'capitalize'}}>
           {initialFormValues.uniqueIdentificationNumber}
          </Typography>
          <IconButton onClick={handleCloseVehicleDetailsModal} edge="end" aria-label="close">
            <Cancel01Icon variant='solid' size={20}/>
          </IconButton>
        </Box>

        <TabContext value={tabValue}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-start', px: 2, }}>
            <TabList onChange={handleChangeTab} aria-label="Vehicle Details">
              <Tab sx={{ textTransform: 'none' }} label={t("vehicle_information")} value="vehicleInfo" />
              <Tab sx={{ textTransform: 'none' }} label={t("unavailability")} value="unavailability" />
            </TabList>
          </Box>

          <TabPanel value="vehicleInfo" sx={{ p:0, overflowY: 'scroll' }}>
            <Typography sx={{pl: 2, py: 2, fontSize: 16}} variant='subtitle2' color="text.secondary">
              {t("vehicle_specifications")}
            </Typography>
            <VehicleInfo fields={vehicleSpecifications} />
            <Typography sx={{pl: 2, pb: 2, fontSize: 16}} variant='subtitle2' color="text.secondary">
              {t("boom_specifications")}
            </Typography>
            <VehicleInfo fields={boomSpecifications} />
            <Typography sx={{pl: 2, pb: 2, fontSize: 16}} variant='subtitle2' color="text.secondary">
              {t("pump_specifications")}
            </Typography>
            <VehicleInfo fields={pumpSpecifications} />
            <Typography sx={{pl: 2, pb: 2, fontSize: 16}} variant='subtitle2' color="text.secondary">
              {t("pipes")}
            </Typography>
            <VehicleInfo fields={pipes} />
          </TabPanel>

          <TabPanel value="unavailability" sx={{ p:0, overflowY: 'scroll' }}>
            <TimeOff
              unavailablePeriods={allUnavailablePeriod?.data}
              vehicleId={initialFormValues.vehicleId!}
            />
          </TabPanel>
        </TabContext>
      </Box>
    </Drawer>
  );
};

export default VehicleDetailsModal;
