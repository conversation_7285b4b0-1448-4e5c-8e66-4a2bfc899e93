import {
  Delete01Icon,
  Tick01Icon,
  UserCircleIcon,
  Calendar04Icon,
} from "@hugeicons/react";
import {
  Autocomplete,
  Box,
  IconButton,
  Stack,
  TextFieldProps,
  Typography,
  useTheme,
} from "@mui/material";
import { LocalizationProvider, DateTimePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { useFormik } from "formik";
import { CeAvatar, CeButton, CeTextField } from "src/common/components";
import {
  CreateTaskCommentDto,
  CreateTaskDto,
  Task,
  TaskComment,
  TaskFormFlow,
  TaskFormValues,
  TaskStatus,
  UpdateTaskDto,
} from "src/common/types/tasks";
import enGb from "date-fns/locale/en-GB";
import { format, isBefore } from "date-fns";
import { getCurrentUser, useUsers } from "src/common/api";

import { stringAvatar } from "src/common/utils/avatar";
import {
  turnTaskFormValuesIntoCreateDto,
  turnTaskFormValuesIntoUpdateDto,
} from "src/common/utils/task";
import { FormRow } from "./FormRow";
import * as yup from "yup";
import CommentsAndActivities from "./CommentsAndActivities";
import { useTranslation } from "react-i18next";
import LabelField from "./LabelField";
import StatusField from "./StatusField";
import PriorityField from "./PriorityField";
import { ErrorMessage } from "src/common/components/custom/ErrorMessage";
import { Role } from "src/common/types";
import { useTaskLabels } from "src/common/api/labels";
import {
  CreateTaskLabelDto,
  UpdateTaskLabelDto,
} from "src/common/types/labels";

interface TaskFormProps {
  initialFormValues: TaskFormValues;
  handleCreateTask: (taskData: CreateTaskDto) => Promise<Task>;
  handleDeleteTask: (taskId: number) => void;
  handleCreateComment: (commentData: CreateTaskCommentDto) => void;
  handleCreateLabel: (labelData: CreateTaskLabelDto) => void;
  handleUpdateLabel: (labelData: UpdateTaskLabelDto) => void;
  handleUpdateTask: (taskToUpdate: UpdateTaskDto) => void;
  closeTaskDrawer: () => void;
}

const TaskForm = ({
  initialFormValues,
  handleCreateTask,
  handleCreateComment,
  closeTaskDrawer,
  handleUpdateTask,
  handleDeleteTask,
  handleCreateLabel,
  handleUpdateLabel,
}: TaskFormProps) => {
  const theme = useTheme();
  const { t } = useTranslation("common");
  const currentUser = getCurrentUser();
  const isCreate = initialFormValues.flow === TaskFormFlow.CREATE;

  const formik = useFormik<TaskFormValues>({
    initialValues: initialFormValues,
    validationSchema: yup.object().shape({
      dueDate: yup
        .date()
        .nullable()
        .required("Due date is required")
        .typeError(t("common:required"))
        .required(t("common:required"))
        .typeError(t("common:cannot-select-past-date-to"))
        .test("is-future-date", "Due date must be in the future", (value) => {
          if (!value) {
            return false;
          }
          return !isBefore(new Date(value), new Date());
        }),
      priority: yup.mixed().required(t("common:required")),
      status: yup.mixed().required(t("common:required")),
      label: yup.mixed().required(t("common:required")),
      description: yup.string().nullable().required(t("common:required")),
      assignee: yup
        .object()
        .required(t("common:required"))
        .typeError(t("common:required")),
    }),

    onSubmit: async (values: TaskFormValues) => {
      if (isCreate) {
        const payload = turnTaskFormValuesIntoCreateDto(values);
        await handleCreateTask(payload);
        return;
      }

      const payload = turnTaskFormValuesIntoUpdateDto(values);
      handleUpdateTask(payload);
    },
  });

  const {
    data: allUsersData,
    isLoading: isLoadingUsers,
    refetch: refetchUsers,
  } = useUsers(
    { role: Role.DISPATCHER, expressions: [], sortModel: [] },
    formik.values.assigneeBeingSet
  );
  const allUsers = allUsersData?.data || [];
  const {
    data: allTaskLabels,
    isLoading: isLoadingTasksLabels,
    refetch: refetchTasks,
  } = useTaskLabels(
    { expressions: [], sortModel: [] },
    formik.values.labelBeingSet
  );

  const createComment = (comment: TaskComment) => {
    if (
      initialFormValues.flow === TaskFormFlow.UPDATE &&
      initialFormValues.taskId
    ) {
      formik.setFieldValue("comments", [...formik.values.comments, comment]);
      handleCreateComment({
        content: comment.content,
        taskId: initialFormValues.taskId,
      });
    }
  };
  const formatedDate =
    formik.values.dueDate &&
    format(new Date(formik.values.dueDate), "dd/MM/yyyy HH:mm");
  const taskIsComplete = formik.values.status === TaskStatus.DONE;
  return (
    <Stack
      direction="column"
      height="100vh"
      overflow="hidden"
      width="35vw"
      component="form"
      noValidate
      onSubmit={formik.handleSubmit}
    >
      <Box
        sx={{
          py: 2,
          px: 3,
          textAlign: "center",
          position: "sticky",
          top: 0,
          zIndex: 1,
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Typography
            variant="h6"
            fontWeight={500}
            fontSize={20}
            letterSpacing={"0.15px"}
            flex={2}
            textAlign="start"
          >
            {isCreate
              ? "Create Task"
              : `Update Task ${initialFormValues.taskId}`}
          </Typography>
          {!isCreate && (
            <Box
              sx={{
                flex: 1,
                display: "flex",
                justifyContent: "flex-end",
                gap: 3,
                alignItems: "center",
              }}
            >
              <IconButton
                edge="end"
                aria-label="delete"
                onClick={() => {
                  if (formik.values.taskId) {
                    handleDeleteTask(formik.values.taskId);
                  }
                }}
              >
                <Delete01Icon
                  size={20}
                  variant="stroke"
                  type="rounded"
                  color={theme.palette.action.active}
                />
              </IconButton>
              <CeButton
                variant="outlined"
                onClick={() => {
                  const payload = turnTaskFormValuesIntoUpdateDto({
                    ...formik.values,
                    status: TaskStatus.DONE,
                  });
                  handleUpdateTask(payload);
                }}
                sx={{
                  color: taskIsComplete
                    ? theme.palette.primary.contrastText
                    : theme.palette.action.active,
                  borderColor: taskIsComplete
                    ? theme.palette.primary.contrastText
                    : theme.palette.action.active,
                  backgroundColor: taskIsComplete
                    ? theme.palette.success.main
                    : theme.palette.background.default,
                  py: 0,
                  height: 30,
                }}
                startIcon={
                  <Tick01Icon
                    size={20}
                    color={"currentColor"}
                    variant={"solid"}
                  />
                }
                size="small"
              >
                <Typography
                  fontWeight={700}
                  fontSize={13}
                  letterSpacing={0.46}
                  whiteSpace="nowrap"
                >
                  {taskIsComplete ? t("completed") : t("mark-complete")}
                </Typography>
              </CeButton>
            </Box>
          )}
        </Stack>
      </Box>

      <Box
        sx={{
          overflowY: "auto",
          flex: 1,
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Box p={3}>
          <Stack px={2} gap={2}>
            <FormRow label={t("assignee")}>
              {!formik.values.assignee && !formik.values.assigneeBeingSet && (
                <>
                  <CeButton
                    variant="text"
                    sx={{ fontWeight: 700, size: 15, letterSpacing: 0.46 }}
                    startIcon={<UserCircleIcon />}
                    onClick={() =>
                      formik.setFieldValue("assigneeBeingSet", true)
                    }
                  >
                    {t("no-assignee")}
                  </CeButton>
                  {formik.touched.assignee && formik.errors.assignee && (
                    <ErrorMessage message={formik.errors.assignee} />
                  )}
                </>
              )}
              {formik.values.assigneeBeingSet && allUsers && (
                <Autocomplete
                  size="small"
                  value={formik.values.assignee}
                  onChange={(_, nextValue) => {
                    formik.setFieldValue("assignee", nextValue);
                    formik.setFieldValue("assigneeBeingSet", false);
                  }}
                  options={allUsers}
                  getOptionLabel={(option) =>
                    `${option?.firstName} ${option?.lastName}` || ""
                  }
                  isOptionEqualToValue={(option, value) =>
                    option.id === value?.id
                  }
                  renderOption={(props, option) => (
                    <li style={{ padding: "8px 16px " }} {...props}>
                      <CeAvatar
                        size="medium"
                        {...stringAvatar(
                          `${option.firstName} ${option.lastName}`
                        )}
                        sx={{
                          mr: 2,
                          backGroundColor: theme.palette.grey[300],
                        }}
                      />
                      <Typography
                        fontWeight={700}
                        fontSize={16}
                        letterSpacing={0.15}
                        onBlur={() => formik.setFieldTouched("assignee", true)}
                      >
                        {option.firstName} {option.lastName}
                      </Typography>
                    </li>
                  )}
                  renderInput={(params) => (
                    <CeTextField
                      sx={{
                        "& .MuiInputBase-input": {
                          fontWeight: 700,
                          fontSize: 16,
                          letterSpacing: 0.15,
                        },
                      }}
                      {...params}
                      InputLabelProps={{ shrink: true }}
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: formik.values.assignee && (
                          <CeAvatar
                            size="medium"
                            {...stringAvatar(
                              `${formik.values.assignee.firstName} ${formik.values.assignee.lastName}`
                            )}
                            sx={{
                              marginRight: 1,
                            }}
                          />
                        ),
                      }}
                      required
                      size="small"
                      fullWidth
                      error={
                        !!formik.errors.assignee && formik.touched.assignee
                      }
                      helperText={
                        formik.touched.assignee && formik.errors.assignee
                      }
                    />
                  )}
                />
              )}
              {formik.values.assignee && !formik.values.assigneeBeingSet && (
                <CeButton
                  variant="text"
                  onClick={() => formik.setFieldValue("assigneeBeingSet", true)}
                >
                  <CeAvatar
                    size="medium"
                    {...stringAvatar(
                      `${formik.values.assignee.firstName} ${formik.values.assignee.lastName}`
                    )}
                    sx={{
                      mr: 2,
                      backGroundColor: theme.palette.grey[300],
                    }}
                  />
                  <Stack direction="row" gap={0.5}>
                    <Typography
                      fontWeight={700}
                      fontSize={16}
                      letterSpacing={0.15}
                      color={theme.palette.text.primary}
                    >
                      {formik.values.assignee.firstName}
                    </Typography>
                    <Typography
                      ml={0.5}
                      fontWeight={700}
                      fontSize={16}
                      letterSpacing={0.15}
                      color={theme.palette.text.primary}
                    >
                      {formik.values.assignee.lastName}
                    </Typography>
                  </Stack>
                </CeButton>
              )}
            </FormRow>

            <FormRow label={t("due-date")}>
              <LocalizationProvider
                dateAdapter={AdapterDateFns}
                adapterLocale={enGb}
              >
                <DateTimePicker
                  open={formik.values.dueDateBeingSet}
                  onClose={() => formik.setFieldValue("dueDateBeingSet", false)}
                  minutesStep={5}
                  renderInput={(props: TextFieldProps) => (
                    <>
                      <CeTextField
                        {...props}
                        sx={{
                          visibility: "hidden",
                          position: "absolute",
                          left: 25,
                        }}
                      />
                      {!formik.values.dueDate ? (
                        <CeButton
                          variant="text"
                          onClick={() =>
                            formik.setFieldValue("dueDateBeingSet", true)
                          }
                          startIcon={<Calendar04Icon />}
                          sx={{
                            fontWeight: 700,
                            fontSize: 15,
                            letterSpacing: 0.48,
                          }}
                        >
                          {t("add-date")}
                        </CeButton>
                      ) : (
                        <CeButton
                          variant="text"
                          sx={{
                            fontWeight: 400,
                            fontSize: 16,
                            letterSpacing: 0.15,
                            color: theme.palette.text.primary,
                          }}
                          onClick={() =>
                            formik.setFieldValue("dueDateBeingSet", true)
                          }
                        >
                          {formatedDate}
                        </CeButton>
                      )}
                      {formik.touched.dueDate && formik.errors.dueDate && (
                        <ErrorMessage message={formik.errors.dueDate} />
                      )}
                    </>
                  )}
                  label={t("due-date")}
                  value={formik.values.dueDate}
                  onChange={(newValue: Date | null) => {
                    if (newValue) {
                      const date = new Date(newValue);
                      if (date.getHours() !== 0 || date.getMinutes() !== 0) {
                        formik.setFieldValue("dueDate", date.toISOString());
                      }
                    }
                  }}
                />
              </LocalizationProvider>
            </FormRow>

            <FormRow label={t("label")}>
              <LabelField
                formik={formik}
                labels={allTaskLabels?.data || []}
                handleCreateLabel={handleCreateLabel}
                handleUpdateLabel={handleUpdateLabel}
              />
              {formik.touched.label && formik.errors.label && (
                <ErrorMessage message={formik.errors.label} />
              )}
            </FormRow>

            <FormRow label={t("priority")}>
              <PriorityField formik={formik} />
            </FormRow>

            <FormRow label={t("status")}>
              <StatusField formik={formik} />
            </FormRow>

            <FormRow label={t("description")} childrenFlex="0 0 63%">
              <CeTextField
                multiline
                name="description"
                value={formik.values.description || ""}
                onChange={formik.handleChange}
                variant="outlined"
                size="small"
                fullWidth
                rows={5}
                onBlur={() => formik.setFieldTouched("description", true)}
                error={
                  !!formik.errors.description && formik.touched.description
                }
                helperText={
                  formik.touched.description && formik.errors.description
                }
              />
            </FormRow>
          </Stack>
        </Box>
        <Box
          p={3}
          sx={{
            backgroundColor: theme.palette.grey[50],
            flexGrow: 1,
            borderTop: `1px solid ${theme.palette.divider}`,
          }}
        >
          {!isCreate && (
            <CommentsAndActivities
              taskId={formik.values.taskId}
              onSubmitComment={createComment}
            />
          )}
        </Box>
      </Box>

      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-end",
          p: "16px 24px",
          gap: 2,
          textAlign: "center",
          position: "sticky",
          bottom: 0,
          zIndex: 1,
          borderTop: `1px solid ${theme.palette.divider}`,
        }}
      >
        <CeButton
          variant="text"
          size="large"
          onClick={closeTaskDrawer}
          sx={{
            fontSize: 15,
            fontWeight: 700,
            letterSpacing: 0.46,
            p: "8px 11px",
          }}
        >
          {t("cancel")}
        </CeButton>
        <CeButton
          type="submit"
          sx={{
            fontSize: 15,
            fontWeight: 700,
            letterSpacing: 0.46,
            p: "8px 22px",
          }}
        >
          {t("submit")}
        </CeButton>
      </Box>
    </Stack>
  );
};

export default TaskForm;
