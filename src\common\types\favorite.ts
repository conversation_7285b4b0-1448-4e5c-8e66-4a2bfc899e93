import { CommonEntity, PossibleSortDir } from "./common";
import { Company } from "./company";
import { Manager } from "./manager";
import { User } from "./user";

export interface Favorite extends CommonEntity {
  dispatcherCompanyId: number;
  operatorCompanyId: number;
  dispatcherManager: User;
  operatorManager: Manager;
  operatorCompany: Company;
}

export interface CreateFavoriteDto {
  dispatcherCompanyId: number;
  operatorCompanyId: number;
}
export interface FavoritesWithCount {
  totalCount: number;
  data: Favorite[];
}

export interface UpdateFavoriteDto extends CreateFavoriteDto {}

export interface GetFavoriteDto {
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
  dispatcherCompanyId?: number | null;
  operatorCompanyId?: number[] | null;
}
