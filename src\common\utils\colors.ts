import {
  red,
  pink,
  purple,
  deepOrange,
  deepPurple,
  indigo,
  blue,
  lightBlue,
  cyan,
  teal,
  green,
  lightGreen,
  lime,
  yellow,
  amber,
  orange,
  brown,
  grey,
  blueGrey,
} from '@mui/material/colors'
import { PossibleTheme } from '../types'
import { secureMathRandom } from 'src/common/utils/secureMathRandom'

const lightShades = [400, 300, 200, 100, 50]
const darkShades = [500, 600, 700, 800, 900]

type PossibleColors =
  | 'deepOrange'
  | 'blue'
  | 'cyan'
  | 'green'
  | 'deepPurple'
  | 'lightBlue'
  | 'blueGrey'
  | 'lightGreen'
  | 'red'
  | 'yellow'
  | 'amber'
  | 'orange'
  | 'brown'
  | 'indigo'
  | 'grey'
  | 'pink'
  | 'teal'
  | 'lime'
  | 'purple'

const colorMap = {
  deepOrange,
  blue,
  cyan,
  green,
  deepPurple,
  lightBlue,
  blueGrey,
  lightGreen,
  red,
  yellow,
  amber,
  orange,
  brown,
  indigo,
  grey,
  pink,
  teal,
  lime,
  purple,
}

const colors = Object.values(colorMap)

let lightColors: string[] = []

for (let i = 0; i < lightShades.length; i++) {
  const lightColorsTemp = colors.map((color: any) => color[lightShades[i]])
  lightColors = [...lightColors, ...lightColorsTemp]
}

let darkColors: string[] = []

for (let i = 0; i < darkShades.length; i++) {
  const darkColorsTemp = colors.map((color: any) => color[darkShades[i]])
  darkColors = [...darkColors, ...darkColorsTemp]
}

const allColors = {
  light: lightColors,
  dark: darkColors,
}

// if theme is dark, we need light colors, else dark colors
const getColorsBasedOnTheme = (theme: PossibleTheme) => {
  const neededColorsBasedOnTheme = theme === 'light' ? 'dark' : 'light'
  return neededColorsBasedOnTheme
}

export const getRandomColor = (theme: PossibleTheme) => {
  const neededColors = allColors[getColorsBasedOnTheme(theme)]
  const randomNumber = secureMathRandom()

  const color = neededColors[Math.floor(randomNumber * lightColors.length)]
  return color
}

export const getColorByOrder = (colorNumber: number, theme: PossibleTheme) => {
  const neededColors = allColors[getColorsBasedOnTheme(theme)]
  const color = neededColors[colorNumber]
  return color
}

export const getColorByName = (colorName: PossibleColors, colorDarkness: 0 | 1 | 2 | 3 | 4, theme: PossibleTheme) => {
  let shades = lightShades
  if (theme === 'light') {
    shades = darkShades
  }
  const shade = shades[colorDarkness]
  const color = colorMap[colorName]
  // @ts-ignore
  return color[shade]
}
