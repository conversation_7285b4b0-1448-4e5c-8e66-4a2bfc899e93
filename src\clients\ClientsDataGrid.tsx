import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Typography } from "@mui/material";

import { Client, ClientFormValues } from "src/common/types/client";
import {
  DataGrid,
  GridColDef,
  GridFilterModel,
  GridRenderCellParams,
  GridSortModel,
} from "@mui/x-data-grid";
import { FeatureFlag } from "src/common/components";
import { useTranslation } from "react-i18next";
import { stringAvatar } from "src/common/utils/avatar";
import {
  useCreateNewClient,
  useDeleteClient,
  useUpdateClient,
} from "src/common/api/client";
import { useRecoilState } from "recoil";
import {
  clientDeleteModalState,
  clientFormValuesState,
} from "src/common/state/client";
import { ClientActionModal } from "./ClientActionModal";
import { turnClientDetailsIntoDto } from "src/common/utils";
import DeleteClientModal from "./DeleteClientModal";
import { CeAvatar } from "src/common/components";
import { CeDataGridToolbar } from "src/common/components/custom/company/CeDataGridToolbar";
import { GridColumnTypography } from "src/common/components/custom/company/GridColumnTypography";
import { Delete01Icon, Edit02Icon } from "@hugeicons/react";
import usePersistentGridState, {
  GridStateSnapshot,
  UpdateGridStatePart,
} from "src/common/utils/gridState";
import { useLocation } from "react-router-dom";
import { useCallback } from "react";
import { doesNumberOrEmptyOperator } from "src/common/types";

interface ClientsDataGridProps {
  page: number;
  pageSize: number;
  total: number;
  isServerDriven: boolean;
  handleSortModelChange?: (sortModel: GridSortModel) => void;
  onDatagridFiltersChange?: (datagridFilter: GridFilterModel) => void;
  gridState: GridStateSnapshot;
  updateGridStatePart: UpdateGridStatePart;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
  data: Client[];
  isLoadingClients: boolean;
  refetchClients: () => void;
  shouldRenderRefreshButton: boolean;
  shouldRenderAddButton: boolean;
}

export const ClientsDataGrid = ({
  page,
  pageSize,
  total,
  isServerDriven,
  handleSortModelChange,
  onDatagridFiltersChange,
  gridState,
  updateGridStatePart,
  onPageChange,
  onPageSizeChange,
  data,
  isLoadingClients,
  refetchClients,
  shouldRenderRefreshButton,
  shouldRenderAddButton,
}: ClientsDataGridProps) => {
  const { t } = useTranslation(["manager", "common", "dispatcher"]);

  const [clientFormValues, setClientFormValues] = useRecoilState(
    clientFormValuesState
  );
  const [clientDeleteValues, setClientDeleteValues] = useRecoilState(
    clientDeleteModalState
  );

  const { mutateAsync: handleCreateClient, isLoading: isCreatingClient } =
    useCreateNewClient();
  const { mutateAsync: handleUpdateClient, isLoading: isUpdatingClient } =
    useUpdateClient();

  const { mutateAsync: handleDeleteClient, isLoading: isDeletingClient } =
    useDeleteClient();

  const isLoading =
    isLoadingClients ||
    isCreatingClient ||
    isUpdatingClient ||
    isDeletingClient;

  const renderEditActions = (params: GridRenderCellParams) => {
    const client: ClientFormValues = params.row;

    return (
      <Stack
        direction="row"
        justifyContent="center"
        alignItems="center"
        spacing={1}
      >
        <IconButton
          aria-label="update client"
          disabled={false}
          size="small"
          onClick={() => {
            if (client.id) {
              const formValues = turnClientDetailsIntoDto(client);
              setClientFormValues({
                ...formValues,
                flow: "Update",
                id: client.id,
              });
            }
          }}
        >
          <Edit02Icon size={16} variant={"stroke"} />
        </IconButton>
        <FeatureFlag flags={["operator manager"]}>
          <IconButton
            aria-label="delete client"
            disabled={false}
            color="error"
            size="small"
            onClick={() => {
              if (client.id) {
                setClientDeleteValues({ flow: "Delete", id: client.id });
              }
            }}
          >
            <Delete01Icon size={16} variant={"stroke"} />
          </IconButton>
        </FeatureFlag>
      </Stack>
    );
  };
  const columns: GridColDef[] = [
    {
      field: "editActions",
      headerName: `${t("common:edit")}`,
      sortable: false,
      renderCell: (params: GridRenderCellParams<string>) =>
        renderEditActions(params),
      filterable: false,
    },
    {
      field: "operator_client.id",
      headerName: `id`,
      headerAlign: "left",
      align: "left",
      width: 220,
      filterOperators: doesNumberOrEmptyOperator,
      renderCell: (params) => <GridColumnTypography value={params.row.email} />,
    },
    {
      field: "operator_client.name",
      headerName: `${t("common:client-name")}`,
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => <GridColumnTypography value={params.row?.name} />,
    },
    {
      field: "operator_client.lastName",
      headerName: `${t("common:last-name")}`,
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => (
        <GridColumnTypography value={params.row?.lastName} />
      ),
    },
    {
      field: "operator_client.email",
      headerName: `${t("common:email")}`,
      headerAlign: "left",
      align: "left",
      width: 220,
      renderCell: (params) => <GridColumnTypography value={params.row.email} />,
    },
    {
      field: "operator_client.phoneNumber",
      headerName: `${t("common:client-phone-number")}`,
      headerAlign: "left",
      align: "left",
      width: 220,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.phoneNumber} />
      ),
    },
    {
      field: "operator_client.companyName",
      headerName: `${t("common:client-company-name")}`,
      headerAlign: "left",
      align: "left",
      width: 220,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.companyName} />
      ),
    },
    {
      field: "operator_client.companyVatNumber",
      headerName: `${t("common:vat-number")}`,
      headerAlign: "left",
      align: "left",
      width: 220,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.companyVatNumber} />
      ),
    },
  ];

  const onFilterChange = useCallback(
    (filterModel: GridFilterModel) => {
      if (onDatagridFiltersChange) {
        onDatagridFiltersChange(filterModel);
        updateGridStatePart("filterModel", filterModel);
      }
    },
    [onDatagridFiltersChange]
  );

  const onSortChange = useCallback(
    (sortModel: GridSortModel) => {
      if (handleSortModelChange) {
        handleSortModelChange(sortModel);
        updateGridStatePart("sortModel", sortModel);
      }
    },
    [handleSortModelChange]
  );

  return (
    <>
      <DataGrid
        sx={{
          border: "none",
          paddingTop: 0.5,
          background: (theme) => theme.palette.background.paper,
        }}
        pagination
        page={gridState.page - 1}
        pageSize={gridState.pageSize}
        rowsPerPageOptions={[20, 40, 60, 80, 100]}
        rowCount={total}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        columnVisibilityModel={gridState.columnVisibilityModel}
        onColumnVisibilityModelChange={(model) =>
          updateGridStatePart("columnVisibilityModel", model)
        }
        sortingMode={isServerDriven ? "server" : "client"}
        onSortModelChange={isServerDriven ? onSortChange : undefined}
        filterMode={isServerDriven ? "server" : "client"}
        onFilterModelChange={isServerDriven ? onFilterChange : undefined}
        sortModel={gridState.sortModel}
        paginationMode="server"
        components={{
          Toolbar: () => (
            <CeDataGridToolbar
              shouldRenderRefreshButton={shouldRenderRefreshButton}
              shouldRenderAddButton={shouldRenderAddButton}
              onRefreshButtonClick={refetchClients}
              addButtonClickHandler={() =>
                setClientFormValues({
                  ...clientFormValues,
                  flow: "Create",
                })
              }
              addButtonDescription="Add client"
            />
          ),
        }}
        columns={columns}
        rows={data}
        disableSelectionOnClick
      />
      <ClientActionModal
        initialFormValues={clientFormValues}
        setClientFormValues={setClientFormValues}
        handleCreateClient={handleCreateClient}
        handleUpdateClient={handleUpdateClient}
        isLoading={isLoading}
      />
      <DeleteClientModal
        setClientDeleteValues={setClientDeleteValues}
        clientDeleteValues={clientDeleteValues}
        isLoading={isLoading}
        handleDeleteClient={handleDeleteClient}
      />
    </>
  );
};
