import { CeButton, MainModal } from "src/common/components";
import { Button, DialogActions, Stack, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";

interface ConfirmReservationModalProps {
    isOpen: boolean;
    handleClose: () => void;
    handleConfirm: () => void;
}

export const ConfirmReservationModal: React.FC<ConfirmReservationModalProps> = ({
    isOpen,
    handleClose,
    handleConfirm,
}) => {
    const { t } = useTranslation(["common"]);
    return (
        <MainModal
            title= {t("confirm-reservation-changes")}
            isOpen={isOpen}
            handleClose={handleClose}
        >
            <Stack
               sx={{ width: "400px" }}
            >
                <Typography variant="body1" gutterBottom>
                    {t("are-you-sure")}
                </Typography>

                <DialogActions>
                    <CeButton onClick={handleClose} variant="text">
                        {t("cancel")}
                    </CeButton>
                    <CeButton type="submit" onClick={handleConfirm}>
                        {t("confirm")}
                    </CeButton>
                </DialogActions>
            </Stack>
        </MainModal>
    );
};
