import { EventContentArg } from "@fullcalendar/core";
import CancelIcon from "@mui/icons-material/Cancel";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import EventNoteIcon from "@mui/icons-material/EventNote";
import PendingIcon from "@mui/icons-material/Pending";
import { Box, Grid, Typography } from "@mui/material";
import { differenceInHours } from "date-fns";
import { useTranslation } from "react-i18next";
import { FC } from "react";
import { JOB_STEPS } from "src/common/constants";

type Props = {
  arg: EventContentArg;
};

const styleIcon = {
  fontSize: 25,
  marginTop: 1,
};

const EventContentDay: FC<Props> = (props) => {
  const { title, start, end } = props.arg.event;
  const { t } = useTranslation(["common"]);
  const { address, date, quantity, type, comment, jobStatus } =
    props.arg.event.extendedProps;

  if (!end || !start) {
    throw new Error("Date start or date end are missing");
  }
  const durationEvent = differenceInHours(end, start);

  const changeJobStatus = (jobStatus: string) => {
    if (jobStatus === "NOT_STARTED") {
      return <EventNoteIcon sx={styleIcon} />;
    } else if (jobStatus === "COMPLETE") {
      return <CheckCircleIcon sx={styleIcon} />;
    } else if (jobStatus === "CANCELLED") {
      return <CancelIcon sx={styleIcon} />;
    } else {
      return <PendingIcon sx={styleIcon} />;
    }
  };

  return (
    <Box
      overflow={"hidden"}
      px={1}
      sx={{ background: (theme) => theme.palette.primary.dark, height: "100%" }}
    >
      {durationEvent < 1 ? (
        <Typography fontWeight="bold" variant="body2">
          {title}
        </Typography>
      ) : null}
      {durationEvent >= 1 && durationEvent < 2 ? (
        <Grid container>
          <Grid item xs={6}>
            <Typography variant="caption">{date}</Typography>
            <Typography fontWeight="bold" variant="body1">
              {title}
            </Typography>
          </Grid>
          {type === "job" ? (
            <>
              <Grid item display="flex" justifyContent="flex-end" xs={4}>
                <Typography mt={1} variant="body2">
                  {t(
                    JOB_STEPS.find((step) => step.name === jobStatus)?.label ||
                      "common:not-started"
                  )}
                </Typography>
              </Grid>
              <Grid item display="flex" justifyContent="flex-end" xs={2}>
                {changeJobStatus(jobStatus)}
              </Grid>
            </>
          ) : null}
        </Grid>
      ) : null}
      {durationEvent >= 2 && durationEvent < 3 ? (
        <Grid container>
          <Grid item xs={6}>
            <Typography variant="caption">{date}</Typography>
            <Typography fontWeight="bold" variant="body1">
              {title}
            </Typography>
          </Grid>
          {type === "job" ? (
            <>
              <Grid item display="flex" justifyContent="flex-end" xs={4}>
                <Typography mt={1} variant="body2">
                  {t(
                    JOB_STEPS.find((step) => step.name === jobStatus)?.label ||
                      "common:not-started"
                  )}
                </Typography>
              </Grid>
              <Grid item display="flex" justifyContent="flex-end" xs={2}>
                {changeJobStatus(jobStatus)}
              </Grid>
              <Grid item mt={1} xs={12}>
                <Typography variant="body2">{address}</Typography>
              </Grid>
            </>
          ) : null}
        </Grid>
      ) : null}
      {durationEvent >= 3 && durationEvent < 4 ? (
        <Grid container>
          <Grid item xs={6}>
            <Typography variant="caption">{date}</Typography>
            <Typography fontWeight="bold" variant="body1">
              {title}
            </Typography>
          </Grid>
          {type === "job" ? (
            <>
              <Grid item display="flex" justifyContent="flex-end" xs={4}>
                <Typography mt={1} variant="body2">
                  {t(
                    JOB_STEPS.find((step) => step.name === jobStatus)?.label ||
                      "common:not-started"
                  )}
                </Typography>
              </Grid>
              <Grid item display="flex" justifyContent="flex-end" xs={2}>
                {changeJobStatus(jobStatus)}
              </Grid>
              <Grid item mt={1} xs={12}>
                <Typography variant="body2">{address}</Typography>
              </Grid>
              <Grid item mt={1} xs={12}>
                <Typography fontWeight="bold" variant="body2">
                  {quantity} m³
                </Typography>
              </Grid>
            </>
          ) : null}
        </Grid>
      ) : null}
      {durationEvent >= 4 ? (
        <Grid container>
          <Grid item xs={6}>
            <Typography variant="caption">{date}</Typography>
            <Typography fontWeight="bold" variant="body1">
              {title}
            </Typography>
          </Grid>
          {type === "job" ? (
            <>
              <Grid item display="flex" justifyContent="flex-end" xs={4}>
                <Typography mt={1} variant="body2">
                  {t(
                    JOB_STEPS.find((step) => step.name === jobStatus)?.label ||
                      "common:not-started"
                  )}
                </Typography>
              </Grid>
              <Grid item display="flex" justifyContent="flex-end" xs={2}>
                {changeJobStatus(jobStatus)}
              </Grid>
              <Grid item mt={1} xs={12}>
                <Typography variant="body2">{address}</Typography>
              </Grid>
              <Grid item mt={1} xs={12}>
                <Typography fontWeight="bold" variant="body2">
                  {quantity} m³
                </Typography>
              </Grid>
            </>
          ) : null}
          <Grid item xs={12}>
            {comment ? (
              <>
                <Typography
                  pt={1}
                  sx={{ textDecoration: "underline" }}
                  variant="body2"
                >
                  {t("comments")}:
                </Typography>
                <Typography fontWeight="bold" variant="body2">
                  {comment}
                </Typography>
              </>
            ) : null}
          </Grid>
        </Grid>
      ) : null}
    </Box>
  );
};

export default EventContentDay;
