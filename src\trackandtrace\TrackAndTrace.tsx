import { <PERSON><PERSON><PERSON>, CardContent, Grid, Stack } from '@mui/material';
import React, { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, CeTextField } from 'src/common/components';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from 'src/common/components/languageSwitcher/LanguageSwitcher';
import { useSetRecoilState } from 'recoil';
import { trackAndTraceState } from 'src/common/state';
import { useTrackAndTraceAuthentication } from 'src/common/api';

export const TrackAndTrace = () => {
    const { t } = useTranslation("common");
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const setTrackAndTraceFormValue = useSetRecoilState(trackAndTraceState);

    const orderNumber = searchParams.get("orderNumber") || "";
    const clientEmail = searchParams.get("clientEmail") || "";

    const {
        mutate: handleTrackAndTrace,
        isLoading,
        isSuccess
      } = useTrackAndTraceAuthentication();

    const formik = useFormik({
        initialValues: {
            orderNumber: orderNumber,
            clientEmail: clientEmail
        },
        validationSchema: yup.object({
            orderNumber: yup
                .string()
                .required(t('orderNumberRequired')),
            clientEmail: yup
                .string()
                .email(t('email-valid'))
                .required(t('email-required')),
        }),
        onSubmit: (values) => {
            handleTrackAndTrace(values)
            setTrackAndTraceFormValue(values)
        }
    });


    useEffect(() => {
        if (isSuccess) {
            navigate(`details/${formik.values.orderNumber}`, { replace: true });
        }
      }, [isSuccess, navigate]); 
      

    return (
        <Grid
            container
            direction="column"
            justifyContent="center"
            alignItems="center"
            sx={{ minHeight: '100vh' }}
        >
            <CeCard sx={{ minWidth: 275, padding: 2 }}>
                <CardContent sx={{ paddingBottom: 0 }}>
                    <img src="/images/ConcretEasy.png" width={250} alt="ConcretEasy Logo" />
                </CardContent>
                <CardContent sx={{ paddingTop: 0, textAlign: 'center' }}>
                    <LanguageSwitcher fullWidth={false} />
                </CardContent>
                <CardContent>
                    <Stack
                        component="form"
                        spacing={2}
                        noValidate
                        onSubmit={formik.handleSubmit}
                    >
                        <CeTextField
                            fullWidth
                            id="clientEmail"
                            name="clientEmail"
                            label={t("Email")}
                            type="email"
                            size="small"
                            value={formik.values.clientEmail}
                            onChange={formik.handleChange}
                            error={formik.touched.clientEmail && Boolean(formik.errors.clientEmail)}
                            helperText={formik.touched.clientEmail && formik.errors.clientEmail}
                            required
                        />
                        <CeTextField
                            fullWidth
                            id="orderNumber"
                            name="orderNumber"
                            label={t("order-number")}
                            type="text"
                            size="small"
                            value={formik.values.orderNumber}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            error={
                                formik.touched.orderNumber &&
                                Boolean(formik.errors.orderNumber)
                            }
                            helperText={
                                formik.touched.orderNumber && formik.errors.orderNumber
                            }
                            required
                        />
                        <CardActions sx={{ justifyContent: "space-between", padding: 0 }}>
                            <CeButton
                                color="primary"
                                variant="contained"
                                fullWidth
                                type="submit"
                                disabled={isLoading}
                            >
                                {t('trackOrder')}
                            </CeButton>
                        </CardActions>
                    </Stack>
                </CardContent>
            </CeCard>
        </Grid>
    );
};
