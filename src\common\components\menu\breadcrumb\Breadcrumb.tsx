import { <PERSON>, Chip, Link, emphasize, styled } from "@mui/material";
import useBreadcrumbs from "use-react-router-breadcrumbs";
import { useTranslation } from "react-i18next";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import { routeBreadCrumbList } from "../static/routeBreadCrumbList";
import { getCurrentUser } from "src/common/api";
import { Role } from "src/common/types";
import { staticItems } from "../static/items";
import { Agreement03Icon } from "@hugeicons/react";
import { JSXElementConstructor, ReactElement } from "react";

const StyledBreadcrumb = styled(Chip)(({ theme }) => {
  const backgroundColor =
    theme.palette.mode === "light"
      ? theme.palette.grey[100]
      : theme.palette.grey[800];
  return {
    backgroundColor,
    height: theme.spacing(3),
    color: theme.palette.text.primary,
    fontWeight: theme.typography.fontWeightRegular,
    "&:hover, &:focus": {
      backgroundColor: emphasize(backgroundColor, 0.06),
    },
    "&:active": {
      boxShadow: theme.shadows[1],
      backgroundColor: emphasize(backgroundColor, 0.12),
    },
  };
}) as typeof Chip; // TypeScript only: need a type cast here because https://github.com/Microsoft/TypeScript/issues/26591

export const RouteBreadcrumbs = () => {
  const breadcrumbs = useBreadcrumbs(routeBreadCrumbList, {
    excludePaths: ["/", "/"],
  });

  const { t, i18n } = useTranslation([
    "common",
    "dispatcher",
    "manager",
    "operator",
  ]);

  const currentUser = getCurrentUser();

  const isOperator = currentUser?.role === Role.OPERATOR;

  return (
    <>
      <Box role="presentation" sx={{ marginBottom: 1.5, marginTop: 1.5 }}>
        {!isOperator ? (
          <Breadcrumbs separator="-" aria-label="breadcrumb">
            {breadcrumbs.map(({ match, breadcrumb }) => {
              const bcKeyPath =
                match.pathname?.slice(1, match.pathname.length + 1) || "";
              const item = staticItems.find((i) => i.path === bcKeyPath);
              const icon = item?.icon as
                | ReactElement<any, string | JSXElementConstructor<any>>
                | undefined;
              return (
                <StyledBreadcrumb
                  key={match.pathname}
                  label={breadcrumb}
                  icon={
                    icon || (
                      <Agreement03Icon
                        style={{ fill: "none" }}
                        size={20}
                        color={"currentColor"}
                        variant={"stroke"}
                        type="rounded"
                        opacity={0.8}
                      />
                    )
                  }
                  sx={{ height: 30 }}
                  variant="outlined"
                  // component={"a"}
                  // to={match.pathname}
                ></StyledBreadcrumb>
              );
            })}
          </Breadcrumbs>
        ) : null}
      </Box>
    </>
  );
};
