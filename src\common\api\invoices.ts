import axios, { AxiosError } from "axios";
import { processApiError, processApiSuccess } from "../utils/errors";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { BulkInvoiceLogWithCount, CreateReservationInvoice, CreateReservationInvoiceResponse, GenerateBulkInvoiceDto, GetInvoiceLog, GetInvoiceLogPdf } from "../types";

const backendUrl = process.env.REACT_APP_API_URL;

export const getInvoiceByReservationId = async (reservationId?: number | null) => {
  if (!reservationId) {
    throw new Error("the reservation ID was not provided");
  }
  return axios
    .get(`${backendUrl}/reservation/invoice/${reservationId}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useInvoiceByReservationId = (reservationId?: number | null, enabled: boolean = true) => {
  return useQuery<string, AxiosError | Error>(
    ["invoice", reservationId],
    () => getInvoiceByReservationId(reservationId),
    {
      onError: (err) => processApiError("Unable to fetch invoice", err),
      enabled,
    }
  );
};

export const createReservationInvoice = async (data: CreateReservationInvoice) => {
  if (!data || !data.reservationIds || data.reservationIds.length === 0) {
    throw new Error("The reservation IDs were not provided.");
  }
  return axios
    .post(`${backendUrl}/reservation/invoice`, data, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useCreateReservationInvoice = () => {
  const queryClient = useQueryClient();
  return useMutation<
    CreateReservationInvoiceResponse,
    AxiosError | Error,
    CreateReservationInvoice,
    () => void
  >((data: CreateReservationInvoice) => createReservationInvoice(data), {
    onSuccess: () => {
      queryClient.invalidateQueries("reservation");
      queryClient.invalidateQueries("reservations");
    },
    onError: (err) => processApiError("Unable to create reservation invoice", err),
  });
}

export const getInvoiceLog = async (attr: GetInvoiceLog) => {
  return axios
    .get(`${backendUrl}/invoice-log`, {
      withCredentials: true,
      params: attr,
    })
    .then((response) => response.data)
}
export const useBulkInvoiceLog = (
  attrs: GetInvoiceLog,
  enabled: boolean = true
) => {
  return useQuery<BulkInvoiceLogWithCount, AxiosError | Error>(
    ["bulkInvoiceLog", attrs],
    () => getInvoiceLog(attrs),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch bulkInvoiceLog", err),
      enabled,
    }
  )
}

export const getInvoiceLogPdf = async (attr: GetInvoiceLogPdf) => {
  return axios
    .get(`${backendUrl}/invoice-log/pdf`, {
      withCredentials: true,
      params: attr,
    })
    .then((response) => response.data)
}
export const useBulkInvoiceLogPdf = (
  attrs: GetInvoiceLogPdf,
  enabled: boolean = true
) => {
  return useQuery<string, AxiosError | Error>(
    ["bulkInvoiceLogPdf", attrs],
    () => getInvoiceLogPdf(attrs),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch bulkInvoiceLogPdf", err),
      enabled,
    }
  )
}

export const createBulkInvoiceReservation = async (attrs: GenerateBulkInvoiceDto) => {
  const response = await axios.post(`${backendUrl}/reservation/bulk-invoice`, attrs, {
    withCredentials: true
  });
  return response.data;
}
export const useCreateBulkInvoiceReservation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    CreateReservationInvoiceResponse,
    AxiosError | Error,
    GenerateBulkInvoiceDto,
    () => void
  >((a: GenerateBulkInvoiceDto) => createBulkInvoiceReservation(a), {
    onSuccess: (response) => {
        queryClient.invalidateQueries("bulkInvoiceLog");
        if(response){
          processApiSuccess("Generating the bulk invoice may take a few moments.")
        }
        
      
    },
    onError: (err) => processApiError("Unable to create bulkInvoiceReservation", err),
  });
};

export const base64ToPdfBlobUrl = (base64: string, returnUrl: boolean = false): Blob | string => {
  const base64Data = base64.replace(/^data:application\/pdf;base64,/, '');
  const byteCharacters = atob(base64Data);
  const byteArray = new Uint8Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteArray[i] = byteCharacters.charCodeAt(i);
  }
  const blob = new Blob([byteArray], { type: 'application/pdf' });
  if (returnUrl) {
    return URL.createObjectURL(blob);
  }
  return blob;

};
