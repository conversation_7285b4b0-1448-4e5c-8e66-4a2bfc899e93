import React from "react";
import { styled, Theme, CSSObject } from "@mui/material/styles";
import MuiDrawer from "@mui/material/Drawer";
import List from "@mui/material/List";
import Divider from "@mui/material/Divider";
import { stringAvatar } from "src/common/utils/avatar";
import { getCurrentUser, useGetPendingContractsCount } from "src/common/api";
import { useLogoutUser } from "src/common/api/auth";
import { useTranslation } from "react-i18next";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import { staticItems } from "../static/items";
import { Link as RouterLink, useLocation, useNavigate } from "react-router-dom";
import {
  Badge,
  Box,
  Collapse,
  IconButton,
  Menu,
  MenuItem,
  Switch,
  <PERSON><PERSON><PERSON>,
  Typography,
  useTheme,
} from "@mui/material";
import { useCallback, useEffect, useState } from "react";
import { FeatureFlag } from "../../featureflag/FeatureFlag";
import { getRoles } from "src/common/utils/user";
import { useRecoilState } from "recoil";
import { themeState } from "src/common/state";
import { ReactComponent as HamburgerMenu } from "../../../../images/Hamburger menu.svg";
import { ReactComponent as ArrowDown } from "../../../../images/Chevron Down.svg";
import { Moon02Icon, Logout02Icon } from "@hugeicons/react";
import { Manager, User } from "src/common/types";
import { Role } from "src/common/types";
import { CeAvatar } from "../..";
const drawerWidth = 260;

const openedMixin = (theme: Theme): CSSObject => ({
  width: drawerWidth,
  transition: theme.transitions.create("width", {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  overflowX: "hidden",
});

const closedMixin = (theme: Theme): CSSObject => ({
  transition: theme.transitions.create("width", {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  overflowX: "hidden",
  width: `calc(${theme.spacing(7)} + 1px)`,
  [theme.breakpoints.up("sm")]: {
    width: `calc(${theme.spacing(8)} + 1px)`,
  },
});

const DrawerHeader = styled("div")(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  padding: theme.spacing(0, 1),
  // necessary for content to be below app bar
  ...theme.mixins.toolbar,
}));

const Drawer = styled(MuiDrawer, {
  shouldForwardProp: (prop) => prop !== "open",
})(({ theme, open }) => ({
  width: drawerWidth,
  flexShrink: 0,
  whiteSpace: "nowrap",
  boxSizing: "border-box",
  ...(open && {
    ...openedMixin(theme),
    "& .MuiDrawer-paper": openedMixin(theme),
  }),
  ...(!open && {
    ...closedMixin(theme),
    "& .MuiDrawer-paper": closedMixin(theme),
  }),
}));

interface SideBarProps {
  open: boolean;
  handleDrawerToggle(): void;
}

export const SideBar: React.FC<SideBarProps> = ({
  open,
  handleDrawerToggle,
}) => {
  const { mutate: handleLogoutUser } = useLogoutUser();
  const location = useLocation();
  const params = location.pathname.substring(1) + location.search;
  const muiTheme = useTheme();

  const { t } = useTranslation(["common", "dispatcher"]);

  const getSelectedId = useCallback(() => {
    for (const { id, submenu } of staticItems) {
      if (submenu) {
        const foundSubmenuItem = submenu.find(({ path }) =>
          params.includes(path)
        );
        if (foundSubmenuItem) {
          return { parentId: id, subId: foundSubmenuItem.id };
        }
      }
    }
    const foundItem = staticItems.find(({ path }) => params.includes(path));
    return foundItem ? { parentId: foundItem.id } : undefined;
  }, [params, staticItems]);
  const navigate = useNavigate();

  const [selectedId, setSelectedId] = useState<
    { parentId: string; subId?: string } | undefined
  >(getSelectedId());
  const [expandedItemId, setExpandedItemId] = useState<string | null>(
    selectedId ? selectedId.parentId : null
  );

  useEffect(() => {
    if (getSelectedId()) {
      setSelectedId(getSelectedId());
    }
  }, [params, getSelectedId]);

  const currentUser = getCurrentUser();

  const manager = currentUser as unknown as Manager;
  const company = currentUser as unknown as User;
  const companyName = company?.company?.name || "";
  const isDispatcherManager = manager.role === Role.DISPATCHER_MANAGER;
  const { data: pendingContractsCount } = useGetPendingContractsCount(
    company?.companyId,
    false
  );

  const calculateNotificationCount = (submenu: any) => {
    return submenu.reduce((total: number, subItem: any) => {
      if (subItem.menuName === "common:contracts") {
        return total + (pendingContractsCount || 0);
      }
      return total;
    }, 0);
  };

  const handleClick = (itemId: string, path?: string, subId?: string) => {
    if (subId) {
      setSelectedId({ parentId: itemId, subId });
      setExpandedItemId(itemId);
      return;
    }
    const isExpanded = expandedItemId === itemId;
    setExpandedItemId(isExpanded ? null : itemId);
    if (path === "vehicles") {
      navigate(path);
    }
    if (!selectedId || selectedId.parentId !== itemId) {
      setSelectedId({ parentId: itemId });
    }
  };

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  return (
    <Drawer variant="permanent" open={open}>
      <DrawerHeader>
        {!open && <img src="/logo.png" width={80} height={"auto"} alt="logo" />}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            width: "100%",
          }}
        >
          {open && (
            <>
              <img
                src="/images/ConcretEasy.png"
                width={130}
                height={"auto"}
                alt=""
              />
              <IconButton
                color="inherit"
                aria-label="toggle drawer"
                onClick={handleDrawerToggle}
                edge="start"
                sx={{
                  marginRight: 1,
                }}
              >
                <HamburgerMenu />
              </IconButton>
            </>
          )}
        </Box>
      </DrawerHeader>
      <Divider />
      <List>
        {!open && (
          <ListItemButton
            onClick={handleDrawerToggle}
            sx={{ justifyContent: "center" }}
          >
            <ListItemIcon
              sx={{
                justifyContent: "center",
              }}
            >
              <HamburgerMenu />
            </ListItemIcon>
          </ListItemButton>
        )}
        {staticItems.map((item) => (
          <FeatureFlag key={item.id} flags={item.flags}>
            <div key={item.id}>
              {item.hasDivider ? <Divider /> : null}
              {item.hasTitle && item.title ? (
                <Typography
                  variant="body1"
                  sx={{ fontSize: "12px", color: "text.secondary", pl: 3 }}
                >
                  {open && t(item.title)}
                </Typography>
              ) : null}
              <ListItemButton
                selected={
                  getSelectedId()?.parentId === item.id &&
                  !getSelectedId()?.subId
                }
                onClick={() => handleClick(item.id, item.path)}
                component={item.submenu.length === 0 ? RouterLink : "div"}
                to={item.submenu.length === 0 ? item.path : ""}
                key={item.id}
                sx={{
                  minHeight: 48,
                  justifyContent: open ? "initial" : "center",
                  px: 2.5,
                  "&.Mui-selected": {
                    backgroundColor: "transparent",
                    color: muiTheme.palette.primary.main,
                    "& .MuiListItemIcon-root": {
                      color: muiTheme.palette.primary.main,
                    },
                    "& .MuiTypography-root": {
                      color: muiTheme.palette.primary.main,
                    },
                  },
                }}
              >
                <Tooltip title={t(item.menuName)}>
                  <ListItemIcon
                    sx={{
                      minWidth: 0,
                      mr: open ? 3 : "auto",
                      justifyContent: "center",
                      color: "inherit",
                      "& svg": {
                        fill:
                          selectedId?.parentId === item.id
                            ? muiTheme.palette.primary.main
                            : "currentColor",
                      },
                    }}
                  >
                    {item.icon}
                  </ListItemIcon>
                </Tooltip>

                <ListItemText
                  primary={
                    <Typography>
                      {t(item.menuName)}
                      {item.path === "finance" &&
                      Role.DISPATCHER_MANAGER === manager.role &&
                      Boolean(pendingContractsCount) ? (
                        <Badge
                          sx={{ ml: 2 }}
                          badgeContent={calculateNotificationCount(
                            item.submenu
                          )}
                          color="error"
                        />
                      ) : null}
                    </Typography>
                  }
                  sx={{ opacity: open ? 1 : 0, color: "inherit" }}
                />
                {item.submenu.length !== 0 && (
                  <ArrowDown
                    style={{
                      marginLeft: open ? "auto" : 0,
                      transition: "transform 0.3s",
                      transform:
                        expandedItemId === item.id
                          ? "rotate(180deg)"
                          : "rotate(0deg)",
                    }}
                  />
                )}
              </ListItemButton>
              {item.submenu && expandedItemId === item.id && (
                <Collapse
                  in={expandedItemId === item.id}
                  timeout="auto"
                  unmountOnExit
                >
                  <List component="div" disablePadding>
                    {item.submenu.map((subItem) => (
                      <FeatureFlag key={subItem.id} flags={subItem.flags}>
                        <ListItemButton
                          key={subItem.id}
                          selected={selectedId?.subId === subItem.id}
                          onClick={() =>
                            handleClick(item.id, subItem.path, subItem.id)
                          }
                          component={RouterLink}
                          to={subItem.path}
                          sx={{
                            minHeight: 48,
                            justifyContent: open ? "initial" : "center",
                            px: 2.5,
                            pl: open ? 4 : null,
                            "&.Mui-selected": {
                              backgroundColor: "transparent",
                              color: muiTheme.palette.primary.main,
                              "& .MuiListItemIcon-root": {
                                color: muiTheme.palette.primary.main,
                              },
                              "& .MuiTypography-root": {
                                color: muiTheme.palette.primary.main,
                              },
                            },
                          }}
                        >
                          <Tooltip title={t(subItem.menuName)}>
                            <ListItemIcon
                              sx={{
                                minWidth: 0,
                                mr: open ? 3 : "auto",
                                justifyContent: "center",
                                color: "inherit",
                                "& svg": {
                                  fill:
                                    selectedId?.subId === subItem.id
                                      ? muiTheme.palette.primary.main
                                      : "currentColor",
                                },
                              }}
                            >
                              {subItem.icon}
                            </ListItemIcon>
                          </Tooltip>
                          <ListItemText
                            primary={
                              <Typography>{t(subItem.menuName)}</Typography>
                            }
                            sx={{ opacity: open ? 1 : 0, color: "inherit" }}
                          />
                          {subItem.menuName === "common:contracts" && (
                            <Badge
                              sx={{ marginRight: 8 }}
                              badgeContent={pendingContractsCount}
                              color="error"
                            />
                          )}
                        </ListItemButton>
                      </FeatureFlag>
                    ))}
                  </List>
                </Collapse>
              )}
            </div>
          </FeatureFlag>
        ))}
      </List>

      <Box sx={{ flexGrow: 0.9 }} />

      {open && currentUser && (
        <Box
          sx={{
            p: 2,
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-start",
            flexWrap: "nowrap",
          }}
        >
          <CeAvatar
            size="medium"
            {...stringAvatar(
              `${currentUser.firstName} ${currentUser.lastName}`
            )}
            sx={{ marginRight: 1.5 }}
          />

          <Box>
            <Typography
              variant="body1"
              fontWeight={"bold"}
              fontSize={14}
              sx={{
                whiteSpace: "normal",
                overflowWrap: "break-word",
                wordWrap: "break-word",
                wordBreak: "break-word",
              }}
            >{`${currentUser.firstName} ${currentUser.lastName}`}</Typography>
            <Typography variant="body2" color="text.secondary">
              {currentUser?.role ? getRoles(currentUser.role) : null}
            </Typography>
          </Box>

          <IconButton
            onClick={handleMenuClick}
            sx={{ marginLeft: 1, marginBottom: 2 }}
          >
            <ArrowDown />
          </IconButton>

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "center",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
          >
            <MenuItem
              onClick={(e) => {
                handleLogoutUser();
              }}
            >
              <Logout02Icon
                style={{ fill: "none" }}
                size={24}
                color={"red"}
                variant={"stroke"}
                type="rounded"
                opacity={0.8}
              />
              <Typography sx={{ marginLeft: 0.5 }} color={"red"}>
                {t("sign-out")}
              </Typography>
            </MenuItem>
          </Menu>
        </Box>
      )}
      {open && (
        <Box
          sx={{
            p: 0,
            textAlign: "center",
            marginTop: 2,
            width: "100%",
          }}
        >
          <Typography variant="body1" color="text.secondary">
            {companyName}
          </Typography>
        </Box>
      )}
    </Drawer>
  );
};
