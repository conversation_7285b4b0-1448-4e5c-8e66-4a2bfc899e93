import { useLocation, useNavigate } from "react-router-dom";
import * as yup from "yup";
import { enGB, nlBE, deAT, frCA } from "date-fns/locale";
import { useTranslation } from "react-i18next";
import queryString from "query-string";

import {
  UserCircleIcon,
  CheckmarkCircle02Icon,
  Upload02Icon,
  InformationCircleIcon,
  Briefcase06Icon,
} from "@hugeicons/react";
import {
  reservationEditDurationValuesState,
  reservationFullFormValuesState,
  vehicleInformationFlowState,
} from "src/common/state";
import { useRecoilState } from "recoil";
import {
  ParkingOn,
  ReservationFullFormValues,
  Vehicle,
  parkingOnList,
  TerrainStability,
  TerrainStabilityList,
  Cleaning,
  cleaningList,
  Manager,
  Role,
  SearchVehicleType,
  JobType,
  JobTypeList,
} from "src/common/types";
import { useFormik, getIn, FormikErrors } from "formik";
import { useEffect, useMemo, useState } from "react";
import {
  getCurrentUser,
  useCreateNewReservation,
  useFavorite,
  useGetFavorites,
  useUnFavorite,
  useUpdateReservation,
  useUser,
  useVehicle,
  useVehicles,
} from "src/common/api";
import {
  Autocomplete,
  Box,
  CardContent,
  Checkbox,
  DialogActions,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  InputAdornment,
  Stack,
  Tab,
  Typography,
  useTheme,
} from "@mui/material";
import { format, parseISO } from "date-fns";
import {
  calculateDaysContract,
  calculateFlatFee,
  classifyReservationSchedule,
  currentTabFields,
  findPumpTier,
  findTransportTier,
  getVehicleUniqueId,
  isMovingForward,
  LatLng,
  reservationPayloadIntoFormData,
  tabOrder,
  TabValue,
  turnReservationFormValuesIntoCreateDto,
  turnReservationFormValuesIntoUpdateDto,
  useLocationsDistance,
} from "src/common/utils";
import {
  EMAIL_REGEX_VALIDATION,
  RESERVATION_FULL_FORM_VALUES,
} from "src/common/constants";
import { VehicleInformationModal } from "src/vehicles/vehicleInfoModal/VehicleInformationModal";
import { TabContext, TabList, TabPanel } from "@mui/lab";
import CostEstimation from "./CostEstimation";
import ReservationInfoCard from "./ReservationInfoCard";
import UploadFileButton from "src/common/components/custom/UploadFileButton";
import CustomDropDown from "src/common/components/custom/CustomDropDown";
import { FeatureFlag } from "src/common/components";
import { Client } from "src/common/types/client";
import { EditWorkTimeModal } from "./EditWorkTimeModal";
import {
  CeCard,
  CeButton,
  CeMuiPhoneNumber,
  CeTextField,
} from "src/common/components";
import AssignPricelist from "./AssignPricelist";
import { usePriceLists } from "src/common/api/priceList";
import { usePartners } from "src/common/api/partner";
import { useCompany } from "src/common/api/company";
export const Reservation = () => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState<TabValue>("clientDetailsTab");
  const [tabCompleted, setTabCompleted] = useState({
    clientDetailsTab: false,
    jobDetails: false,
    additionalInformation: false,
  });
  const tabFields = currentTabFields[tabValue] || [];
  const hasErrors = (errors: FormikErrors<ReservationFullFormValues>) => {
    return tabFields.some((field) => getIn(errors, field));
  };
  const currentUser = getCurrentUser();
  const manager = currentUser as unknown as Manager;
  const isOperatorManager = manager.role === Role.OEPRATOR_MANAGER;

  const {
    mutate: handleFavoriteCompany,
    isLoading: isHandleFavoriteCompanyLoading,
  } = useFavorite();
  const {
    mutate: handleUnFavoriteCompany,
    isLoading: isHandleUnFavoriteCompanyLoading,
  } = useUnFavorite();
  const { isLoaded, getGeodesicDistanceInKm } = useLocationsDistance();

  const [vehicleInformationValues, setVehicleInformationValues] =
    useRecoilState(vehicleInformationFlowState);
  const [reservationEditDurationValues, setReservationEditDurationValues] =
    useRecoilState(reservationEditDurationValuesState);
  const [reservationFormValues, setReservationFormValues] = useRecoilState(
    reservationFullFormValuesState
  );

  const { t, i18n } = useTranslation(["common", "dispatcher", "manager"]);
  const selectedLanguage = i18n.language;
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = queryString.parse(location.search) as Record<
    string,
    string
  >;

  const {
    dateFrom,
    dateTo,
    vehicleId,
    dispatcherId,
    siteAddress,
    city,
    plz,
    lat,
    lng,
    editMode,
    searchType,
  } = queryParams;
  const [timePlanned, setTimePlanned] = useState({ dateFrom, dateTo });
  const { data: company } = useCompany(currentUser?.companyId);

  const { data: vehicles, isLoading: isVehicleLoading } = useVehicles(
    {
      vehicleIds: [Number(vehicleId)],
      searchType: (searchType as SearchVehicleType) || "ALL",
      dateFrom: !isOperatorManager ? new Date(dateFrom) : null,
      dateTo: !isOperatorManager ? new Date(dateTo) : null,
    },

    Boolean(vehicleId)
  );
  const vehicle = vehicles?.data[0] || null;

  const { data: dispatcher, isLoading: isDispatcherLoading } = useUser(
    +dispatcherId,
    Boolean(dispatcherId)
  );

  const { data: allPricelists, isLoading: isLoadingPricelists } = usePriceLists(
    {
      expressions: [
        {
          category: '"container"."isDefault"',
          operator: "=",
          value: true,
          conditionType: "AND",
        },
      ],
      sortModel: [],
      relations: ["container", "user"],
    },
    isOperatorManager
  );

  const {
    data: allPartners,
    isLoading: isLoadingPartners,
    refetch: refetchPartners,
  } = usePartners(
    {
      expressions: [
        {
          category: '"operatorCompany"."id"',
          operator: "==",
          value: vehicle?.manager?.company.id,
          conditionType: "AND",
        },
      ],
      sortModel: [],
    },
    !isOperatorManager
  );
  const partners = allPartners?.data || [];
  const vehicleAssignedPricelist = vehicle?.assignedPricelist;
  const defaultOperatorVehiclePricelists = allPricelists?.data?.filter(
    (pricelist) =>
      pricelist.containerId === vehicleAssignedPricelist?.containerId
  );
  const pricelists =
    defaultOperatorVehiclePricelists ||
    partners[0]?.container?.pricelists ||
    [];

  const operatorCompanyId = vehicle?.operator?.companyId
    ? [vehicle.operator?.companyId]
    : [];
  const dispatcherCompanyId =
    dispatcher?.companyId || reservationFormValues.dispatcher?.companyId;
  const favoriteAttrs = {
    dispatcherCompanyId,
    operatorCompanyId,
  };

  const assignPricelist = {
    dispatcherCompanyId,
    operatorCompanyId: vehicle?.operator?.companyId,
  };

  const shouldEnableFavoritesApi = Boolean(
    operatorCompanyId.length && dispatcherCompanyId
  );

  const { data: allFavorites, isLoading: isAllFavoritesLoading } =
    useGetFavorites(favoriteAttrs, shouldEnableFavoritesApi);

  const favorites = allFavorites?.data || [];

  const {
    mutateAsync: handleCreateReservationAsync,
    isLoading: isCreateReservationLoading,
    isSuccess: isCreateReservationSuccess,
  } = useCreateNewReservation();
  const {
    mutateAsync: handleUpdateReservationAsync,
    isLoading: isUpdateReservationLoading,
    isSuccess: isUpdateReservationSuccess,
  } = useUpdateReservation();

  const handleFillClientForm = (clickedClient: Client) => {
    setReservationFormValues((currentValues) => ({
      ...currentValues,
      clientDetails: {
        ...currentValues.clientDetails,
        ...clickedClient,
      },
    }));
  };
  const isLoading =
    isVehicleLoading ||
    isCreateReservationLoading ||
    isUpdateReservationLoading ||
    isDispatcherLoading ||
    isHandleFavoriteCompanyLoading ||
    isHandleUnFavoriteCompanyLoading;

  useEffect(() => {
    if (isCreateReservationSuccess || isUpdateReservationSuccess) {
      setReservationFormValues(RESERVATION_FULL_FORM_VALUES);
      navigate("/reservations");
    }
  }, [
    isCreateReservationSuccess,
    isUpdateReservationSuccess,
    setReservationFormValues,
    navigate,
  ]);
  useEffect(() => {
    return () => {
      setReservationFormValues(RESERVATION_FULL_FORM_VALUES);
    };
  }, [setReservationFormValues, RESERVATION_FULL_FORM_VALUES]);

  const handleVehicleInformationModal = (vehicle: Vehicle) => {
    setVehicleInformationValues({
      vehicleId: vehicle.id,
      flow: "Vehicle Details",
    });
  };

  const availableFlexiblePipeLength80Mm =
    vehicle?.availableFlexiblePipeLength80Mm ?? 0;
  const availableFlexiblePipeLength90Mm =
    vehicle?.availableFlexiblePipeLength90Mm ?? 0;
  const availableFlexiblePipeLength100Mm =
    vehicle?.availableFlexiblePipeLength100Mm ?? 0;
  const availableFlexiblePipeLength120Mm =
    vehicle?.availableFlexiblePipeLength120Mm ?? 0;
  const vehicleFrontOutriggerSpan = vehicle?.frontOutriggerSpan ?? 0;
  const vehicleRearOutriggerSpan = vehicle?.rearOutriggerSpan ?? 0;
  const vehicleWeight = vehicle?.weight ?? 0;
  const vehicleHeight = vehicle?.height ?? 0;

  const formik = useFormik<ReservationFullFormValues>({
    initialValues: {
      ...reservationFormValues,
      pricelist: editMode
        ? reservationFormValues?.pricelist
        : vehicle?.assignedPricelist || null,
    },
    enableReinitialize: true,
    validateOnChange: true,
    validationSchema: yup.object({
      pricelist: yup.object().nullable(),
      clientDetails: yup.object().shape({
        name: yup
          .string()
          .max(100, t("client-name-exceeds"))
          .required("required"),
        lastName: yup
          .string()
          .max(100, t("client-lastName-exceeds"))
          .required("required"),
        email: yup
          .string()
          .matches(EMAIL_REGEX_VALIDATION, t("invalid-email-format"))
          .max(100, t("client-email-exceeds"))
          .required("required"),
        phoneNumber: yup
          .string()
          .max(100, t("client-phone-number-exceeds"))
          .required("required"),
        companyName: yup
          .string()
          .max(100, t("client-company-name-exceeds"))
          .required("required"),
        companyVatNumber: yup
          .string()
          .max(100, t("client-companyVat-number-exceeds"))
          .required("required"),
      }),
      comments: yup.string().max(500, t("comment-exceeds")),
      job: yup.object().shape({
        amountOfConcrete: yup
          .number()
          .nullable()
          .required()
          .min(0.1, t("Must be greater than 0")),
        flowRate: yup
          .number()
          .nullable()
          .required()
          .min(0.1, t("Must be greater than 0")),
        flexiblePipeLength80Mm: yup
          .number()
          .nullable()
          .min(0.1, t("Must be greater than 0"))
          .max(
            availableFlexiblePipeLength80Mm,
            t("common:exceeds-available-stock")
          ),
        flexiblePipeLength90Mm: yup
          .number()
          .nullable()
          .min(0.1, t("Must be greater than 0"))
          .max(
            availableFlexiblePipeLength90Mm,
            t("common:exceeds-available-stock")
          ),
        rigidPipeLength100Mm: yup
          .number()
          .nullable()
          .min(0.1, t("Must be greater than 0"))
          .max(
            availableFlexiblePipeLength100Mm,
            t("common:exceeds-available-stock")
          ),
        rigidPipeLength120Mm: yup
          .number()
          .nullable()
          .min(0.1, t("Must be greater than 0"))
          .max(
            availableFlexiblePipeLength120Mm,
            t("common:exceeds-available-stock")
          ),
        frontOutriggersSpan: yup
          .number()
          .nullable()
          .min(vehicleFrontOutriggerSpan, t("common:below-span-limit"))
          .test(
            "at-least-one-outrigger-span-front",
            " ",
            function (value) {
              const frontSpan = value;
              const rearSpan = this.parent.rearOutriggersSpan;
              return Boolean((frontSpan && frontSpan > 0) || (rearSpan && rearSpan > 0));
            }
          ),
        rearOutriggersSpan: yup
          .number()
          .nullable()
          .min(vehicleRearOutriggerSpan, t("common:below-span-limit"))
          .test(
            "at-least-one-outrigger-span-rear",
            " ",
            function (value) {
              const frontSpan = this.parent.frontOutriggersSpan;
              const rearSpan = value;
              return Boolean((frontSpan && frontSpan > 0) || (rearSpan && rearSpan > 0));
            }
          ),
        frontSideOpening: yup
          .number()
          .required()
          .nullable()
          .min(0.1, t("Must be greater than 0")),
        rearSideOpening: yup
          .number()
          .required()
          .nullable()
          .min(0.1, t("Must be greater than 0")),
        cleaning: yup.string().required(),
        parkingOn: yup.string().required(),
        terrainStability: yup.string().required(),
        jobType: yup.string().required(),
        ciaw: yup.string().nullable(),
        authorizedWeight: yup
          .number()
          .nullable()
          .test(
            "valid-weight-limit",
            t("common:exceeds-weight-limit"),
            function (value) {
              return value == null || value > vehicleWeight;
            }
          )
          .when("tonnageRestriction", {
            is: true,
            then: (schema) => schema.required().min(0),
            otherwise: (schema) => schema.nullable(),
          }),
        heightLimit: yup
          .number()
          .nullable()
          .test(
            "valid-height-limit",
            t("common:exceeds-height-limit"),
            function (value) {
              return value == null || value > vehicleHeight;
            }
          )
          .when("heightRestriction", {
            is: true,
            then: (schema) => schema.required().min(0),
            otherwise: (schema) => schema.nullable(),
          }),
        units: yup
          .number()
          .nullable()
          .when("extraCementBag", {
            is: true,
            then: (schema) => schema.required().min(0),
            otherwise: (schema) => schema.nullable(),
          }),
        voltage: yup
          .number()
          .nullable()
          .when("presenceOfPowerLines", {
            is: true,
            then: (schema) => schema.required().min(0),
            otherwise: (schema) => schema.nullable(),
          }),
      }),
    }),
    onSubmit: async (values: ReservationFullFormValues) => {
      const formValues = { ...values };

      if (dispatcher) {
        formValues.dispatcher = dispatcher;
      }

      if (timePlanned.dateFrom && timePlanned.dateTo) {
        formValues.dateFrom = timePlanned.dateFrom;
        formValues.dateTo = timePlanned.dateTo;
      }

      if (siteAddress) {
        formValues.siteAddress = siteAddress;
      }

      if (city) {
        formValues.city = city;
      }
      if (plz) {
        formValues.plz = Number(plz);
      }
      if (lat && lng) {
        formValues.location = {
          type: "Point",
          coordinates: [Number(lat), Number(lng)],
        };
      }

      if (vehicle && vehicle.manager && vehicle.operator) {
        formValues.vehicle = vehicle;
        formValues.manager = vehicle.manager;
        formValues.operator = vehicle.operator;
      }
      if (editMode) {
        const payload = turnReservationFormValuesIntoUpdateDto(formValues);
        const formData = reservationPayloadIntoFormData(payload);
        await handleUpdateReservationAsync({
          reservationId: formik.values.reservationId!,
          updateReservationArgs: formData,
        });
      } else {
        const payload = turnReservationFormValuesIntoCreateDto(formValues);
        const formData = reservationPayloadIntoFormData(payload);
        await handleCreateReservationAsync(formData);
      }
    },
  });

  const setFieldValue = formik.setFieldValue;

  useEffect(() => {
    if (!editMode && vehicleAssignedPricelist) {
      setFieldValue("pricelist", vehicleAssignedPricelist);
    }
  }, [vehicleAssignedPricelist, editMode, setFieldValue]);

  const getFormatedDateLocale = () => {
    if (!selectedLanguage) {
      return enGB;
    }

    switch (selectedLanguage) {
      case "en":
        return enGB;
      case "de":
        return deAT;
      case "nl":
        return nlBE;
      case "fr":
        return frCA;

      default:
        return enGB;
    }
  };
  const formattedDate = format(
    parseISO(timePlanned.dateFrom),
    "E, d MMMM yyyy",
    {
      locale: getFormatedDateLocale(),
    }
  );
  const formattedTime = `${format(parseISO(timePlanned.dateFrom), "H:mm", {
    locale: getFormatedDateLocale(),
  })} - ${format(parseISO(timePlanned.dateTo), "H:mm")}`;
  const handleOverwriteWorkHours = (endTimeValue: string) => {
    setTimePlanned((timePlanned) => ({
      ...timePlanned,
      dateTo: endTimeValue,
    }));
  };
  const origin: LatLng = useMemo(
    () => ({
      lat: vehicle?.location?.coordinates[1] || 0,
      lng: vehicle?.location?.coordinates[0] || 0,
    }),
    [vehicle?.location?.coordinates]
  );
  const destination: LatLng = useMemo(
    () => ({
      lat: Number(lat),
      lng: Number(lng),
    }),
    [lat, lng]
  );

  useEffect(() => {
    async function fetchDistance() {
      try {
        const distance = await getGeodesicDistanceInKm(origin, destination);
        setFieldValue("siteDistance", distance);
      } catch (error) {
        console.error("Error calculating distance:", error);
      }
    }
    if (isLoaded && !isVehicleLoading) {
      fetchDistance();
    }
  }, [isLoaded, getGeodesicDistanceInKm, origin, destination]);

  const formatedDateFrom = new Date(timePlanned.dateFrom);
  const formateDateTo = new Date(timePlanned.dateTo);
  const currentPricelist = formik.values.pricelist;

  const classifiedReservationSchedule = classifyReservationSchedule(
    formatedDateFrom,
    formateDateTo,
    currentPricelist?.packageFlatFeeDuration || 0,
    company?.settings.workSchedules || [],
    !!currentPricelist?.packageFlatFeeWeekend,
    !!currentPricelist?.packageFlatFeeNight
  );
  const {
    totalDayContractDuration,
    totalOvertimeDuration,
    totalDayContractFee,
    totalOvertimeFee,
  } = calculateDaysContract(
    timePlanned.dateFrom,
    timePlanned.dateTo,
    currentPricelist?.dayContractDuration || 0,
    currentPricelist?.dayContractFee || 0,
    currentPricelist?.dayContractOvertimeRate || 0
  );
  const flatFee =
    calculateFlatFee(
      currentPricelist?.packageFlatFee ?? 0,
      formik?.values?.pricelist?.packageFlatFeeDuration || 0,
      classifiedReservationSchedule.flatPackage.regularWorkingTime
    ) || 0;

  const flatFeeNight =
    calculateFlatFee(
      currentPricelist?.packageFlatFeeNight ?? 0,
      formik?.values?.pricelist?.packageFlatFeeDuration ?? 0,
      classifiedReservationSchedule.flatPackage.nightWorkingTime
    ) || 0;

  const flatFeeWeekend =
    calculateFlatFee(
      currentPricelist?.packageFlatFeeWeekend ?? 0,
      formik?.values?.pricelist?.packageFlatFeeDuration ?? 0,
      classifiedReservationSchedule.flatPackage.weekendWorkingTime
    ) || 0;

  const additionalHour = +(
    (currentPricelist?.additionalHour ?? 0) *
    classifiedReservationSchedule.additionalHours.regularWorkingTime
  ).toFixed(2);

  const additionalHourNight = +(
    (currentPricelist?.additionalHourNight ?? 0) *
    classifiedReservationSchedule.additionalHours.nightWorkingTime
  ).toFixed(2);

  const additionalHourWeekend = +(
    (currentPricelist?.additionalHourWeekend ?? 0) *
    classifiedReservationSchedule.additionalHours.weekendWorkingTime
  ).toFixed(2);

  const rigidPipeLength100 =
    (currentPricelist?.pricePerMeterOfFlexiblePipeLength100Mm ?? 0) *
    (formik.values.job?.rigidPipeLength100Mm ?? 0);
  const rigidPipeLength120 =
    (currentPricelist?.pricePerMeterOfRigidPipeLength120Mm ?? 0) *
    (formik.values.job?.rigidPipeLength120Mm ?? 0);
  const flexiblePipeLength80Mm =
    (currentPricelist?.pricePerMeterOfFlexiblePipeLength80Mm ?? 0) *
    (formik.values.job?.flexiblePipeLength80Mm ?? 0);
  const flexiblePipeLength90Mm =
    (currentPricelist?.pricePerMeterOfFlexiblePipeLength90Mm ?? 0) *
    (formik.values.job?.flexiblePipeLength90Mm ?? 0);
  const amountOfConcrete = formik.values.job?.amountOfConcrete ?? 0;
  const correspondingPumpTier = currentPricelist?.pumpTiers
    ? findPumpTier(currentPricelist?.pumpTiers, amountOfConcrete)
    : null;
  const concreteAmountFee =
    amountOfConcrete *
      (!!correspondingPumpTier?.price
        ? correspondingPumpTier.price
        : currentPricelist?.pricePerMeterPumped!) || 0;
  const cleaningFee =
    formik.values.job?.cleaning === "Worksite"
      ? 0
      : currentPricelist?.cleaningFee ?? 0;
  const extraCementBags =
    (currentPricelist?.extraCementBagPrice ?? 0) *
    (formik.values.job?.units ?? 0);
  const barbotineFee =
    (formik.values.job?.barbotine ? currentPricelist?.barbotine : null) ?? 0;
  const supplyOfTheChemicalSlushieFee =
    (formik.values.job?.supplyOfTheChemicalSlushie
      ? currentPricelist?.supplyOfTheChemicalSlushie
      : null) ?? 0;
  const correspondingTransportTier = findTransportTier(
    currentPricelist?.transportRates || [],
    Number(formik.values.siteDistance)
  );
  const transportTierFee = correspondingTransportTier?.tariff ?? 0;

  const totalCost =
    flatFee +
    flatFeeNight +
    flatFeeWeekend +
    additionalHour +
    additionalHourNight +
    additionalHourWeekend +
    totalDayContractFee +
    totalOvertimeFee +
    rigidPipeLength100 +
    rigidPipeLength120 +
    flexiblePipeLength80Mm +
    amountOfConcrete +
    cleaningFee +
    extraCementBags +
    concreteAmountFee +
    barbotineFee +
    supplyOfTheChemicalSlushieFee +
    transportTierFee;

  const handleTabChange = async (
    event: React.SyntheticEvent,
    newValue: TabValue
  ) => {
    if (newValue === tabValue) return;

    const movingForward = isMovingForward(tabOrder, tabValue, newValue);

    if (movingForward) {
      const errors = await formik.validateForm();
      const tabHasErrors = hasErrors(errors);

      if (tabHasErrors) {
        tabFields.forEach((field) => {
          formik.setFieldTouched(field, true, false);
        });
        return;
      } else {
        setTabCompleted((prev) => ({
          ...prev,
          [tabValue]: true,
        }));
        setTabValue(newValue);
      }
    } else {
      setTabValue(newValue);
    }
  };

  const openEditTimeConfirmModal = () => {
    if (!formik?.values.job?.flowRate) {
      setTimePlanned((timePlanned) => ({
        ...timePlanned,
        dateTo,
      }));
      return;
    }
    setReservationEditDurationValues({
      flow: "Edit Time",
    });
  };

  const vehicleUniqueName = vehicle ? getVehicleUniqueId(vehicle) : "";

  return (
    <Stack
      component="form"
      noValidate
      onSubmit={formik.handleSubmit}
      sx={{ mb: 2 }}
    >
      <Grid container spacing={4}>
        <Grid item xs={7} alignSelf="stretch">
          <CeCard
            sx={{
              height: "100%",
            }}
          >
            <CardContent sx={{ paddingTop: 0 }}>
              <Box>
                <TabContext value={tabValue}>
                  <Box
                    aria-label="box"
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "flext-start",
                      width: "100%",
                      maxWidth: 838,
                      mx: "auto",
                    }}
                  >
                    <TabList
                      onChange={handleTabChange}
                      aria-label="vehicle reservation tablist"
                      sx={{
                        height: "55px",
                        width: "100%",
                        "& .MuiTabs-flexContainer": {
                          display: "flex",
                          justifyContent: "space-between",
                        },
                      }}
                    >
                      <Tab
                        label={
                          <Stack direction="row" alignItems="center" gap={1}>
                            <Typography
                              sx={{
                                fontSize: "14px",
                                textTransform: "capitalize",
                                letterSpacing: "0.4px",
                                ml: "7px",
                              }}
                            >
                              {t("common:client-details")}
                            </Typography>
                            {tabCompleted.clientDetailsTab ? (
                              <CheckmarkCircle02Icon
                                color={theme.palette.success.main}
                                variant="solid"
                                size={18}
                              />
                            ) : null}
                          </Stack>
                        }
                        value="clientDetailsTab"
                        icon={
                          <UserCircleIcon
                            size={20}
                            color={"currentColor"}
                            variant={"stroke"}
                          />
                        }
                        iconPosition="start"
                      />
                      <Tab
                        label={
                          <Stack direction="row" alignItems="center" gap={1}>
                            <Typography
                              sx={{
                                fontSize: "14px",
                                textTransform: "capitalize",
                                letterSpacing: "0.4px",
                                ml: "7px",
                              }}
                            >
                              {t("common:job-details")}
                            </Typography>
                            {tabCompleted.jobDetails ? (
                              <CheckmarkCircle02Icon
                                color={theme.palette.success.main}
                                variant="solid"
                                size={18}
                              />
                            ) : null}
                          </Stack>
                        }
                        value="jobDetails"
                        icon={
                          <Briefcase06Icon
                            size={20}
                            color={"currentColor"}
                            variant={"stroke"}
                          />
                        }
                        iconPosition="start"
                      />
                      <Tab
                        label={
                          <Stack direction="row" alignItems="center" gap={1}>
                            <Typography
                              sx={{
                                fontSize: "14px",
                                textTransform: "capitalize",
                                letterSpacing: "0.4px",
                                ml: "7px",
                              }}
                            >
                              {t("common:additional-information")}
                            </Typography>
                            {tabCompleted.additionalInformation ? (
                              <CheckmarkCircle02Icon
                                color={theme.palette.success.main}
                                variant="solid"
                                size={18}
                              />
                            ) : null}
                          </Stack>
                        }
                        value="additionalInformation"
                        iconPosition="start"
                        icon={
                          <InformationCircleIcon
                            size={20}
                            color={"currentColor"}
                            variant={"stroke"}
                          />
                        }
                      />
                    </TabList>
                  </Box>

                  <TabPanel value="clientDetailsTab">
                    <Grid
                      container
                      rowGap={2}
                      mx="auto"
                      width="100%"
                      maxWidth={650}
                      sx={{
                        "& > :nth-of-type(2n-1)": {
                          pr: 1,
                        },
                        "& > :nth-of-type(2n)": {
                          pl: 1,
                        },
                        "& > :first-of-type, & > :nth-of-type(2)": {
                          p: 0,
                        },
                      }}
                    >
                      <Grid item sm={12} xs={6}>
                        <Box
                          alignItems="center"
                          justifyContent="center"
                          display="flex"
                          sx={{ my: 4 }}
                          gap={1}
                        >
                          <UserCircleIcon
                            size={24}
                            color={"currentColor"}
                            variant={"stroke"}
                          />
                          <Typography
                            fontSize={20}
                            fontWeight={500}
                            letterSpacing={0.15}
                            color="text.primary"
                            variant="h6"
                          >
                            {t("common:client-details")}
                          </Typography>
                        </Box>
                      </Grid>
                      <FeatureFlag flags={["dispatcher", "dispatcher manager"]}>
                        <Grid item sm={12} xs={12}>
                          <CeTextField
                            fullWidth
                            id="orderNumber"
                            name="orderNumber"
                            label={t("common:order-number")}
                            value={formik.values.orderNumber || ""}
                            onChange={formik.handleChange}
                            size="small"
                            InputLabelProps={{ shrink: true }}
                            disabled={isLoading}
                            variant="outlined"
                          />
                        </Grid>
                      </FeatureFlag>
                      <FeatureFlag flags={["operator manager"]}>
                        <Grid item xs={12} sx={{ position: "relative" }}>
                          <CustomDropDown
                            onClientClick={handleFillClientForm}
                          />
                        </Grid>
                      </FeatureFlag>
                      <Grid item sm={6} xs={6}>
                        <CeTextField
                          fullWidth
                          id="name"
                          name="clientDetails.name"
                          label={t("common:first-name")}
                          size="small"
                          value={formik.values.clientDetails?.name}
                          onChange={(e) => {
                            formik.handleChange(e);
                            formik.setFieldTouched(
                              "clientDetails.name",
                              true,
                              false
                            );
                          }}
                          InputLabelProps={{ shrink: true }}
                          error={Boolean(
                            getIn(formik.touched, "clientDetails.name") &&
                              getIn(formik.errors, "clientDetails.name")
                          )}
                          helperText={
                            getIn(formik.touched, "clientDetails.name") &&
                            getIn(formik.errors, "clientDetails.name")
                          }
                          disabled={isLoading}
                          required
                          variant="outlined"
                        />
                      </Grid>

                      <Grid item sm={6} xs={12}>
                        <CeTextField
                          fullWidth
                          id="lastName"
                          name="clientDetails.lastName"
                          label={t("common:last-name")}
                          size="small"
                          value={formik.values.clientDetails?.lastName}
                          onChange={(e) => {
                            formik.handleChange(e);
                            formik.setFieldTouched(
                              "clientDetails.lastName",
                              true,
                              false
                            );
                          }}
                          InputLabelProps={{ shrink: true }}
                          error={Boolean(
                            getIn(formik.touched, "clientDetails.lastName") &&
                              getIn(formik.errors, "clientDetails.lastName")
                          )}
                          helperText={
                            getIn(formik.touched, "clientDetails.lastName") &&
                            getIn(formik.errors, "clientDetails.lastName")
                          }
                          disabled={isLoading}
                          required
                        />
                      </Grid>

                      <Grid item sm={6} xs={12}>
                        <CeTextField
                          fullWidth
                          id="email"
                          name="clientDetails.email"
                          label={t("common:email")}
                          type="email"
                          size="small"
                          value={formik.values.clientDetails?.email}
                          onChange={(e) => {
                            formik.handleChange(e);
                            formik.setFieldTouched(
                              "clientDetails.email",
                              true,
                              false
                            );
                          }}
                          InputLabelProps={{ shrink: true }}
                          error={Boolean(
                            getIn(formik.touched, "clientDetails.email") &&
                              getIn(formik.errors, "clientDetails.email")
                          )}
                          helperText={
                            getIn(formik.touched, "clientDetails.email") &&
                            getIn(formik.errors, "clientDetails.email")
                          }
                          disabled={isLoading}
                          required
                        />
                      </Grid>

                      <Grid item sm={6} xs={12}>
                        <CeMuiPhoneNumber
                          fullWidth
                          defaultCountry={"be"}
                          id="phoneNumber"
                          name="clientDetails.phoneNumber"
                          label={t("common:phone-number")}
                          size="small"
                          required
                          InputLabelProps={{ shrink: true }}
                          value={formik.values.clientDetails?.phoneNumber}
                          error={Boolean(
                            getIn(
                              formik.touched,
                              "clientDetails.phoneNumber"
                            ) &&
                              getIn(formik.errors, "clientDetails.phoneNumber")
                          )}
                          helperText={
                            getIn(
                              formik.touched,
                              "clientDetails.phoneNumber"
                            ) &&
                            getIn(formik.errors, "clientDetails.phoneNumber")
                          }
                          onChange={(value) =>
                            formik.setFieldValue(
                              "clientDetails.phoneNumber",
                              value
                            )
                          }
                          onBlur={() =>
                            formik.setFieldTouched(
                              "clientDetails.phoneNumber",
                              true
                            )
                          }
                          disabled={isLoading}
                          variant="outlined"
                        />
                      </Grid>

                      <Grid item sm={6} xs={12}>
                        <CeTextField
                          fullWidth
                          id="companyName"
                          name="clientDetails.companyName"
                          label={t("common:company-name")}
                          size="small"
                          required
                          value={formik.values.clientDetails?.companyName}
                          InputLabelProps={{ shrink: true, size: "small" }}
                          onChange={(e) => {
                            formik.handleChange(e);
                            formik.setFieldTouched(
                              "clientDetails.companyName",
                              true,
                              false
                            );
                          }}
                          error={Boolean(
                            getIn(
                              formik.touched,
                              "clientDetails.companyName"
                            ) &&
                              getIn(formik.errors, "clientDetails.companyName")
                          )}
                          helperText={
                            getIn(
                              formik.touched,
                              "clientDetails.companyName"
                            ) &&
                            getIn(formik.errors, "clientDetails.companyName")
                          }
                          disabled={isLoading}
                        />
                      </Grid>

                      <Grid item sm={6} xs={12}>
                        <CeTextField
                          fullWidth
                          id="companyAddress"
                          name="clientDetails.companyVatNumber"
                          label={t("common:company-vat-number")}
                          size="small"
                          required
                          error={Boolean(
                            getIn(
                              formik.touched,
                              "clientDetails.companyVatNumber"
                            ) &&
                              getIn(
                                formik.errors,
                                "clientDetails.companyVatNumber"
                              )
                          )}
                          helperText={
                            getIn(
                              formik.touched,
                              "clientDetails.companyVatNumber"
                            ) &&
                            getIn(
                              formik.errors,
                              "clientDetails.companyVatNumber"
                            )
                          }
                          value={formik.values.clientDetails?.companyVatNumber}
                          onChange={(e) => {
                            formik.handleChange(e);
                            formik.setFieldTouched(
                              "clientDetails.companyVatNumber",
                              true,
                              false
                            );
                          }}
                          InputLabelProps={{ shrink: true }}
                          disabled={isLoading}
                        />
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        display="flex"
                        alignSelf="center"
                        justifyContent="center"
                        m="60px auto 0"
                        width={335}
                      >
                        <CeButton
                          variant="outlined"
                          color="primary"
                          onClick={(event) =>
                            handleTabChange(event, "jobDetails")
                          }
                          size="medium"
                          disabled={isLoading}
                          sx={{
                            borderRadius: "8px",
                            padding: "8px 16px",
                            width: "100%",
                          }}
                        >
                          {t("common:next")}
                        </CeButton>
                      </Grid>
                    </Grid>
                  </TabPanel>
                  <TabPanel value="jobDetails">
                    <Grid
                      container
                      rowGap={2}
                      mx="auto"
                      width="100%"
                      maxWidth={650}
                      sx={{
                        "& > :nth-of-type(2n)": {
                          pr: 1,
                        },
                        "& > :nth-of-type(2n-1)": {
                          pl: 1,
                        },

                        "& > :first-of-type": {
                          p: 0,
                        },
                      }}
                    >
                      <Grid item sm={12} xs={6}>
                        <Box
                          alignItems="center"
                          justifyContent="center"
                          display="flex"
                          sx={{ my: 4 }}
                          gap={1}
                        >
                          <Briefcase06Icon
                            size={24}
                            color={"currentColor"}
                            variant={"stroke"}
                          />
                          <Typography
                            fontSize={20}
                            fontWeight={500}
                            letterSpacing={0.15}
                            color="text.primary"
                          >
                            {t("common:job-details")}
                          </Typography>
                        </Box>
                      </Grid>

                      <Grid item sm={6} xs={12}>
                        <CeTextField
                          fullWidth
                          id="amountOfConcrete"
                          name="job.amountOfConcrete"
                          label={t("common:amount-of-concrete")}
                          size="small"
                          type="number"
                          required
                          onBlur={openEditTimeConfirmModal}
                          value={formik.values.job?.amountOfConcrete || ""}
                          error={Boolean(
                            getIn(formik.touched, "job.amountOfConcrete") &&
                              getIn(formik.errors, "job.amountOfConcrete")
                          )}
                          InputProps={{
                            inputProps: { min: 0 },
                            endAdornment: (
                              <InputAdornment position="end">㎥</InputAdornment>
                            ),
                          }}
                          onChange={(e) => {
                            formik.handleChange(e);
                            formik.setFieldTouched(
                              "job.amountOfConcrete",
                              true,
                              false
                            );
                          }}
                          InputLabelProps={{ shrink: true }}
                          disabled={isLoading}
                        />
                      </Grid>
                      <Grid item sm={6} xs={12}>
                        <FormControl
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "flex-start",
                            flexDirection: "row",
                          }}
                        >
                          <CeTextField
                            fullWidth
                            id="flowRate"
                            name="job.flowRate"
                            label={t("common:flow-rate")}
                            size="small"
                            type="number"
                            onBlur={openEditTimeConfirmModal}
                            value={formik.values.job?.flowRate || ""}
                            error={Boolean(
                              getIn(formik.touched, "job.flowRate") &&
                                getIn(formik.errors, "job.flowRate")
                            )}
                            InputProps={{
                              inputProps: { min: 0 },
                              endAdornment: (
                                <InputAdornment position="end">
                                  ㎥/h
                                </InputAdornment>
                              ),
                            }}
                            onChange={(e) => {
                              formik.handleChange(e);
                              formik.setFieldTouched(
                                "job.flowRate",
                                true,
                                false
                              );
                            }}
                            sx={{ flex: 1 }}
                            InputLabelProps={{ shrink: true }}
                            disabled={isLoading}
                          />

                          <FormControlLabel
                            sx={{ flex: 1, pl: 1 }}
                            checked={formik.values.job?.balance}
                            control={<Checkbox />}
                            label={t("common:balance")}
                            name="job.balance"
                            onChange={() =>
                              formik.setFieldValue(
                                "job.balance",
                                !formik.values.job?.balance
                              )
                            }
                          />
                        </FormControl>
                      </Grid>
                      <Grid item sm={6} xs={12}>
                        <CeTextField
                          fullWidth
                          id="flexiblePipeLength80Mm"
                          name="job.flexiblePipeLength80Mm"
                          label={t("common:flexible-pipe-length-80mm")}
                          size="small"
                          type="number"
                          value={
                            formik.values.job?.flexiblePipeLength80Mm ?? ""
                          }
                          error={Boolean(
                            getIn(formik.errors, "job.flexiblePipeLength80Mm")
                          )}
                          helperText={getIn(
                            formik.errors,
                            "job.flexiblePipeLength80Mm"
                          )}
                          InputProps={{
                            inputProps: { min: 0 },
                            endAdornment: (
                              <InputAdornment position="end">
                                {t("meter-symbol")}
                              </InputAdornment>
                            ),
                          }}
                          onChange={async (event) => {
                            const target = event.target as HTMLInputElement;
                            formik.handleChange(event);
                            formik.setFieldTouched(
                              "job.flexiblePipeLength80Mm",
                              true,
                              false
                            );
                            const { name, value } = target;
                            if (name === "job.flexiblePipeLength80Mm") {
                              const flexiblePipeLength80 =
                                vehicle?.assignedPricelist
                                  ?.pricePerMeterOfFlexiblePipeLength80Mm ?? 0;
                              const units = parseFloat(value);
                              const flexiblePipeLength80Mm = formik?.values?.job
                                ?.flexiblePipeLength80Mm
                                ? flexiblePipeLength80 * units
                                : 0;
                              await formik.setFieldValue(
                                "flexiblePipeLength80Mm",
                                flexiblePipeLength80Mm,
                                false
                              );
                            }
                          }}
                          InputLabelProps={{ shrink: true }}
                          disabled={isLoading}
                        />
                      </Grid>

                      <Grid item sm={6} xs={12}>
                        <CeTextField
                          fullWidth
                          id="flexiblePipeLength90Mm"
                          name="job.flexiblePipeLength90Mm"
                          label={t("common:flexible-pipe-length-90mm")}
                          size="small"
                          type="number"
                          value={
                            formik.values.job?.flexiblePipeLength90Mm ?? ""
                          }
                          error={Boolean(
                            getIn(formik.errors, "job.flexiblePipeLength90Mm")
                          )}
                          helperText={getIn(
                            formik.errors,
                            "job.flexiblePipeLength90Mm"
                          )}
                          InputProps={{
                            inputProps: { min: 0 },
                            endAdornment: (
                              <InputAdornment position="end">
                                {t("meter-symbol")}
                              </InputAdornment>
                            ),
                          }}
                          onChange={async (event) => {
                            const target = event.target as HTMLInputElement;
                            formik.handleChange(event);
                            formik.setFieldTouched(
                              "job.flexiblePipeLength90Mm",
                              true,
                              false
                            );
                            const { name, value } = target;
                            if (name === "job.flexiblePipeLength90Mm") {
                              const flexiblePipeLength90 =
                                vehicle?.assignedPricelist
                                  ?.pricePerMeterOfFlexiblePipeLength90Mm ?? 0;
                              const units = parseFloat(value);
                              const flexiblePipeLength90Mm = formik?.values?.job
                                ?.flexiblePipeLength90Mm
                                ? flexiblePipeLength90 * units
                                : 0;
                              await formik.setFieldValue(
                                "flexiblePipeLength90Mm",
                                flexiblePipeLength90Mm,
                                false
                              );
                            }
                          }}
                          InputLabelProps={{ shrink: true }}
                          disabled={isLoading}
                        />
                      </Grid>

                      <Grid item sm={6} xs={12}>
                        <CeTextField
                          fullWidth
                          id="rigidPipeLength100Mm"
                          name="job.rigidPipeLength100Mm"
                          label={t("common:rigid-pipe-length-100")}
                          size="small"
                          type="number"
                          value={formik.values.job?.rigidPipeLength100Mm ?? ""}
                          error={Boolean(
                            getIn(formik.errors, "job.rigidPipeLength100Mm")
                          )}
                          helperText={getIn(
                            formik.errors,
                            "job.rigidPipeLength100Mm"
                          )}
                          InputProps={{
                            inputProps: { min: 0 },
                            endAdornment: (
                              <InputAdornment position="end">
                                {t("meter-symbol")}
                              </InputAdornment>
                            ),
                          }}
                          onChange={async (event) => {
                            const target = event.target as HTMLInputElement;
                            formik.handleChange(event);
                            formik.setFieldTouched(
                              "job.rigidPipeLength100Mm",
                              true,
                              false
                            );
                            const { name, value } = target;
                            if (name === "job.rigidPipeLength100Mm") {
                              const rigidPipeLength100 =
                                vehicle?.assignedPricelist
                                  ?.pricePerMeterOfFlexiblePipeLength100Mm ?? 0;
                              const units = parseFloat(value);
                              const rigidPipeLength100Mm = formik?.values?.job
                                ?.rigidPipeLength100Mm
                                ? rigidPipeLength100 * units
                                : 0;
                              await formik.setFieldValue(
                                "rigidPipeLength100Mm",
                                rigidPipeLength100Mm,
                                false
                              );
                            }
                          }}
                          InputLabelProps={{ shrink: true }}
                          disabled={isLoading}
                        />
                      </Grid>

                      <Grid item sm={6} xs={12}>
                        <CeTextField
                          fullWidth
                          id="rigidPipeLength120Mm"
                          name="job.rigidPipeLength120Mm"
                          label={t("common:rigid-pipe-length")}
                          size="small"
                          type="number"
                          value={formik.values.job?.rigidPipeLength120Mm ?? ""}
                          error={Boolean(
                            getIn(formik.errors, "job.rigidPipeLength120Mm")
                          )}
                          helperText={getIn(
                            formik.errors,
                            "job.rigidPipeLength120Mm"
                          )}
                          InputProps={{
                            inputProps: { min: "0" },
                            endAdornment: (
                              <InputAdornment position="end">
                                {t("meter-symbol")}
                              </InputAdornment>
                            ),
                          }}
                          onChange={async (event) => {
                            const target = event.target as HTMLInputElement;
                            formik.handleChange(event);
                            formik.setFieldTouched(
                              "job.rigidPipeLength120Mm",
                              true,
                              false
                            );
                            const { name, value } = target;
                            if (name === "job.rigidPipeLength120Mm") {
                              const rigidPipeLength120 =
                                vehicle?.assignedPricelist
                                  ?.pricePerMeterOfRigidPipeLength120Mm ?? 0;
                              const units = parseFloat(value);
                              const rigidPipeLength120Mm = formik?.values?.job
                                ?.rigidPipeLength120Mm
                                ? rigidPipeLength120 * units
                                : 0;
                              await formik.setFieldValue(
                                "rigidPipeLength120Mm",
                                rigidPipeLength120Mm,
                                false
                              );
                            }
                          }}
                          InputLabelProps={{ shrink: true }}
                          disabled={isLoading}
                        />
                      </Grid>
                      <Grid item sm={6} xs={12}>
                        <CeTextField
                          fullWidth
                          id="frontOutriggersSpan"
                          name="job.frontOutriggersSpan"
                          label={t("common:front-outriggers-span")}
                          size="small"
                          type="number"
                          value={formik.values.job?.frontOutriggersSpan ?? ""}
                          error={Boolean(
                            getIn(formik.errors, "job.frontOutriggersSpan") &&
                              getIn(formik.touched, "job.frontOutriggersSpan")
                          )}
                          helperText={
                            getIn(formik.touched, "job.frontOutriggersSpan") &&
                            getIn(formik.errors, "job.frontOutriggersSpan")
                          }
                          InputProps={{
                            inputProps: { min: "0" },
                            endAdornment: (
                              <InputAdornment position="end">
                                {t("meter-symbol")}
                              </InputAdornment>
                            ),
                          }}
                          onChange={(e) => {
                            formik.handleChange(e);
                            formik.setFieldTouched(
                              "job.frontOutriggersSpan",
                              true,
                              false
                            );
                          }}
                          InputLabelProps={{ shrink: true }}
                          disabled={isLoading}
                        />
                      </Grid>

                      <Grid item sm={6} xs={12}>
                        <CeTextField
                          fullWidth
                          id="rearOutriggersSpan"
                          name="job.rearOutriggersSpan"
                          label={t("common:rear-outriggers-span")}
                          size="small"
                          type="number"
                          value={formik.values.job?.rearOutriggersSpan ?? ""}
                          error={Boolean(
                            getIn(formik.errors, "job.rearOutriggersSpan") &&
                              getIn(formik.touched, "job.rearOutriggersSpan")
                          )}
                          helperText={
                            getIn(formik.touched, "job.rearOutriggersSpan") &&
                            getIn(formik.errors, "job.rearOutriggersSpan")
                          }
                          InputProps={{
                            inputProps: { min: "0" },
                            endAdornment: (
                              <InputAdornment position="end">
                                {t("meter-symbol")}
                              </InputAdornment>
                            ),
                          }}
                          onChange={(e) => {
                            formik.handleChange(e);
                            formik.setFieldTouched(
                              "job.rearOutriggersSpan",
                              true,
                              false
                            );
                          }}
                          InputLabelProps={{ shrink: true }}
                          disabled={isLoading}
                        />
                      </Grid>
                      <Grid item sm={6} xs={12}>
                        <CeTextField
                          fullWidth
                          id="frontSideOpening"
                          name="job.frontSideOpening"
                          label={t("common:front-side-opening")}
                          size="small"
                          type="number"
                          value={formik.values.job?.frontSideOpening ?? ""}
                          error={Boolean(
                            getIn(formik.errors, "job.frontSideOpening") &&
                              getIn(formik.touched, "job.frontSideOpening")
                          )}
                          InputProps={{
                            inputProps: { min: "0" },
                            endAdornment: (
                              <InputAdornment position="end">
                                {t("meter-symbol")}
                              </InputAdornment>
                            ),
                          }}
                          onChange={(e) => {
                            formik.handleChange(e);
                            formik.setFieldTouched(
                              "job.frontSideOpening",
                              true,
                              false
                            );
                          }}
                          InputLabelProps={{ shrink: true }}
                          disabled={isLoading}
                          required
                        />
                      </Grid>
                      <Grid item sm={6} xs={12}>
                        <CeTextField
                          fullWidth
                          id="rearSideOpening"
                          name="job.rearSideOpening"
                          label={t("common:rear-side-opening")}
                          size="small"
                          type="number"
                          value={formik.values.job?.rearSideOpening ?? ""}
                          error={Boolean(
                            getIn(formik.errors, "job.rearSideOpening") &&
                              getIn(formik.touched, "job.rearSideOpening")
                          )}
                          InputProps={{
                            inputProps: { min: "0" },
                            endAdornment: (
                              <InputAdornment position="end">
                                {t("meter-symbol")}
                              </InputAdornment>
                            ),
                          }}
                          onChange={(e) => {
                            formik.handleChange(e);
                            formik.setFieldTouched(
                              "job.rearSideOpening",
                              true,
                              false
                            );
                          }}
                          InputLabelProps={{ shrink: true }}
                          disabled={isLoading}
                          required
                        />
                      </Grid>
                      <Grid item xs={6}>
                        <Autocomplete
                          id="cleaning"
                          fullWidth
                          value={formik.values.job?.cleaning || null}
                          onChange={(
                            event: any,
                            nextValues: Cleaning | null
                          ) => {
                            if (nextValues) {
                              formik.setFieldValue("job.cleaning", nextValues);
                            }
                          }}
                          onBlur={() =>
                            formik.setFieldTouched("job.cleaning", true)
                          }
                          options={cleaningList}
                          filterSelectedOptions
                          renderInput={(params) => (
                            <CeTextField
                              {...params}
                              InputLabelProps={{ shrink: true }}
                              label={t("common:cleaning")}
                              size="small"
                              required
                              error={Boolean(
                                getIn(formik.errors, "job.cleaning") &&
                                  getIn(formik.touched, "job.cleaning")
                              )}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={6}>
                        <Autocomplete
                          id="parkingOn"
                          fullWidth
                          value={formik.values.job?.parkingOn || null}
                          onChange={(
                            event: any,
                            nextValues: ParkingOn | null
                          ) => {
                            if (nextValues) {
                              formik.setFieldValue("job.parkingOn", nextValues);
                            }
                          }}
                          onBlur={() =>
                            formik.setFieldTouched("job.parkingOn", true)
                          }
                          options={parkingOnList}
                          filterSelectedOptions
                          renderInput={(params) => (
                            <CeTextField
                              {...params}
                              InputLabelProps={{ shrink: true }}
                              label={t("common:parking-on")}
                              size="small"
                              required
                              error={Boolean(
                                getIn(formik.errors, "job.parkingOn") &&
                                  getIn(formik.touched, "job.parkingOn")
                              )}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={6}>
                        <Autocomplete
                          id="terrainStability"
                          fullWidth
                          value={formik.values.job?.terrainStability || null}
                          onChange={(
                            event: any,
                            nextValues: TerrainStability | null
                          ) => {
                            if (nextValues) {
                              formik.setFieldValue(
                                "job.terrainStability",
                                nextValues
                              );
                            }
                          }}
                          onBlur={() =>
                            formik.setFieldTouched("job.terrainStability", true)
                          }
                          options={TerrainStabilityList}
                          filterSelectedOptions
                          renderInput={(params) => (
                            <CeTextField
                              {...params}
                              InputLabelProps={{ shrink: true }}
                              label={t("common:terrain-stability")}
                              size="small"
                              required
                              error={Boolean(
                                getIn(formik.errors, "job.terrainStability") &&
                                  getIn(formik.touched, "job.terrainStability")
                              )}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={6}>
                        <CeTextField
                          fullWidth
                          id="ciaw"
                          name="job.ciaw"
                          label={t("CIAW (Check in At Work)")}
                          size="small"
                          required
                          value={formik.values.job?.ciaw}
                          InputLabelProps={{ shrink: true, size: "small" }}
                          onChange={(e) => {
                            formik.handleChange(e);
                            formik.setFieldTouched("job.ciaw", true, false);
                          }}
                          error={Boolean(
                            getIn(formik.touched, "job.ciaw") &&
                              getIn(formik.errors, "job.ciaw")
                          )}
                          helperText={
                            getIn(formik.touched, "job.ciaw") &&
                            getIn(formik.errors, "job.ciaw")
                          }
                          disabled={isLoading}
                        />
                      </Grid>
                      <Grid item xs={6}>
                        <Autocomplete
                          id="jobType"
                          fullWidth
                          value={formik.values.job?.jobType || null}
                          onChange={(
                            event: any,
                            nextValues: JobType | null
                          ) => {
                            if (nextValues) {
                              formik.setFieldValue("job.jobType", nextValues);
                            }
                          }}
                          onBlur={() =>
                            formik.setFieldTouched("job.jobType", true)
                          }
                          options={JobTypeList}
                          filterSelectedOptions
                          renderInput={(params) => (
                            <CeTextField
                              {...params}
                              InputLabelProps={{ shrink: true }}
                              label={t("common:job-type")}
                              size="small"
                              required
                              error={Boolean(
                                getIn(formik.errors, "job.jobType") &&
                                  getIn(formik.touched, "job.jobType")
                              )}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={6}></Grid>
                      <Grid item xs={6}>
                        <Stack spacing={1} flexDirection={"column"}>
                          <FormControl
                            fullWidth
                            sx={{ margin: "10px 0 5px 10px" }}
                          >
                            <FormControlLabel
                              checked={formik.values.job?.extraCementBag}
                              control={<Checkbox size="small" />}
                              label={`${t("common:extra-cement-bags")}`}
                              name="job.extraCementBag"
                              onChange={(event) => {
                                formik.handleChange(event);
                                const { name, checked } =
                                  event.target as HTMLInputElement;
                                if (!checked) {
                                  formik.setFieldValue("job.units", "");
                                  return;
                                }
                                if (name === "job.extraCementBag") {
                                  const extraCementBagPrice =
                                    vehicle?.assignedPricelist
                                      ?.extraCementBagPrice ?? 0;
                                  const units = formik?.values?.job?.units ?? 0;
                                  const extraCementBags = checked
                                    ? extraCementBagPrice * units
                                    : 0;
                                  formik.setFieldValue(
                                    "extraCementBags",
                                    extraCementBags
                                  );
                                }
                              }}
                            />
                          </FormControl>
                          {formik.values.job?.extraCementBag && (
                            <CeTextField
                              fullWidth
                              id="units"
                              name="job.units"
                              label={t("common:units")}
                              size="small"
                              type="number"
                              value={formik.values.job?.units ?? ""}
                              InputProps={{
                                inputProps: { min: 0 },
                                endAdornment: (
                                  <InputAdornment position="end">
                                    {t("units")}
                                  </InputAdornment>
                                ),
                              }}
                              error={Boolean(
                                formik.values.job?.extraCementBag &&
                                  getIn(formik.errors, "job.units") &&
                                  getIn(formik.touched, "job.units")
                              )}
                              onChange={(event) => {
                                const target = event.target as HTMLInputElement;
                                formik.handleChange(event);
                                const { name, value } = target;
                                if (name === "job.units") {
                                  const extraCementBagPrice =
                                    vehicle?.assignedPricelist
                                      ?.extraCementBagPrice ?? 0;
                                  const units = parseFloat(value);
                                  const extraCementBags = formik?.values?.job
                                    ?.extraCementBag
                                    ? extraCementBagPrice * units
                                    : 0;
                                  formik.setFieldValue(
                                    "extraCementBags",
                                    extraCementBags
                                  );
                                }
                              }}
                              InputLabelProps={{ shrink: true }}
                              disabled={
                                isLoading || !formik.values.job?.extraCementBag
                              }
                            />
                          )}
                        </Stack>
                      </Grid>
                      <Grid item xs={6}>
                        <Stack
                          spacing={1}
                          flexDirection={"column"}
                          alignItems={"center"}
                        >
                          <FormControl
                            fullWidth
                            sx={{ margin: "10px 0 5px 20px" }}
                          >
                            <FormControlLabel
                              checked={formik.values.job?.presenceOfPowerLines}
                              control={<Checkbox size="small" />}
                              label={t("common:presence-of-power-lines")}
                              name="job.presenceOfPowerLines"
                              onChange={(event) => {
                                const target = event.target as HTMLInputElement;
                                const { name, checked } = target;
                                if (!checked) {
                                  formik.setFieldValue("job.voltage", "");
                                }
                                formik.setFieldValue(
                                  "job.presenceOfPowerLines",
                                  !formik.values.job?.presenceOfPowerLines
                                );
                              }}
                            />
                          </FormControl>
                          {formik.values.job?.presenceOfPowerLines && (
                            <CeTextField
                              fullWidth
                              id="voltage"
                              name="job.voltage"
                              label={t("common:voltage")}
                              size="small"
                              type="number"
                              value={formik.values.job?.voltage ?? ""}
                              InputProps={{
                                inputProps: { min: 0 },
                                endAdornment: (
                                  <InputAdornment position="end">
                                    {t("volts")}
                                  </InputAdornment>
                                ),
                              }}
                              error={Boolean(
                                getIn(formik.errors, "job.voltage") &&
                                  getIn(formik.touched, "job.voltage")
                              )}
                              onChange={formik.handleChange}
                              InputLabelProps={{ shrink: true }}
                              disabled={
                                isLoading ||
                                !formik.values.job?.presenceOfPowerLines
                              }
                            />
                          )}
                        </Stack>
                      </Grid>
                      <Grid item xs={6}>
                        <Stack
                          spacing={1}
                          flexDirection={"column"}
                          alignItems={"center"}
                        >
                          <FormControl
                            fullWidth
                            sx={{ margin: "10px 0 5px 20px" }}
                          >
                            <FormControlLabel
                              checked={formik.values.job?.tonnageRestriction}
                              control={<Checkbox size="small" />}
                              label={t("common:tonnage-restriction")}
                              name="job.tonnageRestriction"
                              onChange={(event) => {
                                const target = event.target as HTMLInputElement;
                                const { name, checked } = target;
                                if (!checked) {
                                  formik.setFieldValue(
                                    "job.authorizedWeight",
                                    ""
                                  );
                                }
                                formik.setFieldValue(
                                  "job.tonnageRestriction",
                                  !formik.values.job?.tonnageRestriction
                                );
                              }}
                            />
                          </FormControl>
                          {formik.values.job?.tonnageRestriction && (
                            <CeTextField
                              fullWidth
                              id="authorizedWeight"
                              name="job.authorizedWeight"
                              label={t("common:authorized-weight")}
                              size="small"
                              type="number"
                              value={formik.values.job?.authorizedWeight ?? ""}
                              error={Boolean(
                                getIn(formik.errors, "job.authorizedWeight") &&
                                  getIn(formik.touched, "job.authorizedWeight")
                              )}
                              InputProps={{
                                inputProps: { min: "0" },
                                endAdornment: (
                                  <InputAdornment position="end">
                                    {t("t")}
                                  </InputAdornment>
                                ),
                              }}
                              onChange={formik.handleChange}
                              InputLabelProps={{ shrink: true }}
                              disabled={
                                isLoading ||
                                !formik.values.job?.tonnageRestriction
                              }
                            />
                          )}
                        </Stack>
                      </Grid>
                      <Grid item xs={6}>
                        <Stack
                          spacing={1}
                          flexDirection={"column"}
                          alignItems={"center"}
                        >
                          <FormControl
                            fullWidth
                            sx={{ margin: "10px 0 5px 20px" }}
                          >
                            <FormControlLabel
                              checked={formik.values.job?.heightRestriction}
                              control={<Checkbox size="small" />}
                              label={t("common:height-restriction")}
                              name="job.heightRestriction"
                              onChange={(event) => {
                                const target = event.target as HTMLInputElement;
                                const { name, checked } = target;
                                if (!checked) {
                                  formik.setFieldValue("job.heightLimit", "");
                                }
                                formik.setFieldValue(
                                  "job.heightRestriction",
                                  !formik.values.job?.heightRestriction
                                );
                              }}
                            />
                          </FormControl>
                          {formik.values.job?.heightRestriction && (
                            <CeTextField
                              fullWidth
                              id="heightLimit"
                              name="job.heightLimit"
                              label={t("common:height-limit")}
                              size="small"
                              type="number"
                              value={formik.values.job?.heightLimit ?? ""}
                              error={Boolean(
                                getIn(formik.errors, "job.heightLimit") &&
                                  getIn(formik.touched, "job.heightLimit")
                              )}
                              InputProps={{
                                inputProps: {
                                  min: 0,
                                },
                                endAdornment: (
                                  <InputAdornment position="end">
                                    {t("meter-symbol")}
                                  </InputAdornment>
                                ),
                              }}
                              onChange={formik.handleChange}
                              InputLabelProps={{ shrink: true }}
                              disabled={
                                isLoading ||
                                !formik.values.job?.heightRestriction
                              }
                            />
                          )}
                        </Stack>
                      </Grid>
                      {/* <Grid item xs={6}>
                        <FormControl fullWidth sx={{ margin: "5px 0 0 10px" }}>
                          <FormControlLabel
                            checked={formik.values.job?.enlistSecondTechnician}
                            control={<Checkbox size="small" />}
                            label={t("common:enlist-second-technician")}
                            name="job.enlistSecondTechnician"
                            onChange={() =>
                              formik.setFieldValue(
                                "job.enlistSecondTechnician",
                                !formik.values.job?.enlistSecondTechnician
                              )
                            }
                          />
                        </FormControl>
                      </Grid> */}
                      <Grid item xs={6}>
                        <Stack
                          spacing={2}
                          flexDirection={"row"}
                          alignItems={"center"}
                          justifyContent={"center"}
                          mx="auto"
                        >
                          <FormControl
                            fullWidth
                            sx={{ margin: "5px 0 0 10px" }}
                          >
                            <FormControlLabel
                              checked={formik.values.job?.pipeStartingFromBAC}
                              control={<Checkbox size="small" />}
                              label={t("common:pipe-starting-from-bac")}
                              name="job.pipeStartingFromBAC"
                              onChange={() =>
                                formik.setFieldValue(
                                  "job.pipeStartingFromBAC",
                                  !formik.values.job?.pipeStartingFromBAC
                                )
                              }
                            />
                          </FormControl>
                        </Stack>
                      </Grid>
                      <Grid item xs={6}>
                        <FormControl fullWidth sx={{ margin: "5px 0 0 10px" }}>
                          <FormControlLabel
                            checked={formik.values.job?.barbotine}
                            control={<Checkbox size="small" />}
                            label={t("common:barbotine")}
                            name="job.barbotine"
                            onChange={() =>
                              formik.setFieldValue(
                                "job.barbotine",
                                !formik.values.job?.barbotine
                              )
                            }
                          />
                        </FormControl>
                      </Grid>
                      <Grid item xs={6}>
                        <FormControl fullWidth sx={{ margin: "5px 0 0 10px" }}>
                          <FormControlLabel
                            checked={
                              formik.values.job?.supplyOfTheChemicalSlushie
                            }
                            control={<Checkbox size="small" />}
                            label={t("common:supply-of-the-chemical-slushie")}
                            name="job.supplyOfTheChemicalSlushie"
                            onChange={() =>
                              formik.setFieldValue(
                                "job.supplyOfTheChemicalSlushie",
                                !formik.values.job?.supplyOfTheChemicalSlushie
                              )
                            }
                          />
                        </FormControl>
                      </Grid>
                      <Grid
                        item
                        display="flex"
                        alignSelf="center"
                        justifyContent="center"
                        gap={1}
                        m="60px auto 0"
                        width={335}
                      >
                        <CeButton
                          variant="text"
                          color="primary"
                          onClick={(event) =>
                            handleTabChange(event, "clientDetailsTab")
                          }
                          size="medium"
                          disabled={isLoading}
                          sx={{
                            borderRadius: "8px",
                            padding: "8px 16px",
                            flex: 1,
                          }}
                        >
                          {t("common:back")}
                        </CeButton>
                        <CeButton
                          variant="outlined"
                          color="primary"
                          onClick={(event) =>
                            handleTabChange(event, "additionalInformation")
                          }
                          size="medium"
                          disabled={isLoading}
                          sx={{
                            borderRadius: "8px",
                            padding: "8px 16px",
                            flex: 1,
                          }}
                        >
                          {t("common:next")}
                        </CeButton>
                      </Grid>
                    </Grid>
                  </TabPanel>
                  <TabPanel value="additionalInformation">
                    <Grid
                      container
                      rowGap={2}
                      mx="auto"
                      width="100%"
                      maxWidth={650}
                    >
                      <Grid item sm={12} xs={6}>
                        <Box
                          alignItems="center"
                          justifyContent="center"
                          display="flex"
                          sx={{ my: 4 }}
                          gap={1}
                        >
                          <InformationCircleIcon
                            size={24}
                            color={"currentColor"}
                            variant={"stroke"}
                          />
                          <Typography
                            fontSize={20}
                            fontWeight={500}
                            letterSpacing={0.15}
                            color="text.primary"
                          >
                            {t("common:additional-information")}
                          </Typography>
                        </Box>
                      </Grid>

                      <Grid
                        item
                        xs={12}
                        display="flex"
                        alignItems="stretch"
                        justifyContent="center"
                        gap="10px"
                        height="190px"
                      >
                        <Stack
                          spacing={0}
                          flexDirection={"column"}
                          alignItems={"center"}
                          gap="10px"
                        >
                          <UploadFileButton
                            documentName={t(
                              "common:local-admin-authentication"
                            )}
                            onChange={(event: any) => {
                              const file = event.target?.files[0];

                              if (file) {
                                formik.setFieldValue(
                                  "localAdministrationAuthorizationFile",
                                  file
                                );
                              }
                            }}
                            icon={
                              <Upload02Icon
                                size={16}
                                color={"currentColor"}
                                variant={"stroke"}
                              />
                            }
                          />

                          {Boolean(
                            formik.values?.localAdministrationAuthorizationFile
                          ) ||
                          Boolean(
                            formik.values?.localAdministrationAuthorizationKey
                          ) ? (
                            <CheckmarkCircle02Icon
                              color={theme.palette.success.main}
                              variant="solid"
                            />
                          ) : null}
                        </Stack>
                        <Stack
                          flexDirection={"column"}
                          alignItems={"center"}
                          gap="10px"
                        >
                          <UploadFileButton
                            documentName={t("common:traffic-plan")}
                            onChange={(event: any) => {
                              const file = event.target?.files[0];

                              if (file) {
                                formik.setFieldValue("trafficPlanFile", file);
                              }
                            }}
                            icon={
                              <Upload02Icon
                                size={16}
                                color={"currentColor"}
                                variant={"stroke"}
                              />
                            }
                          />

                          {Boolean(formik.values?.trafficPlanFile) ||
                          Boolean(formik.values?.trafficPlanKey) ? (
                            <CheckmarkCircle02Icon
                              color={theme.palette.success.main}
                              variant="solid"
                            />
                          ) : null}
                        </Stack>
                        <Stack
                          flexDirection={"column"}
                          alignItems={"center"}
                          gap="10px"
                        >
                          <UploadFileButton
                            documentName={t("common:parking-permit-acquired")}
                            onChange={(event: any) => {
                              const file = event.target?.files[0];

                              if (file) {
                                formik.setFieldValue(
                                  "parkingPermitAcquiredFile",
                                  file
                                );
                              }
                            }}
                            icon={
                              <Upload02Icon
                                size={16}
                                color={"currentColor"}
                                variant={"stroke"}
                              />
                            }
                          />

                          {Boolean(formik.values?.parkingPermitAcquiredFile) ||
                          Boolean(formik.values?.parkingPermitAcquiredKey) ? (
                            <CheckmarkCircle02Icon
                              color={theme.palette.success.main}
                              variant="solid"
                            />
                          ) : null}
                        </Stack>
                      </Grid>
                      <Grid item sx={{ margin: "30px auto 10px" }} xs={12}>
                        <Divider
                          sx={{
                            opacity: 0.6,
                            borderBottomWidth: "3px",
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sx={{ mx: "auto" }}>
                        <CeTextField
                          sx={{ mt: 2 }}
                          fullWidth
                          id="comments"
                          name="job.comments"
                          label={t("common:comments")}
                          size="small"
                          multiline
                          rows={4}
                          value={formik.values.job?.comments}
                          onChange={formik.handleChange}
                          error={Boolean(
                            getIn(formik.touched, "clientDetails.comments") &&
                              getIn(formik.errors, "clientDetails.comments")
                          )}
                          helperText={
                            getIn(formik.touched, "clientDetails.comments") &&
                            getIn(formik.errors, "clientDetails.comments")
                          }
                          InputLabelProps={{ shrink: true }}
                          disabled={isLoading}
                        />
                      </Grid>
                    </Grid>

                    <Grid
                      item
                      xs={6}
                      display="flex"
                      alignSelf="center"
                      justifyContent="center"
                      m="60px auto 0"
                    >
                      <CeButton
                        variant="text"
                        color="primary"
                        onClick={(event) =>
                          handleTabChange(event, "jobDetails")
                        }
                        size="medium"
                        disabled={isLoading}
                        sx={{
                          borderRadius: "8px",
                          padding: "8px 16px",
                          width: "335px",
                        }}
                      >
                        {t("common:back")}
                      </CeButton>
                    </Grid>
                  </TabPanel>
                </TabContext>
              </Box>
            </CardContent>
          </CeCard>
        </Grid>

        <Grid item xs={5}>
          <ReservationInfoCard
            vehicleUniqueName={vehicleUniqueName}
            formattedDate={formattedDate || "-"}
            formattedTime={formattedTime || "-"}
            siteAddress={siteAddress || "-"}
            operatorManager={vehicle?.operator}
            isFavorited={Boolean(favorites.length)}
            handleVehicleInformationModal={() => {
              if (vehicle) {
                handleVehicleInformationModal(vehicle);
              }
            }}
            handleFavoriteCompany={(companyId: number) => {
              if (!dispatcherCompanyId) {
                throw new Error("Dispatcher manager was not found!");
              }

              if (favorites.length) {
                handleUnFavoriteCompany({
                  operatorCompanyId: companyId,
                  dispatcherCompanyId,
                });
              } else {
                handleFavoriteCompany({
                  operatorCompanyId: companyId,
                  dispatcherCompanyId,
                });
              }
            }}
          />
          {currentPricelist?.isContract ? null : (
            <AssignPricelist
              pricelists={pricelists}
              attrs={assignPricelist}
              assignedPricelistId={currentPricelist?.id!}
              onPricelistChange={(pricelist) => {
                formik.setFieldValue("pricelist", pricelist);
              }}
            />
          )}

          <CostEstimation
            flatFee={flatFee}
            flatFeeNight={flatFeeNight}
            flatFeeWeekend={flatFeeWeekend}
            extraCementBags={extraCementBags}
            additionalHour={additionalHour}
            additionalHourNight={additionalHourNight}
            additionalHourWeekend={additionalHourWeekend}
            flexiblePipeLength80Mm={flexiblePipeLength80Mm}
            flexiblePipeLength90Mm={flexiblePipeLength90Mm}
            rigidPipeLength100={rigidPipeLength100}
            rigidPipeLength120={rigidPipeLength120}
            amountOfConcrete={concreteAmountFee}
            totalDayContractFee={totalDayContractFee}
            totalOvertimeFee={totalOvertimeFee}
            totalDayContractDuration={totalDayContractDuration}
            totalOvertimeDuration={totalOvertimeDuration}
            cleaningFee={cleaningFee}
            barbotineFee={barbotineFee}
            supplyOfTheChemicalSlushieFee={supplyOfTheChemicalSlushieFee}
            transportTierFee={transportTierFee}
            classifiedReservationSchedule={classifiedReservationSchedule}
            totalCost={totalCost}
          />

          <CeCard sx={{ borderRadius: "0 0 12px 12px" }}>
            <CardContent>
              <DialogActions>
                <CeButton
                  type="submit"
                  color="primary"
                  variant="contained"
                  size="medium"
                  disabled={isLoading}
                  sx={{
                    borderRadius: "8px",
                    padding: "8px 16px",
                    width: "335px",
                    margin: " 5px auto 0",
                  }}
                >
                  {editMode
                    ? t("common:update-reservation")
                    : t("common:confirm-reservation")}
                </CeButton>
              </DialogActions>
            </CardContent>
          </CeCard>
        </Grid>
      </Grid>
      <VehicleInformationModal vehicleId={vehicleInformationValues.vehicleId} />
      <EditWorkTimeModal
        onHandleOverwriteTime={handleOverwriteWorkHours}
        dateFrom={timePlanned.dateFrom}
        jobInfo={formik?.values?.job}
        isModalOpen={reservationEditDurationValues.flow === "Edit Time"}
        handleCloseModal={() =>
          setReservationEditDurationValues({ flow: null })
        }
      />
    </Stack>
  );
};
