import { Box, Grid, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { priceListReviewRowStyles } from "src/common/components/custom/customCss";
import {
  PriceList as PriceListProps,
  PumpTier,
  TransportRate,
} from "src/common/types/priceList";

interface VehicleInfoModalPriceTabProps {
  priceList?: PriceListProps;
  gridItemSize?: number;
}

const PriceListView = ({
  priceList,
  gridItemSize,
}: VehicleInfoModalPriceTabProps) => {
  const { t } = useTranslation(["dispatcher"]);
  return (
    <Grid container sx={{ py: 0.5, px: 1 }}>
      <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
        <Box display="flex" justifyContent="space-between">
          <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
            {t("common:day-contract-fee")}
          </Typography>
          <Typography fontSize={14} fontWeight="bold" letterSpacing="0.17px">
            {priceList?.dayContractFee ? priceList.dayContractFee + " €" : "-"}
          </Typography>
        </Box>
      </Grid>
      <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
        <Box display="flex" justifyContent="space-between">
          <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
            {t("common:day-contract-duration")}
          </Typography>
          <Typography fontSize={14} fontWeight="bold" letterSpacing="0.17px">
            {priceList?.dayContractDuration
              ? priceList.dayContractDuration + " € / h"
              : "-"}
          </Typography>
        </Box>
      </Grid>
      {priceList?.pricePerMeterPumped && (
        <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
          <Box display="flex" justifyContent="space-between">
            <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
              {t("common:price-per-meter-pumped")}
            </Typography>
            <Typography fontSize={14} fontWeight="bold" letterSpacing="0.17px">
              {priceList.pricePerMeterPumped + " € / h"}
            </Typography>
          </Box>
        </Grid>
      )}
      <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
        <Box display="flex" justifyContent="space-between">
          <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
            {t("common:day-contract-overtime-rate")}
          </Typography>
          <Typography fontSize={14} fontWeight="bold" letterSpacing="0.17px">
            {priceList?.dayContractOvertimeRate
              ? priceList.dayContractOvertimeRate + " € / h"
              : "-"}
          </Typography>
        </Box>
      </Grid>
      {priceList?.cleaningFee ? (
        <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
          <Box display="flex" justifyContent="space-between">
            <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
              {t("common:cleaning-fee")}
            </Typography>
            <Typography fontSize={14} fontWeight="bold" letterSpacing="0.17px">
              {priceList?.cleaningFee ? priceList.cleaningFee + " €" : "-"}
            </Typography>
          </Box>
        </Grid>
      ) : null}
      <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
        <Box display="flex" justifyContent="space-between">
          <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
            {t("common:price-per-meter-of-flexible-pipe-length-80Mm")}
          </Typography>
          <Typography fontSize={14} fontWeight="bold" letterSpacing="0.17px">
            {priceList?.pricePerMeterOfFlexiblePipeLength80Mm
              ? priceList?.pricePerMeterOfFlexiblePipeLength80Mm + " € / m"
              : "-"}
          </Typography>
        </Box>
      </Grid>
      <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
        <Box display="flex" justifyContent="space-between">
          <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
            {t("common:price-per-meter-of-flexible-pipe-length-90Mm")}
          </Typography>
          <Typography fontSize={14} fontWeight="bold" letterSpacing="0.17px">
            {priceList?.pricePerMeterOfFlexiblePipeLength90Mm
              ? priceList.pricePerMeterOfFlexiblePipeLength90Mm + " € / m"
              : "-"}
          </Typography>
        </Box>
      </Grid>
      <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
        <Box display="flex" justifyContent="space-between">
          <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
            {t("common:price-per-meter-of-flexible-pipe-length-100Mm")}
          </Typography>
          <Typography fontSize={14} fontWeight="bold">
            {priceList?.pricePerMeterOfFlexiblePipeLength100Mm
              ? priceList.pricePerMeterOfFlexiblePipeLength100Mm + " € / m"
              : "-"}
          </Typography>
        </Box>
      </Grid>
      <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
        <Box display="flex" justifyContent="space-between">
          <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
            {t("common:price-per-meter-of-rigid-pipe-length-120Mm")}
          </Typography>
          <Typography fontSize={14} fontWeight="bold">
            {priceList?.pricePerMeterOfRigidPipeLength120Mm
              ? priceList.pricePerMeterOfRigidPipeLength120Mm + " € / m"
              : "-"}
          </Typography>
        </Box>
      </Grid>
      <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
        <Box display="flex" justifyContent="space-between">
          <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
            {t("common:supply-of-the-chemical-slushie")}
          </Typography>
          <Typography fontSize={14} fontWeight="bold">
            {priceList?.supplyOfTheChemicalSlushie
              ? priceList.supplyOfTheChemicalSlushie + " €"
              : "-"}
          </Typography>
        </Box>
      </Grid>
      <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
        <Box display="flex" justifyContent="space-between">
          <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
            {t("common:barbotine")}
          </Typography>
          <Typography fontSize={14} fontWeight="bold">
            {priceList?.barbotine ? priceList.barbotine + " €" : "-"}
          </Typography>
        </Box>
      </Grid>
      <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
        <Box display="flex" justifyContent="space-between">
          <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
            {t("common:pipeInvoicingStartsFrom")}
          </Typography>
          <Typography fontSize={14} fontWeight="bold">
            {priceList?.pipeInvoicingStartsFrom
              ? priceList.pipeInvoicingStartsFrom + " € / m"
              : "-"}
          </Typography>
        </Box>
      </Grid>
      <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
        <Box display="flex" justifyContent="space-between">
          <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
            {t("common:secondTechnicianFee")}
          </Typography>
          <Typography fontSize={14} fontWeight="bold">
            {priceList?.secondTechnicianHourFee
              ? priceList.secondTechnicianHourFee + " €"
              : "-"}
          </Typography>
        </Box>
      </Grid>
      {priceList?.pumpTiers && priceList.pumpTiers.length ? (
        <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
          {priceList.pumpTiers.map((tier: PumpTier, index) => (
            <Box display="flex" justifyContent="space-between" key={index}>
              <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
                {`${t("common:pump-tiers")} ${index + 1}`}
              </Typography>
              <Typography
                fontSize={14}
                fontWeight="bold"
                letterSpacing="0.17px"
              >
                {tier.price + " €"}
              </Typography>
            </Box>
          ))}
        </Grid>
      ) : null}
      <Grid item sx={priceListReviewRowStyles} xs={gridItemSize || 12}>
        {priceList?.transportRates?.map((rate: TransportRate, index) => (
          <Box display="flex" justifyContent="space-between">
            <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
              {`${t("common:transport-rates")} ${index + 1}`}
            </Typography>
            <Typography fontSize={14} fontWeight="bold" letterSpacing="0.17px">
              {rate.tariff + " €"}
            </Typography>
          </Box>
        ))}
      </Grid>
      <Grid
        item
        sx={{
          padding: 2,
        }}
        xs={gridItemSize || 12}
      >
        <Box display="flex" justifyContent="space-between">
          <Typography fontSize={14} variant="body2" letterSpacing="0.17px">
            {t("common:extra-cement-bag-price")}
          </Typography>
          <Typography fontSize={14} fontWeight="bold" letterSpacing="0.17px">
            {priceList?.extraCementBagPrice
              ? priceList.extraCementBagPrice + " € / bag"
              : "-"}
          </Typography>
        </Box>
      </Grid>
    </Grid>
  );
};

export default PriceListView;
