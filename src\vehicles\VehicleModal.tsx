import { AxiosError } from "axios";
import { UseMutateFunction } from "react-query";
import { MainModal } from "src/common/components";
import {
  CreateVehicleDto,
  UpdateVehicleDto,
  Vehicle,
  VehicleFormValues,
} from "src/common/types";
import { VehicleForm } from "./VehicleForm";
import { SetterOrUpdater } from "recoil";
import { VehicleDetailsOneForm } from "./formSteps/VehicleDetailsOneForm";
import { Drawer } from "@mui/material";

interface VehicleModalProps {
  isLoading: boolean;
  initialFormValues: VehicleFormValues;
  setInitialFormValues: SetterOrUpdater<VehicleFormValues>;
  handleCloseVehicleModal: () => void;
  handleCreateNewVehicle?: UseMutateFunction<
    Vehicle,
    AxiosError<CreateVehicleDto, CreateVehicleDto> | Error,
    CreateVehicleDto,
    () => void
  >;
  handleUpdateVehicle?: UseMutateFunction<
    Vehicle,
    AxiosError<UpdateVehicleDto, UpdateVehicleDto> | Error,
    UpdateVehicleDto,
    () => void
  >;
  existingVehicles: Vehicle[];
  getValuesFromExistingVehicle: (vehicle: Vehicle) => void;
}
export const VehicleModal: React.FC<VehicleModalProps> = ({
  isLoading,
  initialFormValues,
  setInitialFormValues,
  handleCloseVehicleModal,
  handleCreateNewVehicle,
  handleUpdateVehicle,
  existingVehicles,
  getValuesFromExistingVehicle,
}) => {
  const renderTitle = () => {
    switch (initialFormValues.flow) {
      case "Create":
        return "Create Vehicle";
      case "Update":
        return "Update Vehicle";

      default:
        return "Vehicle";
    }
  };
   
  const isFlowCreateOrUpdate =
  initialFormValues.flow === "Create" || initialFormValues.flow === "Update";
  return (
    <Drawer
      anchor="right"
      open={isFlowCreateOrUpdate}
      onClose={handleCloseVehicleModal}
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 2,
        display: "flex",
        flexDirection: "column",
        gap: 3,
        "& .MuiBackdrop-root": {
          backgroundColor: "transparent",
        },
      }}
    >
      <VehicleDetailsOneForm
        title={renderTitle()}
        initialFormValues={initialFormValues}
        setInitialFormValues={setInitialFormValues}
        handleCreateNewVehicle={handleCreateNewVehicle}
        handleUpdateVehicle={handleUpdateVehicle}
        isLoading={isLoading}
        existingVehicles={existingVehicles}
        getValuesFromExistingVehicle={getValuesFromExistingVehicle}
      />
    </Drawer>
  );
};
