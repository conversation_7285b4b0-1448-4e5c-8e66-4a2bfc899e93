import { getCurrentUser, useGetContracts, useUsers } from "src/common/api";
import { useWindowSize } from "@react-hook/window-size";
import { useState } from "react";
import { ContractDatagrid } from "./datagrid/ContractDatagrid";
import { Role } from "src/common/types";
import { CePaper } from "src/common/components";

export const Contracts = () => {
  const [, height] = useWindowSize();
  const currentUser = getCurrentUser();

  const [pageState, setPageState] = useState<{
    page: number;
    pageSize: number;
  }>({
    page: 1,
    pageSize: 20,
  });

  const {
    data: allContracts,
    isLoading: isLoadingContracts,
    refetch: refetchContracts,
  } = useGetContracts(
    {
      expressions: [],
      sortModel: [],
      limit: pageState.pageSize,
      offset: (pageState.page - 1) * pageState.pageSize,

      relations: ["dispatcherCompany", "operatorCompany"],
    },
    <PERSON><PERSON><PERSON>(currentUser?.companyId)
  );

  const handlePageChange = (newPage: number) => {
    setPageState((old) => ({
      ...old,
      page: newPage + 1,
    }));
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageState((old) => ({
      ...old,
      pageSize: newPageSize,
    }));
  };

  const isOperatorManager = currentUser?.role === Role.OEPRATOR_MANAGER;

  return (
    <CePaper sx={{ height: `${height - 100}px`, p: 2 }}>
      <ContractDatagrid
        data={allContracts?.data || []}
        isFetchingContracts={isLoadingContracts}
        refetchContracts={refetchContracts}
        shouldRenderRefreshButton
        shouldRenderAddButton={isOperatorManager}
        shouldRenderEditActionsColumn
        page={pageState.page}
        pageSize={pageState.pageSize}
        total={allContracts?.totalCount || 0}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />
    </CePaper>
  );
};
