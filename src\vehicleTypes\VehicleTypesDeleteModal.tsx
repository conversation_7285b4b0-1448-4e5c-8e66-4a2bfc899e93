import { Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { DeleteModal } from "src/common/components";
import {
  DeleteVehicleTypeDto,
  VehicleTypeDeleteModalFlow,
} from "src/common/types";

interface VehicleTypesDeleteProps {
  flow: VehicleTypeDeleteModalFlow;
  vehicleTypeTitle?: string;
  isLoading: boolean;
  vehicleTypeId?: number;
  handleCloseVehicleModalDelete: () => void;
  handleDeleteVehicleType: (args: DeleteVehicleTypeDto) => void;
}
export const VehicleTypesDeleteModal: React.FC<VehicleTypesDeleteProps> = ({
  flow,
  vehicleTypeTitle,
  vehicleTypeId,
  isLoading,
  handleCloseVehicleModalDelete,
  handleDeleteVehicleType,
}) => {
  const { t } = useTranslation(["common"]);

  const onDeleteVehicle = () => {
    if (vehicleTypeId) {
      handleDeleteVehicleType({ vehicleTypeId });
    }
  };

  const DeleteModalHelperText = () => (
    <Typography sx={{ marginBottom: 1 }}>
      {t("delete-vehicle-type-message", {
        vehicleTypeTitle: vehicleTypeTitle || "unknown vehicle",
      })}
    </Typography>
  );

  return (
    <DeleteModal
      isLoading={isLoading}
      title={t("common:delete-vehicle-type")}
      helperText={DeleteModalHelperText()}
      isOpen={flow === "Delete"}
      handleSubmit={() => onDeleteVehicle()}
      handleClose={handleCloseVehicleModalDelete}
    />
  );
};
