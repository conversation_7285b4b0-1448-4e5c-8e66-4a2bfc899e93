import { useState } from "react";
import Box from "@mui/material/Box";
import { Header } from "./header/Header";
import { SideBar } from "./sidebar/Sidebar";
import { Content } from "./content/Content";
import { getCurrentUser } from "src/common/api";
import { Role } from "src/common/types";
import { SideBarMobile } from "./sidebar/SidebarMobile";
import { HeaderMobile } from "./header/HeaderMobile";

export const Menu = () => {
  const currentUser = getCurrentUser();

  const isOperator = currentUser?.role === Role.OPERATOR;
  const sidebarDefaultState = isOperator ? false : true;

  const [open, setOpen] = useState<boolean>(sidebarDefaultState);

  const handleDrawerToggle = (): void => {
    setOpen((open) => !open);
  };

  return (
    <Box sx={{ 
      display: "flex", 
      marginTop: isOperator ? 8 : 1,
    }}>
      {isOperator ? (
        <> 
        <HeaderMobile open={open} handleDrawerToggle={handleDrawerToggle} />
        <SideBarMobile open={open} setOpen={setOpen} />
        </>
      ) : (
        <SideBar open={open} handleDrawerToggle={handleDrawerToggle} />
      )}

      <Content />
    </Box>
  );
};
