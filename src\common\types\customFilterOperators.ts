import {
    getGridNumericOperators,
    getGridStringOperators,
    GridFilterInputSingleSelect,
    GridFilterInputValue,
    GridFilterItem,
    GridFilterOperator,
  } from '@mui/x-data-grid'
  import { getGridDateOperators, getGridSingleSelectOperators } from '@mui/x-data-grid-pro'
  import { GridCellParams } from '@mui/x-data-grid/models/params/gridCellParams'
  import { addDays, isAfter, isBefore, subDays } from 'date-fns'
  import { CustomDatePicker } from 'src/common/components/custom/CustomDatePicker'
  
  export const notContainsFilter: GridFilterOperator = {
    label: 'not contains',
    value: 'not_contains',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
  
      return (params: GridCellParams): boolean => {
        const cellValue = String(params.value).toLowerCase()
        const filterValue = String(filterItem.value).toLowerCase()
        return !cellValue.includes(filterValue)
      }
    },
    InputComponent: GridFilterInputValue,
  }
  
  export const notContainsOrEmptyFilter: GridFilterOperator = {
    label: 'not contains or empty',
    value: 'not_contains_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
  
      return (params: GridCellParams): boolean => {
        const cellValue = String(params.value).toLowerCase()
        const filterValue = String(filterItem.value).toLowerCase()
        return !cellValue.includes(filterValue) || cellValue.trim() === ''
      }
    },
    InputComponent: GridFilterInputValue,
  }
  
  export const doesNotContainOperator = [...getGridStringOperators(), notContainsFilter, notContainsOrEmptyFilter]
  
  export const doesNotContainOperatorWithoutAnyOf = [
    ...getGridStringOperators().filter((o) => o.value !== 'isAnyOf'),
    notContainsFilter,
    notContainsOrEmptyFilter,
  ]
  
  export const beforeOrEmptyFilter: GridFilterOperator = {
    label: '< or empty',
    value: 'before_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value as string
        if (!value) {
          return true
        }
        const cellValue = new Date(value)
        const filterValue = new Date(filterItem.value)
        return cellValue < filterValue
      }
    },
    InputComponent: CustomDatePicker,
    InputComponentProps: { type: 'date' },
  }
  
  const customNativeDateOperators: GridFilterOperator[] = getGridDateOperators()
    .filter((operator) => operator.value !== 'is')
    .map((operator) => {
      // operators with empty values, no need for datepicker
      if (['isEmpty', 'isNotEmpty'].includes(operator.value)) {
        return operator
      }
  
      return {
        ...operator,
        InputComponent: CustomDatePicker,
        InputComponentProps: { type: 'date' },
      }
    })
  
  export const onOrBeforeOrEmptyFilter: GridFilterOperator = {
    label: '<= or empty',
    value: 'onOrBefore_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value as string
        if (!value) {
          return true
        }
        const cellValue = new Date(value)
        const filterValue = new Date(filterItem.value)
        return cellValue <= filterValue
      }
    },
    InputComponent: CustomDatePicker,
    InputComponentProps: { type: 'date' },
  }
  
  export const isOnDateFilter: GridFilterOperator = {
    label: 'is on',
    value: 'isOn',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        let cellValue
        const value = params.value as string
        if (!value) {
          return true
        }
        if (typeof value === 'string' && value.includes('-')) {
          cellValue = new Date(value.replace('-', '')).setHours(0, 0, 0, 0)
        } else {
          cellValue = new Date(value).setHours(0, 0, 0, 0)
        }
        const filterValue = new Date(filterItem.value).setHours(0, 0, 0, 0)
        return cellValue === filterValue
      }
    },
    InputComponent: CustomDatePicker,
    InputComponentProps: { type: 'date' },
  }
  
  export const isNotOnDateFilter: GridFilterOperator = {
    label: 'is not on',
    value: 'isNotOn',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        let cellValue
        const value = params.value as string
        if (!value) {
          return true
        }
        if (typeof value === 'string' && value.includes('-')) {
          cellValue = new Date(value.replace('-', '')).setHours(0, 0, 0, 0)
        } else {
          cellValue = new Date(value).setHours(0, 0, 0, 0)
        }
        const filterValue = new Date(filterItem.value).setHours(0, 0, 0, 0)
        return cellValue !== filterValue
      }
    },
    InputComponent: CustomDatePicker,
    InputComponentProps: { type: 'date' },
  }
  
  export const isInCurrentCalendarYearFilter: GridFilterOperator = {
    label: 'is in current calendar year',
    value: 'isInCurrentCalendarYear',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value as string
        if (!value) {
          return false
        }
  
        let cellDate: Date
        if (typeof value === 'string' && value.includes('-')) {
          cellDate = new Date(value.replace('-', ''))
        } else {
          cellDate = new Date(value)
        }
        const currentYear = new Date().getFullYear()
        return cellDate.getFullYear() === currentYear
      }
    },
  }
  
  export const isNotInCurrentCalendarYearFilter: GridFilterOperator = {
    label: 'is not in current calendar year',
    value: 'isNotInCurrentCalendarYear',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value as string
        if (!value) {
          return true
        }
  
        let cellDate: Date
        if (typeof value === 'string' && value.includes('-')) {
          cellDate = new Date(value.replace('-', ''))
        } else {
          cellDate = new Date(value)
        }
  
        const currentYear = new Date().getFullYear()
        return cellDate.getFullYear() !== currentYear
      }
    },
  }
  
  export const onOrAfterOrEmptyFilter: GridFilterOperator = {
    label: '>= or empty',
    value: 'onOrAfter_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value as string
        if (!value) {
          return true
        }
        const cellValue = new Date(value)
        const filterValue = new Date(filterItem.value)
        return cellValue >= filterValue
      }
    },
    InputComponent: CustomDatePicker,
    InputComponentProps: { type: 'date' },
  }
  
  export const afterOrEmptyFilter: GridFilterOperator = {
    label: '> or empty',
    value: 'after_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value as string
  
        if (!value) {
          return true
        }
        const cellValue = new Date(value)
        const filterValue = new Date(filterItem.value)
        return cellValue > filterValue
      }
    },
    InputComponent: CustomDatePicker,
    InputComponentProps: { type: 'date' },
  }
  
  // Days ago
  
  export const lessDaysAgoFilter: GridFilterOperator<any, number> = {
    label: '< days ago',
    value: 'lt_days_ago',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
  
        if (value === null || value === undefined || value === '') {
          return false
        }
        const dateInTheRow = filterItem.value
        const dateInTheFilter = subDays(new Date(), Number(value))
        return isBefore(dateInTheRow, dateInTheFilter)
      }
    },
    InputComponent: GridFilterInputValue,
    InputComponentProps: { type: 'number' },
  }
  
  export const lessOrEmptyDaysAgoFilter: GridFilterOperator<any, number> = {
    label: '< days ago (or empty)',
    value: 'lt_days_ago_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
        if (value === null || value === undefined || value === '') {
          return true
        }
        const dateInTheRow = filterItem.value
        const dateInTheFilter = subDays(new Date(), Number(value))
        return isBefore(dateInTheRow, dateInTheFilter)
      }
    },
    InputComponent: GridFilterInputValue,
    InputComponentProps: { type: 'number' },
  }
  
  export const moreDaysAgoFilter: GridFilterOperator<any, number> = {
    label: '> days ago',
    value: 'gt_days_ago',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
        if (value === null || value === undefined || value === '') {
          return false
        }
        const dateInTheRow = filterItem.value
        const dateInTheFilter = subDays(new Date(), Number(value))
        return isAfter(dateInTheRow, dateInTheFilter)
      }
    },
    InputComponent: GridFilterInputValue,
    InputComponentProps: { type: 'number' },
  }
  
  export const moreOrEmptyDaysAgoFilter: GridFilterOperator<any, number> = {
    label: '> days ago (or empty)',
    value: 'gt_days_ago_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
        if (value === null || value === undefined || value === '') {
          return true
        }
        const dateInTheRow = filterItem.value
        const dateInTheFilter = subDays(new Date(), Number(value))
        return isAfter(dateInTheRow, dateInTheFilter)
      }
    },
    InputComponent: GridFilterInputValue,
    InputComponentProps: { type: 'number' },
  }
  
  // Days in the future
  
  export const lessDaysInFutureFilter: GridFilterOperator<any, number> = {
    label: '< days in future',
    value: 'lt_days_in_future',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
        if (value === null || value === undefined || value === '') {
          return false
        }
        const dateInTheRow = filterItem.value
        const dateInTheFilter = addDays(new Date(), Number(value))
        return isBefore(dateInTheRow, dateInTheFilter)
      }
    },
    InputComponent: GridFilterInputValue,
    InputComponentProps: { type: 'number' },
  }
  
  export const lessOrEmptyDaysInFutureFilter: GridFilterOperator<any, number> = {
    label: '< days in future (or empty)',
    value: 'lt_days_in_future_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
        if (value === null || value === undefined || value === '') {
          return true
        }
        const dateInTheRow = filterItem.value
        const dateInTheFilter = addDays(new Date(), Number(value))
        return isBefore(dateInTheRow, dateInTheFilter)
      }
    },
    InputComponent: GridFilterInputValue,
    InputComponentProps: { type: 'number' },
  }
  
  export const moreDaysInFutureFilter: GridFilterOperator<any, number> = {
    label: '> days in future',
    value: 'gt_days_in_future',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
        if (value === null || value === undefined || value === '') {
          return false
        }
        const dateInTheRow = filterItem.value
        const dateInTheFilter = addDays(new Date(), Number(value))
        return isAfter(dateInTheRow, dateInTheFilter)
      }
    },
    InputComponent: GridFilterInputValue,
    InputComponentProps: { type: 'number' },
  }
  
  export const moreOrEmptyDaysInFutureFilter: GridFilterOperator<any, number> = {
    label: '> days in future (or empty)',
    value: 'gt_days_in_future_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
        if (value === null || value === undefined || value === '') {
          return true
        }
        const dateInTheRow = filterItem.value
        const dateInTheFilter = addDays(new Date(), Number(value))
        return isAfter(dateInTheRow, dateInTheFilter)
      }
    },
    InputComponent: GridFilterInputValue,
    InputComponentProps: { type: 'number' },
  }
  
  export const doesDateOrEmptyOperator = [
    isOnDateFilter,
    isNotOnDateFilter,
    isInCurrentCalendarYearFilter,
    isNotInCurrentCalendarYearFilter,
    //not using customNativeDateOperators becuase it's causing issue in client side datagrid filtering
    ...getGridDateOperators().filter((o) => o.value !== 'is'),
    beforeOrEmptyFilter,
    onOrBeforeOrEmptyFilter,
    afterOrEmptyFilter,
    onOrAfterOrEmptyFilter,
    lessDaysAgoFilter,
    moreDaysAgoFilter,
    lessOrEmptyDaysAgoFilter,
    moreOrEmptyDaysAgoFilter,
    lessDaysInFutureFilter,
    lessOrEmptyDaysInFutureFilter,
    moreDaysInFutureFilter,
    moreOrEmptyDaysInFutureFilter,
  ]
  
  export const doesDateOrEmptyOperatorWithoutDateOperators = [
    beforeOrEmptyFilter,
    onOrBeforeOrEmptyFilter,
    afterOrEmptyFilter,
    onOrAfterOrEmptyFilter,
  ]
  
  const trueOrEmptyOperator: GridFilterOperator = {
    label: 'true or empty',
    value: 'true_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
        if (filterItem.operatorValue === 'true_or_empty') {
          return value === true || value === null
        }
        return false
      }
    },
    requiresFilterValue: false,
  }
  
  const falseOrEmptyOperator: GridFilterOperator = {
    label: 'false or empty',
    value: 'false_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
        if (filterItem.operatorValue === 'false_or_empty') {
          return value === false || value === null
        }
        return false
      }
    },
    requiresFilterValue: false,
  }
  
  const isOperator: GridFilterOperator = {
    label: 'is',
    value: 'is',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      const { columnField, value, operatorValue } = filterItem
      if (!columnField || value === undefined || value === null || !operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        console.log(params, filterItem)
        return Boolean(params.value) === Boolean(filterItem.value)
      }
    },
    InputComponent: GridFilterInputSingleSelect,
    InputComponentProps: { type: 'boolean' },
  }
  
  export const doesBooleanOrEmptyOperator: GridFilterOperator[] = [
    isOperator,
    ...getGridStringOperators().filter((o) => o.value === 'isEmpty' || o.value === 'isNotEmpty'),
    trueOrEmptyOperator,
    falseOrEmptyOperator,
  ]
  
  export const lessOrEmptyFilter: GridFilterOperator<any, number> = {
    label: '< or empty',
    value: 'lt_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
        if (value === null || value === undefined || value === '') {
          return true
        }
        return Number(value) < Number(filterItem.value)
      }
    },
    InputComponent: GridFilterInputValue,
    InputComponentProps: { type: 'number' },
  }
  
  export const lessOrEqualslOrEmptyFilter: GridFilterOperator<any, number> = {
    label: '<= or empty',
    value: 'lte_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
        if (value === null || value === undefined || value === '') {
          return true
        }
        return Number(value) <= Number(filterItem.value)
      }
    },
    InputComponent: GridFilterInputValue,
    InputComponentProps: { type: 'number' },
  }
  
  export const moreOrEmptyFilter: GridFilterOperator<any, number> = {
    label: '> or empty',
    value: 'gt_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
        if (value === null || value === undefined || value === '') {
          return true
        }
        return Number(value) > Number(filterItem.value)
      }
    },
    InputComponent: GridFilterInputValue,
    InputComponentProps: { type: 'number' },
  }
  
  export const moreOrEqualsOrEmptyFilter: GridFilterOperator<any, number> = {
    label: '>= or empty',
    value: 'gte_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
        if (value === null || value === undefined || value === '') {
          return true
        }
        return Number(value) >= Number(filterItem.value)
      }
    },
    InputComponent: GridFilterInputValue,
    InputComponentProps: { type: 'number' },
  }
  
  export const notEqualOrEmptyFilter: GridFilterOperator<any, number> = {
    label: '!= or empty',
    value: '!=_or_empty',
    getApplyFilterFn: (filterItem: GridFilterItem) => {
      if (!filterItem.columnField || !filterItem.value || !filterItem.operatorValue) {
        return null
      }
      return (params: GridCellParams): boolean => {
        const value = params.value
        if (value === null || value === undefined || value === '') {
          return true
        }
        return Number(value) != Number(filterItem.value)
      }
    },
    InputComponent: GridFilterInputValue,
    InputComponentProps: { type: 'number' },
  }
  
  export const doesNumberOrEmptyOperator = [
    ...getGridNumericOperators().filter((o) => o.value !== 'isAnyOf'),
    lessOrEmptyFilter,
    lessOrEqualslOrEmptyFilter,
    moreOrEmptyFilter,
    moreOrEqualsOrEmptyFilter,
    notEqualOrEmptyFilter,
  ]
  
  export const doesNumberOrEmptyOperatorWithoutIsEmpty = [
    ...getGridNumericOperators().filter((o) => o.value !== 'isAnyOf' && o.value !== 'isEmpty' && o.value !== 'isNotEmpty'),
    lessOrEmptyFilter,
    lessOrEqualslOrEmptyFilter,
    moreOrEmptyFilter,
    moreOrEqualsOrEmptyFilter,
    notEqualOrEmptyFilter,
  ]
  
  const customStringOperators = getGridStringOperators()
    .filter((operator) => operator.value !== 'equals' && operator.value !== 'isAnyOf')
    .map((operator) => {
      switch (operator.value) {
        case 'contains':
          return { ...operator, label: 'case status contains' }
        case 'isEmpty':
          return { ...operator, label: 'case status is empty' }
        case 'isNotEmpty':
          return { ...operator, label: 'case status is not empty' }
        case 'startsWith':
          return { ...operator, label: 'case status starts with' }
        case 'endsWith':
          return { ...operator, label: 'case status ends with' }
        default:
          return operator
      }
    })
  
  const customSingleSelectOperators = getGridSingleSelectOperators()
    .filter((operator) => operator.value !== 'isAnyOf')
    .map((operator) => {
      switch (operator.value) {
        case 'is':
          return { ...operator, label: 'case status is' }
        case 'not':
          return { ...operator, label: 'case status is not' }
        default:
          return operator
      }
    })
  
  export const customOperators = [...customSingleSelectOperators, ...customStringOperators]
  