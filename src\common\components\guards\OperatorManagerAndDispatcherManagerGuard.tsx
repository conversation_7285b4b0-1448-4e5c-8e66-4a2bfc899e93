import type { FC, ReactNode } from "react";
import { isAuthenticated } from "src/common/api/auth";
import { Role } from "src/common/types";

interface OperatorManagerAndDispatcherGuardProps {
  children: ReactNode;
}
export const OperatorManagerAndDispatcherManagerGuard: FC<OperatorManagerAndDispatcherGuardProps> = ({
  children,
}) => {
  const { role } = isAuthenticated();

  if (role === Role.OEPRATOR_MANAGER || role === Role.DISPATCHER_MANAGER) {
    return <>{children}</>;
  }

  return null;
};
