export const formatPhoneNumber = (value?: string | null) => {
  if (value === '+' || value === '+1' || value === '' || value === undefined || value === null) {
    return null
  }

  return `+${value?.replace(/\D/g, '')}`
}

export function formatPhoneNumberUs(phoneNumberString: string) {
  const cleaned = ('' + phoneNumberString).replace(/\D/g, '')
  const match = cleaned.match(/^(1|)?(\d{3})(\d{3})(\d{4})$/)
  if (match) {
    const intlCode = match[1] ? '+1 ' : ''
    return [intlCode, '(', match[2], ') ', match[3], '-', match[4]].join('')
  }
  return null
}
