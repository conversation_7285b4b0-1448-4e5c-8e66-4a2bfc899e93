import { GridColDef } from "@mui/x-data-grid-pro";

export interface FilterBoxOption {
  columnText: string;
  columnField: string;
  type: string;
}

export interface HhGridColDef extends GridColDef {
  hhFilter?: { columnField: string };
}

export type ExpressionOperator =
  | "="
  | "=="
  | "equals"
  | "is"
  | "!="
  | "not"
  | "in"
  | "isAnyOf"
  | "not_in"
  | "lt"
  | "lte"
  | "<"
  | "<="
  | "before"
  | "onOrBefore"
  | "gt"
  | "gte"
  | ">"
  | ">="
  | "after"
  | "onOrAfter"
  | "contains"
  | "not_contains"
  | "starts_with"
  | "startsWith"
  | "not_starts_with"
  | "ends_with"
  | "endsWith"
  | "not_ends_with"
  | "isEmpty"
  | "isNotEmpty"
  | "true_or_empty"
  | "false_or_empty"
  | "between"
  | "isInCurrentCalendarYear"
  | "isNotInCurrentCalendarYear";

export type ExpressionConditionType = "AND" | "OR" | null | undefined;

export class Expression {
  conditionType?: ExpressionConditionType;

  category?: string | null | undefined;

  operator?: ExpressionOperator | null | undefined;

  value?: string | number | null | string[] | number[] | undefined | boolean;

  expressions?: Expression[] | null | undefined;
}
