import { CommonEntity, PossibleSortDir } from "./common";
import { Manager } from "./manager";
import { User } from "./user";

export interface ReservationInvoice extends CommonEntity {
  data: string;
}

export interface BulkInvoiceLog extends CommonEntity {
  id: number;
  dateFrom: string;
  dateTo: string;
  reservationIds: number[];
  operatorManagerId: number;
  dispatcherManagerId: number;
  total: number;
  dispatcherManager: User;
  operatorManager: Manager;
  invoiceNumber: string;
  status: InvoiceStatus;
}
export interface CreateReservationInvoice {
  reservationIds: number[];
  dispatcherCompanyId: number;
}
export interface CreateReservationInvoiceResponse {
  status: string;
}
export interface GetInvoiceLog {
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
  type?: InvoiceType;
  dispatcherCompanyIds?: number[] | null;
  operatorCompanyIds?: number[] | null;
}
export interface GetInvoiceLogPdf {
  invoiceNumber?: string;
  reservationId?: number;
  invoiceType?: InvoiceType;
}
export interface BulkInvoiceLogWithCount {
  totalCount: number;
  data: BulkInvoiceLog[];
}

export interface GenerateBulkInvoiceDto {
  reservationIds: number[];
  dispatcherCompanyId: number;
}

export type GenerateBulkInvoiceModalFlow = "Generate" | null;


export enum InvoiceStatus {
  PENDING = "Pending",
  COMPLETED = "Completed",
  CANCELLED = "Cancelled",
  FAILED = "Failed",
}

export enum InvoiceType {
  SINGLE = 'single',
  BULK = 'bulk',
}

export const invoiceTypes: InvoiceType[] = [InvoiceType.BULK, InvoiceType.SINGLE];