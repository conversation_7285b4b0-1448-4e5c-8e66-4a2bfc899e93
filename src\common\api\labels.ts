import axios, { AxiosError } from "axios";
import {
  CreateTaskLabelDto,
  GetLabelTaskDto,
  TaskLabel,
  TaskLabelWithCount,
  UpdateTaskLabelDto,
} from "../types/labels";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { processApiError } from "../utils/errors";
import { wrapEachFieldIntoDoubleQuotes } from "../utils";
const backendUrl = process.env.REACT_APP_API_URL;

export const getTaskLabels = async (queryParams: GetLabelTaskDto) => {
  return axios
    .post(`${backendUrl}/task-labels/get`, queryParams, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const useTaskLabels = (
  queryParams: GetLabelTaskDto,
  enabled: boolean = true
) => {
  const formattedSortModel = wrapEachFieldIntoDoubleQuotes(
    queryParams.sortModel
  );
  const formattedAttrs = { ...queryParams, sortModel: formattedSortModel };
  return useQuery<TaskLabelWithCount, AxiosError | Error>(
    ["labels", queryParams],
    () => getTaskLabels(queryParams),
    {
      keepPreviousData: true,
      onError: (err) => console.error("Unable to fetch task labels", err),
      enabled,
    }
  );
};

export const createTaskLabel = async (
  createTaskLabelDto: CreateTaskLabelDto
) => {
  return axios
    .post(`${backendUrl}/task-labels`, createTaskLabelDto, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const useCreateTaskLabel = () => {
  const queryClient = useQueryClient();

  return useMutation<
    TaskLabel,
    AxiosError | Error,
    CreateTaskLabelDto,
    () => void
  >((createLabelArgs: CreateTaskLabelDto) => createTaskLabel(createLabelArgs), {
    onSuccess: () => {
      queryClient.invalidateQueries("label");
      queryClient.invalidateQueries("labels");
    },
    onError: (err) => processApiError("Unable to create task label", err),
  });
};

export const updateLabel = (updateTaskLabelArgs: UpdateTaskLabelDto) => {
  const { labelId, ...label } = updateTaskLabelArgs;
  if (!labelId) {
    throw new Error("the label ID was not provided");
  }
  return axios
    .patch(`${backendUrl}/task-labels/${labelId}`, label, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useUpdateTaskLabel = () => {
  const queryClient = useQueryClient();
  return useMutation<
    TaskLabel,
    AxiosError | Error,
    UpdateTaskLabelDto,
    () => void
  >(
    (updateTaskLabelArgs: UpdateTaskLabelDto) =>
      updateLabel(updateTaskLabelArgs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("label");
        queryClient.invalidateQueries("labels");
      },
      onError: (err) => {
        processApiError("Unable to update label", err);
      },
    }
  );
};
