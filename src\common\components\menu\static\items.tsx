import { ReactComponent as BulletedList } from "../../../../images/Bulleted list.svg";
import { ReactComponent as Pump } from "../../../../images/Pump.svg";
import {
  ContractsIcon,
  FileEuroIcon,
  Invoice01Icon,
  Search01Icon,
  Home01Icon,
  CheckListIcon,
  Settings01Icon,
  HorizontalResizeIcon,
  UserSettings02Icon,
} from "@hugeicons/react";
import { UserMultipleIcon } from "@hugeicons/react";
import { Wallet01Icon } from "@hugeicons/react";
import { UserGroupIcon } from "@hugeicons/react";
import { Task01Icon } from "@hugeicons/react";
import { Calendar03Icon } from "@hugeicons/react";
import { v4 as uuidv4 } from "uuid";
import { UserRole } from "src/common/types";

export interface StaticItem {
  id: string;
  menuName: string;
  menuDescription: string;
  path: string;
  icon: Object;
  hasDivider: boolean;
  submenu: StaticItem[];
  hasTitle: boolean;
  flags: UserRole[];
  title?: string;
}

export const staticItems: StaticItem[] = [
  {
    id: uuidv4(),
    menuName: "common:planning",
    menuDescription: "",
    path: "dispatcher-planning",
    icon: (
      <Calendar03Icon
        style={{ fill: "none" }}
        size={24}
        color={"currentColor"}
        variant={"stroke"}
        type="rounded"
        opacity={0.8}
      />
    ),
    hasDivider: false,
    submenu: [],
    hasTitle: false,
    flags: ["dispatcher", "dispatcher manager"],
  },
  {
    id: uuidv4(),
    menuName: "common:dashboard",
    menuDescription: "",
    path: "dashboard",
    icon: (
      <Home01Icon
        style={{ fill: "none" }}
        size={24}
        color={"currentColor"}
        variant={"stroke"}
        type="rounded"
        opacity={0.8}
      />
    ),
    hasDivider: false,
    submenu: [],
    hasTitle: false,
    flags: ["dispatcher manager"],
  },
  {
    id: uuidv4(),
    menuName: "common:search-vehicles",
    menuDescription: "",
    path: "search",
    icon: (
      <Search01Icon
        style={{ fill: "none" }}
        size={24}
        color={"currentColor"}
        variant={"stroke"}
        type="rounded"
        opacity={0.8}
      />
    ),
    hasDivider: false,
    submenu: [],
    hasTitle: false,
    flags: ["dispatcher", "dispatcher manager"],
  },
  {
    id: uuidv4(),
    menuName: "common:planning",
    menuDescription: "",
    path: "planning",
    icon: (
      <Calendar03Icon
        style={{ fill: "none" }}
        size={24}
        color={"currentColor"}
        variant={"stroke"}
        type="rounded"
        opacity={0.8}
      />
    ),
    hasDivider: false,
    submenu: [],
    hasTitle: false,
    flags: ["operator manager"],
  },
  {
    id: uuidv4(),
    menuName: "common:vehicles",
    menuDescription: "",
    path: "vehicles",
    icon: <Pump />,
    hasDivider: false,
    submenu: [],
    hasTitle: false,
    flags: ["operator manager"],
  },
  {
    id: uuidv4(),
    menuName: "common:reservations",
    menuDescription: "",
    path: "reservations",
    icon: (
      <Task01Icon
        style={{ fill: "none" }}
        size={24}
        color={"currentColor"}
        variant={"stroke"}
        type="rounded"
        opacity={0.8}
      />
    ),
    hasDivider: false,
    submenu: [],
    hasTitle: false,
    flags: ["operator manager", "dispatcher", "dispatcher manager"],
  },
  {
    id: uuidv4(),
    menuName: "common:managers",
    menuDescription: "",
    path: "managers",
    icon: (
      <UserMultipleIcon
        style={{ fill: "none" }}
        size={24}
        color={"currentColor"}
        variant={"stroke"}
        type="rounded"
        opacity={0.8}
      />
    ),
    hasDivider: false,
    submenu: [],
    hasTitle: true,
    title: "common:user-management",
    flags: ["manager"],
  },
  {
    id: uuidv4(),
    menuName: "common:operators",
    menuDescription: "",
    path: "operators",
    icon: (
      <UserMultipleIcon
        style={{ fill: "none" }}
        size={24}
        color={"currentColor"}
        variant={"stroke"}
        type="rounded"
        opacity={0.8}
      />
    ),
    hasDivider: false,
    submenu: [],
    hasTitle: false,
    flags: ["operator manager"],
  },
  {
    id: uuidv4(),
    menuName: "common:dispatchers",
    menuDescription: "",
    path: "dispatchers",
    icon: (
      <UserMultipleIcon
        style={{ fill: "none" }}
        size={24}
        color={"currentColor"}
        variant={"stroke"}
        type="rounded"
        opacity={0.8}
      />
    ),
    hasDivider: false,
    submenu: [],
    hasTitle: false,
    flags: ["dispatcher manager"],
  },
  {
    id: uuidv4(),
    menuName: "common:partners",
    menuDescription: "",
    path: "partners",
    icon: <UserGroupIcon style={{ fill: "none" }} />,
    hasDivider: false,
    submenu: [],
    hasTitle: false,
    flags: ["dispatcher manager", "dispatcher"],
  },
  {
    id: uuidv4(),
    menuName: "common:todo",
    menuDescription: "",
    path: "todo",
    icon: (
      <CheckListIcon
        style={{ fill: "none" }}
        size={24}
        color={"currentColor"}
        variant={"stroke"}
        type="rounded"
        opacity={0.8}
      />
    ),
    hasDivider: false,
    submenu: [],
    hasTitle: false,
    flags: ["dispatcher manager", "dispatcher"],
  },
  {
    id: uuidv4(),
    menuName: "common:finance",
    menuDescription: "",
    path: "finance",
    icon: (
      <Wallet01Icon
        style={{ fill: "none" }}
        size={24}
        color={"currentColor"}
        variant={"stroke"}
        type="rounded"
        opacity={0.8}
      />
    ),
    hasDivider: false,
    submenu: [
      {
        id: uuidv4(),
        menuName: "common:invoices",
        menuDescription: "",
        path: "finance/invoices",
        icon: (
          <Invoice01Icon
            style={{ fill: "none" }}
            size={24}
            color={"currentColor"}
            variant={"stroke"}
            type="rounded"
            opacity={0.8}
          />
        ),
        hasDivider: false,
        submenu: [],
        hasTitle: false,
        flags: ["operator manager"],
      },
      {
        id: uuidv4(),
        menuName: "common:contracts",
        menuDescription: "",
        path: "finance/contracts",
        icon: (
          <ContractsIcon
            style={{ fill: "none" }}
            size={24}
            color={"currentColor"}
            variant={"stroke"}
            type="rounded"
            opacity={0.8}
          />
        ),
        hasDivider: false,
        submenu: [],
        hasTitle: false,
        flags: ["operator manager", "dispatcher manager"],
      },
      {
        id: uuidv4(),
        menuName: "common:pricelist",
        menuDescription: "",
        path: "finance/pricelist",
        icon: (
          <FileEuroIcon
            style={{ fill: "none" }}
            size={24}
            color={"currentColor"}
            variant={"stroke"}
            type="rounded"
            opacity={0.8}
          />
        ),
        hasDivider: false,
        submenu: [],
        hasTitle: false,
        flags: ["operator manager"],
      },
    ],
    hasTitle: false,
    flags: ["operator manager", "dispatcher manager"],
  },
  {
    id: uuidv4(),
    menuName: "common:settings",
    menuDescription: "",
    path: "settings",
    icon: (
      <Settings01Icon
        style={{ fill: "none" }}
        size={24}
        color={"currentColor"}
        variant={"stroke"}
        type="rounded"
        opacity={0.8}
      />
    ),
    hasDivider: false,
    submenu: [],
    hasTitle: false,
    flags: ["dispatcher manager", "operator manager", "dispatcher", "operator"],
  },
  {
    id: uuidv4(),
    menuName: "common:clients",
    menuDescription: "",
    path: "clients",
    icon: (
      <UserGroupIcon
        style={{ fill: "none" }}
        size={24}
        color={"currentColor"}
        variant={"stroke"}
        type="rounded"
        opacity={0.8}
      />
    ),
    hasDivider: false,
    submenu: [],
    hasTitle: false,
    flags: ["operator manager"],
  },
  {
    id: uuidv4(),
    menuName: "common:upcoming-job",
    menuDescription: "",
    path: "upcoming-jobs",
    icon: <BulletedList />,
    hasDivider: false,
    submenu: [],
    hasTitle: false,
    flags: ["operator"],
  },
  {
    id: uuidv4(),
    menuName: "common:planning",
    menuDescription: "",
    path: "operator-planning",
    icon: (
      <Calendar03Icon
        style={{ fill: "none" }}
        size={24}
        color={"currentColor"}
        variant={"stroke"}
        type="rounded"
        opacity={0.8}
      />
    ),
    hasDivider: false,
    submenu: [],
    hasTitle: false,
    flags: ["operator"],
  },
];
