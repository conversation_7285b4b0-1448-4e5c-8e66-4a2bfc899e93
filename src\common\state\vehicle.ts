import { atom } from "recoil";
import {
  DeleteVehicleModalValues,
  FilterVehiclesFormValues,
  OpenVehicleInformationValues,
  VehicleFormValues,
} from "../types";
import {
  VEHICLE_DELETE_DEFAULT,
  VEHICLE_FILTER_VALUES_DEFAULT,
  VEHICLE_FORM_VALUES,
  VEHICLE_INFORMATION_VALUES_DEFAULT,
} from "../constants/vehicle";

export const vehicleFormValuesState = atom<VehicleFormValues>({
  key: "vehicleFormValuesState",
  default: VEHICLE_FORM_VALUES,
});

export const vehicleDeleteValuesState = atom<DeleteVehicleModalValues>({
  key: "vehicleDeleteValuesState",
  default: VEHICLE_DELETE_DEFAULT,
});

export const vehicleFilterFormValuesState = atom<FilterVehiclesFormValues>({
  key: "vehicleFilterFormValuesState",
  default: VEHICLE_FILTER_VALUES_DEFAULT,
});


export const vehicleInformationFlowState = atom<OpenVehicleInformationValues>({
  key: "vehicleInformationFlowState",
  default: VEHICLE_INFORMATION_VALUES_DEFAULT,
});
