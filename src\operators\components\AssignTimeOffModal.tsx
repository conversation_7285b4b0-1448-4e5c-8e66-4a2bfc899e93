import React from 'react'
import { Drawer, Box, Typography, IconButton, Stack, useTheme } from '@mui/material'
import { ArrowLeft01Icon, Calendar04Icon, Cancel01Icon } from '@hugeicons/react'
import { CreateUnavailabilityVehicleDto, CreateUnavailablePeriodDto, UnavailablePeriod, UnavailablePeriodFormValues, UpdateUnavailabilityVehicleDto, UpdateUnavailablePeriodDto } from 'src/common/types';
import { UseMutateFunction } from 'react-query';
import { AxiosError } from 'axios';
import { useFormik } from 'formik';
import * as yup from "yup";
import { DateTimePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { CeButton, CeTextField } from 'src/common/components';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { enGB } from 'date-fns/locale';
import { differenceInCalendarDays, isBefore } from 'date-fns';
import { useTranslation } from 'react-i18next';

interface AssignTimeOffModalProps {
    isLoading: boolean;
    initialFormValues: UnavailablePeriodFormValues;
    handleCloseUnavailablePeriod: () => void;
    handleCreateUnavailablePeriod?: UseMutateFunction<
        UnavailablePeriod,
        AxiosError<CreateUnavailablePeriodDto, CreateUnavailablePeriodDto> | Error,
        CreateUnavailablePeriodDto,
        () => void
    >;
    handleCreateUnavailabilityVehicle?: UseMutateFunction<
        UnavailablePeriod,
        AxiosError<CreateUnavailabilityVehicleDto, CreateUnavailabilityVehicleDto> | Error,
        CreateUnavailabilityVehicleDto,
        () => void
    >;
    handleUpdateUnavailablePeriod?: UseMutateFunction<
        UnavailablePeriod,
        AxiosError<UpdateUnavailablePeriodDto, UpdateUnavailablePeriodDto> | Error,
        UpdateUnavailablePeriodDto,
        () => void
    >;
    handleUpdateUnavailabilityVehicle?: UseMutateFunction<
        UnavailablePeriod,
        AxiosError<UpdateUnavailabilityVehicleDto, UpdateUnavailabilityVehicleDto> | Error,
        UpdateUnavailabilityVehicleDto,
        () => void
    >;
}

const AssignTimeOffModal: React.FC<AssignTimeOffModalProps> = ({
    isLoading,
    initialFormValues,
    handleCloseUnavailablePeriod,
    handleCreateUnavailablePeriod,
    handleCreateUnavailabilityVehicle,
    handleUpdateUnavailablePeriod,
    handleUpdateUnavailabilityVehicle
}) => {
    const { t } = useTranslation("common");
    const theme = useTheme();
    const renderTitle = () => {
        switch (initialFormValues.flow) {
          case "Create":
            return  t("assignTimeOff");
          case "Update":
            return t("updateTimeOff");
          case "CreateVehicle":
            return t("assign_unavailability_period");
          case "UpdateVehicle":
            return t("update_unavailability_period");
          default:
            return t("assignTimeOff");
        }
      };
    
    const formik = useFormik<UnavailablePeriodFormValues>({
        initialValues: initialFormValues,
        enableReinitialize: true,
        validationSchema: yup.object({
            from: yup
                .date()
                .required(t('unavailableFromRequired'))
                .test(
                    'not-in-past',
                    t('common:cannot-select-past-date-from'),
                    function (value) {
                        if (!value) return false;
                        if (
                            initialFormValues.from &&
                            new Date(value).getTime() === new Date(initialFormValues.from).getTime()
                          ) {
                            return true;
                          }
                          
                        const currentTime = new Date();
                        return value >= currentTime;
                    }
                ),
            to: yup
                .date()
                .required(t('unavailableToRequired'))
                .test(
                    'is-after',
                    t('unavailableToAfterFrom'),
                    function (value) {
                        const { from } = this.parent;
                        return from && value ? value >= from : true;
                    }
                )
        }),
        onSubmit: (values) => {
            const { flow } = initialFormValues;
            switch (flow) {
                case "Create":
                    if (handleCreateUnavailablePeriod) {
                        const payload: CreateUnavailablePeriodDto = {
                            from: values.from,
                            to: values.to,
                            operatorId: values.operatorId!
                        };
                        handleCreateUnavailablePeriod(payload);
                    }
                    break;

                case "Update":
                    if (handleUpdateUnavailablePeriod) {
                        const payload: UpdateUnavailablePeriodDto = {
                            from: values.from,
                            to: values.to,
                            operatorId: values.operatorId!,
                            id: values.id!
                        };
                        handleUpdateUnavailablePeriod(payload);
                    }
                    break;

                case "CreateVehicle":
                    if (handleCreateUnavailabilityVehicle) {
                        const payload: CreateUnavailabilityVehicleDto = {
                            from: values.from,
                            to: values.to,
                            vehicleIds: values.vehicleIds!
                        };
                        handleCreateUnavailabilityVehicle(payload);
                    }
                    break;

                case "UpdateVehicle":
                    if (handleUpdateUnavailabilityVehicle) {
                        const payload: UpdateUnavailabilityVehicleDto = {
                            from: values.from,
                            to: values.to,
                            vehicleIds: values.vehicleIds!,
                            id: values.id!
                        };
                        handleUpdateUnavailabilityVehicle(payload);
                    }
                    break;

                default:
                    break;
            }
        }
    });
    const isFromPast = initialFormValues.from
    ? isBefore(new Date(initialFormValues.from), new Date())
    : false;
    const days =
      formik.values.from && formik.values.to
        ? differenceInCalendarDays(new Date(formik.values.to), new Date(formik.values.from)) + 1
        : 0;

    return (
        <Drawer
            anchor="right"
            open={!!initialFormValues.flow}
            onClose={handleCloseUnavailablePeriod}
            sx={{
                zIndex: (theme) => theme.zIndex.drawer + 2,
                '& .MuiDrawer-paper': {
                    boxSizing: 'border-box',
                    width: 500,
                },
                '& .MuiBackdrop-root': {
                    backgroundColor: 'transparent',
                },
            }}
        >
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    height: '100%',
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        px: 3,
                        py: 2
                    }}
                >
                    <Stack sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                        <IconButton edge="start" color="inherit" onClick={handleCloseUnavailablePeriod} aria-label="back">
                            <ArrowLeft01Icon size={24} />
                        </IconButton>
                        <Typography variant="h6" component="div" sx={{textTransform:'capitalize'}}>
                           {renderTitle()}
                        </Typography>
                    </Stack>
                    <IconButton onClick={handleCloseUnavailablePeriod} edge="end" aria-label="close">
                        <Cancel01Icon variant='solid' size={20} />
                    </IconButton>
                </Box>
                <Box sx={{ p: 4 }}>
                    <Stack
                        component="form"
                        spacing={2}
                        noValidate
                        onSubmit={formik.handleSubmit}
                    >
                        <Stack direction={"row"} gap={1}>
                            <LocalizationProvider
                                dateAdapter={AdapterDateFns}
                                adapterLocale={enGB}
                            >
                                <DateTimePicker
                                    minutesStep={5}
                                    label={t("common:unavailableFrom")}
                                    value={formik.values.from}
                                    disablePast
                                    disabled={isFromPast}
                                    onChange={(newValue) => {
                                        formik.setFieldValue("from", newValue);
                                    }}
                                    components={{
                                        OpenPickerIcon: Calendar04Icon,
                                    }}
                                    renderInput={(params) => (
                                        <CeTextField
                                            {...params}
                                            fullWidth
                                            size="small"
                                            placeholder={t("common:unavailableFrom")}
                                            onBlur={() => formik.setFieldTouched("from", true)}
                                            error={!!formik.errors.from && formik.touched.from}
                                            helperText={formik.touched.from && formik.errors.from}
                                        />
                                    )}
                                />
                            </LocalizationProvider>
                            <LocalizationProvider
                                dateAdapter={AdapterDateFns}
                                adapterLocale={enGB}
                            >
                                <DateTimePicker
                                    minutesStep={5}
                                    label={t("common:unavailableTo")}
                                    value={formik.values.to}
                                    onChange={(newValue) => {
                                        formik.setFieldValue("to", newValue);
                                    }}
                                    components={{
                                        OpenPickerIcon: Calendar04Icon,
                                    }}
                                    renderInput={(params) => (
                                        <CeTextField
                                            {...params}
                                            fullWidth
                                            size="small"
                                            placeholder={t("common:unavailableTo")}
                                            onBlur={() => formik.setFieldTouched("to", true)}
                                            error={!!formik.errors.to && formik.touched.to}
                                            helperText={formik.touched.to && formik.errors.to}
                                        />
                                    )}
                                />
                            </LocalizationProvider>
                        </Stack>

                        <Stack>
                            <Box
                                sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    backgroundColor: theme.palette.grey[50],
                                    borderRadius: theme.shape.borderRadius,
                                    padding: 3,
                                    mb: 2
                                }}
                            >
                                <Typography variant="subtitle1" fontWeight={500}>
                                   {t("total")}
                                </Typography>
                                <Typography variant="subtitle1" fontWeight={500}>
                                    {days} {days > 1 ? t("days"): t("day")}
                                </Typography>
                            </Box>
                        </Stack>

                        <Stack direction={"row"} sx={{ gap: 2, justifyContent: 'center', pt: 2 }}>
                            <CeButton
                                variant="text"
                                onClick={handleCloseUnavailablePeriod}
                                disabled={isLoading}
                            >
                                {t("common:cancel")}
                            </CeButton>
                            <CeButton
                                type="submit"
                                disabled={isLoading}
                                variant="contained"
                                color="primary"
                            >
                                {initialFormValues.flow === "Create" || "CreateVehicle" ? t("common:assignee"): t("common:update")}
                            </CeButton>
                        </Stack>
                    </Stack>
                </Box>
            </Box>
        </Drawer>
    )
}

export default AssignTimeOffModal;
