import { Grid } from "@mui/material";
import DashboardCard from "./components/DashboardCard";
import {
  useAverageJobCompletionTime,
  useAverageM3PerHour,
  useAverageM3PerJob,
  useTotalExpensesPerMonth,
  useTotalReservationsPerMonth,
} from "src/common/api/analytics";
import { getCurrentUser } from "src/common/api";
import { GetAnalyticstDto } from "src/common/types/analytics";
import { endOfMonth, format, startOfMonth } from "date-fns";
import { DashboardCharts } from "./components/DashboardCharts";
import DashboardTables from "./components/DashboardTables";
import { useTranslation } from "react-i18next";

export const Dashboard = () => {
  const { t } = useTranslation(["dispatcher"]);
  const currentUser = getCurrentUser();
  const attrs: GetAnalyticstDto = {
    dispatcherCompanyId: currentUser?.companyId,
    startDate: format(startOfMonth(new Date()), "yyyy-MM-dd"),
    endDate: format(endOfMonth(new Date()), "yyyy-MM-dd"),
  };

  const { data: totalReservationsPerMonth } = useTotalReservationsPerMonth(
    attrs,
    Boolean(currentUser?.id)
  );
  const { data: totalExpensesPerMonth } = useTotalExpensesPerMonth(
    attrs,
    Boolean(currentUser?.id)
  );
  const { data: averageJobCompletionTime } = useAverageJobCompletionTime(
    attrs,
    Boolean(currentUser?.id)
  );
  const { data: averageM3PerJob } = useAverageM3PerJob(
    attrs,
    Boolean(currentUser?.id)
  );
  const { data: averageM3PerHour } = useAverageM3PerHour(
    attrs,
    Boolean(currentUser?.id)
  );

  return (
    <>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={2.4}>
          <DashboardCard
            title={t("total-reservations-per-month")}
            value={
              totalReservationsPerMonth?.totalReservationsPerMonth[0]
                ?.totalReservations || 0
            }
            trendData={
              totalReservationsPerMonth?.totalReservationsPerMonth[0]
                ?.progress || 0
            }
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <DashboardCard
            title={t("total-expenses-per-month")}
            value={
              totalExpensesPerMonth?.totalExpensesPerMonth[0]?.totalExpenses ||
              0
            }
            unit="€"
            trendData={
              totalExpensesPerMonth?.totalExpensesPerMonth[0]?.progress || 0
            }
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <DashboardCard
            title={t("average-job-completion-time")}
            value={averageJobCompletionTime?.averageCompletionTimeInHours || 0}
            unit="hours"
            trendData={averageJobCompletionTime?.progress || 0}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <DashboardCard
            title={t("average-m3-per-job")}
            value={averageM3PerJob?.averageM3PerJob || 0}
            unit="m³"
            trendData={averageM3PerJob?.progress || 0}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <DashboardCard
            title={t("average-m3-per-hour")}
            value={averageM3PerHour?.averageM3PerHour || 0}
            unit="m³"
            trendData={averageM3PerHour?.progress || 0}
          />
        </Grid>
      </Grid>
      <DashboardCharts />
      <DashboardTables />
    </>
  );
};
