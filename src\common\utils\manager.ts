import { userRolesDisplay, userStatusDisplay } from "../constants";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Manager,
  ManagerFormValues,
  ManagerModalFlow,
  UpdateManagerDto,
} from "../types";

export const turnManagerFormValuesIntoCreateDto = (
  values: ManagerFormValues
): CreateManagerDto => {
  const payload = {
    firstName: values.firstName,
    lastName: values.lastName,
    companyName: values.companyName,
    companyAddress: values.companyAddress,
    email: values.email,
    phoneNumber: values.phoneNumber,
    vatNumber: values.vatNumber,
    companyType: values.companyType!,
    status: values.status?.id!,
    role: values.role?.id!,
    country: values.country!,
  };

  return payload;
};

export const turnManagerFormValuesIntoUpdateDto = (
  values: ManagerFormValues
): UpdateManagerDto => {
  const payload: UpdateManagerDto = {
    managerId: values.managerId!,
    firstName: values.firstName,
    lastName: values.lastName,
    companyName: values.companyName,
    companyAddress: values.companyAddress,
    email: values.email,
    phoneNumber: values.phoneNumber,
    companyType: values.companyType!,
    vatNumber: values.vatNumber,
    status: values.status?.id!,
    role: values.role?.id!,
    country: values.country!,
  };

  return payload;
};

export const turnManagerIntoFormValues = (
  manager: Manager,
  flow: ManagerModalFlow
): ManagerFormValues => {
  const status =
    userStatusDisplay.find((status) => status.id === manager.status) || null;
  const role =
    userRolesDisplay.find((role) => role.id === manager.role) || null;

  const payload = {
    managerId: manager.id,
    firstName: manager.firstName || "",
    lastName: manager.lastName || "",
    vatNumber: manager.company.vatNumber || "",
    companyName: manager.company.name || "",
    companyAddress: manager.company.address || "",
    companyType: manager.company.type,
    email: manager.email || "",
    phoneNumber: manager.phoneNumber || "",
    country: manager.country || "",
    status: status,
    role: role,
    flow,
  };

  return payload;
};
