import { Stack, CardA<PERSON>, CardContent, Typography } from "@mui/material";
import { useFormik } from "formik";
import { useNavigate } from "react-router-dom";
import { useSetRecoilState } from "recoil";
import * as yup from "yup";

import { SignInUserWithEmailDto } from "src/common/types";
import { signInState } from "src/common/state";
import { useTranslation } from "react-i18next";
import { CeCard, CeButton, CeTextField } from "src/common/components";

interface SignInWithEmailFormProps {
  handleSignIn: (attrs: SignInUserWithEmailDto) => void;
  isLoading: boolean;
}
export const SignInWithEmailForm: React.FC<SignInWithEmailFormProps> = ({
  isLoading,
  handleSignIn,
}) => {
  const { t } = useTranslation("common");
  const navigate = useNavigate();
  const setSignInFormValue = useSetRecoilState(signInState);

  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
    },
    validationSchema: yup.object({
      email: yup
        .string()
        .email("Enter a valid email")
        .required("Email is required"),
      password: yup
        .string()
        .min(5, "Invalid password")
        .required("Password is required"),
    }),
    onSubmit: (values) => {
      setSignInFormValue(values);
      handleSignIn(values);
    },
  });

  return (
    <>
      <CeCard sx={{ minWidth: 275, padding: 2 }}>
        <CardContent>
          <img src="/images/ConcretEasy.png" width={250} alt="" />
        </CardContent>
        <CardContent>
          <Typography
            variant="h5"
            textAlign="center"
            sx={{ marginTop: 0, marginBottom: 2 }}
          >
            {t("sign-in")}
          </Typography>
          <Stack
            component="form"
            spacing={2}
            noValidate
            onSubmit={formik.handleSubmit}
          >
            <CeTextField
              fullWidth
              id="email"
              name="email"
              label={t("Email")}
              type="email"
              size="small"
              value={formik.values.email}
              onChange={formik.handleChange}
              error={formik.touched.email && Boolean(formik.errors.email)}
              helperText={formik.touched.email && formik.errors.email}
              disabled={isLoading}
              required
            />
            <CeTextField
              fullWidth
              id="password"
              name="password"
              label={t("password")}
              type="password"
              size="small"
              value={formik.values.password}
              onChange={formik.handleChange}
              error={formik.touched.password && Boolean(formik.errors.password)}
              helperText={formik.touched.password && formik.errors.password}
              disabled={isLoading}
              required
            />
            <CeButton
              color="primary"
              variant="contained"
              fullWidth
              type="submit"
              disabled={isLoading}
            >
              {t("sign-in")}
            </CeButton>
          </Stack>
        </CardContent>
        <CardActions sx={{ justifyContent: "space-between", paddingX: 2 }}>
          <CeButton
            variant="text"
            onClick={() => navigate("/auth/password/forgot")}
            size="small"
          >
            {t("forgot-password")}
          </CeButton>
        </CardActions>
      </CeCard>
    </>
  );
};
