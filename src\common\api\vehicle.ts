import { useMutation, useQuery, useQueryClient } from "react-query";
import { processApiError, processApiSuccess } from "../utils/errors";
import axios, { AxiosError } from "axios";
import {
  CheckVehicleAvailabilityDto,
  CreateVehicleDto,
  DeleteVehicleDto,
  GetSearchedVehicleDto,
  VehicleReservationFilterDto,
  UpdateVehicleDto,
  Vehicle,
  VehicleReservationsResponse,
  VehiclesWithCount,
  getVehiclesDto,
} from "../types";

const backendUrl = process.env.REACT_APP_API_URL;

// GET ONE Vehicle
export const getVehicle = async (vehicleId: number | null) => {
  if (!vehicleId) {
    throw new Error("the vehicleId ID was not provided");
  }
  return axios
    .get(`${backendUrl}/vehicle/${vehicleId}`, { withCredentials: true })
    .then((response) => response.data);
};
export const useVehicle = (
  vehicleId: number | null,
  enabled: boolean = true
) => {
  return useQuery<Vehicle, AxiosError | Error>(
    ["vehicle", vehicleId],
    () => getVehicle(vehicleId),
    {
      onError: (err) => processApiError("Unable to fetch vehicle", err),
      enabled
    }
  );
};

// GET MANY Vehicles
export const getVehicles = async (attr: getVehiclesDto) => {
  return axios
    .get(`${backendUrl}/vehicle`, {
      withCredentials: true,
      params: attr
    })
    .then((response) => response.data);
};
export const useVehicles = (attrs: getVehiclesDto, enabled: boolean = true) => {
  const { siteAddress, ...params } = attrs;
  // remove site address for now until the google api is ready
  return useQuery<VehiclesWithCount, AxiosError | Error>(
    ["vehicles", params],
    () => getVehicles(params),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch vehicles", err),
      enabled
    }
  );
};


// GET MANY searched Vehicles
export const getSearchedVehicles = async (attr: GetSearchedVehicleDto) => {
  return axios
    .get(`${backendUrl}/vehicle/search`, {
      withCredentials: true,
      params: attr,
    })
    .then((response) => response.data);
};
export const useSearchedVehicles = (
  attrs: GetSearchedVehicleDto,
  enabled: boolean = true
) => {
  // remove site address for now until the google api is ready
  return useQuery<VehiclesWithCount, AxiosError | Error>(
    ["searchedVehicles", attrs],
    () => getSearchedVehicles(attrs),
    {
      keepPreviousData: true,
      onError: (err) =>
        processApiError("Unable to fetch searched vehicles", err),
      enabled,
    }
  );
};

// CREATE NEW Vehicle
export const createNewVehicle = (attrs: CreateVehicleDto) => {
  return axios
    .post(`${backendUrl}/vehicle/`, attrs, { withCredentials: true })
    .then((response) => response.data);
};

export const useCreateNewVehicle = () => {
  const queryClient = useQueryClient();
  return useMutation<Vehicle, AxiosError | Error, CreateVehicleDto, () => void>(
    (a: CreateVehicleDto) => createNewVehicle(a),
    {
      onSuccess: (newVehicle) => {
        queryClient.invalidateQueries("vehicle");
        queryClient.invalidateQueries("vehicles");
      },
      onError: (err) => processApiError("Unable to create vehicle", err)
    }
  );
};

// CREATE NEW Vehicle
export const checkVehicleSlotAvailability = (
  attrs: CheckVehicleAvailabilityDto
) => {
  return axios
    .post(`${backendUrl}/vehicle/checkVehicleSlotAvailability`, attrs, {
      withCredentials: true
    })
    .then((response) => response.data);
};

export const useCheckVehicleSlotAvailability = () => {
  const queryClient = useQueryClient();
  return useMutation<
    Boolean,
    AxiosError | Error,
    CheckVehicleAvailabilityDto,
    () => void
  >((a: CheckVehicleAvailabilityDto) => checkVehicleSlotAvailability(a), {
    onSuccess: (response) => {
      queryClient.invalidateQueries("vehicle");
      queryClient.invalidateQueries("vehicles");
      queryClient.invalidateQueries("reservation");
      queryClient.invalidateQueries("reservations");
    },
    onError: (err) =>
      processApiError("Unable to check vehicle availability", err)
  });
};

// UPDATE VEHICLE BY ID
export const handleUpdateVehicle = (updateVehicleArgs: UpdateVehicleDto) => {
  const { vehicleId, ...vehicle } = updateVehicleArgs;
  if (!vehicleId) {
    throw new Error("the vehicle ID was not provided");
  }
  return axios
    .patch(`${backendUrl}/vehicle/${vehicleId}`, vehicle, {
      withCredentials: true
    })
    .then((response) => response.data);
};

export const useUpdateVehicle = () => {
  const queryClient = useQueryClient();
  return useMutation<Vehicle, AxiosError | Error, UpdateVehicleDto, () => void>(
    (updateVehicleArgs: UpdateVehicleDto) =>
      handleUpdateVehicle(updateVehicleArgs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("vehicle");
        queryClient.invalidateQueries("vehicles");
      },
      onError: (err) => {
        processApiError("Unable to update vehicle", err);
      }
    }
  );
};

// DELETE VEHICLE BY ID
export const deleteVehicle = (deleteVehicleDto: DeleteVehicleDto) => {
  const { vehicleId } = deleteVehicleDto;
  if (!vehicleId) {
    throw new Error("the vehicle ID was not provided");
  }
  return axios
    .delete(`${backendUrl}/vehicle/${vehicleId}`, { withCredentials: true })
    .then((response) => response.data);
};

export const useDeleteVehicle = () => {
  const queryClient = useQueryClient();
  return useMutation<Vehicle, AxiosError | Error, DeleteVehicleDto, () => void>(
    (deleteVehicleDto: DeleteVehicleDto) => deleteVehicle(deleteVehicleDto),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("vehicle");
        queryClient.invalidateQueries("vehicles");
      },
      onError: (err) => processApiError("Unable to delete vehicle", err)
    }
  );
};

export const getVehiclesWithReservations = async (attr: VehicleReservationFilterDto) => {
  return axios
    .get(`${backendUrl}/vehicle/many-with-reservations`, {
      withCredentials: true,
      params: attr,
    })
    .then((response) => response.data);
};

export const useVehiclesWithReservations = (
  attrs: VehicleReservationFilterDto,
  enabled: boolean = true
) => {
  return useQuery<VehicleReservationsResponse, AxiosError | Error>(
    ["vehiclesWithReservations", attrs],
    () => getVehiclesWithReservations(attrs),
    {
      keepPreviousData: true,
      onError: (err) =>
        processApiError("Unable to fetch vehicle with reservations", err),
      enabled,
    }
  );
};
