import { atom } from "recoil";
import {
  CancelReservationModalValues,
  DeleteReservationModalValues,
  EditReservationDurationModalValues,
  ReservationFormValues,
  ReservationFullFormValues
} from "../types";

import {
  RESERVATION_CANCEL_DEFAULT,
  RESERVATION_DELETE_DEFAULT,
  RESERVATION_EDIT_DURATION_DEFAULT,
  RESERVATION_FORM_VALUES,
  RESERVATION_FULL_FORM_VALUES
} from "../constants";

export const reservationFormValuesState = atom<ReservationFormValues>({
  key: "reservationFormValuesState",
  default: RESERVATION_FORM_VALUES
});

export const reservationFullFormValuesState = atom<ReservationFullFormValues>({
  key: "reservationFullFormValuesState",
  default: RESERVATION_FULL_FORM_VALUES
});

export const reservationEditValuesState = atom<ReservationFormValues>({
  key: "reservationEditValuesState",
  default: RESERVATION_FORM_VALUES
});

export const reservationCancelValuesState = atom<CancelReservationModalValues>({
  key: "reservationCancelValuesState",
  default: RESERVATION_CANCEL_DEFAULT
});

export const reservationDeleteValuesState = atom<DeleteReservationModalValues>({
  key: "reservationDeleteValuesState",
  default: RESERVATION_DELETE_DEFAULT
});

export const reservationEditDurationValuesState =
  atom<EditReservationDurationModalValues>({
    key: "reservationEditDurationValuesState",
    default: RESERVATION_EDIT_DURATION_DEFAULT
  });
