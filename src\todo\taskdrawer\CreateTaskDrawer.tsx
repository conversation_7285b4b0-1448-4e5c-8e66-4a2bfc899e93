import { Drawer } from "@mui/material";
import {
  CreateTaskCommentDto,
  CreateTaskDto,
  Task,
  TaskFormValues,
  UpdateTaskDto
} from "src/common/types/tasks";
import TaskForm from "./TaskForm";
import {
  CreateTaskLabelDto,
  UpdateTaskLabelDto
} from "src/common/types/labels";

interface CreateTaskDrawerProps {
  isLoading?: boolean;
  initialFormValues: TaskFormValues;
  handleCloseTaskDrawer: () => void;
  handleCreateTask: (taskData: CreateTaskDto) => Promise<Task>;
  handleDeleteTask: (taskId: number) => void;
  handleCreateComment: (commentData: CreateTaskCommentDto) => void;
  handleCreateLabel: (labelData: CreateTaskLabelDto) => void;
  handleUpdateLabel: (labelData: UpdateTaskLabelDto) => void;
  handleUpdateTask: (taskToUpdate: UpdateTaskDto) => void;
}
const CreateTaskDrawer = ({
  initialFormValues,
  handleCreateTask,
  handleUpdateTask,
  handleCloseTaskDrawer,
  handleCreateComment,
  handleDeleteTask,
  handleCreateLabel,
  handleUpdateLabel
}: CreateTaskDrawerProps) => {
  return (
    <Drawer
      anchor="right"
      open={!!initialFormValues.flow}
      onClose={handleCloseTaskDrawer}
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 2,
        "& .MuiBackdrop-root": {
          backgroundColor: "transparent"
        }
      }}
    >
      <TaskForm
        handleCreateTask={handleCreateTask}
        handleCreateComment={handleCreateComment}
        initialFormValues={initialFormValues}
        closeTaskDrawer={handleCloseTaskDrawer}
        handleUpdateTask={handleUpdateTask}
        handleDeleteTask={handleDeleteTask}
        handleCreateLabel={handleCreateLabel}
        handleUpdateLabel={handleUpdateLabel}
      />
    </Drawer>
  );
};

export default CreateTaskDrawer;
