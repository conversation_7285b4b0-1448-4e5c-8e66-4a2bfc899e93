import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  InputAdornment,
  Radio,
  RadioGroup,
  Stack,
  Typography,
} from "@mui/material";
import { FormikErrors, FormikProvider, useFormik } from "formik";
import * as yup from "yup";
import { PumpTier, TransportRate } from "src/common/types/priceList";
import { useTranslation } from "react-i18next";
import {
  getInitialPumpFeeOption,
  getPackageFlatHelperText,
  getTierHelperText,
  updateCurrentMaxAndNextMin,
  updateCurrentMinAndPrevMax,
} from "src/common/utils/priceList";
import { Add01Icon, Delete01Icon } from "@hugeicons/react";
import { CeButton, CeTextField } from "src/common/components";
import React, { useState } from "react";
import {
  ContractFormValues,
  CreateContractDto,
  UnavailablePeriod,
  Vehicle,
} from "src/common/types";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import enGb from "date-fns/locale/en-GB";
import isBefore from "date-fns/isBefore";
import { Partner } from "src/common/types/partners";
import {
  checkVehicleAvailability,
  turnContractFormValuesIntoCreateDto,
} from "src/common/utils";
import { useUnavailablePeriods } from "src/common/api";
import toast from "react-hot-toast";

interface ContractFormProps {
  title?: string;
  isLoading: boolean;
  handleClose: () => void;
  handleCreateContract: (args: CreateContractDto) => void;
  initialFormValues: ContractFormValues;
  vehicles: Vehicle[];
  partners: Partner[];
}

export const CreateContractForm = ({
  title,
  handleClose,
  isLoading,
  handleCreateContract,
  initialFormValues,
  vehicles,
  partners,
}: ContractFormProps) => {
  const { t } = useTranslation(["dispatcher", "common"]);
  const [vehicle, setVehicle] = useState<Vehicle | null>(null);
  const initialPumpFeeOptions = getInitialPumpFeeOption(
    initialFormValues.pricelist.flow,
    initialFormValues.pricelist
  );

  const { data: unavailablePeriod } = useUnavailablePeriods(
    {
      limit: 1000,
      offset: 0,
      expressions: [
        {
          operator: "=",
          value: vehicle?.id,
          category: `"vehicles"."id"`,
          conditionType: "AND",
        },
      ],
      sortModel: [],
      relations: ["vehicles"],
    },
    Boolean(vehicle?.id)
  );
  const unavailableVehiclePeriods = unavailablePeriod?.data || [];

  const formik = useFormik<ContractFormValues>({
    initialValues: {
      ...initialFormValues,
      pricelist: {
        ...initialFormValues.pricelist,
        pumpingFeeOptions: initialPumpFeeOptions,
      },
    },
    enableReinitialize: true,
    validationSchema: yup.object({
      partner: yup.object().required("Partner is required").nullable(),
      vehicle: yup.object().nullable().required("Vehicle is required"),
      startDate: yup
        .date()
        .required("required")
        .typeError("Start date is required")
        .test(
          "isDateFromInPast",
          `${t("common:cannot-select-past-date-from")}`,
          function (value) {
            if (!value) {
              return false;
            }
            const currentTime = new Date();
            const fiveMinutesAgo = new Date(
              currentTime.getTime() - 5 * 60 * 1000
            );
            return !isBefore(value, fiveMinutesAgo);
          }
        )
        .test("endDate", `${t("common:date-from-error")}`, function (value) {
          if (!value) {
            return false;
          }
          const { endDate } = this.parent;
          return value < endDate;
        }),
      endDate: yup
        .date()
        .required("required")
        .typeError("End date is required")
        .test(
          "isDateToInPast",
          `${t("common:cannot-select-past-date-to")}`,
          function (value) {
            if (!value) {
              return false;
            }
            const currentTime = new Date();
            const fiveMinutesAgo = new Date(
              currentTime.getTime() - 5 * 60 * 1000
            );
            return !isBefore(value, fiveMinutesAgo);
          }
        )
        .test("endDate", `${t("common:date-to-error")}`, function (value) {
          if (!value) {
            return false;
          }
          const { startDate } = this.parent;
          return value > startDate;
        }),
      pricelist: yup.object({
        dayContractFee: yup
          .number()
          .required("Day Contract Fee is required")
          .nullable(),

        dayContractDuration: yup
          .number()
          .required("Day Contract Duration is required")
          .nullable(),

        dayContractOvertimeRate: yup
          .number()
          .required("Day Contract overtime rate is required")
          .nullable(),

        secondTechnicianHourFee: yup
          .number()
          .required("Second Technician Fee is required")
          .nullable(),

        pricePerMeterOfFlexiblePipeLength80Mm: yup
          .number()
          .required("Pipe length 80 is required")
          .nullable(),

        pricePerMeterOfFlexiblePipeLength90Mm: yup
          .number()
          .required("Pipe length 90 is required")
          .nullable(),

        pricePerMeterOfFlexiblePipeLength100Mm: yup
          .number()
          .required("Pipe length 100 is required")
          .nullable(),

        pricePerMeterOfRigidPipeLength120Mm: yup
          .number()
          .required("Pipe length 120 is required")
          .nullable(),

        pipeInvoicingStartsFrom: yup
          .number()
          .required("Pipe Invoicing Starts From is required")
          .nullable(),

        supplyOfTheChemicalSlushie: yup
          .number()
          .required("Supply of the chemical slushie required")
          .nullable(),

        barbotine: yup.number().required("barbotine required").nullable(),

        extraCementBagPrice: yup
          .number()
          .required("Extra cement bag price is required")
          .nullable(),

        transportRates: yup.array().of(
          yup.object({
            tariff: yup.number().required("Tariff is required").nullable(),
            from: yup.number().nullable().required("From value is required"),
            to: yup
              .number()
              .nullable()
              .required("To value is required")
              .moreThan(
                yup.ref("from"),
                "To value must be greater than From value"
              ),
          })
        ),

        pumpingFeeOptions: yup.object({
          tierPricing: yup.boolean(),
          pricePerMeter: yup.boolean(),
        }),

        pricePerMeterPumped: yup
          .number()
          .when("pumpingFeeOptions.pricePerMeter", {
            is: true,
            then: (schema) =>
              schema.nullable().required("Price Per Meter Pumped is required"),
            otherwise: (schema) => schema.nullable(),
          }),

        pumpTiers: yup.array().when("pumpingFeeOptions.tierPricing", {
          is: true,
          then: (schema) =>
            schema.of(
              yup.object({
                price: yup.number().nullable().required("Price is required"),
                minimum: yup
                  .number()
                  .nullable()
                  .required("Minimum value is required"),
                maximum: yup
                  .number()
                  .nullable()
                  .required("Maximum value is required")
                  .moreThan(
                    yup.ref("minimum"),
                    "Maximum must be greater than minimum"
                  ),
              })
            ),
          otherwise: (schema) =>
            schema.of(
              yup.object({
                price: yup.number().nullable(),
                minimum: yup.number().nullable(),
                maximum: yup.number().nullable(),
              })
            ),
        }),
        vehicleId: yup.number().nullable().required("Vehicle is required"),
        cancellationFee: yup
          .string()
          .nullable()
          .when("useCancellationFee", {
            is: true,
            then: (s) => s.required("Cancellation fee is required"),
            otherwise: (s) => s.notRequired(),
          }),

        contractCancellationPeriod: yup
          .string()
          .nullable()
          .when("useCancellationFee", {
            is: true,
            then: (s) => s.required("Cancellation period is required"),
            otherwise: (s) => s.notRequired(),
          }),

        dayCancellationFee: yup
          .string()
          .nullable()
          .when("useLateCancellationFee", {
            is: true,
            then: (s) => s.required("Day cancellation fee is required"),
            otherwise: (s) => s.notRequired(),
          }),

        contractLateCancellationPeriod: yup
          .string()
          .nullable()
          .when("useLateCancellationFee", {
            is: true,
            then: (s) => s.required("Late cancellation period is required"),
            otherwise: (s) => s.notRequired(),
          }),
        secondTechnicianHourFeeWeekend: yup.number().nullable(),
        secondTechnicianHourFeeNight: yup.number().nullable(),
      }),
    }),
    onSubmit: (values) => {
      const payload = turnContractFormValuesIntoCreateDto(values);

      const isVehicleAvailable = checkVehicleAvailability(
        payload.startDate,
        payload.endDate,
        unavailableVehiclePeriods
      );
      if (!isVehicleAvailable) {
        toast.error(t("common:vehicleUnavailable"));
        return;
      }
      handleCreateContract(payload);
    },
  });
  const pricelist = formik.values.pricelist;
  const handleAddTransportRate = () => {
    formik.setFieldValue("pricelist.transportRates", [
      ...pricelist.transportRates,
      {
        name: "",
        tariff: null,
        from:
          (pricelist.transportRates[pricelist.transportRates.length - 1]?.to ??
            0) + 1,
        to: null,
      },
    ]);
  };

  const handleRemoveTransportRate = (index: number) => {
    const updatedRates = pricelist.transportRates.filter((_, i) => i !== index);
    formik.setFieldValue("pricelist.transportRates", updatedRates);
  };

  const handleAddTier = () => {
    formik.setFieldValue("pricelist.pumpTiers", [
      ...pricelist.pumpTiers,
      {
        name: "",
        price: null,
        minimum:
          (pricelist.pumpTiers[pricelist.pumpTiers.length - 1]?.maximum ?? 0) +
          1,
        maximum: null,
      },
    ]);
  };

  const handleRemoveTier = (index: number) => {
    const updatedTiers = pricelist.pumpTiers.filter((_, i) => i !== index);
    formik.setFieldValue("pricelist.pumpTiers", updatedTiers);
  };

  const handlePostValidation = (
    fieldName: string,
    value: string,
    onBlur: boolean = false
  ) => {
    const isValid = /^(2[0-3]|[01]\d):([0-5]\d)(:[0-5]\d)?$/.test(value);

    if (onBlur) {
      formik.setFieldValue(fieldName, isValid ? value : "");
    } else {
      formik.setFieldValue(fieldName, value);
    }
  };

  return (
    <FormikProvider value={formik}>
      <Stack noValidate component="form" onSubmit={formik.handleSubmit}>
        <Box
          sx={{
            width: "100%",
            height: "70px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            position: "sticky",
            top: 0,
            zIndex: 10,
            borderBottom: "1px solid",
            borderColor: (theme) => theme.palette.divider,
            backgroundColor: "inherit",
          }}
        >
          <Typography variant="h6" letterSpacing={0.15} fontSize={20}>
            Add Contracted Vehicle
          </Typography>
        </Box>

        <Stack
          spacing={2}
          sx={{
            height: "calc(100vh - 140px)",
            overflowY: "scroll",
            width: "35vw",
          }}
        >
          <Stack sx={{ mt: 2, px: 4.5, gap: 3 }}>
            <Autocomplete
              id="vehicle"
              fullWidth
              value={
                vehicles.find((v) => v.id === formik.values.vehicle?.id) || null
              }
              onChange={(event: any, newValue: Vehicle | null) => {
                formik.setFieldValue("vehicle", newValue ? newValue : null);
                formik.setFieldValue("pricelist.vehicleId", newValue?.id);
                formik.setFieldValue(
                  "pricelist.title",
                  newValue?.uniqueIdentificationNumber
                );
                setVehicle(newValue);
              }}
              onBlur={() => formik.setFieldTouched("vehicle", true)}
              isOptionEqualToValue={(option: Vehicle, value: Vehicle) =>
                option.id === value.id
              }
              getOptionLabel={(vehicle: Vehicle) =>
                `${vehicle.uniqueIdentificationNumber}` || ""
              }
              options={vehicles}
              renderInput={(params) => (
                <CeTextField
                  {...params}
                  InputLabelProps={{ shrink: true }}
                  label={t("common:vehicles")}
                  size="small"
                  error={
                    formik.touched.vehicle && Boolean(formik.errors.vehicle)
                  }
                  helperText={formik.touched.vehicle && formik.errors.vehicle}
                />
              )}
            />
            <Autocomplete
              id="partner"
              fullWidth
              value={
                partners.find((p) => p.id === formik.values.partner?.id) || null
              }
              onChange={(event: any, newValue: Partner | null) => {
                formik.setFieldValue("partner", newValue ? newValue : null);
              }}
              onBlur={() => formik.setFieldTouched("partner", true)}
              isOptionEqualToValue={(option: Partner, value: Partner) =>
                option.dispatcherCompanyId === value.dispatcherCompanyId
              }
              getOptionLabel={(partner: Partner) =>
                `${partner.dispatcherCompany.name}` || ""
              }
              options={partners}
              renderInput={(params) => (
                <CeTextField
                  {...params}
                  InputLabelProps={{ shrink: true }}
                  label={t("common:partners")}
                  size="small"
                  error={
                    formik.touched.partner && Boolean(formik.errors.partner)
                  }
                  helperText={formik.touched.partner && formik.errors.partner}
                />
              )}
            />
            <Stack direction="row" spacing={2}>
              <LocalizationProvider
                dateAdapter={AdapterDateFns}
                adapterLocale={enGb}
              >
                <DatePicker
                  renderInput={(props: any) => (
                    <CeTextField
                      {...props}
                      size="small"
                      fullWidth
                      onBlur={() => formik.setFieldTouched("startDate", true)}
                      error={
                        !!formik.errors.startDate && formik.touched.startDate
                      }
                      helperText={
                        formik.touched.startDate && formik.errors.startDate
                      }
                      required
                    />
                  )}
                  label={t("date-from")}
                  value={formik.values.startDate}
                  onChange={(newValue) => {
                    formik.setFieldValue("startDate", newValue);
                  }}
                />
              </LocalizationProvider>
              <Divider orientation="vertical" flexItem variant="fullWidth" />
              <LocalizationProvider
                dateAdapter={AdapterDateFns}
                adapterLocale={enGb}
              >
                <DatePicker
                  renderInput={(props: any) => (
                    <CeTextField
                      {...props}
                      fullWidth
                      size="small"
                      error={!!formik.errors.endDate && formik.touched.endDate}
                      helperText={
                        formik.touched.endDate && formik.errors.endDate
                      }
                      onBlur={() => formik.setFieldTouched("endDate", true)}
                      required
                    />
                  )}
                  label={t("date-to")}
                  value={formik.values.endDate}
                  onChange={(newValue) => {
                    formik.setFieldValue("endDate", newValue);
                  }}
                />
              </LocalizationProvider>
            </Stack>
          </Stack>

          <Stack id="packageAndHourlyFees" sx={{ px: 1.5, py: 2, gap: 3 }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                Package & Hourly fees
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>

            <Box sx={{ px: 3 }}>
              <Box sx={{ display: "flex", gap: 2, height: "63px", mb: 2 }}>
                <CeTextField
                  fullWidth
                  id="dayContractFee"
                  name="pricelist.dayContractFee"
                  label={t("common:day-contract-fee")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={pricelist.dayContractFee || ""}
                  onChange={(event) => {
                    const value = event.target.value;
                    formik.setFieldValue("pricelist.dayContractFee", value);

                    if (formik.values.pricelist.standardFeeAsDayContractFee) {
                      formik.setFieldValue(
                        "pricelist.cancellationFee",
                        Number(value)
                      );
                    }
                    if (formik.values.pricelist.lateFeeAsDayContractFee) {
                      formik.setFieldValue(
                        "pricelist.dayCancellationFee",
                        Number(value)
                      );
                    }
                  }}
                  error={
                    formik.touched.pricelist?.dayContractFee &&
                    Boolean(formik.errors.pricelist?.dayContractFee)
                  }
                  helperText={
                    (formik.touched.pricelist?.dayContractFee &&
                      formik.errors.pricelist?.dayContractFee) ||
                    getPackageFlatHelperText(
                      pricelist.dayContractDuration || null,
                      true
                    )
                  }
                  disabled={isLoading}
                />
                <Divider orientation="vertical" flexItem variant="fullWidth" />
                <CeTextField
                  fullWidth
                  id="dayContractDuration"
                  name="pricelist.dayContractDuration"
                  label={t("common:day-contract-duration")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">h</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={pricelist?.dayContractDuration || ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.pricelist?.dayContractDuration &&
                    Boolean(formik.errors.pricelist?.dayContractDuration)
                  }
                  helperText={
                    formik.touched.pricelist?.dayContractDuration &&
                    formik.errors.pricelist?.dayContractDuration
                  }
                  disabled={isLoading}
                />
              </Box>
              <Box height={64}>
                <CeTextField
                  fullWidth
                  id="dayContractOvertimeRate"
                  name="pricelist.dayContractOvertimeRate"
                  label={t("common:day-contract-overtime-rate")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€/h</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={pricelist.dayContractOvertimeRate || ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.pricelist?.dayContractOvertimeRate &&
                    Boolean(formik.errors.pricelist?.dayContractOvertimeRate)
                  }
                  helperText={
                    (formik.touched.pricelist?.dayContractOvertimeRate &&
                      formik.errors.pricelist?.dayContractOvertimeRate) ||
                    getPackageFlatHelperText(
                      pricelist.dayContractDuration || null,
                      false
                    )
                  }
                  disabled={isLoading}
                />
              </Box>
            </Box>
          </Stack>

          <Stack id="pumpingFees" sx={{ px: 1.5, py: 2, gap: 3 }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                Pumping fees
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>

            {/* radio buttons */}
            <Stack sx={{ px: 3, gap: 3 }}>
              <FormControl sx={{ width: "100%" }}>
                <RadioGroup
                  sx={{ width: "100%" }}
                  row
                  name="pricelist.pumpingFeeOptions" // ← prefixed
                  value={
                    pricelist.pumpingFeeOptions?.pricePerMeter
                      ? "pricePerMeter"
                      : "tierPricing"
                  }
                  onChange={(event) => {
                    const selectedValue = event.target.value;
                    formik.setFieldValue(
                      "pricelist.pumpingFeeOptions.tierPricing",
                      selectedValue === "tierPricing"
                    );
                    formik.setFieldValue(
                      "pricelist.pumpingFeeOptions.pricePerMeter",
                      selectedValue === "pricePerMeter"
                    );

                    if (selectedValue === "pricePerMeter") {
                      formik.setFieldValue("pricelist.pumpTiers", []);
                      formik.setFieldValue("pricelist.pricePerMeterPumped", "");
                      return;
                    }
                    formik.setFieldValue("pricelist.pumpTiers", [
                      { name: "", price: null, minimum: 0, maximum: null },
                    ]);
                    formik.setFieldValue("pricelist.pricePerMeterPumped", null);
                  }}
                >
                  <FormControlLabel
                    value="pricePerMeter"
                    sx={{ padding: 1.5 }}
                    control={<Radio size="medium" />}
                    label={
                      <Typography
                        variant="body1"
                        lineHeight={1.5}
                        letterSpacing={0.15}
                      >
                        {t("price-per-meter-pumped")}
                      </Typography>
                    }
                  />
                  <FormControlLabel
                    value="tierPricing"
                    control={<Radio />}
                    label={
                      <Typography variant="body1">
                        {t("tier-pricing")}
                      </Typography>
                    }
                  />
                </RadioGroup>
              </FormControl>

              {pricelist.pumpingFeeOptions?.tierPricing ? (
                <Stack gap={2.5} alignItems="flex-start">
                  {pricelist.pumpTiers.map((tier: PumpTier, index: number) => {
                    const tierErrors = formik.errors.pricelist?.pumpTiers?.[
                      index
                    ] as FormikErrors<PumpTier> | undefined;

                    return (
                      <React.Fragment key={index}>
                        <Stack
                          direction="row"
                          spacing={2}
                          alignItems="flex-start"
                        >
                          <Typography
                            sx={{ fontSize: 14 }}
                            fontWeight={700}
                            variant="overline"
                            lineHeight="32.92px"
                            whiteSpace="nowrap"
                          >
                            {`Tier ${index + 1}`}
                          </Typography>

                          {/* price */}
                          <CeTextField
                            sx={{ flex: "50%" }}
                            id={`pricelist.pumpTiers[${index}].price`}
                            name={`pricelist.pumpTiers[${index}].price`}
                            label={`Price for tier ${index + 1}`}
                            size="small"
                            type="number"
                            value={tier.price ?? ""}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            InputLabelProps={{ shrink: true }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  €/h
                                </InputAdornment>
                              ),
                            }}
                            error={
                              formik.touched.pricelist?.pumpTiers?.[index]
                                ?.price && Boolean(tierErrors?.price)
                            }
                            helperText={
                              (formik.touched.pricelist?.pumpTiers?.[index]
                                ?.price &&
                                tierErrors?.price) ||
                              getTierHelperText(
                                index,
                                tier.minimum,
                                tier.maximum
                              )
                            }
                            disabled={isLoading}
                          />

                          <Divider
                            orientation="vertical"
                            flexItem
                            variant="fullWidth"
                          />

                          <CeTextField
                            sx={{ flex: "25%" }}
                            id={`pricelist.pumpTiers[${index}].minimum`}
                            name={`pricelist.pumpTiers[${index}].minimum`}
                            label={t("min-m3")}
                            size="small"
                            type="number"
                            value={
                              index === 0
                                ? tier.minimum ?? 0
                                : tier.minimum ?? ""
                            }
                            onChange={(event) =>
                              updateCurrentMinAndPrevMax(
                                event,
                                `pricelist.pumpTiers[${index}].minimum`,
                                `pricelist.pumpTiers[${index - 1}].maximum`,
                                index,
                                pricelist.pumpTiers,
                                formik.setFieldValue
                              )
                            }
                            onBlur={formik.handleBlur}
                            InputLabelProps={{ shrink: true }}
                            disabled={index === 0}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  m³
                                </InputAdornment>
                              ),
                            }}
                            error={
                              formik.touched.pricelist?.pumpTiers?.[index]
                                ?.minimum && Boolean(tierErrors?.minimum)
                            }
                            helperText={
                              formik.touched.pricelist?.pumpTiers?.[index]
                                ?.minimum && tierErrors?.minimum
                            }
                          />
                          <CeTextField
                            sx={{ flex: "25%" }}
                            id={`pricelist.pumpTiers[${index}].maximum`}
                            name={`pricelist.pumpTiers[${index}].maximum`}
                            label={t("max-m3")}
                            size="small"
                            type="number"
                            value={tier.maximum ?? ""}
                            onChange={(event) =>
                              updateCurrentMaxAndNextMin(
                                event,
                                `pricelist.pumpTiers[${index}].maximum`,
                                `pricelist.pumpTiers[${index + 1}].minimum`,
                                index,
                                pricelist.pumpTiers,
                                formik.setFieldValue
                              )
                            }
                            onBlur={formik.handleBlur}
                            InputLabelProps={{ shrink: true }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  m³
                                </InputAdornment>
                              ),
                            }}
                            error={
                              formik.touched.pricelist?.pumpTiers?.[index]
                                ?.maximum && Boolean(tierErrors?.maximum)
                            }
                            helperText={
                              formik.touched.pricelist?.pumpTiers?.[index]
                                ?.maximum && tierErrors?.maximum
                            }
                          />
                          {pricelist.pumpTiers.length > 1 && (
                            <Button
                              type="button"
                              onClick={() => handleRemoveTier(index)}
                              color="error"
                              variant="text"
                              size="small"
                              sx={{ minWidth: 34 }}
                              disabled={
                                index !== pricelist.pumpTiers.length - 1
                              }
                            >
                              <Delete01Icon />
                            </Button>
                          )}
                        </Stack>
                      </React.Fragment>
                    );
                  })}
                  <CeButton
                    type="button"
                    startIcon={<Add01Icon />}
                    onClick={handleAddTier}
                    variant="text"
                    size="large"
                  >
                    {t("add-additional-tier")}
                  </CeButton>
                </Stack>
              ) : (
                <Box sx={{ height: 64 }}>
                  <CeTextField
                    fullWidth
                    id="pricelist.pricePerMeterPumped"
                    name="pricelist.pricePerMeterPumped"
                    label={t("price-per-meter-pumped")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">€/m3</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={pricelist.pricePerMeterPumped ?? ""}
                    onChange={formik.handleChange}
                    error={
                      formik.touched.pricelist?.pricePerMeterPumped &&
                      Boolean(formik.errors.pricelist?.pricePerMeterPumped)
                    }
                    helperText={
                      formik.touched.pricelist?.pricePerMeterPumped &&
                      formik.errors.pricelist?.pricePerMeterPumped
                    }
                    disabled={isLoading}
                  />
                </Box>
              )}
            </Stack>
          </Stack>

          <Stack id="pipes" sx={{ px: 1.5, py: 2, gap: 3 }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                Pipes
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>

            <Box sx={{ px: 3, pb: 2 }}>
              <Stack direction="column" gap={3}>
                <Box sx={{ display: "flex", gap: "12px" }}>
                  <CeTextField
                    sx={{ flex: "50%" }}
                    id="pricelist.pricePerMeterOfFlexiblePipeLength80Mm"
                    name="pricelist.pricePerMeterOfFlexiblePipeLength80Mm"
                    label={t("price-per-meter-of-flexible-pipe-length-80Mm")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">€/m</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={
                      pricelist.pricePerMeterOfFlexiblePipeLength80Mm ?? ""
                    }
                    onChange={formik.handleChange}
                    error={
                      formik.touched.pricelist
                        ?.pricePerMeterOfFlexiblePipeLength80Mm &&
                      Boolean(
                        formik.errors.pricelist
                          ?.pricePerMeterOfFlexiblePipeLength80Mm
                      )
                    }
                    helperText={
                      formik.touched.pricelist
                        ?.pricePerMeterOfFlexiblePipeLength80Mm &&
                      formik.errors.pricelist
                        ?.pricePerMeterOfFlexiblePipeLength80Mm
                    }
                    disabled={isLoading}
                  />
                  <CeTextField
                    sx={{ flex: "50%" }}
                    id="pricelist.pricePerMeterOfFlexiblePipeLength90Mm"
                    name="pricelist.pricePerMeterOfFlexiblePipeLength90Mm"
                    label={t("price-per-meter-of-flexible-pipe-length-90Mm")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">€/m</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={
                      pricelist.pricePerMeterOfFlexiblePipeLength90Mm ?? ""
                    }
                    onChange={formik.handleChange}
                    error={
                      formik.touched.pricelist
                        ?.pricePerMeterOfFlexiblePipeLength90Mm &&
                      Boolean(
                        formik.errors.pricelist
                          ?.pricePerMeterOfFlexiblePipeLength90Mm
                      )
                    }
                    helperText={
                      formik.touched.pricelist
                        ?.pricePerMeterOfFlexiblePipeLength90Mm &&
                      formik.errors.pricelist
                        ?.pricePerMeterOfFlexiblePipeLength90Mm
                    }
                    disabled={isLoading}
                  />
                </Box>

                <Box sx={{ display: "flex", gap: "12px" }}>
                  <CeTextField
                    sx={{ flex: "50%" }}
                    id="pricelist.pricePerMeterOfFlexiblePipeLength100Mm"
                    name="pricelist.pricePerMeterOfFlexiblePipeLength100Mm"
                    label={t("price-per-meter-of-flexible-pipe-length-100Mm")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">€/m</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={
                      pricelist.pricePerMeterOfFlexiblePipeLength100Mm ?? ""
                    }
                    onChange={formik.handleChange}
                    error={
                      formik.touched.pricelist
                        ?.pricePerMeterOfFlexiblePipeLength100Mm &&
                      Boolean(
                        formik.errors.pricelist
                          ?.pricePerMeterOfFlexiblePipeLength100Mm
                      )
                    }
                    helperText={
                      formik.touched.pricelist
                        ?.pricePerMeterOfFlexiblePipeLength100Mm &&
                      formik.errors.pricelist
                        ?.pricePerMeterOfFlexiblePipeLength100Mm
                    }
                    disabled={isLoading}
                  />
                  <CeTextField
                    sx={{ flex: "50%" }}
                    id="pricelist.pricePerMeterOfRigidPipeLength120Mm"
                    name="pricelist.pricePerMeterOfRigidPipeLength120Mm"
                    label={t("price-per-meter-of-rigid-pipe-length-120Mm")}
                    size="small"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">€/m</InputAdornment>
                      ),
                    }}
                    InputLabelProps={{ shrink: true }}
                    value={pricelist.pricePerMeterOfRigidPipeLength120Mm ?? ""}
                    onChange={formik.handleChange}
                    error={
                      formik.touched.pricelist
                        ?.pricePerMeterOfRigidPipeLength120Mm &&
                      Boolean(
                        formik.errors.pricelist
                          ?.pricePerMeterOfRigidPipeLength120Mm
                      )
                    }
                    helperText={
                      formik.touched.pricelist
                        ?.pricePerMeterOfRigidPipeLength120Mm &&
                      formik.errors.pricelist
                        ?.pricePerMeterOfRigidPipeLength120Mm
                    }
                    disabled={isLoading}
                  />
                </Box>

                <CeTextField
                  fullWidth
                  id="pricelist.pipeInvoicingStartsFrom"
                  name="pricelist.pipeInvoicingStartsFrom"
                  label={t("pipeInvoicingStartsFrom")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">m</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={pricelist.pipeInvoicingStartsFrom ?? ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.pricelist?.pipeInvoicingStartsFrom &&
                    Boolean(formik.errors.pricelist?.pipeInvoicingStartsFrom)
                  }
                  helperText={
                    formik.touched.pricelist?.pipeInvoicingStartsFrom &&
                    formik.errors.pricelist?.pipeInvoicingStartsFrom
                  }
                  disabled={isLoading}
                />
              </Stack>
            </Box>
          </Stack>

          <Stack id="additionalServices" sx={{ px: 1.5, py: 2, gap: 3 }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                Additional Services & Products
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>

            <Box sx={{ px: 3 }}>
              <Stack direction="column" gap={3}>
                <CeTextField
                  fullWidth
                  id="pricelist.extraCementBagPrice"
                  name="pricelist.extraCementBagPrice"
                  label={t("extra-cement-bag-price")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€/bag</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={pricelist.extraCementBagPrice ?? ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.pricelist?.extraCementBagPrice &&
                    Boolean(formik.errors.pricelist?.extraCementBagPrice)
                  }
                  helperText={
                    formik.touched.pricelist?.extraCementBagPrice &&
                    formik.errors.pricelist?.extraCementBagPrice
                  }
                  disabled={isLoading}
                />

                <CeTextField
                  fullWidth
                  id="pricelist.supplyOfTheChemicalSlushie"
                  name="pricelist.supplyOfTheChemicalSlushie"
                  label={t("supply-of-the-chemical-slushie")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={pricelist.supplyOfTheChemicalSlushie ?? ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.pricelist?.supplyOfTheChemicalSlushie &&
                    Boolean(formik.errors.pricelist?.supplyOfTheChemicalSlushie)
                  }
                  helperText={
                    formik.touched.pricelist?.supplyOfTheChemicalSlushie &&
                    formik.errors.pricelist?.supplyOfTheChemicalSlushie
                  }
                  disabled={isLoading}
                />

                <CeTextField
                  fullWidth
                  id="pricelist.barbotine"
                  name="pricelist.barbotine"
                  label={t("barbotine")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={pricelist.barbotine ?? ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.pricelist?.barbotine &&
                    Boolean(formik.errors.pricelist?.barbotine)
                  }
                  helperText={
                    formik.touched.pricelist?.barbotine &&
                    formik.errors.pricelist?.barbotine
                  }
                  disabled={isLoading}
                />
              </Stack>
            </Box>
          </Stack>

          <Stack id="transportRates" sx={{ px: 1.5, py: 2, gap: 3 }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                Transport rates
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>
            <Box sx={{ px: 3 }}>
              <Stack direction="column" gap={2.5} alignItems="flex-start">
                {pricelist.transportRates.map(
                  (rate: TransportRate, index: number) => {
                    const transportRateErrors = formik.errors.pricelist
                      ?.transportRates?.[index] as
                      | FormikErrors<TransportRate>
                      | undefined;

                    return (
                      <Stack
                        key={index}
                        direction="row"
                        spacing={2}
                        alignItems="flex-start"
                      >
                        <CeTextField
                          sx={{ flex: "50%" }}
                          id={`pricelist.transportRates[${index}].tariff`}
                          name={`pricelist.transportRates[${index}].tariff`}
                          label={`Tariff ${index + 1}`}
                          size="small"
                          type="number"
                          value={rate.tariff ?? ""}
                          onChange={formik.handleChange}
                          InputLabelProps={{ shrink: true }}
                          onBlur={formik.handleBlur}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">€</InputAdornment>
                            ),
                          }}
                          error={
                            formik.touched.pricelist?.transportRates?.[index]
                              ?.tariff && Boolean(transportRateErrors?.tariff)
                          }
                          helperText={
                            formik.touched.pricelist?.transportRates?.[index]
                              ?.tariff && transportRateErrors?.tariff
                          }
                        />
                        <Divider
                          orientation="vertical"
                          flexItem
                          variant="fullWidth"
                        />
                        <CeTextField
                          sx={{ flex: "25%" }}
                          id={`pricelist.transportRates[${index}].from`}
                          name={`pricelist.transportRates[${index}].from`}
                          label={t("date-from")}
                          size="small"
                          type="number"
                          value={index === 0 ? rate.from ?? 0 : rate.from ?? ""}
                          onChange={(event) =>
                            updateCurrentMinAndPrevMax(
                              event,
                              `pricelist.transportRates[${index}].from`,
                              `pricelist.transportRates[${index - 1}].to`,
                              index,
                              pricelist.transportRates,
                              formik.setFieldValue
                            )
                          }
                          onBlur={formik.handleBlur}
                          InputLabelProps={{ shrink: true }}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">km</InputAdornment>
                            ),
                          }}
                          error={
                            formik.touched.pricelist?.transportRates?.[index]
                              ?.from && Boolean(transportRateErrors?.from)
                          }
                          helperText={
                            formik.touched.pricelist?.transportRates?.[index]
                              ?.from && transportRateErrors?.from
                          }
                        />
                        <CeTextField
                          sx={{ flex: "25%" }}
                          id={`pricelist.transportRates[${index}].to`}
                          name={`pricelist.transportRates[${index}].to`}
                          label={t("date-to")}
                          size="small"
                          type="number"
                          value={rate.to ?? ""}
                          onBlur={formik.handleBlur}
                          onChange={(event) =>
                            updateCurrentMaxAndNextMin(
                              event,
                              `pricelist.transportRates[${index}].to`,
                              `pricelist.transportRates[${index + 1}].from`,
                              index,
                              pricelist.transportRates,
                              formik.setFieldValue
                            )
                          }
                          InputLabelProps={{ shrink: true }}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">km</InputAdornment>
                            ),
                          }}
                          error={
                            formik.touched.pricelist?.transportRates?.[index]
                              ?.to && Boolean(transportRateErrors?.to)
                          }
                          helperText={
                            formik.touched.pricelist?.transportRates?.[index]
                              ?.to && transportRateErrors?.to
                          }
                        />
                        {pricelist.transportRates.length > 1 && (
                          <Button
                            type="button"
                            onClick={() => handleRemoveTransportRate(index)}
                            color="error"
                            variant="text"
                            size="small"
                            sx={{ minWidth: 34 }}
                            disabled={
                              index !== pricelist.transportRates.length - 1
                            }
                          >
                            <Delete01Icon />
                          </Button>
                        )}
                      </Stack>
                    );
                  }
                )}

                <CeButton
                  type="button"
                  startIcon={<Add01Icon />}
                  onClick={handleAddTransportRate}
                  variant="text"
                  size="large"
                >
                  {t("add-tariff")}
                </CeButton>
              </Stack>
            </Box>
          </Stack>

          <Stack id="contingencies" sx={{ px: 1.5, pt: 2, gap: 3, pb: 8 }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                Contingencies
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>
            <Box sx={{ px: 3 }}>
              <Stack direction="column" gap={4}>
                <Box>
                  <Stack direction="column" spacing={2}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          name="pricelist.useCancellationFee"
                          checked={formik.values.pricelist.useCancellationFee}
                          onChange={(e) => {
                            const { checked } = e.target;
                            formik.setFieldValue(
                              "pricelist.useCancellationFee",
                              checked
                            );
                            if (!checked) {
                              formik.setFieldValue(
                                "pricelist.cancellationFee",
                                ""
                              );
                              formik.setFieldValue(
                                "pricelist.standardFeeAsDayContractFee",
                                false
                              );
                            }
                          }}
                        />
                      }
                      label={t("standard-cancellation-fee")}
                      sx={{ mb: 1 }}
                    />
                    <Stack
                      direction="row"
                      justifyContent="space-between"
                      gap={2}
                    >
                      <CeTextField
                        fullWidth
                        id="pricelist.cancellationFee"
                        name="pricelist.cancellationFee"
                        size="small"
                        type="number"
                        label={t("standard-cancellation-fee")}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">€</InputAdornment>
                          ),
                        }}
                        InputLabelProps={{ shrink: true }}
                        value={pricelist.cancellationFee ?? ""}
                        onChange={formik.handleChange}
                        error={
                          pricelist.useCancellationFee &&
                          formik.touched.pricelist?.cancellationFee &&
                          Boolean(formik.errors.pricelist?.cancellationFee)
                        }
                        helperText={
                          pricelist.useCancellationFee &&
                          formik.touched.pricelist?.cancellationFee &&
                          formik.errors.pricelist?.cancellationFee
                        }
                        disabled={isLoading || !pricelist.useCancellationFee}
                      />
                      <CeTextField
                        fullWidth
                        id="contractCancellationPeriod"
                        name="pricelist.contractCancellationPeriod"
                        size="small"
                        type="text"
                        value={pricelist.contractCancellationPeriod || ""}
                        onChange={(event) => {
                          handlePostValidation(
                            "pricelist.contractCancellationPeriod",
                            event.target.value
                          );
                          formik.setFieldValue(
                            "pricelist.contractCancellationPeriod",
                            event.target.value
                          );
                        }}
                        onBlur={(event) => {
                          handlePostValidation(
                            "pricelist.contractCancellationPeriod",
                            event.target.value,
                            true
                          );
                        }}
                        InputLabelProps={{ shrink: true }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">H</InputAdornment>
                          ),
                        }}
                        helperText={
                          pricelist.useCancellationFee &&
                          formik.touched.pricelist
                            ?.contractCancellationPeriod &&
                          formik.errors.pricelist?.contractCancellationPeriod
                        }
                        error={
                          pricelist.useCancellationFee &&
                          formik.touched.pricelist
                            ?.contractCancellationPeriod &&
                          Boolean(
                            formik.errors.pricelist?.contractCancellationPeriod
                          )
                        }
                        disabled={isLoading || !pricelist.useCancellationFee}
                        required
                        label={t("standard-cancellation-period")}
                      />
                    </Stack>
                  </Stack>
                  <FormControlLabel
                    sx={{ mt: 1 }}
                    control={
                      <Checkbox
                        size="small"
                        name="pricelist.standardFeeAsDayContractFee"
                        checked={
                          formik.values.pricelist.standardFeeAsDayContractFee
                        }
                        onChange={(e) => {
                          const { checked } = e.target;
                          formik.setFieldValue(
                            "pricelist.standardFeeAsDayContractFee",
                            checked
                          );
                          formik.setFieldValue(
                            "pricelist.cancellationFee",
                            checked ? Number(pricelist.dayContractFee) : ""
                          );
                        }}
                      />
                    }
                    label={t("common:cancel-fee-as-day-contract-fee")}
                    disabled={!formik.values.pricelist.useCancellationFee}
                  />
                </Box>
                <Box>
                  <Stack direction="column" spacing={2}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          name="pricelist.useLateCancellationFee"
                          checked={
                            formik.values.pricelist.useLateCancellationFee
                          }
                          onChange={(e) => {
                            const { checked } = e.target;
                            formik.setFieldValue(
                              "pricelist.useLateCancellationFee",
                              checked
                            );
                            if (!checked) {
                              formik.setFieldValue(
                                "pricelist.dayCancellationFee",
                                ""
                              );
                              formik.setFieldValue(
                                "pricelist.lateFeeAsDayContractFee",
                                false
                              );
                            }
                          }}
                        />
                      }
                      label={t("late-cancellation-fee")}
                      sx={{ mb: 1 }}
                    />
                    <Stack
                      direction="row"
                      justifyContent="space-between"
                      gap={2}
                    >
                      <CeTextField
                        fullWidth
                        id="pricelist.dayCancellationFee"
                        name="pricelist.dayCancellationFee"
                        size="small"
                        type="number"
                        label={t("late-cancellation-fee")}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">€</InputAdornment>
                          ),
                        }}
                        InputLabelProps={{ shrink: true }}
                        value={pricelist.dayCancellationFee ?? ""}
                        onChange={formik.handleChange}
                        error={
                          pricelist.useLateCancellationFee &&
                          formik.touched.pricelist?.dayCancellationFee &&
                          Boolean(formik.errors.pricelist?.dayCancellationFee)
                        }
                        helperText={
                          pricelist.useLateCancellationFee &&
                          formik.touched.pricelist?.dayCancellationFee &&
                          formik.errors.pricelist?.dayCancellationFee
                        }
                        disabled={
                          isLoading || !pricelist.useLateCancellationFee
                        }
                      />
                      <CeTextField
                        fullWidth
                        id="contractLateCancellationPeriod"
                        name="pricelist.contractLateCancellationPeriod"
                        size="small"
                        type="text"
                        InputLabelProps={{ shrink: true }}
                        value={pricelist.contractLateCancellationPeriod || ""}
                        onChange={(event) => {
                          handlePostValidation(
                            "pricelist.contractLateCancellationPeriod",
                            event.target.value
                          );
                          formik.setFieldValue(
                            "pricelist.contractLateCancellationPeriod",
                            event.target.value
                          );
                        }}
                        onBlur={(event) => {
                          handlePostValidation(
                            "pricelist.contractLateCancellationPeriod",
                            event.target.value,
                            true
                          );
                        }}
                        helperText={
                          pricelist.useLateCancellationFee &&
                          formik.touched.pricelist
                            ?.contractLateCancellationPeriod &&
                          formik.errors.pricelist
                            ?.contractLateCancellationPeriod
                        }
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">H</InputAdornment>
                          ),
                        }}
                        error={
                          pricelist.useLateCancellationFee &&
                          formik.touched.pricelist
                            ?.contractLateCancellationPeriod &&
                          Boolean(
                            formik.errors.pricelist
                              ?.contractLateCancellationPeriod
                          )
                        }
                        disabled={
                          isLoading || !pricelist.useLateCancellationFee
                        }
                        required
                        label={t("late-cancellation-period")}
                      />
                    </Stack>
                  </Stack>

                  <FormControlLabel
                    sx={{ mt: 1 }}
                    control={
                      <Checkbox
                        size="small"
                        name="pricelist.lateFeeAsDayContractFee"
                        checked={pricelist.lateFeeAsDayContractFee}
                        onChange={(e) => {
                          const { checked } = e.target;
                          formik.setFieldValue(
                            "pricelist.lateFeeAsDayContractFee",
                            checked
                          );
                          formik.setFieldValue(
                            "pricelist.dayCancellationFee",
                            checked ? Number(pricelist.dayContractFee) : ""
                          );
                        }}
                      />
                    }
                    label={t("common:cancel-fee-as-day-contract-fee")}
                    disabled={!pricelist.useLateCancellationFee}
                  />
                </Box>

                <CeTextField
                  fullWidth
                  id="pricelist.secondTechnicianHourFee"
                  name="pricelist.secondTechnicianHourFee"
                  label={t("secondTechnicianFee")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€/h</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={pricelist.secondTechnicianHourFee ?? ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.pricelist?.secondTechnicianHourFee &&
                    Boolean(formik.errors.pricelist?.secondTechnicianHourFee)
                  }
                  helperText={
                    formik.touched.pricelist?.secondTechnicianHourFee &&
                    formik.errors.pricelist?.secondTechnicianHourFee
                  }
                  disabled={isLoading}
                />

                <CeTextField
                  fullWidth
                  id="pricelist.secondTechnicianHourFeeNight"
                  name="pricelist.secondTechnicianHourFeeNight"
                  label={t("common:second-technician-fee-night")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€/h</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={pricelist.secondTechnicianHourFeeNight ?? ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.pricelist?.secondTechnicianHourFeeNight &&
                    Boolean(
                      formik.errors.pricelist?.secondTechnicianHourFeeNight
                    )
                  }
                  helperText={
                    formik.touched.pricelist?.secondTechnicianHourFeeNight &&
                    formik.errors.pricelist?.secondTechnicianHourFeeNight
                  }
                  disabled={isLoading}
                />

                <CeTextField
                  fullWidth
                  id="pricelist.secondTechnicianHourFeeWeekend"
                  name="pricelist.secondTechnicianHourFeeWeekend"
                  label={t("second-technician-fee-weekend")}
                  size="small"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">€</InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ shrink: true }}
                  value={pricelist.secondTechnicianHourFeeWeekend ?? ""}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.pricelist?.secondTechnicianHourFeeWeekend &&
                    Boolean(
                      formik.errors.pricelist?.secondTechnicianHourFeeWeekend
                    )
                  }
                  helperText={
                    formik.touched.pricelist?.secondTechnicianHourFeeWeekend &&
                    formik.errors.pricelist?.secondTechnicianHourFeeWeekend
                  }
                  disabled={isLoading}
                />
              </Stack>
            </Box>
          </Stack>
        </Stack>
        <Box
          sx={{
            width: "100%",
            height: "70px",
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-end",
            position: "sticky",
            bottom: 0,
            pr: 4,
            zIndex: 10,
            gap: 2,
            borderTop: "1px solid",
            borderColor: (theme) => theme.palette.divider,
            backgroundColor: "inherit",
          }}
        >
          <CeButton
            variant="text"
            size="large"
            onClick={handleClose}
            disabled={isLoading}
          >
            {t("common:cancel")}
          </CeButton>
          <CeButton type="submit" size="large" disabled={isLoading}>
            {t("common:submit")}
          </CeButton>
        </Box>
      </Stack>
    </FormikProvider>
  );
};
