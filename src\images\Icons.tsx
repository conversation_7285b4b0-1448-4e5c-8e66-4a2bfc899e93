import { ReactComponent as Calendar } from "./Calendar.svg";
import { ReactComponent as CalendarIcon } from "./Calendar edit.svg";
import { ReactComponent as VehicleOutlined } from "./Vehicle-Outlined.svg";
import { ReactComponent as Delete } from "./Delete.svg";
import { ReactComponent as EditSquared } from "./Edit Square.svg";
import { ReactComponent as InfoCircle } from "./Info Circle.svg";
import { ReactComponent as Hide } from "./Hide 2.svg";
import { ReactComponent as Location } from "./Location.svg";
import { ReactComponent as Plus } from "./Plus 4.svg";
import { ReactComponent as Time } from "./Time Circle.svg";
import { ReactComponent as Upload } from "./Upload.svg";
import { ReactComponent as User } from "./user circle.svg";
import { ReactComponent as Work } from "./Work.svg";
import { ReactComponent as UserInfo } from "./User Information-Outlined.svg";
import { ReactComponent as OfficeOutlined } from "./Building Office - Outlined.svg";
import { ReactComponent as Search } from "./Search.svg";
import { ReactComponent as ReservationBook } from "./Notebook check.svg";
import { ReactComponent as Pricelist } from "./Euro document.svg";
import { ReactComponent as Users } from "./Users.svg";
import { ReactComponent as BulletedList } from "./Bulleted list.svg";

export {
  Calendar,
  CalendarIcon,
  VehicleOutlined,
  Delete,
  EditSquared,
  InfoCircle,
  Hide,
  Location,
  Plus,
  Time,
  Upload,
  User,
  Work,
  UserInfo,
  OfficeOutlined,
  Search,
  ReservationBook,
  Pricelist,
  Users,
  BulletedList
};
