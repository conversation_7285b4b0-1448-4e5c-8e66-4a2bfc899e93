import { Clock01Icon, Tick02Icon } from "@hugeicons/react";
import { Box, Divider, Grid, InputAdornment, Typography } from "@mui/material";
import { LocalizationProvider, TimePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { FormikErrors, useFormik } from "formik";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useRecoilState } from "recoil";
import { useUser } from "src/common/api";
import { useCompany } from "src/common/api/company";
import {
  useCreateNewCompanySettings,
  useUpdateCompanySetting,
} from "src/common/api/companySettings";
import { CeButton, CeTextField } from "src/common/components";
import { companySettingsFormValuesState } from "src/common/state/companySettings";
import { WorkSchedule } from "src/common/types/companySettings";
import {
  days,
  getShortDayName,
  turnCompanySettingsFormValuesIntoUpdateDto,
  turnCompanySettingsIntoFormValues,
} from "src/common/utils/companySettings";
import * as yup from "yup";

interface SettingsOperationalProfileProps {
  userId?: number;
}

export const SettingsOperationalProfile: React.FC<
  SettingsOperationalProfileProps
> = ({ userId }) => {
  const { t } = useTranslation(["common", "manager", "dispatcher"]);

  const [companySettingFormValues, setCompanySettingFormValues] =
    useRecoilState(companySettingsFormValuesState);

  const {
    data: user,
    isLoading: isFullUserLoading,
    isSuccess: isFullUserSuccess,
    refetch: refetchUser,
  } = useUser(userId, Boolean(userId));

  const {
    data: companyData,
    isLoading: isCompanyLoading,
    isSuccess: isCompanySuccess,
  } = useCompany(user?.company?.id, Boolean(user?.company.id));

  const companySettings = companyData?.settings;

  const {
    mutate: handleCreateCompanySettings,
    isSuccess: isCreateCompanySettingSuccess,
    isLoading: isCreateCompanySettingLoading,
  } = useCreateNewCompanySettings();

  const {
    mutate: handleUpdateCompanySetting,
    isSuccess: isUpdateCompanySettingSuccess,
    isLoading: isUpdateCompanySettingLoading,
  } = useUpdateCompanySetting();

  const isLoading = isFullUserLoading || isUpdateCompanySettingLoading;

  const formik = useFormik({
    initialValues: companySettingFormValues,
    enableReinitialize: true,
    validationSchema: yup.object({
      workSchedules: yup.array().of(
        yup.object().shape({
          day: yup.string().nullable(),
          startTime: yup.string().nullable(),
          endTime: yup.string().nullable(),
        })
      ),
      cancellation: yup.object().shape({
        cancellationWindow: yup.string().required().nullable(),
        reducedCancellationWindow: yup.string().required().nullable(),
        minTimeBetweenJobs: yup.string().required().nullable(),
      }),
    }),
    onSubmit: async (values) => {
      if (companySettings) {
        const payload = turnCompanySettingsFormValuesIntoUpdateDto({
          ...values,
          id: companySettings.id!,
          companyId: user?.companyId,
        });
        handleUpdateCompanySetting(payload);
      } else {
        const payload = turnCompanySettingsFormValuesIntoUpdateDto({
          ...values,
          companyId: user?.companyId,
        });
        handleCreateCompanySettings(payload);
      }
    },
  });

  useEffect(() => {
    if (companySettings && isCompanySuccess) {
      const companySettingsFormValues =
        turnCompanySettingsIntoFormValues(companySettings);

      const updatedWorkSchedules =
        companySettingsFormValues.workSchedules || [];
      const updatedCancellation = {
        cancellationWindow:
          companySettingsFormValues.cancellation?.cancellationWindow?.toString() ||
          "",
        reducedCancellationWindow:
          companySettingsFormValues.cancellation?.reducedCancellationWindow?.toString() ||
          "",
        minTimeBetweenJobs:
          companySettingsFormValues.cancellation?.minTimeBetweenJobs?.toString() ||
          "",
      };

      const updatedFormValues = {
        ...companySettingsFormValues,
        workSchedules: updatedWorkSchedules,
        cancellation: updatedCancellation,
      };

      const selectedDays: string[] = [];

      if (updatedWorkSchedules.length) {
        updatedWorkSchedules.forEach((uws) => {
          selectedDays.push(uws.day);
        });
      }

      setSelectedDays(selectedDays);

      setCompanySettingFormValues(updatedFormValues);
    }
  }, [companySettings, isCompanySuccess]);

  const [selectedDays, setSelectedDays] = useState<string[]>(
    companySettingFormValues.workSchedules.map((schedule) => schedule.day)
  );

  const handleDayToggle = (day: string) => {
    setSelectedDays((prevSelectedDays) =>
      prevSelectedDays.includes(day)
        ? prevSelectedDays.filter((d) => d !== day)
        : [...prevSelectedDays, day]
    );

    const defaultStartTime = "06:00:00";
    const defaultEndTime = "18:00:00";

    let workSchedulesClone = [...companySettingFormValues.workSchedules];

    if (selectedDays.includes(day)) {
      workSchedulesClone = workSchedulesClone.filter((wsc) => wsc.day !== day);
    } else {
      workSchedulesClone.push({
        day: day,
        startTime: defaultStartTime,
        endTime: defaultEndTime,
      });
    }

    setCompanySettingFormValues((formValues) => {
      return {
        ...formValues,
        workSchedules: [...workSchedulesClone],
      };
    });
  };

  const handleWorkScheduleChange = (
    newTime: Date | null,
    index: number,
    field: string
  ) => {
    if (newTime) {
      const hours = newTime.getHours().toString().padStart(2, "0");
      const minutes = newTime.getMinutes().toString().padStart(2, "0");
      const formattedTime = `${hours}:${minutes}`;
      formik.setFieldValue(`workSchedules[${index}].${field}`, formattedTime);
      formik.setFieldValue(
        `workSchedules[${index}].day`,
        formik.values.workSchedules[index]?.day || days[index]?.full
      );
    }
  };

  const handlePostValidation = (
    fieldName: string,
    value: string,
    onBlur: boolean = false
  ) => {
    const isValid = /^(2[0-3]|[01]\d):([0-5]\d)(:[0-5]\d)?$/.test(value);

    if (onBlur) {
      formik.setFieldValue(fieldName, isValid ? value : "");
    } else {
      formik.setFieldValue(fieldName, value);
    }
  };

  return (
    <>
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <form onSubmit={formik.handleSubmit}>
          <Box sx={{ overflow: "hidden", height: "calc(100vh - 20vh)" }}>
            <Box
              display="flex"
              gap={2}
              sx={{
                overflow: "auto",
                height: "75vh",
                paddingY: 2,
                paddingX: 1.5,
              }}
            >
              <Box display="flex" flexDirection="column" flex="1">
                {/* Work Schedule */}
                <Box display="flex" flexDirection="column" gap={3}>
                  <Typography
                    variant="body1"
                    color="text.disabled"
                    fontWeight="bold"
                    gutterBottom
                  >
                    {t("work-schedule")}
                  </Typography>
                  <Divider />

                  {/* Button Days */}
                  <Box display="flex" alignItems="center" gap={2} paddingX={2}>
                    <Typography variant="body2" sx={{ flex: "50%" }}>
                      {t("days")}
                    </Typography>
                    <Box display="flex" flex="50%" gap={1}>
                      {days.map((schedule) => (
                        <CeButton
                          key={schedule.full}
                          variant={
                            selectedDays.includes(schedule.full)
                              ? "contained"
                              : "outlined"
                          }
                          sx={{
                            width: "36px",
                            height: "36px",
                            padding: 0,
                            minWidth: "unset",
                          }}
                          onClick={() => handleDayToggle(schedule.full)}
                        >
                          {getShortDayName(schedule.full)}
                        </CeButton>
                      ))}
                    </Box>
                  </Box>

                  {/* Schedule */}
                  <Box
                    display="flex"
                    flexDirection="column"
                    gap={2}
                    paddingX={2}
                  >
                    {companySettingFormValues.workSchedules.map(
                      (schedule, index) =>
                        selectedDays.includes(schedule.day) && (
                          <Box
                            key={index}
                            display="flex"
                            alignItems="center"
                            gap={1.5}
                          >
                            <Typography sx={{ width: "60%" }}>
                              {schedule.day}
                            </Typography>
                            <TimePicker
                              ampm={false}
                              value={
                                formik.values.workSchedules[index]?.startTime
                                  ? new Date(
                                      `1970-01-01T${formik.values.workSchedules[index].startTime}`
                                    )
                                  : new Date(`1970-01-01T06:00:00`)
                              }
                              onChange={(newTime) =>
                                handleWorkScheduleChange(
                                  newTime,
                                  index,
                                  "startTime"
                                )
                              }
                              renderInput={(params) => (
                                <CeTextField
                                  {...params}
                                  id={`startTime-${index}`}
                                  name={`workSchedules[${index}].startTime`}
                                  value={
                                    formik.values.workSchedules[index]
                                      ?.startTime || ""
                                  }
                                  variant="outlined"
                                  size="small"
                                  onChange={(event) =>
                                    formik.handleChange(event)
                                  }
                                  error={
                                    Array.isArray(
                                      formik.touched.workSchedules
                                    ) &&
                                    Array.isArray(
                                      formik.errors.workSchedules
                                    ) &&
                                    formik.touched.workSchedules[index]
                                      ?.startTime &&
                                    Boolean(
                                      (
                                        formik.errors
                                          .workSchedules as FormikErrors<WorkSchedule>[]
                                      )[index]?.startTime
                                    )
                                  }
                                  helperText={
                                    Array.isArray(
                                      formik.touched.workSchedules
                                    ) &&
                                    Array.isArray(
                                      formik.errors.workSchedules
                                    ) &&
                                    formik.touched.workSchedules[index]
                                      ?.startTime &&
                                    (
                                      formik.errors
                                        .workSchedules as FormikErrors<WorkSchedule>[]
                                    )[index]?.startTime
                                  }
                                  disabled={isLoading}
                                  sx={{ width: "120px" }}
                                />
                              )}
                            />
                            <Typography variant="body1" color="text.disabled">
                              to
                            </Typography>
                            <TimePicker
                              ampm={false}
                              value={
                                formik.values.workSchedules[index]?.endTime
                                  ? new Date(
                                      `1970-01-01T${formik.values.workSchedules[index].endTime}`
                                    )
                                  : new Date(`1970-01-01T18:00:00`)
                              }
                              onChange={(newTime) =>
                                handleWorkScheduleChange(
                                  newTime,
                                  index,
                                  "endTime"
                                )
                              }
                              renderInput={(params) => (
                                <CeTextField
                                  {...params}
                                  id={`endTime-${index}`}
                                  name={`workSchedules[${index}].endTime`}
                                  value={
                                    formik.values.workSchedules[index]
                                      ?.endTime || ""
                                  }
                                  variant="outlined"
                                  size="small"
                                  onChange={(event) =>
                                    formik.handleChange(event)
                                  }
                                  error={
                                    Array.isArray(
                                      formik.touched.workSchedules
                                    ) &&
                                    Array.isArray(
                                      formik.errors.workSchedules
                                    ) &&
                                    formik.touched.workSchedules[index]
                                      ?.endTime &&
                                    Boolean(
                                      (
                                        formik.errors
                                          .workSchedules as FormikErrors<WorkSchedule>[]
                                      )[index]?.endTime
                                    )
                                  }
                                  helperText={
                                    Array.isArray(
                                      formik.touched.workSchedules
                                    ) &&
                                    Array.isArray(
                                      formik.errors.workSchedules
                                    ) &&
                                    formik.touched.workSchedules[index]
                                      ?.endTime &&
                                    (
                                      formik.errors
                                        .workSchedules as FormikErrors<WorkSchedule>[]
                                    )[index]?.endTime
                                  }
                                  disabled={isLoading}
                                  sx={{ width: "120px" }}
                                />
                              )}
                            />
                          </Box>
                        )
                    )}
                  </Box>
                </Box>
              </Box>

              <Box
                display="flex"
                flexDirection="column"
                paddingY={1.5}
                paddingX={2}
                flex="1"
              >
                {/* Contingencies */}
                <Box>
                  <Typography
                    variant="body1"
                    color="text.disabled"
                    fontWeight="bold"
                    gutterBottom
                  >
                    {t("contingencies")}
                  </Typography>
                  <Divider sx={{ marginBottom: 2, marginTop: 2 }} />
                  <Box
                    display="flex"
                    flexDirection="column"
                    paddingX={2}
                    gap={2}
                  >
                    <Box display="flex" alignItems="center" gap={2}>
                      <Typography variant="body2" sx={{ flex: "50%" }}>
                        {t("standard-cancellation-period")}
                      </Typography>

                      <Box sx={{ flex: "50%" }}>
                        <CeTextField
                          fullWidth
                          id="reducedCancellationWindow"
                          name="cancellation.reducedCancellationWindow"
                          size="small"
                          type="text"
                          value={
                            formik.values.cancellation
                              .reducedCancellationWindow || ""
                          }
                          onChange={(event) => {
                            handlePostValidation(
                              "cancellation.reducedCancellationWindow",
                              event.target.value
                            );
                            formik.setFieldValue(
                              "cancellation.reducedCancellationWindow",
                              event.target.value
                            );
                          }}
                          onBlur={(event) => {
                            handlePostValidation(
                              "cancellation.reducedCancellationWindow",
                              event.target.value,
                              true
                            );
                          }}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                <Clock01Icon
                                  size={20}
                                  color={"currentColor"}
                                  variant={"stroke"}
                                />
                              </InputAdornment>
                            ),
                          }}
                          helperText={t("two-days-cancellation-deadline")}
                          error={
                            formik.touched.cancellation
                              ?.reducedCancellationWindow &&
                            Boolean(
                              formik.errors.cancellation
                                ?.reducedCancellationWindow
                            )
                          }
                          disabled={isLoading}
                          required
                        />
                      </Box>
                    </Box>

                    <Box display="flex" alignItems="center" gap={2}>
                      <Typography variant="body2" sx={{ flex: "50%" }}>
                        {t("late-cancellation-period")}
                      </Typography>

                      <Box sx={{ flex: "50%" }}>
                        <CeTextField
                          fullWidth
                          id="cancellationWindow"
                          name="cancellation.cancellationWindow"
                          size="small"
                          type="text"
                          value={
                            formik.values.cancellation.cancellationWindow || ""
                          }
                          onChange={(event) => {
                            handlePostValidation(
                              "cancellation.cancellationWindow",
                              event.target.value
                            );
                          }}
                          onBlur={(event) => {
                            handlePostValidation(
                              "cancellation.cancellationWindow",
                              event.target.value,
                              true
                            );
                          }}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                <Clock01Icon
                                  size={20}
                                  color={"currentColor"}
                                  variant={"stroke"}
                                />
                              </InputAdornment>
                            ),
                          }}
                          helperText={t("one-day-cancellation-deadline")}
                          error={
                            formik.touched.cancellation?.cancellationWindow &&
                            Boolean(
                              formik.errors.cancellation?.cancellationWindow
                            )
                          }
                          disabled={isLoading}
                          required
                        />
                      </Box>
                    </Box>

                    <Box display="flex" alignItems="center" gap={2}>
                      <Typography variant="body2" sx={{ flex: "50%" }}>
                        {t("minimum-time-between-jobs")}
                      </Typography>

                      <Box sx={{ flex: "50%" }}>
                        <CeTextField
                          fullWidth
                          id="minTimeBetweenJobs"
                          name="cancellation.minTimeBetweenJobs"
                          size="small"
                          type="text"
                          value={
                            formik.values.cancellation.minTimeBetweenJobs || ""
                          }
                          onChange={(event) => {
                            handlePostValidation(
                              "cancellation.minTimeBetweenJobs",
                              event.target.value
                            );
                          }}
                          onBlur={(event) => {
                            handlePostValidation(
                              "cancellation.minTimeBetweenJobs",
                              event.target.value,
                              true
                            );
                          }}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                <Clock01Icon
                                  size={20}
                                  color={"currentColor"}
                                  variant={"stroke"}
                                />
                              </InputAdornment>
                            ),
                          }}
                          error={
                            formik.touched.cancellation?.minTimeBetweenJobs &&
                            Boolean(
                              formik.errors.cancellation?.minTimeBetweenJobs
                            )
                          }
                          disabled={isLoading}
                          required
                        />
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Box>
          </Box>

          {/* Submit Button */}
          <Box
            sx={{
              paddingY: 1.5,
              paddingX: 2,
              width: "100%",
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-end",
              position: "sticky",
              bottom: 0,
              pr: 4,
              zIndex: 10,
              gap: 2,
              borderTop: "1px solid",
              borderColor: (theme) => theme.palette.divider,
              backgroundColor: "background.paper",
            }}
          >
            <Grid
              item
              xs={12}
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                marginTop: 3,
              }}
            >
              <CeButton
                variant="text"
                onClick={() => formik.resetForm()}
                disabled={isLoading}
                sx={{ marginRight: 1 }}
              >
                {t("cancel")}
              </CeButton>
              <CeButton
                type="submit"
                variant="contained"
                disabled={isLoading}
                endIcon={
                  <Tick02Icon
                    size={20}
                    color={"currentColor"}
                    variant={"stroke"}
                  />
                }
              >
                {t("common:save")}
              </CeButton>
            </Grid>
          </Box>
        </form>
      </LocalizationProvider>
    </>
  );
};
