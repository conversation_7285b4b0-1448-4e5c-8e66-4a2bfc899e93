import {
  ContainerModalFormValues,
  DeletePriceListModalValues,
  PriceListFormValues,
  ReviewPriceListModalValues,
} from "../types/priceList";

export const PRICELIST_FORM_VALUES_DEFAULT: PriceListFormValues = {
  priceListId: null,
  title: "",
  packageFlatFee: null,
  packageFlatFeeDuration: null,
  pricePerMeterPumped: null,
  additionalHour: null,
  cleaningFee: null,
  pricePerMeterOfFlexiblePipeLength80Mm: null,
  pricePerMeterOfFlexiblePipeLength90Mm: null,
  pricePerMeterOfFlexiblePipeLength100Mm: null,
  pricePerMeterOfRigidPipeLength120Mm: null,
  supplyOfTheChemicalSlushie: null,
  barbotine: null,
  extraCementBagPrice: null,
  transportRates: [{ name: "", tariff: null, from: 0, to: null }],
  pumpTiers: [{ name: "", price: null, maximum: null, minimum: 0 }],
  vehicleId: null,
  vehicleTypeId: null,
  containerId: null,
  flow: null,
  isNightContract: false,
  isWeekendContract: false,
};

export const PRICELIST_DELETE_DEFAULT: DeletePriceListModalValues = {
  flow: null,
};

export const PRICELIST_REVIEW_DEFAULT: ReviewPriceListModalValues = {
  flow: null,
  contractId: undefined,
  pricelistId: null,
  vehicleId: null,
};

export const CONTAINER_FORM_DEFAULT_VALUES: ContainerModalFormValues = {
  title: null,
  isPublic: false,
  isDefault: false,
  flow: null,
  id: null,
  partnerIds: [],
  partners: [],
  copyFromContainer: null,
};
