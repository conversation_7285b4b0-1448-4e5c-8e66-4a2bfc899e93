import { Box } from "@mui/material";
import React from "react";
import { useTaskActivities } from "src/common/api/tasks";
import Activity from "./Activity";

interface ActivitiesType {
  taskId: number | null;
}

const Activities = ({ taskId }: ActivitiesType) => {
  const { data: activities } = useTaskActivities(taskId!, <PERSON><PERSON>an(taskId));
  const taskActivities = activities || [];
  return (
    <>
      {taskActivities.map((activity, index) => (
        <Box key={index}>
          <Activity activity={activity} />
        </Box>
      ))}
    </>
  );
};

export default Activities;
