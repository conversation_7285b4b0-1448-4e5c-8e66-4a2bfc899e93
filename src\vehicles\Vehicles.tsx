import { useWindowSize } from "@react-hook/window-size";
import { VehiclesDatagrid } from "./datagrid/VehiclesDatagrid";
import { getCurrentUser, useVehicles } from "src/common/api";
import { useState } from "react";
import { CePaper } from "src/common/components";
import { useLocation } from "react-router-dom";
import usePersistentGridState from "src/common/utils/gridState";

export const Vehicles = () => {
  const currentUser = getCurrentUser();

  const [, height] = useWindowSize();
  const location = useLocation();
  const localStorageKey = location.pathname.slice(1);
  const [gridState, updateGridStatePart] = usePersistentGridState(
    localStorageKey,
    1,
    20,
  );
  
  const {
    data: allVehicles,
    isLoading: isLoadingVehicles,
    refetch: refetchVehicles
  } = useVehicles(
    {
      operatorManagerId: currentUser?.id,
      limit: gridState.pageSize,
      offset: (gridState.page - 1) * gridState.pageSize
    },
    Bo<PERSON>an(currentUser?.id)
  );

  const handlePageChange = (newPage: number) => {
    updateGridStatePart("page", newPage + 1);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    updateGridStatePart("pageSize", newPageSize);
  };

  return (
    <CePaper sx={{ height: `${height - 100}px`, p: 2 }}>
      <VehiclesDatagrid
        data={allVehicles?.data || []}
        isFetchingVehicles={isLoadingVehicles}
        refetchVehicles={refetchVehicles}
        shouldRenderRefreshButton
        shouldRenderAddButton
        shouldRenderEditActionsColumn
        gridState={gridState}
        total={allVehicles?.totalCount || 0}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        updateGridStatePart={updateGridStatePart}
      />
    </CePaper>
  );
};
