import { GridSortModel } from "@mui/x-data-grid";
import { CommonEntity, PossibleSortDir } from "./common";
import { Company } from "./company";
import { Expression } from "./filters";
import { Manager } from "./manager";
import { Partner } from "./partners";
import {
  CreatePriceListDto,
  PriceList,
  PriceListFormValues,
} from "./priceList";
import { User } from "./user";
import { Vehicle } from "./vehicle";

export interface Contract extends CommonEntity {
  id: number;
  dispatcherCompanyId: number;
  operatorManagerId: number;
  pricelistId: number;
  status: ContractedStatus;
  company: Company;
  dispatcherCompany: Company;
  operatorManager: Manager;
  operatorCompany: Company;
  pricelist?: PriceList;
  endDate?: Date;
  startDate?: Date;
  vehicleId?: number;
}

export interface ContractsWithCount {
  totalCount: number;
  data: Contract[];
}

export interface GetContractedPriceList {
  dispatcherCompanyId?: number | null;
  operatorCompanyId?: number | null;
}

export interface GetContractDto {
  expressions: Expression[];
  sortModel: GridSortModel;
  relations?: string[];
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
  status?: ContractedStatus | null;
  pricelistId?: number | null;
  dispatcherCompanyId?: number | null;
  operatorCompanyId?: number[] | null;
}

export interface ContractedPriceList extends PriceListFormValues {
  useCancellationFee: boolean;
  useLateCancellationFee: boolean;
  standardFeeAsDayContractFee: boolean;
  lateFeeAsDayContractFee: boolean;
}

export interface ContractFormValues {
  partner: Partner | null;
  pricelist: ContractedPriceList;
  flow: ContractModalFlow;
  comment?: string | null;
  status: ContractedStatus | null;
  vehicle: Vehicle | null;
  startDate: Date | null;
  endDate: Date | null;
}

export interface CreateContractDto {
  dispatcherCompanyId: number;
  vehicleId: number;
  startDate: string;
  endDate: string;
  pricelist: CreatePriceListDto;
  comment: string | null;
}

export interface UpdateContractDto extends CreateContractDto {}
export interface CancelContractValues {
  startDate: Date | null;
  endDate: Date | null;
  flow: CancelContractFlow;
  contractId: number | null;
  comment: string | null;
}
export interface CancelContractDto {
  suspensionStart: string;
  suspensionEnd: string;
  contractId: number;
}

export type ContractModalFlow = "Request" | null;

export type CancelContractFlow = "Cancel" | "Suspend" | null;
export interface RespondToContractPayload {
  comment?: string;
}
export enum ContractResponse {
  ACCEPT = "accept",
  REJECT = "reject",
}

export enum ContractedStatus {
  APPROVED = "approved",
  PENDING = "pending",
  DECLINED = "declined",
}
