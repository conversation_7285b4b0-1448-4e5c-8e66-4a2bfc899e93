import { userRolesDisplay, userStatusDisplay } from "../constants";
import {
  CreateDispatcher<PERSON><PERSON>,
  Dispatcher,
  DispatcherFormValues,
  DispatcherModalFlow,
  UpdateDispatcherDto,
} from "../types";

export const turnDispatcherFormValuesIntoCreateDto = (
  values: DispatcherFormValues
): CreateDispatcherDto => {
  const payload = {
    firstName: values.firstName,
    lastName: values.lastName,
    email: values.email,
    phoneNumber: values.phoneNumber,
    status: values.status?.id!,
    role: values.role?.id!,
  };

  return payload;
};

export const turnDispatcherFormValuesIntoUpdateDto = (
  values: DispatcherFormValues
): UpdateDispatcherDto => {
  const payload: UpdateDispatcherDto = {
    dispatcherId: values.dispatcherId!,
    firstName: values.firstName,
    lastName: values.lastName,
    email: values.email,
    phoneNumber: values.phoneNumber,
    status: values.status?.id!,
    role: values.role?.id!,
  };

  return payload;
};

export const turnDispatcherIntoFormValues = (
  dispatcher: Dispatcher,
  flow: DispatcherModalFlow
): DispatcherFormValues => {
  const status =
    userStatusDisplay.find((status) => status.id === dispatcher.status) || null;
  const role =
    userRolesDisplay.find((role) => role.id === dispatcher.role) || null;

  const payload = {
    dispatcherId: dispatcher.id,
    firstName: dispatcher.firstName || "",
    lastName: dispatcher.lastName || "",
    email: dispatcher.email || "",
    phoneNumber: dispatcher.phoneNumber || "",
    status: status,
    role: role,
    flow,
  };

  return payload;
};
