import {
  CompanySettings,
  CompanySettingsFormValues,
  UpdateCompanySettingsDto,
} from "../types/companySettings";

export const turnCompanySettingsIntoFormValues = (
  company?: CompanySettings
): CompanySettingsFormValues => {
  const defaultStartTime = "06:00";
  const defaultEndTime = "18:00";

  return {
    companyId: company?.companyId,
    workSchedules: company?.workSchedules?.length
      ? company?.workSchedules.map((schedule, index) => ({
          day: schedule.day || days[index].full,
          startTime: schedule.startTime || defaultStartTime,
          endTime: schedule.endTime || defaultEndTime,
        }))
      : days.map((day) => ({
          day: day.full,
          startTime: defaultStartTime,
          endTime: defaultEndTime,
        })),
    cancellation: {
      cancellationWindow: parseIntervalString(
        company?.cancellation?.cancellationWindow
      ),
      reducedCancellationWindow: parseIntervalString(
        company?.cancellation?.reducedCancellationWindow
      ),
      minTimeBetweenJobs: parseIntervalString(
        company?.cancellation?.minTimeBetweenJobs
      ),
    },
  };
};

export const turnCompanySettingsFormValuesIntoUpdateDto = (
  values: CompanySettingsFormValues
): UpdateCompanySettingsDto => {
  const { companyId, workSchedules, cancellation, id } = values;

  const filteredWorkSchedules = workSchedules
    .filter(
      (schedule, index) =>
        (schedule.day || days[index]?.full) &&
        (schedule.startTime !== "" || schedule.endTime !== "")
    )
    .map((schedule, index) => ({
      day: schedule.day || days[index]?.full,
      startTime: schedule.startTime,
      endTime: schedule.endTime,
    }));

  return {
    companySettingsId: id!,
    companyId: companyId!,
    workSchedules: filteredWorkSchedules,
    cancellation: cancellation || {
      cancellationWindow: "",
      reducedCancellationWindow: "",
      minTimeBetweenJobs: "",
    },
  };
};

export const days = [
  { short: "M", full: "Monday" },
  { short: "T", full: "Tuesday" },
  { short: "W", full: "Wednesday" },
  { short: "T", full: "Thursday" },
  { short: "F", full: "Friday" },
  { short: "S", full: "Saturday" },
  { short: "S", full: "Sunday" },
];

export const getShortDayName = (fullDayName: string) => {
  switch (fullDayName) {
    case "Monday":
      return "M";
    case "Tuesday":
      return "T";
    case "Wednesday":
      return "W";
    case "Thursday":
      return "T";
    case "Friday":
      return "F";
    case "Saturday":
      return "S";
    case "Sunday":
      return "S";
    default:
      return fullDayName.charAt(0);
  }
};

export const parseIntervalString = (interval: any): string => {
  if (typeof interval === "string") {
    return interval;
  } else if (typeof interval === "object" && interval !== null) {
    const hours = interval.hours?.toString().padStart(2, "0") || "00";
    const minutes = interval.minutes?.toString().padStart(2, "0") || "00";
    return `${hours}:${minutes}`;
  }

  return "00:00";
};
