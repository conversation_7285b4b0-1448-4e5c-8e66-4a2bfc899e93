import { useEffect } from "react";
import { Grid } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { SignInWithEmailForm } from "./SignInWithEmailForm";
import { useSignInWithEmailAndPassword } from "src/common/api/auth";
import { Role } from "src/common/types";

export const SignIn: React.FC = () => {
  const navigate = useNavigate();
  const {
    data,
    mutate: handleSignIn,
    isLoading,
    isSuccess,
  } = useSignInWithEmailAndPassword();

  useEffect(() => {
    if (isSuccess) {
      let redirectUrl = "";
      const user = data.user;
      const { role } = user;

      if (role === Role.MANAGER) {
        redirectUrl = "/managers";
      } else if (role === Role.DISPATCHER_MANAGER) {
        redirectUrl = "/dispatchers";
      } else if (role === Role.OEPRATOR_MANAGER) {
        redirectUrl = "/planning";
      } else if (role === Role.DISPATCHER) {
        redirectUrl = "/dispatcher-planning";
      } else if (role === Role.OPERATOR) {
        redirectUrl = "/operator-planning";
      }

      // Send them back to the page they tried to visit when they were
      // redirected to the SignIn page. Use { replace: true } so we don't create
      // another entry in the history stack for the SignIn page.  This means that
      // when they get to the protected page and click the back button, they
      // won't end up back on the SignIn page, which is also really nice for the
      // user experience.
      navigate(redirectUrl, { replace: true });
    }
  }, [isLoading, isSuccess, navigate, data?.user]);

  return (
    <Grid
      container
      direction="column"
      justifyContent="center"
      alignItems="center"
    >
      <SignInWithEmailForm handleSignIn={handleSignIn} isLoading={isLoading} />
    </Grid>
  );
};
