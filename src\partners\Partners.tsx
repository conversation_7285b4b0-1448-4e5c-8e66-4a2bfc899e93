import { useWindowSize } from "@react-hook/window-size";
import { useEffect, useState } from "react";
import {
  useCreateNewPartners,
  useDeletePartner,
  usePartners,
} from "src/common/api/partner";
import { CePaper } from "src/common/components";
import PartnersDatagrid from "./datagrid/PartnersDatagrid";
import { CompanyCategory, Expression, Role } from "src/common/types";
import { GridFilterModel, GridSortModel } from "@mui/x-data-grid";
import { turnDatagridFilterIntoExpressions } from "src/common/utils";
import usePersistentGridState from "src/common/utils/gridState";
import { useLocation } from "react-router-dom";
import { useCompanies } from "src/common/api/company";
import { useRecoilState } from "recoil";
import { partnerFormValues } from "src/common/state/partner";
import { PARTNERS_FORM_VALUES_DEFAULT } from "src/common/constants/partners";
import { CreatePartnerDto } from "src/common/types/partners";
import { getCurrentUser } from "src/common/api";

export const Partners = () => {
  const [, height] = useWindowSize();
  const [partnersFormState, setPartnersFormState] =
    useRecoilState(partnerFormValues);
  const currentUser = getCurrentUser();
  const isManager = currentUser?.role === Role.DISPATCHER_MANAGER;
  const location = useLocation();
  const localStorageKey = location.pathname.slice(1);

  const [gridState, updateGridStatePart] = usePersistentGridState(
    localStorageKey,
    1,
    20
  );

  const expression = turnDatagridFilterIntoExpressions(gridState.filterModel);
  const [expressions, setExpressions] = useState<Expression[]>(expression);
  const [sortingModelOptions, setSortingModelOptions] = useState<GridSortModel>(
    gridState.sortModel
  );

  const {
    data: allPartners,
    isLoading: isLoadingPartners,
    refetch: refetchPartners,
  } = usePartners({ 
    expressions, 
    sortModel: sortingModelOptions,
    limit: gridState.pageSize,
    offset: (gridState.page - 1) * gridState.pageSize
  });

  const partners = allPartners?.data || [];
  const partnerOperatorIds = partners.map(
    (partner) => partner.operatorCompanyId
  );

  const { data: allOperatorCompanies, isLoading: isOperatorCompanyLoading } =
    useCompanies(
      {
        expressions: [
          {
            category: '"company"."category"',
            operator: "=",
            value: CompanyCategory.OPERATOR,
            conditionType: "AND",
          },
          {
            conditionType: "AND",
            category: '"company"."id"',
            operator: "not_in",
            value: partnerOperatorIds,
          },
        ],
        sortModel: [],
        offset: 0,
        limit: 20,
      },
      isManager
    );

  const operatorCompanies = allOperatorCompanies?.data || [];

  const {
    mutate: createPartners,
    isLoading: isCreatingPartners,
    isSuccess: isCreatePartnersSuccess,
  } = useCreateNewPartners();
  const {
    mutate: deletePartner,
    isLoading: isDeletingPartner,
    isSuccess: isDeletingPartnerSuccess,
  } = useDeletePartner();

  const handlePageChange = (newPage: number) => {
    updateGridStatePart("page", newPage + 1);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    updateGridStatePart("pageSize", newPageSize);
  };
  const handleSortModelChange = (sortModel: GridSortModel) => {
    setSortingModelOptions(sortModel);
  };

  const onDatagridFiltersChange = (filterModel: GridFilterModel) => {
    const expressions = turnDatagridFilterIntoExpressions(filterModel);
    setExpressions(expressions);
  };

  const onCreatenewPartner = (operatorCompany: CreatePartnerDto) => {
    createPartners(operatorCompany);
  };
  const onDeletePartner = () => {
    const partnerId = partnersFormState.partnerId || null;
    if (partnerId) {
      deletePartner({ partnerId });
    }
  };
  const closePartnersActionModal = () => {
    setPartnersFormState(PARTNERS_FORM_VALUES_DEFAULT);
  };

  useEffect(() => {
    if (isCreatePartnersSuccess || isDeletingPartnerSuccess) {
      setPartnersFormState(PARTNERS_FORM_VALUES_DEFAULT);
    }
  }, [
    isCreatePartnersSuccess,
    isDeletingPartnerSuccess,
    setPartnersFormState,
    PARTNERS_FORM_VALUES_DEFAULT,
  ]);

  return (
    <CePaper sx={{ height: `${height - 100}px`, p: 2 }}>
      <PartnersDatagrid
        onDeletePartner={onDeletePartner}
        setPartnersFormsState={setPartnersFormState}
        partnersFormsState={partnersFormState}
        data={partners}
        isFetchingPartners={isLoadingPartners}
        refetchPartners={refetchPartners}
        shouldRenderEditActionsColumn
        page={gridState.page}
        pageSize={gridState.pageSize}
        total={allPartners?.totalCount || 0}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        gridState={gridState}
        updateGridStatePart={updateGridStatePart}
        isServerDriven
        handleSortModelChange={handleSortModelChange}
        onDatagridFiltersChange={onDatagridFiltersChange}
        onCreatenNewPartners={onCreatenewPartner}
        shouldRenderAddButton={isManager}
        shouldRenderRefreshButton
        operatorCompanies={operatorCompanies}
        closePartnersActionModal={closePartnersActionModal}
      />
    </CePaper>
  );
};
