import { Chip, Grid, IconButton, Typography, useTheme } from "@mui/material";
import { EventContentArg } from "@fullcalendar/core";
import { JobStatus, Operator, User, Vehicle } from "src/common/types";
import { useTranslation } from "react-i18next";
import { JOB_STEPS, STATUS_COLORS } from "src/common/constants";
import { VehicleOutlined, UserInfo } from "src/images/Icons";
import {
  Building06Icon,
  IdIcon,
  InformationCircleIcon,
} from "@hugeicons/react";
import { HtmlTooltipResource } from "src/common/components/custom/HtmlTooltipResource";
import { EventContentDayRow } from "../event-content/EventContentDayRow";

interface ListViewResource {
  id: string;
  name: string;
  extendedProps: {
    vehicleId: number;
    operator: Operator | null;
    managerId: number | null;
  };
}

interface ListViewProps {
  arg: EventContentArg;
  resource: ListViewResource | null;
  handleVehicleInformationModal: (vehicleId: number) => void;
}
const EventContentList = ({
  arg,
  resource,
  handleVehicleInformationModal,
}: ListViewProps) => {
  const { t, i18n } = useTranslation(["dispatcher", "common"]);
  const theme = useTheme();
  const vehicle = resource?.extendedProps as {
    _id: string;
    vehicleId: number;
    operator: Operator | null;
    managerId: number | null;
  } & Vehicle;
  const vehicleId = Number(vehicle?.vehicleId);
  const jobStatus = arg.event._def.extendedProps.jobStatus || "";
  const getStatus = (name: string) => {
    const jobStep = JOB_STEPS.find((step) => step.name === name);
    return jobStep ? jobStep.label : null;
  };
  const status = getStatus(jobStatus);

  const statusColors = STATUS_COLORS[jobStatus];
  return (
    <Grid
      container
      columnSpacing={2}
      sx={{
        width: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
      }}
    >
      <Grid item xs={5} alignItems="center">
        <EventContentDayRow arg={arg} />
      </Grid>
      <Grid container item xs={7} columnSpacing={2} alignItems="center">
        <Grid item xs={2} textAlign={"center"}>
          <HtmlTooltipResource
            title={<Typography>{t(`${status}`)}</Typography>}
          >
            <Chip
              label={
                <Typography
                  sx={{
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    letterSpacing: "0.16px",
                    fontSize: "13px",
                    fontWeight: 400,
                    lineHeight: "18px",
                  }}
                >
                  {t(`${status}`)}
                </Typography>
              }
              size="small"
              color={statusColors.chipColor}
              sx={{
                color:
                  jobStatus === JobStatus.NOT_STARTED
                    ? statusColors.color
                    : theme.palette.primary.contrastText,
                padding: "15px 4px",
              }}
            />
          </HtmlTooltipResource>
        </Grid>
        <Grid container item xs={5} columnSpacing={1}>
          <Grid
            item
            xs={3}
            display="flex"
            alignItems="center"
            justifyContent="flex-end"
          >
            <VehicleOutlined
              width="25px"
              height="25px"
              color={theme.palette.primary.main}
            />
          </Grid>
          <Grid
            item
            xs={6}
            sx={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              textAlign: "center",
            }}
          >
            <HtmlTooltipResource
              title={<Typography>{resource?.name || ""}</Typography>}
            >
              <Typography
                fontSize={12}
                variant="button"
                fontWeight={700}
                letterSpacing="0.14px"
                lineHeight="20px"
                color={"text.secondary"}
              >
                {resource?.name || ""}
              </Typography>
            </HtmlTooltipResource>
          </Grid>
          <Grid item xs={3} textAlign={"start"}>
            <HtmlTooltipResource
              placement="bottom-start"
              title={<Typography>{t("common:vehicle-details")}</Typography>}
            >
              <IconButton
                color="primary"
                sx={{ p: 0 }}
                onClick={(event: React.SyntheticEvent) => {
                  event.stopPropagation();
                  handleVehicleInformationModal(vehicleId);
                }}
              >
                <InformationCircleIcon
                  size="25px"
                  stroke={theme.palette.primary.main}
                />
              </IconButton>
            </HtmlTooltipResource>
          </Grid>
        </Grid>
        <Grid container item xs={5}>
          <Grid item xs={2} />

          <Grid item xs={5}>
            <HtmlTooltipResource
              title={
                <Typography
                  sx={{
                    textTransform: "capitalize",
                  }}
                >
                  {vehicle?.operator
                    ? `${vehicle.operator?.name}`
                    : "None"}
                </Typography>
              }
            >
              <Chip
                size="small"
                sx={{
                  backgroundColor: "rgba(0, 0, 0, 0.03)",
                  color: theme.palette.text.primary,
                }}
                icon={
                  <IdIcon
                    size={16}
                    color={"currentColor"}
                    variant={"stroke"}
                    style={{ flexShrink: "0" }}
                  />
                }
                label={
                  <Typography
                    sx={{
                      fontWeight: "500",
                      fontSize: "12px",
                      letterSpacing: "0.16px",
                      lineHeight: "11px",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                      textTransform: "capitalize",
                    }}
                  >
                    {vehicle?.operator
                      ? `${vehicle.operator?.name} `
                      : "None"}
                  </Typography>
                }
              />
            </HtmlTooltipResource>
          </Grid>
          <Grid item xs={5}>
            <HtmlTooltipResource
              title={
                <Typography
                  sx={{
                    textTransform: "capitalize",
                  }}
                >
                  {arg?.event?._def?.title}
                </Typography>
              }
            >
              <Chip
                size="small"
                sx={{
                  backgroundColor: "rgba(0, 0, 0, 0.03)",
                  color: theme.palette.text.primary,
                }}
                icon={
                  <Building06Icon
                    size={16}
                    color={"currentColor"}
                    variant={"stroke"}
                    style={{ flexShrink: "0" }}
                  />
                }
                label={
                  <Typography
                    sx={{
                      fontWeight: "500",
                      fontSize: "12px",
                      letterSpacing: "0.16px",
                      lineHeight: "11px",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                      textTransform: "capitalize",
                    }}
                  >
                    {arg?.event?._def?.title}
                  </Typography>
                }
              />
            </HtmlTooltipResource>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

export default EventContentList;
