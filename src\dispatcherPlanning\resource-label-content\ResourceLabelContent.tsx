import { ResourceLabelContentArg } from "@fullcalendar/resource";
import { useTranslation } from "react-i18next";
import { VehicleOutlined } from "../../images/Icons";
import {
  Building06Icon,
  IdIcon,
  InformationCircleIcon,
} from "@hugeicons/react";
import {
  Box,
  Chip,
  IconButton,
  Stack,
  Typography,
  useTheme,
} from "@mui/material";
import { FC } from "react";
import { Manager, Operator, Role, Vehicle } from "src/common/types";
import { HtmlTooltipResource } from "src/common/components/custom/HtmlTooltipResource";
import { CePaper } from "src/common/components";
import TooltipResource from "./TooltipResource";
import { getCurrentUser } from "src/common/api";

type ResourceLabelContentProps = {
  arg: ResourceLabelContentArg;
  handleVehicleInformationModal: (vehicleId: number) => void;
};

const ResourceLabelContent: FC<ResourceLabelContentProps> = (props) => {
  const currentUser = getCurrentUser();
  const { t } = useTranslation("dispatcher");
  const theme = useTheme();
  const { arg, handleVehicleInformationModal } = props;
  const vehicle = arg.resource.extendedProps as {
    _id: string;
    vehicleId: number;
    operator: Operator | null;
    manager: Manager | null;
  } & Vehicle;
  const operatorFullName = vehicle.operator
    ? `${vehicle.operator?.name || ""}`
    : "None";
  const isDispatcherView = currentUser?.role !== Role.OEPRATOR_MANAGER;
  return (
    <CePaper
      sx={{
        p: 1,
        width: "100%",
      }}
    >
      <Box mb="6px" sx={{ display: "flex", alignItems: "center" }}>
        <HtmlTooltipResource
          title={<Typography>{arg.resource.extendedProps.name}</Typography>}
        >
          <Box
            sx={{
              alignItems: "center",
              display: "flex",
              flex: "1",
              justifyContent: "flex-start",
              px: "9px",
            }}
          >
            <VehicleOutlined
              width="16px"
              height="16px"
              color={theme.palette.primary.main}
            />
            <Box
              ml={1}
              flex="1"
              minWidth="0"
              sx={{
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
              }}
            >
              <Typography
                fontSize={12}
                fontWeight={700}
                color={theme.palette.text.primary}
                letterSpacing={0.1}
                lineHeight="14px"
              >
                {arg.resource.extendedProps.name}
              </Typography>
            </Box>
          </Box>
        </HtmlTooltipResource>
        <IconButton
          color="primary"
          sx={{ p: 0 }}
          onClick={(event: React.SyntheticEvent) => {
            event.stopPropagation();
            handleVehicleInformationModal(vehicle.vehicleId);
          }}
        >
          <InformationCircleIcon
            size="18px"
            stroke={theme.palette.primary.main}
          />
        </IconButton>
      </Box>
      <Box
        display="flex"
        alignItems="center"
        justifyContent={"space-between"}
        gap="8px"
      >
        <HtmlTooltipResource
          title={
            <Stack>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "flex-start",
                  gap: 1,
                }}
              >
                <Typography>{t("common:name")}:</Typography>
                <Typography>{operatorFullName}</Typography>
              </Box>
              <Box>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-start",
                    gap: 1,
                  }}
                >
                  <Typography>{t("common:phone-number")}:</Typography>
                  <Typography>
                    {vehicle?.operator?.phoneNumber || ""}
                  </Typography>
                </Box>
              </Box>
            </Stack>
          }
          sx={{
            flexShrink: "0",
          }}
        >
          <Chip
            size="small"
            sx={{
              padding: "4px",
              borderRadius: "100px",
              display: "flex",
              alignItems: "center",
              backgroundColor: "rgba(0, 0, 0, 0.03)",
              "& span": isDispatcherView ? { width: "84px", pr: "0" } : null,
            }}
            icon={
              <IdIcon size={16} color={"currentColor"} variant={"stroke"} />
            }
            label={
              <Typography
                sx={{
                  fontWeight: "500",
                  fontSize: "12px",
                  letterSpacing: "0.16px",
                  lineHeight: "11px",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                }}
              >
                {vehicle?.operator?.name || ""}
              </Typography>
            }
          />
        </HtmlTooltipResource>
        {isDispatcherView ? (
          <HtmlTooltipResource
            title={
              <TooltipResource
                phoneNumber={vehicle.operator?.company?.phoneNumber || "-"}
                contactEmail={vehicle.operator?.company?.contactEmail || "-"}
                companyName={vehicle.operator?.company?.name || "-"}
              />
            }
          >
            <Chip
              size="small"
              sx={{
                padding: "4px",
                borderRadius: "100px",
                display: "flex",
                alignItems: "center",
                backgroundColor: "rgba(0, 0, 0, 0.03)",
                color: theme.palette.text.primary,
                "& span": { width: "84px", pr: "0" },
              }}
              icon={
                <Building06Icon
                  size={16}
                  color={"currentColor"}
                  variant={"stroke"}
                />
              }
              label={
                <Typography
                  sx={{
                    fontWeight: "500",
                    fontSize: "12px",
                    letterSpacing: "0.16px",
                    lineHeight: "19.92px",
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                  }}
                  variant="caption"
                >
                  {vehicle.operator?.company?.name || ""}
                </Typography>
              }
            />
          </HtmlTooltipResource>
        ) : null}
      </Box>
    </CePaper>
  );
};

export default ResourceLabelContent;
