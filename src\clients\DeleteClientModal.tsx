import { DialogActions, Typography } from "@mui/material";
import { AxiosError } from "axios";
import { useTranslation } from "react-i18next";
import { UseMutateAsyncFunction } from "react-query";
import { SetterOrUpdater } from "recoil";
import { MainModal } from "src/common/components";
import { CeButton } from "src/common/components";
import { DEFAULT_CLIENT_DELETE_VALUES } from "src/common/constants/client";
import { ClientDeleteModalValues } from "src/common/types/client";
interface ClientDeleteProps {
  isLoading: boolean;
  clientDeleteValues: ClientDeleteModalValues;
  setClientDeleteValues: SetterOrUpdater<ClientDeleteModalValues>;
  handleDeleteClient: UseMutateAsyncFunction<
    string,
    Error | AxiosError<any, any>,
    string,
    () => void
  >;
}
const DeleteClientModal = ({
  isLoading,
  clientDeleteValues,
  setClientDeleteValues,
  handleDeleteClient,
}: ClientDeleteProps) => {
  const { t, i18n } = useTranslation(["common", "manager"]);
  const handleCloseDeleteModal = () => {
    if (!isLoading) {
      setClientDeleteValues(DEFAULT_CLIENT_DELETE_VALUES);
    }
  };

  const deleteClient = () => {
    if (clientDeleteValues.id) {
      handleDeleteClient(clientDeleteValues.id);
      handleCloseDeleteModal();
    }
  };

  return (
    <MainModal
      title={t("common:delete-client")}
      isOpen={Boolean(clientDeleteValues.flow)}
      handleClose={handleCloseDeleteModal}
      maxWidth="sm"
      helperText={t("common:delete-client-confirm")}
    >
      <DialogActions sx={{ display: "flex", alignItems: "center" }}>
        <CeButton
          variant="contained"
          size="large"
          color="primary"
          disabled={isLoading}
          onClick={deleteClient}
        >
          <Typography
            sx={{
              fontSize: "16px",
              letterSpacing: "0.4px",
            }}
          >
            {t(`common:delete-client`)}
          </Typography>
        </CeButton>
        <CeButton
          variant="text"
          color="primary"
          size="large"
          onClick={handleCloseDeleteModal}
          disabled={isLoading}
        >
          <Typography
            sx={{
              fontSize: "16px",
              letterSpacing: "0.4px",
            }}
          >
            {t("common:cancel")}
          </Typography>
        </CeButton>
      </DialogActions>
    </MainModal>
  );
};

export default DeleteClientModal;
