import { addMinutes } from "date-fns";
import {
  DeleteVehicleModalValues,
  FilterVehiclesFormValues,
  OpenVehicleInformationValues,
  VehicleFormValues,
  VehicleTypeCode,
  VehicleTypeEnum,
} from "../types";

export const vehicleTypeCodeMap = {
  [VehicleTypeEnum.PUMP]: VehicleTypeCode.PUMP,
  [VehicleTypeEnum.CITY_PUMP]: VehicleTypeCode.CITY_PUMP,
  [VehicleTypeEnum.MIXO_PUMP]: VehicleTypeCode.MIXO_PUMP,
  [VehicleTypeEnum.STATIONARY_PUMP]: VehicleTypeCode.STATIONARY_PUMP,
  [VehicleTypeEnum.MIXER]: VehicleTypeCode.MIXER,
};

export const VEHICLE_FORM_VALUES: VehicleFormValues = {
  vehicleId: null,
  operator: null,
  type: null,
  typeOfMotorization: null,
  vehicleBrand: "",
  brandModel: "",
  licensePlateNumber: "",
  weight: null,
  height: null,
  length: null,
  width: null,
  boomSize: null,
  maxVerticalReach: null,
  maxHorizontalReach: null,
  hasStrangler: false,
  invoicingPipesFrom: null,
  pipeLengthForSecondTechnician: null,
  endHoseLength: null,
  maxFlowRate: null,
  maxConcretePressure: null,
  availableFlexiblePipeLength80Mm: null,
  availableFlexiblePipeLength90Mm: null,
  availableFlexiblePipeLength100Mm: null,
  availableFlexiblePipeLength120Mm: null,
  availableRigidPipeLength: null,
  maxDownwardReach: null,
  numberOfBoomSections: null,
  minUnfoldingHeight: null,
  boomRotation: null,
  frontSideOpening: null,
  rearSideOpening: null,
  frontOutriggerSpan: null,
  rearOutriggerSpan: null,
  frontPressureOnOutrigger: null,
  rearPressureOnOutrigger: null,
  boomUnfoldingSystem: null,
  bacExit: false,
  bacExitReverse: false,
  siteAddress: "",
  country: "",
  location: {
    type: "Point",
    coordinates: [0, 0],
  },
  uniqueIdentificationNumber: "0",
  completedJobs: 0,
  flow: null,
};

export const VEHICLE_FILTER_VALUES_DEFAULT: FilterVehiclesFormValues = {
  dateFrom: new Date().toString(),
  dateTo: addMinutes(new Date(), 180).toString(),
  type: "Pump",
  boomSize: null,
  pipeDiameter: null,
  requiredPipes: null,
  frontOutriggerSpan: null,
  rearOutriggerSpan: null,
  flow: null,
  coordinates: null,
  radius: 20000,
};

export const VEHICLE_INFORMATION_VALUES_DEFAULT: OpenVehicleInformationValues =
  {
    vehicleId: null,
    flow: null,
  };

export const VEHICLE_DELETE_DEFAULT: DeleteVehicleModalValues = {
  flow: null,
};

export const vehicleValuesRanges = {
  boomSize: {
    min: 0,
    max: 100,
  },
  cityPump: {
    min: 0,
    max: 36,
  },
  weight: {
    min: 0,
    max: 100,
  },
  height: {
    min: 0,
    max: 10,
  },
  length: {
    min: 0,
    max: 100,
  },
  width: {
    min: 0,
    max: 10,
  },
  maxVerticalReach: {
    min: 0,
    max: 100,
  },
  maxHorizontalReach: {
    min: 0,
    max: 100,
  },
  endHoseLength: {
    min: 0,
    max: 100,
  },
  maxFlowRate: {
    min: 0,
    max: 500,
  },
  maxConcretePressure: {
    min: 0,
    max: 500,
  },
  availableFlexiblePipeLength80Mm: {
    min: 0,
    max: 500,
  },
  availableFlexiblePipeLength90Mm: {
    min: 0,
    max: 500,
  },
  availableFlexiblePipeLength100Mm: {
    min: 0,
    max: 500,
  },
  availableFlexiblePipeLength120Mm: {
    min: 0,
    max: 500,
  },
  availableRigidPipeLength: {
    min: 0,
    max: 500,
  },
  maxDownwardReach: {
    min: 0,
    max: 100,
  },
  numberOfBoomSections: {
    min: 0,
    max: 100,
  },
  minUnfoldingHeight: {
    min: 0,
    max: 100,
  },
  boomRotation: {
    min: -500,
    max: 500,
  },
  frontOutriggerSpan: {
    min: 0,
    max: 100,
  },
  rearOutriggerSpan: {
    min: 0,
    max: 100,
  },
  frontPressureOnOutrigger: {
    min: 0,
    max: 100,
  },
  rearPressureOnOutrigger: {
    min: 0,
    max: 100,
  },
  completedJobs: {
    min: 0,
    max: 10000,
  },
};
