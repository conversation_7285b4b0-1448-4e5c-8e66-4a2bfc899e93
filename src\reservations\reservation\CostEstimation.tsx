import {
  Box,
  CardContent,
  Grid,
  SxProps,
  Theme,
  Typography,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { CeCard } from "src/common/components";
import {
  ClassifiedReservationSchedule,
  numberIntoTimePeriod,
} from "src/common/utils";

interface CostEstimationProps {
  flatFee: number;
  flatFeeNight: number;
  flatFeeWeekend: number;
  extraCementBags: number;
  additionalHour: number;
  additionalHourNight: number;
  additionalHourWeekend: number;
  flexiblePipeLength80Mm: number;
  flexiblePipeLength90Mm: number;
  rigidPipeLength100: number;
  rigidPipeLength120: number;
  amountOfConcrete: number;
  cleaningFee: number;
  totalCost: number;
  supplyOfTheChemicalSlushieFee: number;
  barbotineFee: number;
  transportTierFee: number;
  classifiedReservationSchedule: ClassifiedReservationSchedule;
  totalDayContractFee: number;
  totalOvertimeFee: number;
  totalDayContractDuration: number;
  totalOvertimeDuration: number;
}

const cardStyle: SxProps<Theme> = {
  mt: 2.5,
  padding: "10px 5px",
  borderRadius: "12px 12px 0 0",
  "& > *": { padding: "16px" },
};

const costGridStyle: SxProps<Theme> = {
  borderBottom: "solid #3333 1.5px",
  padding: "0 10px 10px",
};

const typographyStyle: SxProps<Theme> = {
  fontSize: "14px",
  letterSpacing: "0.17",
  fontWeight: "400",
  lineHeight: "20.02px",
};

const CostEstimation = ({
  flatFee,
  extraCementBags,
  additionalHour,
  flexiblePipeLength80Mm,
  flexiblePipeLength90Mm,
  rigidPipeLength100,
  rigidPipeLength120,
  amountOfConcrete,
  cleaningFee,
  totalCost,
  supplyOfTheChemicalSlushieFee,
  barbotineFee,
  transportTierFee,
  flatFeeNight,
  flatFeeWeekend,
  additionalHourNight,
  additionalHourWeekend,
  classifiedReservationSchedule,
  totalDayContractFee,
  totalOvertimeFee,
  totalDayContractDuration,
  totalOvertimeDuration,
}: CostEstimationProps) => {
  const { t } = useTranslation(["common", "dispatcher"]);
  const flatFeeDuration = numberIntoTimePeriod(
    classifiedReservationSchedule.flatPackage.regularWorkingTime,
    "minutes"
  );
  const flatFeeDurationNight = numberIntoTimePeriod(
    classifiedReservationSchedule.flatPackage.nightWorkingTime,
    "minutes"
  );
  const flatFeeDurationWeekend = numberIntoTimePeriod(
    classifiedReservationSchedule.flatPackage.weekendWorkingTime,
    "minutes"
  );

  const GridItem = ({
    description,
    cost,
    customStyle,
  }: {
    description: string;
    cost: number | string;
    customStyle?: SxProps<Theme>;
  }) => {
    return (
      <Grid item container xs={12} sx={costGridStyle}>
        <Grid item xs={8}>
          <Typography sx={{ ...typographyStyle, ...customStyle }}>
            {description}
          </Typography>
        </Grid>
        <Grid item sx={{ display: "flex", justifyContent: "flex-end" }} xs={4}>
          <Typography sx={{ ...typographyStyle, ...customStyle }}>
            {cost} €
          </Typography>
        </Grid>
      </Grid>
    );
  };

  return (
    <CeCard sx={cardStyle}>
      <CardContent>
        <Grid container columnSpacing={0} rowSpacing={1}>
          <Grid item xs={12}>
            <Typography
              fontSize={20}
              fontWeight="400"
              paddingBottom={1}
              textAlign="center"
            >
              {t("common:cost-estimation")}
            </Typography>
          </Grid>

          <GridItem
            description={t("common:services")}
            cost={t("common:cost")}
            customStyle={{ fontWeight: "500" }}
          />

          <GridItem
            description={t("common:flat-fee", {
              duration: flatFeeDuration || 0,
            })}
            cost={flatFee}
          />
          <GridItem
            description={t("common:flat-fee-night", {
              duration: flatFeeDurationNight || 0,
            })}
            cost={flatFeeNight}
          />
          <GridItem
            description={t("common:flat-fee-weekend", {
              duration: flatFeeDurationWeekend || 0,
            })}
            cost={flatFeeWeekend}
          />
          <GridItem
            description={`${t(
              "common:day-contract-fee"
            )} (${totalDayContractDuration} ${t("hours")})`}
            cost={totalDayContractFee}
          />
          <GridItem
            description={`${t(
              "common:day-contract-overtime-rate"
            )} (${totalOvertimeDuration} ${t("hours")})`}
            cost={totalOvertimeFee}
          />
          <GridItem
            description={t("extra-cement-bags")}
            cost={extraCementBags}
          />
          <GridItem
            description={t("common:additional-hour-beyond")}
            cost={additionalHour}
          />
          <GridItem
            description={t("common:night-hour")}
            cost={additionalHourNight}
          />
          <GridItem
            description={t("common:weekend-additional-hour")}
            cost={additionalHourWeekend}
          />
          <GridItem
            description={t("common:flexible-pipe-80")}
            cost={flexiblePipeLength80Mm}
          />
          <GridItem
            description={t("common:flexible-pipe-90")}
            cost={flexiblePipeLength90Mm}
          />
          <GridItem
            description={t("common:rigid-pipe-100")}
            cost={rigidPipeLength100}
          />
          <GridItem
            description={t("common:rigid-pipe-120")}
            cost={rigidPipeLength120}
          />

          <GridItem
            description={t("common:concrete-amount")}
            cost={amountOfConcrete}
          />
          <GridItem description={t("transport-rate")} cost={transportTierFee} />
          <GridItem
            description={t("common:supply-of-the-chemical-slushie")}
            cost={supplyOfTheChemicalSlushieFee}
          />
          <GridItem description={t("common:barbotine")} cost={barbotineFee} />
          <GridItem description={t("common:cleaning")} cost={cleaningFee} />

          <Grid item display="flex" justifyContent="flex-end" xs={12}>
            <Typography variant="h6" fontWeight="500">
              {t("common:total")} {totalCost} €
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Box alignItems="center" display="flex;">
              <Typography align="center" color="grey" variant="caption">
                {t("common:info-text")}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </CeCard>
  );
};

export default CostEstimation;
