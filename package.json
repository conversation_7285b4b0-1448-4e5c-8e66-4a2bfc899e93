{"name": "web", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.9.3", "@emotion/styled": "^11.9.3", "@fontsource/roboto": "^4.5.7", "@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/list": "^6.1.10", "@fullcalendar/react": "^6.1.10", "@fullcalendar/resource-timeline": "^6.1.10", "@hugeicons/react": "^0.6.2", "@mui/icons-material": "^5.10.6", "@mui/lab": "^5.0.0-alpha.102", "@mui/material": "^5.10.8", "@mui/x-charts": "^6.19.8", "@mui/x-data-grid": "^5.17.5", "@mui/x-data-grid-pro": "^5.17.5", "@mui/x-date-pickers": "^5.0.3", "@mui/x-date-pickers-pro": "^5.0.3", "@react-google-maps/api": "^2.19.2", "@react-hook/window-size": "^3.0.7", "@rollup/plugin-terser": "^0.4.4", "@testing-library/jest-dom": "^5.16.3", "@testing-library/react": "^12.1.4", "@testing-library/user-event": "^13.5.0", "@twilio/audioplayer": "^1.0.6", "@twilio/conversations": "^2.2.1", "@twilio/voice-sdk": "^2.2.0", "@types/jest": "^27.4.1", "@types/node": "^16.11.26", "@types/react": "^17.0.43", "@types/react-dom": "^17.0.14", "@types/react-signature-canvas": "^1.0.2", "autosuggest-highlight": "^3.3.4", "axios": "^0.26.1", "date-fns": "^2.28.0", "date-fns-tz": "^1.3.4", "dayjs": "^1.11.7", "dnd-core": "^15.1.2", "emoji-picker-react": "^3.6.2", "eslint-plugin-security-node": "^1.1.1", "formik": "^2.2.9", "fullcalendar": "^6.1.10", "html2canvas": "^1.4.1", "i18next": "^23.7.16", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.4.2", "immutability-helper": "^3.1.1", "iso8601-duration": "^2.1.1", "jspdf": "^2.5.1", "jszip": "^3.10.1", "jwt-decode": "^3.1.2", "material-ui-phone-number": "^3.0.0", "mustache": "^4.2.0", "prepend-http": "^4.0.0", "prettier": "^2.6.1", "query-string": "^7.1.1", "react": "^17.0.2", "react-cool-dimensions": "^2.0.7", "react-dnd": "^15.1.2", "react-dnd-html5-backend": "^15.1.3", "react-dom": "^17.0.2", "react-draggable": "^4.4.5", "react-dropzone": "^12.0.5", "react-geolocated": "^4.1.2", "react-hot-toast": "^2.2.0", "react-i18next": "^14.0.0", "react-jwt": "^1.2.0", "react-password-checklist": "^1.3.3", "react-query": "^3.34.17", "react-responsive": "^9.0.0", "react-router-dom": "^6.2.2", "react-scripts": "5.0.0", "react-signature-canvas": "1.0.6", "react-to-print": "^2.15.1", "recharts": "^2.1.9", "recoil": "^0.6.1", "typescript": "^4.6.3", "use-debounce": "^7.0.1", "use-query-params": "^2.0.0", "use-react-router-breadcrumbs": "^3.2.1", "util": "^0.12.4", "uuid": "^8.3.2", "web-vitals": "^2.1.4", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint \"src/**/*.tsx\"", "lint:fix": "eslint \"src/**/*.tsx\" --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/autosuggest-highlight": "^3.2.3", "@types/mustache": "^4.2.1", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "eslint-plugin-security": "^1.7.1"}}