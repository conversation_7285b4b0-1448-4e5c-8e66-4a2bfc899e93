import {
  Checkbox,
  FormControl,
  FormControlLabel,
  InputAdornment,
  Stack,
} from "@mui/material";
import * as yup from "yup";
import { useTranslation } from "react-i18next";

import { useFormik } from "formik";
import { JobReportFormValues } from "src/common/types";
import { SetterOrUpdater } from "recoil";
import { CeTextField } from "src/common/components";
interface JobReportFormProps {
  isLoading: boolean;
  initialFormValues: JobReportFormValues;
  setInitialFormValues: SetterOrUpdater<JobReportFormValues>;
}

export const JobReportForm: React.FC<JobReportFormProps> = ({
  setInitialFormValues,
  isLoading,
  initialFormValues,
}) => {
  const { t } = useTranslation(["common", "manager", "dispatcher", "operator"]);

  const formik = useFormik<JobReportFormValues>({
    initialValues: initialFormValues,
    enableReinitialize: true,
    validationSchema: yup.object({}),
    onSubmit: () => {},
  });

  return (
    <Stack
      component="form"
      spacing={2}
      padding={2}
      marginTop={2}
      noValidate
      onSubmit={formik.handleSubmit}
    >
      <CeTextField
        fullWidth
        id="amountOfConcrete"
        name="job.amountOfConcrete"
        label={t("common:amount-of-concrete")}
        size="small"
        type="number"
        required
        value={formik.values.amountOfConcrete || ""}
        error={
          !!formik.errors.amountOfConcrete && formik.touched.amountOfConcrete
        }
        helperText={
          formik.touched.amountOfConcrete && formik.errors.amountOfConcrete
        }
        InputProps={{
          endAdornment: <InputAdornment position="end">㎥</InputAdornment>,
        }}
        onChange={(event) => {
          const value = Number(event.target.value);

          setInitialFormValues((values: JobReportFormValues) => {
            return {
              ...values,
              amountOfConcrete: value,
            };
          });
        }}
        InputLabelProps={{ shrink: true }}
        disabled={isLoading}
      />

      <Stack direction={"row"} spacing={2}>
        <CeTextField
          fullWidth
          id="flexiblePipeLength80Mm"
          name="flexiblePipeLength80Mm"
          label={t("common:price-per-meter-of-flexible-pipe-length-80Mm")}
          size="small"
          type="number"
          InputProps={{
            endAdornment: <InputAdornment position="end">€/m</InputAdornment>,
          }}
          InputLabelProps={{ shrink: true }}
          value={formik.values.flexiblePipeLength80Mm || ""}
          onChange={(event) => {
            const value = Number(event.target.value);

            setInitialFormValues((values: JobReportFormValues) => {
              return {
                ...values,
                flexiblePipeLength80Mm: value,
              };
            });
          }}
          error={
            formik.touched.flexiblePipeLength80Mm &&
            Boolean(formik.errors.flexiblePipeLength80Mm)
          }
          helperText={
            formik.touched.flexiblePipeLength80Mm &&
            formik.errors.flexiblePipeLength80Mm
          }
          disabled={isLoading}
        />
        <CeTextField
          fullWidth
          id="flexiblePipeLength90Mm"
          name="flexiblePipeLength90Mm"
          label={t("common:price-per-meter-of-flexible-pipe-length-90Mm")}
          size="small"
          type="number"
          InputProps={{
            endAdornment: <InputAdornment position="end">€/m</InputAdornment>,
          }}
          InputLabelProps={{ shrink: true }}
          value={formik.values.flexiblePipeLength90Mm || ""}
          onChange={(event) => {
            const value = Number(event.target.value);

            setInitialFormValues((values: JobReportFormValues) => {
              return {
                ...values,
                flexiblePipeLength90Mm: value,
              };
            });
          }}
          error={
            formik.touched.flexiblePipeLength90Mm &&
            Boolean(formik.errors.flexiblePipeLength90Mm)
          }
          helperText={
            formik.touched.flexiblePipeLength90Mm &&
            formik.errors.flexiblePipeLength90Mm
          }
          disabled={isLoading}
        />
      </Stack>
      <Stack direction={"row"} spacing={2}>
        <CeTextField
          fullWidth
          id="rigidPipeLength100Mm"
          name="rigidPipeLength100Mm"
          label={t("common:price-per-meter-of-flexible-pipe-length-100Mm")}
          size="small"
          type="number"
          InputProps={{
            endAdornment: <InputAdornment position="end">€/m</InputAdornment>,
          }}
          InputLabelProps={{ shrink: true }}
          value={formik.values.rigidPipeLength100Mm || ""}
          onChange={(event) => {
            const value = Number(event.target.value);

            setInitialFormValues((values: JobReportFormValues) => {
              return {
                ...values,
                rigidPipeLength100Mm: value,
              };
            });
          }}
          error={
            formik.touched.rigidPipeLength100Mm &&
            Boolean(formik.errors.rigidPipeLength100Mm)
          }
          helperText={
            formik.touched.rigidPipeLength100Mm &&
            formik.errors.rigidPipeLength100Mm
          }
          disabled={isLoading}
        />
        <CeTextField
          fullWidth
          id="rigidPipeLength120Mm"
          name="rigidPipeLength120Mm"
          label={t("common:price-per-meter-of-rigid-pipe-length-120Mm")}
          size="small"
          type="number"
          InputProps={{
            endAdornment: <InputAdornment position="end">€/m</InputAdornment>,
          }}
          InputLabelProps={{ shrink: true }}
          value={formik.values.rigidPipeLength120Mm || ""}
          onChange={(event) => {
            const value = Number(event.target.value);

            setInitialFormValues((values: JobReportFormValues) => {
              return {
                ...values,
                rigidPipeLength120Mm: value,
              };
            });
          }}
          error={
            formik.touched.rigidPipeLength120Mm &&
            Boolean(formik.errors.rigidPipeLength120Mm)
          }
          helperText={
            formik.touched.rigidPipeLength120Mm &&
            formik.errors.rigidPipeLength120Mm
          }
          disabled={isLoading}
        />
      </Stack>

      <CeTextField
        fullWidth
        id="cleaningTime"
        name="cleaningTime"
        label={t("common:cleaning-time")}
        size="small"
        type="number"
        required
        value={formik.values.cleaningTime}
        error={!!formik.errors.cleaningTime && formik.touched.cleaningTime}
        helperText={formik.touched.cleaningTime && formik.errors.cleaningTime}
        onBlur={() => formik.setFieldTouched("cleaningTime", true)}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              {t("common:minutes")}
            </InputAdornment>
          ),
        }}
        onChange={(event) => {
          const cleaningTime = parseInt(event.target.value, 10);
          // Update the cleaningTime field
          setInitialFormValues((values: JobReportFormValues) => {
            return {
              ...values,
              cleaningTime: cleaningTime,
            };
          });
        }}
        InputLabelProps={{ shrink: true }}
        disabled={isLoading}
      />

      <Stack spacing={0} flexDirection={"row"} alignItems={"center"}>
        <FormControl fullWidth>
          <FormControlLabel
            value={formik.values.extraCementBags}
            control={<Checkbox />}
            label={`${t("common:extra-cement-bags")}`}
            name="extraCementBag"
            onChange={(event: any) => {
              const value = Boolean(event.target?.value);
              setInitialFormValues((values: JobReportFormValues) => {
                return {
                  ...values,
                  extraCementBags: value,
                };
              });
            }}
          />
        </FormControl>

        <CeTextField
          fullWidth
          id="cementBags"
          name="cementBags"
          label={t("common:cement-bags")}
          size="small"
          type="number"
          value={formik.values.cementBags || ""}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">{t("units")}</InputAdornment>
            ),
          }}
          onChange={(event) => {
            const value = parseInt(event.target.value, 10);
            // Update the cleaningTime field
            setInitialFormValues((values: JobReportFormValues) => {
              return {
                ...values,
                cementBags: value,
              };
            });
          }}
          InputLabelProps={{ shrink: true }}
          disabled={isLoading}
        />
      </Stack>

      <Stack>
        <FormControl fullWidth>
          <FormControlLabel
            value={formik.values.secondTechnician}
            control={<Checkbox />}
            label={t("common:second-technician")}
            name="secondTechnician"
            onChange={(event: any) => {
              const value = Boolean(event.target?.value);
              setInitialFormValues((values: JobReportFormValues) => {
                return {
                  ...values,
                  secondTechnician: value,
                };
              });
            }}
          />
        </FormControl>
      </Stack>
    </Stack>
  );
};
