import {
  <PERSON>,
  Button,
  <PERSON>alogA<PERSON>,
  Stack,
  Step,
  <PERSON><PERSON><PERSON><PERSON>,
  Stepper,
  Typography,
} from "@mui/material";
import {
  CreateVehicleDto,
  UpdateVehicleDto,
  VehicleFormValues,
} from "src/common/types";
import { useState } from "react";
import { VehicleDetailsOneForm } from "./formSteps/VehicleDetailsOneForm";
import { VehicleDetailsTwoForm } from "./formSteps/VehicleDetailsTwoForm";
import { SetterOrUpdater } from "recoil";
import { useTranslation } from "react-i18next";
import { buttonTextTransform } from "src/common/components/custom/customCss";

interface VehicleFormProps {
  isLoading: boolean;
  initialFormValues: VehicleFormValues;
  setInitialFormValues: SetterOrUpdater<VehicleFormValues>;
  handleClose: () => void;
  handleCreateNewVehicle?: (args: CreateVehicleDto) => void;
  handleUpdateVehicle?: (args: UpdateVehicleDto) => void;
}

export const VehicleForm: React.FC<VehicleFormProps> = ({
  handleClose,
  isLoading,
  handleCreateNewVehicle,
  handleUpdateVehicle,
  setInitialFormValues,
  initialFormValues,
}) => {
  const { t } = useTranslation(["manager", "dispatcher", "common"]);
  const steps = [t("vehicle-details-one"), t("vehicle-details-two")];
  const [activeStep, setActiveStep] = useState(0);

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleReset = () => {
    setActiveStep(0);
  };

  return (
    <Stack spacing={2} sx={{ width: "500px" }}>
      <DialogActions>
        <Box sx={{ width: "100%" }}>
          <Stepper activeStep={activeStep}>
            {steps.map((label) => {
              const stepProps: { completed?: boolean } = {};
              const labelProps: {
                optional?: React.ReactNode;
              } = {};

              return (
                <Step key={label} {...stepProps}>
                  <StepLabel {...labelProps}>{label}</StepLabel>
                </Step>
              );
            })}
          </Stepper>
          {activeStep === steps.length ? (
            <>
              <Typography sx={{ mt: 2, mb: 1 }}>
                {t("steps-completed")}
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "row", pt: 2 }}>
                <Box sx={{ flex: "1 1 auto" }} />
                <Button
                  sx={{ mx: "0", ...buttonTextTransform }}
                  onClick={handleReset}
                >
                  {t("reset")}
                </Button>
              </Box>
            </>
          ) : (
            <>
              <Stack sx={{ paddingTop: 3 }}></Stack>
            </>
          )}
        </Box>
      </DialogActions>
    </Stack>
  );
};
