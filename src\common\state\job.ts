import { atom } from "recoil";
import { JOB_REPORT_FORM_VALUES, JOB_SECURITY_VALIDATION_FORM_VALUES } from "../constants";
import { JobReportFormValues, SecurityValidationFormValues } from "../types";

export const jobReportFormValuesState = atom<JobReportFormValues>({
  key: "jobReportFormValuesState",
  default: JOB_REPORT_FORM_VALUES,
});

export const jobSecurityValidationFormValuesState =
  atom<SecurityValidationFormValues>({
    key: "jobSecurityValidationFormValuesState",
    default: JOB_SECURITY_VALIDATION_FORM_VALUES,
  });
