import { Grid } from "@mui/material";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useForgotPassword } from "src/common/api";
import { ForgotPasswordForm } from "./ForgotPasswordForm";

export const ForgotPassword: React.FC = () => {
  const navigate = useNavigate();
  const {
    mutate: handleSubmitForm,
    isLoading,
    isSuccess,
  } = useForgotPassword();
  const redirectTo = "/auth/emailsent";

  useEffect(() => {
    if (!isLoading && isSuccess) {
      navigate(redirectTo, { replace: true });
    }
  }, [isLoading, isSuccess, redirectTo, navigate]);

  return (
    <Grid
      container
      direction="column"
      justifyContent="center"
      alignItems="center"
    >
      <ForgotPasswordForm
        handleSubmitForm={handleSubmitForm}
        isLoading={isLoading}
      />
    </Grid>
  );
};
