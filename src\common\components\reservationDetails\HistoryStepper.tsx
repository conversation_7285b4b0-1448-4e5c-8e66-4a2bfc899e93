import React from "react";
import { Box, Stepper, Step, StepLabel, Typography } from "@mui/material";
import { JobEvent, JobStatus, Reservation } from "src/common/types";
import { JOB_STEPS } from "src/common/constants";
import { formatDateWithoutTimezone } from "src/common/utils/reservationDetails";
import { useTranslation } from "react-i18next";
import { CePaper } from "src/common/components";

interface HistoryStepperProps {
  status: string;
  progress: number;
  jobEvents: JobEvent[];
  getStepIcon: (
    stepName: string,
    currentStatus: string,
    currentProgress: number,
    jobEvents: JobEvent[]
  ) => JSX.Element;
  reservation: Reservation | undefined;
}

const HistoryStepper: React.FC<HistoryStepperProps> = ({
  status,
  progress,
  jobEvents,
  getStepIcon,
  reservation
}) => {
  const { t } = useTranslation(["dispatcher", "common", "manager"]);

  const getJobEventTimestamp = (jobEvents: JobEvent[], status: JobStatus) => {
    const event = jobEvents.find((event) =>
      event.statusDescription.includes(status)
    );
    return event ? formatDateWithoutTimezone(new Date(event.updated_at)) : null;
  };
  const reservationCancelled = status === JobStatus.CANCELLED;

  const stepBeforeCancel = JOB_STEPS.find(
    (step) => step.name === jobEvents.slice(-2)[0]?.currentStatus
  );

  return (
    <Box
      component="section"
      display="flex"
      flexDirection="column"
      gap={1}
      sx={{ width: "100%" }}
    >
      <CePaper
        sx={{
          padding: 3,
          marginBottom: 2
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: "bold", marginBottom: 2 }}>
          {t("history")}
        </Typography>
        <Box display="flex" flexDirection={{ xs: "column", md: "row" }} gap={1}>
          <Box flex={1}>
            <Stepper
              orientation="vertical"
              sx={{
                "& .MuiStepConnector-root": {
                  display: "none"
                }
              }}
            >
              {JOB_STEPS.map(
                (step, index) =>
                  step.name !== JobStatus.CANCELLED && (
                    <Step active={step.name === status} key={step.name}>
                      <StepLabel
                        StepIconComponent={(props) =>
                          getStepIcon(
                            step.name,
                            reservationCancelled
                              ? stepBeforeCancel?.name!
                              : status,
                            reservationCancelled
                              ? stepBeforeCancel?.progress!
                              : progress,
                            jobEvents
                          )
                        }
                        sx={{ marginLeft: 0.5 }}
                      >
                        <Typography variant="body1" fontWeight={700}>
                          {t(step.label)}
                        </Typography>
                        <Typography color="text.secondary" variant="caption">
                          {getJobEventTimestamp(
                            jobEvents,
                            step.name as JobStatus
                          )}
                        </Typography>
                      </StepLabel>
                    </Step>
                  )
              )}
            </Stepper>
          </Box>

          {/* Order Information Section */}
          <Box flex={1} display="flex" flexDirection="column" gap={1}>
            <CePaper
              elevation={0}
              sx={{
                padding: 3,
                mb: 2,
                border: "1px solid #eeeeee"
              }}
            >
              <Typography variant="h6" sx={{ fontWeight: "bold", mb: 2 }}>
                {t("order-information")}
              </Typography>
              <Typography
                variant="body2"
                color="text.disabled"
                sx={{ marginBottom: 1 }}
              >
                {t("order-time")}
              </Typography>
              <Typography variant="body2">
                {reservation?.job?.start
                  ? formatDateWithoutTimezone(new Date(reservation.job.start))
                  : "-"}
              </Typography>
              <Typography
                variant="body2"
                color="text.disabled"
                sx={{ marginBottom: 0.5, marginTop: 3 }}
              >
                {t("invoiced-time")}
              </Typography>
              <Typography variant="body2">
                {reservation?.job?.end
                  ? formatDateWithoutTimezone(new Date(reservation.job.end))
                  : "-"}
              </Typography>
              <Typography
                variant="body2"
                color="text.disabled"
                sx={{ marginBottom: 0.5, marginTop: 3 }}
              >
                {t("completion-time")}
              </Typography>
              <Typography variant="body2">
                {reservation?.job?.end
                  ? formatDateWithoutTimezone(new Date(reservation.job.end))
                  : "-"}
              </Typography>
            </CePaper>

            {/* Signature Section */}
            <CePaper
              elevation={0}
              sx={{ padding: 3, border: "1px solid #eeeeee" }}
            >
              <Typography variant="h6" sx={{ fontWeight: "bold", mb: 2 }}>
                {t("signature")}
              </Typography>
              <Typography variant="body2" sx={{ marginBottom: 2 }}>
                {reservation?.clientDetails?.name}{" "}
                {reservation?.clientDetails?.lastName}
              </Typography>
              <Typography variant="body2">
                {reservation?.clientDetails?.phoneNumber}
              </Typography>
              <Box
                sx={{
                  width: "100%",
                  height: 100,
                  marginTop: 1,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center"
                }}
              >
                <Typography variant="body2" color="text.secondary">
                  {t("signature")}
                </Typography>
              </Box>
            </CePaper>
          </Box>
        </Box>
      </CePaper>
    </Box>
  );
};

export default HistoryStepper;
