import { Box } from "@mui/material";
import { useTranslation } from "react-i18next";
import { CeButton } from "src/common/components";
interface VehicleFormButtonsProps {
  formik: any;
  activeStep: number;
  steps: string[];
  handleBack: () => void;
}

export const VehicleFormButtons: React.FC<VehicleFormButtonsProps> = ({
  formik,
  activeStep,
  steps,
  handleBack
}) => {
  const { t } = useTranslation(["common", "manager", "dispatcher"]);
  const isLastStep = activeStep === steps.length - 1;
  return (
    <Box sx={{ display: "flex", flexDirection: "row", pt: 2 }}>
      <CeButton
        variant="text"
        color="inherit"
        disabled={activeStep === 0}
        onClick={handleBack}
        sx={{ mr: 1 }}
      >
        {t("back")}
      </CeButton>
      <Box sx={{ flex: "1 1 auto" }} />

      <CeButton type={"submit"}>
        {isLastStep ? t("complete") : t("next")}
      </CeButton>
    </Box>
  );
};
