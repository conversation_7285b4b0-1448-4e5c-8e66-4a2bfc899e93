import {
  Add01Icon,
  Calendar03Icon,
  LayoutTable01Icon,
  LeftToRightListBulletIcon,
} from "@hugeicons/react";
import { Tab<PERSON><PERSON>xt, TabList, Tab<PERSON>anel } from "@mui/lab";
import { Stack, Tab, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { Ce<PERSON>utton, CePaper } from "src/common/components";
import {
  CreateTaskDto,
  Task,
  TaskFormFlow,
  TaskViews,
  UpdateTaskDto,
} from "src/common/types/tasks";
import BoardView from "./views/BoardView";
import CalendarView from "./views/calendarView/CalendarView";
import ListView from "./views/ListView";
import CreateTaskDrawer from "./taskdrawer/CreateTaskDrawer";
import { taskFormValuesState } from "src/common/state/task";
import { useRecoilState } from "recoil";
import { TASK_FORM_VALUES_DEFAULT } from "src/common/constants/task";
import {
  useCreateTask,
  useCreateTaskComment,
  useDeleteTask,
  useTasks,
  useUpdateTask,
} from "src/common/api/tasks";
import { turnTaskIntoFormValues } from "src/common/utils/task";
import { useCreateTaskLabel, useUpdateTaskLabel } from "src/common/api/labels";
import { useTranslation } from "react-i18next";

export const Tasks = () => {
  const [taskFormValues, setTaskFormValues] =
    useRecoilState(taskFormValuesState);
  const [view, setView] = useState<TaskViews>(TaskViews.CALENDAR);

  const { t } = useTranslation(["common", "dispatcher"]);

  const {
    data: allTasks,
    isLoading: isLoadingTasks,
    refetch: refetchTasks,
  } = useTasks({ expressions: [], sortModel: [] });

  const {
    mutateAsync: handleCreateTaskAsync,
    isLoading: isCreateTaskLoading,
    isSuccess: isCreateTaskSuccess,
  } = useCreateTask();

  const { mutate: handleUpdateTask, isLoading: isUpdatingTask } =
    useUpdateTask();
  const { mutate: handleDeleteTask } = useDeleteTask();

  const {
    mutate: handleCreateComment,
    isLoading: isLoadingComment,
    isError,
    isSuccess,
  } = useCreateTaskComment();

  const { mutate: handleCreateLabel, isLoading: isLoadingCreateLabel } =
    useCreateTaskLabel();
  const { mutate: handleUpdateLabel, isLoading: isLoadingUpdateLabel } =
    useUpdateTaskLabel();

  const closeTaskDrawer = () => {
    setTaskFormValues(TASK_FORM_VALUES_DEFAULT);
  };

  const onCreateTask = async (taskData: CreateTaskDto) => {
    const dataCreated = await handleCreateTaskAsync(taskData);
    closeTaskDrawer();
    return dataCreated;
  };

  const onUpdateTask = (taskToUpdate: UpdateTaskDto) => {
    handleUpdateTask(taskToUpdate);
    closeTaskDrawer();
  };
  const onDeleTask = (taskId: number) => {
    handleDeleteTask({ taskId });
    closeTaskDrawer();
  };
  const handleViewChange = (
    event: React.SyntheticEvent,
    newValue: TaskViews
  ) => {
    setView(newValue);
  };

  const openUpdateTaskDrawer = (task: Task) => {
    const taskFormValues = turnTaskIntoFormValues(task);
    setTaskFormValues(taskFormValues);
  };

  return (
    <>
      <CePaper elevation={1} sx={{ p: 2 }}>
        <TabContext value={view}>
          <Stack
            direction="row"
            alignItems="flex-end"
            justifyContent="space-between"
          >
            <TabList
              onChange={handleViewChange}
              sx={{
                height: 55,
                "& button": { py: 0 },
              }}
            >
              <Tab
                label={
                  <Typography
                    sx={{
                      fontSize: "14px",
                      letterSpacing: "0.4px",

                      fontWeight: 700,
                      textTransform: "initial",
                    }}
                  >
                    {t("calendar")}
                  </Typography>
                }
                value={TaskViews.CALENDAR}
                icon={
                  <Calendar03Icon size={20} variant="stroke" type="rounded" />
                }
                iconPosition="start"
              />
              <Tab
                label={
                  <Typography
                    sx={{
                      fontSize: "14px",
                      letterSpacing: "0.4px",
                      fontWeight: 700,
                      textTransform: "initial",
                    }}
                  >
                    {t("list")}
                  </Typography>
                }
                value={TaskViews.LIST}
                icon={
                  <LeftToRightListBulletIcon
                    size={20}
                    variant="stroke"
                    type="rounded"
                  />
                }
                iconPosition="start"
              />
              <Tab
                label={
                  <Typography
                    sx={{
                      fontSize: "14px",
                      letterSpacing: "0.4px",
                      fontWeight: 700,
                      textTransform: "initial",
                    }}
                  >
                    {t("board")}
                  </Typography>
                }
                value={TaskViews.BOARD}
                icon={
                  <LayoutTable01Icon
                    size={20}
                    variant="stroke"
                    type="rounded"
                  />
                }
                iconPosition="start"
              />
            </TabList>
            <CeButton
              variant="contained"
              startIcon={
                <Add01Icon size={16} color={"currentColor"} variant={"solid"} />
              }
              size="medium"
              color="primary"
              onClick={() =>
                setTaskFormValues({
                  ...taskFormValues,
                  flow: TaskFormFlow.CREATE,
                })
              }
            >
              {t("add-task")}
            </CeButton>
          </Stack>
          <TabPanel value={TaskViews.CALENDAR}>
            <CalendarView
              taskData={allTasks?.data || []}
              onDragorResize={handleUpdateTask}
              clickEventHandler={openUpdateTaskDrawer}
            />
          </TabPanel>
          <TabPanel value={TaskViews.LIST}>
            <ListView
              taskData={allTasks?.data || []}
              onTaskDrag={handleUpdateTask}
              onListTaskClick={openUpdateTaskDrawer}
            />
          </TabPanel>
          <TabPanel value={TaskViews.BOARD}>
            <BoardView
              taskData={allTasks?.data || []}
              onBoardCardClick={openUpdateTaskDrawer}
              onTaskDrag={handleUpdateTask}
            />
          </TabPanel>
        </TabContext>
      </CePaper>
      <CreateTaskDrawer
        handleUpdateTask={onUpdateTask}
        handleCreateTask={onCreateTask}
        initialFormValues={taskFormValues}
        handleCloseTaskDrawer={closeTaskDrawer}
        handleCreateComment={handleCreateComment}
        handleDeleteTask={onDeleTask}
        handleCreateLabel={handleCreateLabel}
        handleUpdateLabel={handleUpdateLabel}
      />
    </>
  );
};
