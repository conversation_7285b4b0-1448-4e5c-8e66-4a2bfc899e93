import { Company, CompanyFormValues, UpdateCompanyDto } from "../types/company";
import countries from "./countries";
import { turnDateIntoStrWithoutTimezone } from "./formatDate";

export const turnCompanyIntoFormValues = (
  company: Company
): CompanyFormValues => {
  const country = countries.find((c) => c.value === company.country);
  return {
    companyId: company.id,
    name: company.name || "",
    type: company.type || "",
    contactEmail: company.contactEmail || "",
    billingEmail: company.billingEmail || "",
    planningEmail: company.planningEmail || "",
    phoneNumber: company.phoneNumber || "",
    address: company.address || "",
    secondaryAddress: company.secondaryAddress || "",
    country: country || null,
    city: company.city || "",
    zipCode: company.zipCode || null,
    vatNumber: company.vatNumber || "",
    taxes: company.taxes || [{ name: "", percentage: null }],
  };
};

export const turnCompanyFormValuesIntoUpdateDto = (
  values: CompanyFormValues
): UpdateCompanyDto => {
  const {
    companyId,
    name,
    type,
    contactEmail,
    billingEmail,
    planningEmail,
    phoneNumber,
    address,
    secondaryAddress,
    country,
    city,
    zipCode,
    vatNumber,
    taxes,
  } = values;

  return {
    companyId: companyId!,
    name: name || "",
    type: type || "",
    contactEmail,
    billingEmail,
    planningEmail,
    phoneNumber,
    address,
    secondaryAddress,
    country: country?.value || "",
    city,
    zipCode,
    vatNumber,
    taxes: taxes || [],
  };
};
