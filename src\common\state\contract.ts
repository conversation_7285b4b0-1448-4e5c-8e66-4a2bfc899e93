import { atom } from "recoil";
import { CancelContractValues, ContractFormValues } from "../types";
import {
  CANCEL_CONTRACT_DEFAULT_FORM_VALUES,
  CONTRACT_DEFAULT_FORM_VALUES,
} from "../constants";

export const contractFormValuesState = atom<ContractFormValues>({
  key: "contractFormValuesState",
  default: CONTRACT_DEFAULT_FORM_VALUES,
});

export const cancelOrSuspendContractState = atom<CancelContractValues>({
  key: "cancelContractFormValuesState",
  default: CANCEL_CONTRACT_DEFAULT_FORM_VALUES,
});
