import { atom } from "recoil";
import { DeleteDispatcherModalValues, DispatcherFormValues } from "../types";
import {
  DISPATCHER_DELETE_DEFAULT,
  DISPATCHER_FORM_VALUES_DEFAULT,
} from "../constants/dispatcher";

export const dispatcherFormValuesState = atom<DispatcherFormValues>({
  key: "dispatcherFormValuesState",
  default: DISPATCHER_FORM_VALUES_DEFAULT,
});

export const dispatcherDeleteValuesState = atom<DeleteDispatcherModalValues>({
  key: "dispatcherDeleteValuesState",
  default: DISPATCHER_DELETE_DEFAULT,
});
