import { Alert, Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { DeleteModal } from "src/common/components";
import {
  DeleteOperatorDto,
  DeleteVehicleDto,
  VehicleModalDeleteFlow,
} from "src/common/types";

interface ReservationModalDeleteProps {
  flow: VehicleModalDeleteFlow;
  vehicleTitle?: string;
  isLoading: boolean;
  vehicleId?: number;
  handleCloseVehicleModalDelete: () => void;
  handleDeleteVehicle: (args: DeleteVehicleDto) => void;
}
export const VehicleModalDelete: React.FC<ReservationModalDeleteProps> = ({
  flow,
  vehicleTitle,
  vehicleId,
  isLoading,
  handleCloseVehicleModalDelete,
  handleDeleteVehicle,
}) => {
  const { t } = useTranslation("common");

  const onDeleteVehicle = () => {
    if (vehicleId) {
      handleDeleteVehicle({ vehicleId });
    }
  };

  const DeleteModalHelperText = () => (
    <Box>
      <Typography sx={{ marginBottom: 1 }}>
        {t("delete-vehicle-message", {
          vehicleTitle: vehicleTitle || "unknown vehicle",
        })}
      </Typography>
    </Box>
  );

  return (
    <DeleteModal
      isLoading={isLoading}
      title="Delete vehicle"
      helperText={DeleteModalHelperText()}
      isOpen={flow === "Delete"}
      handleSubmit={() => onDeleteVehicle()}
      handleClose={handleCloseVehicleModalDelete}
    />
  );
};
