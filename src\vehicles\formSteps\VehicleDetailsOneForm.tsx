import {
  Autocomplete,
  Box,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  InputAdornment,
  Stack,
  Typography,
} from "@mui/material";
import { FormikProvider, useFormik } from "formik";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import {
  BoomUnfoldingSystemType,
  CreateVehicleDto,
  MotorisationType,
  Role,
  UpdateVehicleDto,
  User,
  Vehicle,
  VehicleFormValues,
  VehicleMotorisationTypes,
  VehicleTypes,
  boomUnfoldingSystemTypes,
  vehicleTypes,
} from "src/common/types";
import { number, string } from "yup";
import { vehicleValuesRanges } from "src/common/constants";
import { VehicleFormButtons } from "./VehicleFormButtons";
import { SetterOrUpdater } from "recoil";
import {
  getCurrentUser,
  useUsers,
  useVehicleModels,
  useVehicleTypes,
} from "src/common/api";
import { CeButton, CeTextField } from "src/common/components";
import AutocompleteInput from "src/common/components/custom/AutocompleteGoogleInput";
import { turnVehicleFormValuesIntoCreateVehicleDto } from "src/common/utils";

interface VehicleDetailsOneFormProps {
  initialFormValues: VehicleFormValues;
  title: string;
  isLoading: boolean;
  handleCreateNewVehicle?: (args: CreateVehicleDto) => void;
  handleUpdateVehicle?: (args: UpdateVehicleDto) => void;
  setInitialFormValues: SetterOrUpdater<VehicleFormValues>;
  existingVehicles: Vehicle[];
  getValuesFromExistingVehicle: (vehicle: Vehicle) => void;
}

export const VehicleDetailsOneForm: React.FC<VehicleDetailsOneFormProps> = ({
  initialFormValues,
  title,
  isLoading,
  setInitialFormValues,
  handleCreateNewVehicle,
  handleUpdateVehicle,
  existingVehicles,
  getValuesFromExistingVehicle,
}) => {
  const currentUser = getCurrentUser();
  const { data: allOperators } = useUsers(
    {
      sortModel: [],
      expressions: [
        {
          category: '"user"."id"',
          operator: "!=",
          value: currentUser?.id,
          conditionType: "AND",
        },
      ],
    },
    Boolean(currentUser?.id)
  );

  const operators = allOperators?.data || [];

  const { t } = useTranslation("common");

  const formik = useFormik<VehicleFormValues>({
    initialValues: initialFormValues,
    enableReinitialize: true,
    validationSchema: yup.object({
      type: yup.string().required("required").nullable(),
      siteAddress: yup
        .string()
        .required("Address is required")
        .matches(/\d+/, "Street number is required"),
      city: yup.string(),
      plz: yup.number(),
      location: yup.object().nullable(),
      typeOfMotorization: string().required("required").nullable(),
      vehicleBrand: string().required("required").nullable(),
      brandModel: string().required("required").nullable(),
      licensePlateNumber: string()
        .max(100, "license-plate-number-exceeds")
        .required("required"),
      boomSize: number()
        .min(vehicleValuesRanges["boomSize"].min, t("out-of-range"))
        .max(vehicleValuesRanges["weight"].max, t("out-of-range")),
      weight: number()
        .min(vehicleValuesRanges["weight"].min, t("out-of-range"))
        .max(vehicleValuesRanges["weight"].max, t("out-of-range"))
        .required("required")
        .nullable(),
      height: number()
        .min(vehicleValuesRanges["height"].min, t("out-of-range"))
        .max(vehicleValuesRanges["height"].max, t("out-of-range"))
        .required("required")
        .nullable(),
      length: number()
        .min(vehicleValuesRanges["length"].min, t("out-of-range"))
        .max(vehicleValuesRanges["length"].max, t("out-of-range"))
        .required("required")
        .nullable(),
      width: number()
        .min(vehicleValuesRanges["width"].min, t("out-of-range"))
        .max(vehicleValuesRanges["width"].max, t("out-of-range"))
        .nullable(),
      maxVerticalReach: number()
        .min(vehicleValuesRanges["maxVerticalReach"].min, t("out-of-range"))
        .max(vehicleValuesRanges["maxVerticalReach"].max, t("out-of-range"))
        .required("required")
        .nullable(),
      maxHorizontalReach: number()
        .min(vehicleValuesRanges["maxHorizontalReach"].min, t("out-of-range"))
        .max(vehicleValuesRanges["maxHorizontalReach"].max, t("out-of-range"))
        .required("required")
        .nullable(),
      endHoseLength: number()
        .min(vehicleValuesRanges["endHoseLength"].min, t("out-of-range"))
        .max(vehicleValuesRanges["endHoseLength"].max, t("out-of-range"))
        .nullable(),
      maxFlowRate: number()
        .required("required")
        .min(vehicleValuesRanges["maxFlowRate"].min, t("out-of-range"))
        .max(vehicleValuesRanges["maxFlowRate"].max, t("out-of-range"))
        .nullable()
        .nullable(),
      maxConcretePressure: number()
        .min(vehicleValuesRanges["maxConcretePressure"].min, t("out-of-range"))
        .max(vehicleValuesRanges["maxConcretePressure"].max, t("out-of-range"))
        .nullable(),
      availableFlexiblePipeLength80Mm: number()
        .min(
          vehicleValuesRanges["availableFlexiblePipeLength80Mm"].min,
          t("out-of-range")
        )
        .max(
          vehicleValuesRanges["availableFlexiblePipeLength80Mm"].max,
          t("out-of-range")
        )
        .required("required")
        .nullable(),
      availableFlexiblePipeLength90Mm: number()
        .min(
          vehicleValuesRanges["availableFlexiblePipeLength90Mm"].min,
          t("out-of-range")
        )
        .max(
          vehicleValuesRanges["availableFlexiblePipeLength90Mm"].max,
          t("out-of-range")
        )
        .required("required")
        .nullable(),
      availableFlexiblePipeLength100Mm: number()
        .min(
          vehicleValuesRanges["availableFlexiblePipeLength100Mm"].min,
          t("out-of-range")
        )
        .max(
          vehicleValuesRanges["availableFlexiblePipeLength100Mm"].max,
          t("out-of-range")
        )
        .required("required")
        .nullable(),
      availableFlexiblePipeLength120Mm: number()
        .min(
          vehicleValuesRanges["availableFlexiblePipeLength120Mm"].min,
          t("out-of-range")
        )
        .max(
          vehicleValuesRanges["availableFlexiblePipeLength120Mm"].max,
          t("out-of-range")
        )
        .required("required")
        .nullable(),
      maxDownwardReach: number()
        .min(vehicleValuesRanges["maxDownwardReach"].min, t("out-of-range"))
        .max(vehicleValuesRanges["maxDownwardReach"].max, t("out-of-range"))
        .required("required")
        .nullable(),
      numberOfBoomSections: number()
        .min(vehicleValuesRanges["numberOfBoomSections"].min, t("out-of-range"))
        .max(vehicleValuesRanges["numberOfBoomSections"].max, t("out-of-range"))
        .nullable(),
      minUnfoldingHeight: number()
        .min(vehicleValuesRanges["minUnfoldingHeight"].min, t("out-of-range"))
        .max(vehicleValuesRanges["minUnfoldingHeight"].max, t("out-of-range"))
        .nullable(),
      boomRotation: number()
        .min(vehicleValuesRanges["boomRotation"].min, t("out-of-range"))
        .max(vehicleValuesRanges["boomRotation"].max, t("out-of-range"))
        .nullable(),
      frontOutriggerSpan: number()
        .min(vehicleValuesRanges["frontOutriggerSpan"].min, t("out-of-range"))
        .max(vehicleValuesRanges["frontOutriggerSpan"].max, t("out-of-range"))
        .nullable(),
      rearOutriggerSpan: number()
        .min(vehicleValuesRanges["rearOutriggerSpan"].min, t("out-of-range"))
        .max(vehicleValuesRanges["rearOutriggerSpan"].max, t("out-of-range"))
        .nullable(),
      pipeLengthForSecondTechnician: number().required("required").nullable(),
      boomUnfoldingSystem: string().required("required").nullable(),
    }),
    onSubmit: (values) => {
      if (initialFormValues.flow === "Create" && handleCreateNewVehicle) {
        const payloadIntoDto = turnVehicleFormValuesIntoCreateVehicleDto({
          ...initialFormValues,
          ...values,
        });
        const payload: CreateVehicleDto = {
          ...payloadIntoDto,
          operatorId: values.operator?.id!,
        };
        handleCreateNewVehicle(payload);
      }

      if (initialFormValues.flow === "Update" && handleUpdateVehicle) {
        const payload: UpdateVehicleDto = {
          ...initialFormValues,
          ...values,
          vehicleId: initialFormValues.vehicleId!,
          operatorId: values.operator?.id!,
        };

        handleUpdateVehicle(payload);
      }
    },
  });

  return (
    <FormikProvider value={formik}>
      <Stack
        component="form"
        spacing={2}
        noValidate
        onSubmit={formik.handleSubmit}
      >
        <Box
          sx={{
            width: "100%",
            height: "70px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            position: "sticky",
            top: 0,
            zIndex: 10,
            borderBottom: "1px solid",
            borderColor: (theme) => theme.palette.divider,
            backgroundColor: "white",
          }}
        >
          <Typography variant="h6" letterSpacing={0.15} fontSize={20}>
            {title}
          </Typography>
        </Box>
        <Stack
          spacing={2}
          sx={{
            height: "calc(100vh - 100px)",
            overflowY: "scroll",
            width: "40vw",
          }}
        >
          <Stack sx={{ mt: 2, px: 2.5, gap: 3 }}>
            <Autocomplete
              id="type"
              fullWidth
              value={formik.values.type}
              onChange={(event: any, nextValue: VehicleTypes | null) => {
                formik.setFieldValue("type", nextValue);
              }}
              onBlur={() => formik.setFieldTouched("type", true)}
              options={vehicleTypes}
              getOptionLabel={(option: VehicleTypes) => option || ""}
              filterSelectedOptions
              renderInput={(params) => (
                <CeTextField
                  {...params}
                  InputLabelProps={{ shrink: true }}
                  error={formik.touched.type && Boolean(formik.errors.type)}
                  helperText={formik.touched.type && formik.errors.type}
                  required
                  label={t("type")}
                  size="small"
                />
              )}
            />
            <CeTextField
              fullWidth
              id="boomSize"
              name="boomSize"
              label={t("vehicle-boom-size")}
              size="small"
              type="number"
              InputLabelProps={{ shrink: true }}
              value={formik.values.boomSize || ""}
              onChange={formik.handleChange}
              error={formik.touched.boomSize && Boolean(formik.errors.boomSize)}
              helperText={formik.touched.boomSize && formik.errors.boomSize}
              disabled={isLoading}
            />

            {initialFormValues.flow === "Create" ? (
              <Autocomplete
                id="vehicle"
                fullWidth
                value={
                  existingVehicles.find(
                    (vehicle) => vehicle.id === initialFormValues.vehicleId
                  ) ?? null
                }
                onChange={(_, newValue: Vehicle | null) => {
                  if (newValue) {
                    getValuesFromExistingVehicle(newValue);
                  }
                }}
                isOptionEqualToValue={(option: Vehicle, value: Vehicle) =>
                  option.id === value.id
                }
                getOptionLabel={(vehicle: Vehicle) =>
                  `${vehicle.uniqueIdentificationNumber}` || ""
                }
                options={existingVehicles}
                renderInput={(params) => (
                  <CeTextField
                    {...params}
                    InputLabelProps={{ shrink: true }}
                    label={t("copy-from")}
                    size="small"
                  />
                )}
              />
            ) : null}
          </Stack>

          {/* Vehicle Specifications */}
          <Stack gap={2.5} px={3}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
                marginTop: 3,
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                {t("vehicle-specifications")}
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>

            <Stack direction={"row"} spacing={2}>
              <CeTextField
                fullWidth
                id="vehicleBrand"
                name="vehicleBrand"
                label={t("vehicle-brand")}
                size="small"
                type="text"
                InputLabelProps={{ shrink: true }}
                value={formik.values.vehicleBrand || ""}
                onChange={formik.handleChange}
                error={
                  formik.touched.vehicleBrand &&
                  Boolean(formik.errors.vehicleBrand)
                }
                helperText={
                  formik.touched.vehicleBrand && formik.errors.vehicleBrand
                }
                disabled={isLoading}
                required
              />

              <CeTextField
                fullWidth
                id="brandModel"
                name="brandModel"
                label={t("brand-model")}
                size="small"
                type="text"
                InputLabelProps={{ shrink: true }}
                value={formik.values.brandModel || ""}
                onChange={formik.handleChange}
                error={
                  formik.touched.brandModel && Boolean(formik.errors.brandModel)
                }
                helperText={
                  formik.touched.brandModel && formik.errors.brandModel
                }
                disabled={isLoading}
                required
              />
            </Stack>

            <Stack direction={"row"} spacing={2}>
              <Autocomplete
                id="typeOfMotorization"
                fullWidth
                value={formik.values.typeOfMotorization || null}
                onChange={(event: any, nextValues: MotorisationType | null) => {
                  if (nextValues) {
                    formik.setFieldValue("typeOfMotorization", nextValues);
                  }
                }}
                onBlur={() =>
                  formik.setFieldTouched("typeOfMotorization", true)
                }
                options={VehicleMotorisationTypes}
                filterSelectedOptions
                renderInput={(params) => (
                  <CeTextField
                    {...params}
                    InputLabelProps={{ shrink: true }}
                    error={
                      formik.touched.typeOfMotorization &&
                      Boolean(formik.errors.typeOfMotorization)
                    }
                    helperText={
                      formik.touched.typeOfMotorization &&
                      formik.errors.typeOfMotorization
                    }
                    required
                    label={t("manager:type-of-motorisation")}
                    size="small"
                  />
                )}
              />

              <CeTextField
                fullWidth
                id="licensePlateNumber"
                name="licensePlateNumber"
                label={t("license-plate-number")}
                size="small"
                type="text"
                InputLabelProps={{ shrink: true }}
                value={formik.values.licensePlateNumber || ""}
                onChange={formik.handleChange}
                error={
                  formik.touched.licensePlateNumber &&
                  Boolean(formik.errors.licensePlateNumber)
                }
                helperText={
                  formik.touched.licensePlateNumber &&
                  formik.errors.licensePlateNumber
                }
                disabled={isLoading}
                required
              />
            </Stack>

            <Stack direction={"row"} spacing={2}>
              <CeTextField
                fullWidth
                id="weight"
                name="weight"
                label={t("weight")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                value={formik.values.weight || ""}
                onChange={formik.handleChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">{t("tons")}</InputAdornment>
                  ),
                }}
                error={formik.touched.weight && Boolean(formik.errors.weight)}
                helperText={formik.touched.weight && formik.errors.weight}
                disabled={isLoading}
                required
              />
              <CeTextField
                fullWidth
                id="width"
                name="width"
                label={t("manager:width")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                value={formik.values.width || ""}
                onChange={formik.handleChange}
                error={formik.touched.width && Boolean(formik.errors.width)}
                helperText={formik.touched.width && formik.errors.width}
                disabled={isLoading}
                required
              />
            </Stack>

            <Stack direction={"row"} spacing={2}>
              <CeTextField
                fullWidth
                id="height"
                name="height"
                label={t("height")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                value={formik.values.height || ""}
                onChange={formik.handleChange}
                error={formik.touched.height && Boolean(formik.errors.height)}
                helperText={formik.touched.height && formik.errors.height}
                disabled={isLoading}
                required
              />
              <CeTextField
                fullWidth
                id="length"
                name="length"
                label={t("length")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                value={formik.values.length || ""}
                onChange={formik.handleChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                error={formik.touched.length && Boolean(formik.errors.length)}
                helperText={formik.touched.length && formik.errors.length}
                disabled={isLoading}
                required
              />
            </Stack>

            <Stack direction={"row"} spacing={2}>
              <CeTextField
                fullWidth
                id="frontOutriggerSpan"
                name="frontOutriggerSpan"
                label={t("front-outriggers-span")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                value={formik.values.frontOutriggerSpan || ""}
                onChange={formik.handleChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                error={
                  formik.touched.frontOutriggerSpan &&
                  Boolean(formik.errors.frontOutriggerSpan)
                }
                helperText={
                  formik.touched.frontOutriggerSpan &&
                  formik.errors.frontOutriggerSpan
                }
                disabled={isLoading}
              />
              <CeTextField
                fullWidth
                id="rearOutriggerSpan"
                name="rearOutriggerSpan"
                label={t("rear-outriggers-span")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                value={formik.values.rearOutriggerSpan || ""}
                onChange={formik.handleChange}
                error={
                  formik.touched.rearOutriggerSpan &&
                  Boolean(formik.errors.rearOutriggerSpan)
                }
                helperText={
                  formik.touched.rearOutriggerSpan &&
                  formik.errors.rearOutriggerSpan
                }
                disabled={isLoading}
              />
            </Stack>

            <Stack direction={"row"} spacing={2}>
              <CeTextField
                fullWidth
                id="frontSideOpening"
                name="frontSideOpening"
                label={t("front-side-opening")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                value={formik.values.frontSideOpening || ""}
                onChange={formik.handleChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                error={
                  formik.touched.frontSideOpening &&
                  Boolean(formik.errors.frontSideOpening)
                }
                helperText={
                  formik.touched.frontSideOpening &&
                  formik.errors.frontSideOpening
                }
                disabled={isLoading}
              />
              <CeTextField
                fullWidth
                id="rearSideOpening"
                name="rearSideOpening"
                label={t("rear-side-opening")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                value={formik.values.rearSideOpening || ""}
                onChange={formik.handleChange}
                error={
                  formik.touched.rearSideOpening &&
                  Boolean(formik.errors.rearSideOpening)
                }
                helperText={
                  formik.touched.rearSideOpening &&
                  formik.errors.rearSideOpening
                }
                disabled={isLoading}
              />
            </Stack>

            <Stack direction={"row"} spacing={2}>
              <CeTextField
                fullWidth
                id="frontPressureOnOutrigger"
                name="frontPressureOnOutrigger"
                label={t("front-pressure-on-outrigger")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                value={formik.values.frontPressureOnOutrigger || ""}
                onChange={formik.handleChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">kN</InputAdornment>
                  ),
                }}
                error={
                  formik.touched.frontPressureOnOutrigger &&
                  Boolean(formik.errors.frontPressureOnOutrigger)
                }
                helperText={
                  formik.touched.frontPressureOnOutrigger &&
                  formik.errors.frontPressureOnOutrigger
                }
                disabled={isLoading}
              />
              <CeTextField
                fullWidth
                id="rearPressureOnOutrigger"
                name="rearPressureOnOutrigger"
                label={t("rear-pressure-on-outrigger")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">kN</InputAdornment>
                  ),
                }}
                value={formik.values.rearPressureOnOutrigger || ""}
                onChange={formik.handleChange}
                error={
                  formik.touched.rearPressureOnOutrigger &&
                  Boolean(formik.errors.rearPressureOnOutrigger)
                }
                helperText={
                  formik.touched.rearPressureOnOutrigger &&
                  formik.errors.rearPressureOnOutrigger
                }
                disabled={isLoading}
              />
            </Stack>

            <Autocomplete
              id="operators"
              fullWidth
              value={formik.values.operator || null}
              onChange={(event: any, nextValues: User | null) => {
                formik.setFieldValue("operator", nextValues);
              }}
              onBlur={() => formik.setFieldTouched("operator", true)}
              isOptionEqualToValue={(option: User, value: User) =>
                option.id === value.id
              }
              getOptionLabel={(operator: User) =>
                `${operator.firstName || ""} ${operator.lastName}`
              }
              options={operators.map((op) => op)}
              filterSelectedOptions
              disabled={isLoading}
              renderInput={(params) => (
                <CeTextField
                  {...params}
                  InputLabelProps={{ shrink: true }}
                  label={t("operators")}
                  size="small"
                />
              )}
            />

            <AutocompleteInput
              id="siteAddress"
              name="siteAddress"
              label={t("common:site-address")}
              variant="outlined"
              onAddressChange={({
                city,
                postalCode,
                lat,
                lng,
                siteAddress,
              }) => {
                formik.setFieldValue("siteAddress", siteAddress);
                formik.setFieldValue("location", {
                  type: "Point",
                  coordinates: [lng, lat],
                });
                formik.setFieldValue("plz", postalCode);
                formik.setFieldValue("city", city);
              }}
              existingInputValue={
                formik.values.siteAddress ||
                formik.values.plz ||
                formik.values.city
                  ? `${formik.values.siteAddress || ""}, ${
                      formik.values.plz || ""
                    } ${formik.values.city || ""}`.replace(
                      /(^[,\s]+)|([,\s]+$)/g,
                      ""
                    )
                  : ""
              }
              disabled={isLoading}
            />

            <FormControl fullWidth sx={{ marginBottom: 2 }}>
              <FormControlLabel
                checked={formik.values.bacExit}
                control={<Checkbox />}
                label={t("bac-exit")}
                name="bacExit"
                onChange={() =>
                  formik.setFieldValue("bacExit", !formik.values.bacExit)
                }
              />

              <FormControlLabel
                checked={formik.values.bacExitReverse}
                control={<Checkbox />}
                label={t("bac-exit-reverse")}
                name="bacExitReverse"
                onChange={() =>
                  formik.setFieldValue(
                    "bacExitReverse",
                    !formik.values.bacExitReverse
                  )
                }
              />
              <FormControlLabel
                checked={formik.values.hasStrangler}
                control={<Checkbox />}
                label={t("strangler")}
                name="hasStrangler"
                onChange={() =>
                  formik.setFieldValue(
                    "hasStrangler",
                    !formik.values.hasStrangler
                  )
                }
              />
            </FormControl>
          </Stack>

          {/* Boom specifications */}
          <Stack gap={2.5} px={3}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
                marginTop: 3,
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                {t("boom-specifications")}
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>

            <Stack direction={"row"} spacing={2}>
              <CeTextField
                fullWidth
                id="maxVerticalReach"
                name="maxVerticalReach"
                label={t("vertical-reach")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                value={formik.values.maxVerticalReach || ""}
                onChange={formik.handleChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                error={
                  formik.touched.maxVerticalReach &&
                  Boolean(formik.errors.maxVerticalReach)
                }
                helperText={
                  formik.touched.maxVerticalReach &&
                  formik.errors.maxVerticalReach
                }
                disabled={isLoading}
                required
              />
              <CeTextField
                fullWidth
                id="maxHorizontalReach"
                name="maxHorizontalReach"
                label={t("horizontal-reach")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                value={formik.values.maxHorizontalReach || ""}
                onChange={formik.handleChange}
                error={
                  formik.touched.maxHorizontalReach &&
                  Boolean(formik.errors.maxHorizontalReach)
                }
                helperText={
                  formik.touched.maxHorizontalReach &&
                  formik.errors.maxHorizontalReach
                }
                disabled={isLoading}
                required
              />
            </Stack>

            <Stack direction={"row"} spacing={2}>
              <CeTextField
                fullWidth
                id="minUnfoldingHeight"
                name="minUnfoldingHeight"
                label={t("min-unfolding-height")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                value={formik.values.minUnfoldingHeight || ""}
                onChange={formik.handleChange}
                error={
                  formik.touched.minUnfoldingHeight &&
                  Boolean(formik.errors.minUnfoldingHeight)
                }
                helperText={
                  formik.touched.minUnfoldingHeight &&
                  formik.errors.minUnfoldingHeight
                }
                disabled={isLoading}
              />
              <CeTextField
                fullWidth
                id="maxDownwardReach"
                name="maxDownwardReach"
                label={t("max-downward-reach")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                value={formik.values.maxDownwardReach || ""}
                onChange={formik.handleChange}
                error={
                  formik.touched.maxDownwardReach &&
                  Boolean(formik.errors.maxDownwardReach)
                }
                helperText={
                  formik.touched.maxDownwardReach &&
                  formik.errors.maxDownwardReach
                }
                disabled={isLoading}
                required
              />
            </Stack>

            <Stack direction={"row"} spacing={2}>
              <CeTextField
                fullWidth
                id="numberOfBoomSections"
                name="numberOfBoomSections"
                label={t("number-of-boom-sections")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                value={formik.values.numberOfBoomSections || ""}
                onChange={formik.handleChange}
                error={
                  formik.touched.numberOfBoomSections &&
                  Boolean(formik.errors.numberOfBoomSections)
                }
                helperText={
                  formik.touched.numberOfBoomSections &&
                  formik.errors.numberOfBoomSections
                }
                disabled={isLoading}
              />
              <CeTextField
                fullWidth
                id="boomRotation"
                name="boomRotation"
                label={t("boom-rotation")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                value={formik.values.boomRotation || ""}
                onChange={formik.handleChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">°</InputAdornment>
                  ),
                }}
                error={
                  formik.touched.boomRotation &&
                  Boolean(formik.errors.boomRotation)
                }
                helperText={
                  formik.touched.boomRotation && formik.errors.boomRotation
                }
                disabled={isLoading}
              />

              <Autocomplete
                id="boomUnfoldingSystem"
                fullWidth
                value={formik.values.boomUnfoldingSystem || null}
                onChange={(
                  event: any,
                  nextValues: BoomUnfoldingSystemType | null
                ) => {
                  formik.setFieldValue("boomUnfoldingSystem", nextValues);
                }}
                onBlur={() =>
                  formik.setFieldTouched("boomUnfoldingSystem", true)
                }
                options={boomUnfoldingSystemTypes}
                filterSelectedOptions
                renderInput={(params) => (
                  <CeTextField
                    {...params}
                    InputLabelProps={{ shrink: true }}
                    error={
                      formik.touched.boomUnfoldingSystem &&
                      Boolean(formik.errors.boomUnfoldingSystem)
                    }
                    helperText={
                      formik.touched.boomUnfoldingSystem &&
                      formik.errors.boomUnfoldingSystem
                    }
                    required
                    label={t("boom-unfolding-system")}
                    size="small"
                  />
                )}
              />
            </Stack>

            <CeTextField
              fullWidth
              id="endHoseLength"
              name="endHoseLength"
              label={t("manager:endHoseLength")}
              size="small"
              type="number"
              InputLabelProps={{ shrink: true }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    {t("meter-symbol")}
                  </InputAdornment>
                ),
              }}
              value={formik.values.endHoseLength || ""}
              onChange={formik.handleChange}
              error={
                formik.touched.endHoseLength &&
                Boolean(formik.errors.endHoseLength)
              }
              helperText={
                formik.touched.endHoseLength && formik.errors.endHoseLength
              }
              disabled={isLoading}
            />
          </Stack>

          {/* Pump Specifications */}
          <Stack gap={2.5} px={3}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
                marginTop: 3,
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                {t("pump-specifications")}
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>

            <Stack direction={"row"} spacing={2}>
              <CeTextField
                fullWidth
                id="maxFlowRate"
                name="maxFlowRate"
                label={t("max-flow-rate")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                value={formik.values.maxFlowRate || ""}
                onChange={formik.handleChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                error={
                  formik.touched.maxFlowRate &&
                  Boolean(formik.errors.maxFlowRate)
                }
                helperText={
                  formik.touched.maxFlowRate && formik.errors.maxFlowRate
                }
                disabled={isLoading}
                required
              />
              <CeTextField
                fullWidth
                id="maxConcretePressure"
                name="maxConcretePressure"
                label={t("manager:max-concrete-pressure")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">{t("bar")}</InputAdornment>
                  ),
                }}
                value={formik.values.maxConcretePressure || ""}
                onChange={formik.handleChange}
                error={
                  formik.touched.maxConcretePressure &&
                  Boolean(formik.errors.maxConcretePressure)
                }
                helperText={
                  formik.touched.maxConcretePressure &&
                  formik.errors.maxConcretePressure
                }
                disabled={isLoading}
              />
            </Stack>
          </Stack>

          {/* Pipes */}
          <Stack gap={2.5} px={3}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                alignItems: "center",
                marginTop: 3,
              }}
            >
              <Typography
                variant="body1"
                fontWeight={700}
                letterSpacing={0.15}
                fontSize={16}
                color="text.disabled"
                lineHeight={1.5}
              >
                {t("pipes")}
              </Typography>
              <Divider variant="fullWidth" flexItem />
            </Box>

            <Stack direction={"row"} spacing={2}>
              <CeTextField
                fullWidth
                id="availableFlexiblePipeLength80Mm"
                name="availableFlexiblePipeLength80Mm"
                label={t("manager:available-flexible-pipe-length-80")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                value={formik.values.availableFlexiblePipeLength80Mm || ""}
                onChange={formik.handleChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                error={
                  formik.touched.availableFlexiblePipeLength80Mm &&
                  Boolean(formik.errors.availableFlexiblePipeLength80Mm)
                }
                helperText={
                  formik.touched.availableFlexiblePipeLength80Mm &&
                  formik.errors.availableFlexiblePipeLength80Mm
                }
                disabled={isLoading}
                required
              />
              <CeTextField
                fullWidth
                id="availableFlexiblePipeLength90Mm"
                name="availableFlexiblePipeLength90Mm"
                label={t("manager:available-flexible-pipe-length-90")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                value={formik.values.availableFlexiblePipeLength90Mm || ""}
                onChange={formik.handleChange}
                error={
                  formik.touched.availableFlexiblePipeLength90Mm &&
                  Boolean(formik.errors.availableFlexiblePipeLength90Mm)
                }
                helperText={
                  formik.touched.availableFlexiblePipeLength90Mm &&
                  formik.errors.availableFlexiblePipeLength90Mm
                }
                disabled={isLoading}
                required
              />
            </Stack>

            <Stack direction={"row"} spacing={2}>
              <CeTextField
                fullWidth
                id="availableFlexiblePipeLength100Mm"
                name="availableFlexiblePipeLength100Mm"
                label={t("manager:available-flexible-pipe-length-100")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                value={formik.values.availableFlexiblePipeLength100Mm || ""}
                onChange={formik.handleChange}
                error={
                  formik.touched.availableFlexiblePipeLength100Mm &&
                  Boolean(formik.errors.availableFlexiblePipeLength100Mm)
                }
                helperText={
                  formik.touched.availableFlexiblePipeLength100Mm &&
                  formik.errors.availableFlexiblePipeLength100Mm
                }
                disabled={isLoading}
                required
              />

              <CeTextField
                fullWidth
                id="availableFlexiblePipeLength120Mm"
                name="availableFlexiblePipeLength120Mm"
                label={t("manager:available-flexible-pipe-length-120")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                value={formik.values.availableFlexiblePipeLength120Mm || ""}
                onChange={formik.handleChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                error={
                  formik.touched.availableFlexiblePipeLength120Mm &&
                  Boolean(formik.errors.availableFlexiblePipeLength120Mm)
                }
                helperText={
                  formik.touched.availableFlexiblePipeLength120Mm &&
                  formik.errors.availableFlexiblePipeLength120Mm
                }
                disabled={isLoading}
                required
              />
            </Stack>

            <Stack direction={"row"} spacing={2}>
              <CeTextField
                fullWidth
                id="pipeLengthForSecondTechnician"
                name="pipeLengthForSecondTechnician"
                label={t("pipe-length-for-second-technician")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                value={formik.values.pipeLengthForSecondTechnician || ""}
                onChange={formik.handleChange}
                error={
                  formik.touched.pipeLengthForSecondTechnician &&
                  Boolean(formik.errors.pipeLengthForSecondTechnician)
                }
                helperText={
                  formik.touched.pipeLengthForSecondTechnician &&
                  formik.errors.pipeLengthForSecondTechnician
                }
                required
                disabled={isLoading}
              />
              <CeTextField
                fullWidth
                id="invoicingPipesFrom"
                name="invoicingPipesFrom"
                label={t("invoicing-pipes-from")}
                size="small"
                type="number"
                InputLabelProps={{ shrink: true }}
                value={formik.values.invoicingPipesFrom || ""}
                onChange={formik.handleChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {t("meter-symbol")}
                    </InputAdornment>
                  ),
                }}
                error={
                  formik.touched.invoicingPipesFrom &&
                  Boolean(formik.errors.invoicingPipesFrom)
                }
                helperText={
                  formik.touched.invoicingPipesFrom &&
                  formik.errors.invoicingPipesFrom
                }
                disabled={isLoading}
              />
            </Stack>
          </Stack>
        </Stack>

        <Box
          sx={{
            width: "100%",
            height: "70px",
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-end",
            position: "sticky",
            bottom: 0,
            pr: 2,
            zIndex: 10,
            gap: 2,
            borderTop: "1px solid",
            borderColor: (theme: any) => theme.palette.divider,
            backgroundColor: "white",
          }}
        >
          <CeButton
            variant="text"
            size="large"
            onClick={() => formik.resetForm()}
            disabled={isLoading}
          >
            {t("common:cancel")}
          </CeButton>
          <CeButton type="submit" size="large" disabled={isLoading}>
            {t("common:submit")}
          </CeButton>
        </Box>
      </Stack>
    </FormikProvider>
  );
};
