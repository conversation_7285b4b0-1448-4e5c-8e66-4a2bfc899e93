import { useWindowSize } from "@react-hook/window-size";
import { useState } from "react";
import { InvoiceListDatagrid } from "./datagrid/InvoiceListDatagrid";
import { useBulkInvoiceLog } from "src/common/api/invoices";
import { getCurrentUser } from "src/common/api";
import { CePaper } from "src/common/components";
import { useLocation } from "react-router-dom";
import usePersistentGridState from "src/common/utils/gridState";
import { InvoiceType } from "src/common/types";

export const Invoices = () => {
  const [, height] = useWindowSize();
  const currentUser = getCurrentUser();
  const location = useLocation();
  const localStorageKey = location.pathname.slice(1);
  const [gridState, updateGridStatePart] = usePersistentGridState(
    localStorageKey,
    1,
    20,
  );
  const [selectedInvoiceType, setSelectedInvoiceType] = useState<InvoiceType>(InvoiceType.BULK);

  const {
    data: allBulkInvoice,
    isLoading: isLoadingBulkInvoice,
    refetch: refetchBulkInvoice,
  } = useBulkInvoiceLog(
    {
      limit: gridState.pageSize,
      offset: (gridState.page - 1) * gridState.pageSize,
      type: selectedInvoiceType,
    },
    Boolean(currentUser?.id)
  );

  const handlePageChange = (newPage: number) => {
    updateGridStatePart("page", newPage + 1);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    updateGridStatePart("pageSize", newPageSize);
  };

  const handleInvoiceTypeChange = (newType: InvoiceType) => {
    setSelectedInvoiceType(newType);
  };

  return (
    <CePaper sx={{ height: `${height - 100}px`, p: 2 }}>
      <InvoiceListDatagrid
        data={allBulkInvoice?.data || []}
        isFetchingBulkInvoice={isLoadingBulkInvoice}
        refetchBulkInvoice={refetchBulkInvoice}
        shouldRenderRefreshButton
        gridState={gridState}
        total={allBulkInvoice?.totalCount || 0}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        updateGridStatePart={updateGridStatePart}
        selectedInvoiceType={selectedInvoiceType}
        onInvoiceTypeChange={handleInvoiceTypeChange}
      />
    </CePaper>
  );
};
