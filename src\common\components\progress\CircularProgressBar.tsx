import { Box, CircularProgress, Typography } from "@mui/material";

interface CircularProgressProps {
    value: number
}

export const CircularProgressWithLabel: React.FC<CircularProgressProps> = ({ value }) => (
    <Box sx={{ position: "relative", display: "inline-flex" }}>
        <CircularProgress variant="determinate" value={value} />
        <Box
            sx={{
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                position: "absolute",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
            }}
        >
            <Typography
                variant="caption"
                component="div"
                color="text.secondary"
            >{`${Math.round(value)}%`}</Typography>
        </Box>
    </Box>
);