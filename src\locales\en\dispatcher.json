{"add-company-to-favorites": "Add company to favorites", "advanced-view": "Planning view", "all": "All", "available-rigid-pipe-length": "Available rigid pipe length", "available-flex-pipe-length-eight": "Available flexible pipe (80 mm)", "available-flex-pipe-length-nine": "Available flexible pipe (90 mm)", "add-reservation": "Add Reservation", "additional-hour": "Additional hour", "add-dispatcher": "Add Dispatcher", "add-pricelist": "Add Price list", "add-variant": "<PERSON><PERSON>", "add-tariff": "Add additional tariff", "add-additional-tier": "Add additional Tier", "variant": "<PERSON><PERSON><PERSON>", "authorized-weight": "Authorized weight", "additional-information": "Additional information", "additional-hour-beyond": "Additional hour", "balance": "Balance", "backup-pump-package": "Backup pump package", "bag": "bag", "barbotine": "<PERSON><PERSON><PERSON>", "cleaning": "Cleaning:", "cleaning-fee": "Cleaning fee", "client-details": "Client details", "client-email": "Client email", "company-vat-number": "Company VAT number", "completed-jobs": "Completed jobs", "confirm-reservation": "Confirm reservation", "contract-price": "Contract price", "cost": "Cost", "cost-estimation": "Cost Estimation", "cleanup-in-concrete-plant": "Cleanup in concrete plant", "completed": "Completed", "completion-time": "Completion time:", "client-info": "Client info", "city": "City", "chartered-equipment": "Chartered equipment", "delivery-pressure": "Delivery pressure (bars)", "date-from": "From", "date-time": "Date/Time", "date-to": "To", "details-reservation": "Reservation details", "dimensions-varying-according-vehicle": "*Dimensions may vary according to vehicle assembly", "distance-asc": "Distance (closest to farthest)", "distance-desc": "Distance (farthest to closest)", "delete-dispatcher": "Delete dispatcher", "delete-reservation": "Delete reservation", "dispatchers": "Dispatchers", "details": "Details", "end-date": "End date", "empty-search-hint": "Fill in the address, date and vehicle type, then hit search to view results", "end-hose-length": "End hose length", "engine-type": "Engine type", "extra-cement-bag-price": "Extra cement bag price", "edit": "Edit", "enlist-second-technician": "Enlist second technician", "flow-rate": "Flow rate", "favorite-company": "Favorite company", "filter-by": "Filter by", "flat-fee": "Flat fee", "flat-fee-weekdays": "Flat fee (weekdays)", "flat-fee-weekend": "Flat fee (weekend)", "flexible-pipe-length-80mm": "Flexible pipe (80 mm)", "flexible-pipe-length-90mm": "Flexible pipe (90 mm)", "rigid-pipe-length-100mm": "Rigid pipe (100 mm)", "rigid-pipe-length-120mm": "Rigid pipe (120 mm)", "front-outrigger-span": "Front outrigger span", "horizontal-reach": "Horizontal reach", "hourly-rate": "Hourly rate", "height-restriction": "Height restriction", "height-limit": "Height limit", "history": "History", "info-text": "This number is only an estimation and may vary depending on external factors. Please advise the client that this price is subject to modification", "invoiced-time": "Invoiced time:", "job": "Job", "local-admin-authentication": "Local admin. authentication", "local-admin-authorization": "Local admin. authorization", "media": "Media", "make-a-reservation": "Make a reservation", "manager-phone-number": "Manager phone number", "max-concrete-pressure": "Max. concrete pressure", "max-downward-reach": "Max. downward reach", "maximum-concrete-pressure-rod-end": "Maximum concrete pressure (rod end)", "maximum-flow-rate-rod-end": "Maximum flow rate (rod end)", "no-uploads": "No uploads", "no-results": "Sorry, there are no results for your research", "no-available-vehicles": "Unfortunately, we couldn't find any vehicles matching your search criteria ", "operating-weight": "Operating weight", "out-coverage-fifty-km": "Out coverage 50km fee", "out-coverage-hundred-km": "Out coverage 100km fee", "only-favorites": "Only favorite companies", "order-information": "Order information", "order-time": "Order time", "power-line": "Power line", "pumped-volume": "Pumped volume", "pipes": "Pipes", "please-contact-manager-and-client": "Please ensure you have contacted both the client and the pump manager before deleting this reservation.", "price-per-meter-of-flexible-placed": "Price per meter of flexible pipe placed", "price-per-meter-of-rigid-placed": "Price per meter of rigid pipe placed", "price-per-meter-pumped": "Price per m³ pumped", "price-weekday-asc": "Hourly price weekday (lowest to highest)", "price-weekday-desc": "Hourly price weekday (highest to lowest)", "price-weekdays": "Price weekdays", "price-weekend": "Price weekend", "price-weekend-asc": "Hourly price weekend (lowest to highest)", "price-weekend-desc": "Hourly price weekend (highest to lowest)", "pump-company-name": "Pump company name", "package-flat-fee": "Package/Flat fee", "price-per-meter-of-flexible-pipe-length-80Mm": "Flexible pipe (80 mm)", "price-per-meter-of-flexible-pipe-length-90Mm": "Flexible pipe (90 mm)", "price-per-meter-of-flexible-pipe-length-100Mm": "Rigid pipe (100 mm)", "price-per-meter-of-rigid-pipe-length-120Mm": "Rigid pipe (120 mm)", "packageFlatFeeDuration": "Package/Flat fee duration", "pipeInvoicingStartsFrom": "Pipe invoicing starts from", "backupPumpPackage": "Backup pump package", "secondTechnicianFee": "2nd technician fee", "transport-rates": "Transport rates", "pump-tiers": "Tier pricing", "price-for-tier-1": "Price for Tier 1 (m³)", "price-for-tier-2": "Price for Tier 2 (m³)", "price-for-tier-3": "Price for Tier 3 (m³)", "price-for-tier-4": "Price for Tier 4 (m³)", "price-for-tier-5": "Price for Tier 5 (m³)", "price-for-tier-6": "Price for Tier 6 (m³)", "parking-on": "Parking on", "parking-permit-acquired": "Parking Permit Acquired", "pipe-starting-from-bac": "Pipe starting from BAC", "presence-of-power-lines": "Presence of power lines", "parking-permit": "Parking permit", "print": "Print", "reservation-order-number": "Order #2193jiwqq", "registration-number": "Registration number", "rear-outrigger-span": "Rear outrigger span", "reservation-complete": "Reservation complete", "reservation-confirmation-email": "Confirmation emails have been sent to the client and to the pump manager at the following addresses: ", "reset-filters": "Reset filters", "results": "results", "return-to-search-results": "Return to search results", "signature": "Signature", "secured": "Secured", "select": "Select", "side-outrigger-span": "Side outrigger span", "sort-by": "Sort by", "success-registered": "Your profile has been successfully registered. You can make a reservation here.", "status": "Status", "supply-of-the-chemical-slushie": "Supply of the chemical slushie", "start-date": "Start date", "total": "Total:", "title": "Title", "terrain-stability": "Terrain stability", "tonnage-restriction": "Tonnage restriction", "traffic-plan": "Traffic plan", "terrain": "Terrain", "type": "Type", "unavailable": "Unavailable", "units": "Units", "vehicle-name": "Vehicle name", "vehicle-company": "Vehicle/Company", "vehicle-details": "Vehicle details", "vehicle-height-folded": "Vehicle height (folded)", "vehicle-length-folded": "Vehicle length (folded)", "vehicle-width-folded": "Vehicle width (folded)", "vertical-reach": "Vertical reach", "view-reservations": "View reservations", "voltage": "Voltage", "vat-info": "All price listed are exclusive of VAT", "worksite": "Worksite", "worksite-name": "Worksite name", "weight-restriction": "Weight restriction", "at-least-one-dispatcher-required": "At least one dispatcher must be selected", "dispatchers-required": "Dispatchers are required", "total-reservations-per-month": "Total reservations per month", "total-expenses-per-month": "Total expenses per month", "average-job-completion-time": "Average job completion time", "average-m3-per-job": "Average m³ per job", "average-m3-per-hour": "Average m³ per hour", "total-expenses": "Total Expenses", "total-reservations": "Total Reservations", "expenses": "Expenses", "reservations": "Reservations", "most-active-contractors": "Most active contractors", "frequently-used-vehicles": "Frequently used vehicles", "company": "Company", "no-of-reservations": "No. of reservations", "total-invoiced": "Total invoiced", "vehicle": "Vehicle", "search": "Search", "day-contract": "Day contract", "day-contract-fee": "Day contract fee", "day-contract-duration": "Day contract duration", "day-contract-overtime-rate": "Day contract overtime rate", "weekend-pricing": "Weekend pricing", "weekend-flat-fee": "Package / flat fee (weekend)", "weekend-additional-hour": "Additional Hour (Weekend)", "night-pricing": "Night Pricing", "night-flat-fee": "Package / flat fee (night)", "night-hour": "Additional Hour (night)", "minimum-charge": "Minimum charge (Flat Fee)", "minimum-m3-charged": "Minimum m3 charged", "min-m3": "Minimum m³", "max-m3": "Maximum m³", "late-cancellation-fee": "Late cancellation fee ", "standard-cancellation-fee": "Standard cancellation fee", "standard-cancellation-period": "Standard cancellation period", "late-cancellation-period": "Late cancellation period", "incur-cancelation-fee": "This will incur a cancelation fee of {{cancelationFee}} EUR.", "confirm-reservation-cancelation": "Are you sure you want to cancel reservation {{reservationTitle}}?", "second-technician-fee-weekend": "2nd Technic<PERSON> (weekend)", "tier-pricing": "Tier Pricing"}