import axios, { AxiosError } from "axios";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  CreateFavoriteDto,
  Favorite,
  FavoritesWithCount,
  GetFavoriteDto,
  UpdateFavoriteDto,
} from "../types";
import { processApiError } from "../utils/errors";

const backendUrl = process.env.REACT_APP_API_URL;

export const getFavoriteList = async (attr: GetFavoriteDto) => {
  return axios
    .get(`${backendUrl}/favorites`, {
      withCredentials: true,
      params: attr,
    })
    .then((response) => response.data);
};
export const useGetFavorites = (
  attrs: GetFavoriteDto,
  enabled: boolean = true
) => {
  return useQuery<FavoritesWithCount, AxiosError | Error>(
    ["favorites", attrs],
    () => getFavoriteList(attrs),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch favorites", err),
      enabled,
    }
  );
};

export const favorite = (attrs: CreateFavoriteDto) => {
  return axios
    .post(`${backendUrl}/favorites`, attrs, { withCredentials: true })
    .then((response) => response.data);
};

export const useFavorite = () => {
  const queryClient = useQueryClient();
  return useMutation<
    Favorite,
    AxiosError | Error,
    CreateFavoriteDto,
    () => void
  >((a: CreateFavoriteDto) => favorite(a), {
    onSuccess: () => {
      queryClient.invalidateQueries("favorites");
      queryClient.invalidateQueries("vehicles");
    },
    onError: (err) => processApiError("Unable to create favorite", err),
  });
};

export const unFavorite = (updateFavoriteDto: UpdateFavoriteDto) => {
  const { dispatcherCompanyId, operatorCompanyId } = updateFavoriteDto;
  if (!dispatcherCompanyId) {
    throw new Error("the dispatcherCompanyId ID was not provided");
  }
  if (!operatorCompanyId) {
    throw new Error("the operatorCompanyId ID was not provided");
  }
  return axios
    .post(`${backendUrl}/favorites/unfavorite`, updateFavoriteDto, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useUnFavorite = () => {
  const queryClient = useQueryClient();
  return useMutation<
    Favorite,
    AxiosError | Error,
    UpdateFavoriteDto,
    () => void
  >((updateFavoriteDto: UpdateFavoriteDto) => unFavorite(updateFavoriteDto), {
    onSuccess: () => {
      queryClient.invalidateQueries("favorites");
      queryClient.invalidateQueries("vehicles");
    },
    onError: (err) => processApiError("Unable to un favorite", err),
  });
};
