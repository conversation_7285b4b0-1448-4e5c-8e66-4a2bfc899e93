import React, { useEffect } from "react";
import {
  Autocomplete,
  Box,
  Divider,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from "@mui/material";
import { useFormik } from "formik";
import * as yup from "yup";
import { useUpdateUser, useUser, useForgotPassword } from "src/common/api";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import enGB from "date-fns/locale/en-GB";
import {
  CeButton,
  CeMuiPhoneNumber,
  CeSelect,
  CeTextField,
  FeatureFlag,
} from "src/common/components";
import { useTranslation } from "react-i18next";
import { isValid } from "date-fns";
import { Calendar04Icon, Tick02Icon } from "@hugeicons/react";
import countries from "src/common/utils/countries";
import LanguageSwitcher from "src/common/components/languageSwitcher/LanguageSwitcher";
import { useRecoilState } from "recoil";
import { settingsUserFormValuesState } from "src/common/state";
import {
  turnUserFormValuesIntoUpdateDto,
  turnUserIntoFormValues,
} from "src/common/utils/user";
import { themeState } from "src/common/state";
import { PossibleTheme } from "src/common/types";

interface SettingsUserProfileProps {
  userId?: number;
}

export const SettingsUserProfile: React.FC<SettingsUserProfileProps> = ({
  userId,
}) => {
  const { t } = useTranslation(["common", "manager", "dispatcher"]);
  const [theme, setTheme] = useRecoilState(themeState);
  const handleThemeChange = (event: SelectChangeEvent) => {
    setTheme(event.target.value as PossibleTheme);
  };

  const [settingsUserFormValues, setSettingsUserFormValues] = useRecoilState(
    settingsUserFormValuesState
  );

  const {
    data: user,
    isLoading: isFullUserLoading,
    isSuccess: isFullUserSuccess,
    refetch: refetchUser,
  } = useUser(userId, Boolean(userId));

  const {
    mutate: handleUpdateUser,
    isSuccess: isUpdateUserSuccess,
    isLoading: isUpdatingUser,
    mutateAsync: handleUpdateUserAsync,
  } = useUpdateUser();

  const {
    mutate: handleForgotPassword,
    isLoading: isForgotPasswordLoading,
  } = useForgotPassword();

  const isLoading = isFullUserLoading || isUpdatingUser || isForgotPasswordLoading;

  const formik = useFormik({
    initialValues: settingsUserFormValues,
    enableReinitialize: true,
    validationSchema: yup.object({
      firstName: yup.string(),
      lastName: yup.string(),
      email: yup.string().email("Enter a valid email").nullable(),
      birthdate: yup.date().nullable(),
      phoneNumber: yup.string(),
      phoneNumberPersonal: yup.string(),
      address: yup.string().required("address is required"),
      zipCode: yup.string().required("zip code is required"),
      city: yup.string().required("City is required"),
      country: yup.object().required("Country is required").nullable(),
      companyType: yup.string().nullable(),
    }),
    onSubmit: async (values) => {
      if (user?.id) {
        const payload = turnUserFormValuesIntoUpdateDto({
          ...values,
          userId: user.id,
        });
        handleUpdateUser(payload);
      }
    },
  });

  useEffect(() => {
    if (user && isFullUserSuccess) {
      const userFormValues = turnUserIntoFormValues(user);
      setSettingsUserFormValues(userFormValues);
    }
  }, [user, isFullUserSuccess]);

  return (
    <form onSubmit={formik.handleSubmit}>
      {/* Profile */}
      <Box sx={{ overflow: "hidden", height: "calc(100vh - 20vh)" }}>
        <Box sx={{ overflow: "auto", height: "75vh", pb:6 }}>
          <Box sx={{ paddingY: 2, paddingX: 1.5 }}>
            <Box>
              <Typography
                variant="body1"
                color="text.disabled"
                fontWeight="bold"
                gutterBottom
              >
                {t("profile")}
              </Typography>
              <Divider sx={{ marginBottom: 2, marginTop: 2 }} />
            </Box>
            <Box
              display="flex"
              flexDirection="column"
              gap={3}
              sx={{ paddingY: 1.5, paddingX: 3, width: "90%" }}
            >
              {/* Name and Last Name */}
              <Box display="flex" alignItems="center" gap={2}>
                <Typography variant="body2" sx={{ flex: "30%" }}>
                  {t("name-lastname")}
                </Typography>
                <Box display="flex" flex="70%" gap={2}>
                  <Box flex="1">
                    <CeTextField
                      fullWidth
                      id="name"
                      name="firstName"
                      placeholder={t("name")}
                      size="small"
                      value={formik.values.firstName}
                      onChange={formik.handleChange}
                      error={
                        formik.touched.firstName &&
                        Boolean(formik.errors.firstName)
                      }
                      helperText={
                        formik.touched.firstName && formik.errors.firstName
                      }
                      disabled={isLoading}
                      required
                    />
                  </Box>
                  <Box flex="1">
                    <CeTextField
                      fullWidth
                      id="surname"
                      name="lastName"
                      placeholder={t("last-name")}
                      size="small"
                      value={formik.values.lastName}
                      onChange={formik.handleChange}
                      error={
                        formik.touched.lastName &&
                        Boolean(formik.errors.lastName)
                      }
                      helperText={
                        formik.touched.lastName && formik.errors.lastName
                      }
                      disabled={isLoading}
                      required
                    />
                  </Box>
                </Box>
              </Box>
              {/* Date of Birth */}
              <Box display="flex" alignItems="center" gap={2}>
                <Typography variant="body2" sx={{ flex: "30%" }}>
                  {t("date-of-birth")}
                </Typography>

                <Box sx={{ flex: "70%" }}>
                  <LocalizationProvider
                    dateAdapter={AdapterDateFns}
                    adapterLocale={enGB}
                  >
                    <DatePicker
                      value={formik.values.birthdate}
                      onChange={(newValue) => {
                        if (isValid(newValue)) {
                          formik.setFieldValue("birthdate", newValue);
                        } else {
                          formik.setFieldValue("birthdate", null);
                        }
                      }}
                      components={{
                        OpenPickerIcon: Calendar04Icon,
                      }}
                      renderInput={(params) => (
                        <CeTextField
                          {...params}
                          fullWidth
                          size="small"
                          placeholder={t("date-of-birth")}
                          error={
                            !!formik.errors.birthdate &&
                            formik.touched.birthdate
                          }
                          helperText={
                            formik.touched.birthdate && formik.errors.birthdate
                          }
                          disabled={isFullUserLoading}
                        />
                      )}
                    />
                  </LocalizationProvider>
                </Box>
              </Box>

              {/* Address */}
              <Box display="flex" alignItems="center" gap={2}>
                <Typography variant="body2" sx={{ flex: "30%" }}>
                  {t("address")}
                </Typography>

                <Box display="flex" flex="70%" gap={2} flexDirection="column">
                  <Box display="flex" gap={2}>
                    <Box flex="1">
                      <CeTextField
                        fullWidth
                        id="address"
                        name="address"
                        size="small"
                        placeholder={t("address-line")}
                        value={formik.values.address}
                        onChange={formik.handleChange}
                        error={
                          formik.touched.address &&
                          Boolean(formik.errors.address)
                        }
                        helperText={
                          formik.touched.address && formik.errors.address
                        }
                        disabled={isLoading}
                        required
                      />
                    </Box>
                    <Box flex="1">
                      <CeTextField
                        fullWidth
                        id="secondaryAddress"
                        name="secondaryAddress"
                        size="small"
                        placeholder={t("additional-address-line")}
                        value={formik.values.secondaryAddress}
                        onChange={formik.handleChange}
                        disabled={isLoading}
                      />
                    </Box>
                  </Box>
                </Box>
              </Box>

              {/* Region */}
              <Box>
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="body2" sx={{ flex: "30%" }}>
                    {t("region")}
                  </Typography>
                  <Box display="flex" flex="70%" gap={2}>
                    <Box flex="1">
                      <CeTextField
                        fullWidth
                        id="zipCode"
                        name="zipCode"
                        size="small"
                        placeholder={t("zip-code")}
                        value={formik.values.zipCode}
                        onChange={formik.handleChange}
                        error={
                          formik.touched.zipCode &&
                          Boolean(formik.errors.zipCode)
                        }
                        helperText={
                          formik.touched.zipCode && formik.errors.zipCode
                        }
                        disabled={isLoading}
                        required
                      />
                    </Box>
                    <Box flex="1">
                      <CeTextField
                        fullWidth
                        id="city"
                        name="city"
                        size="small"
                        placeholder={t("common:city")}
                        value={formik.values.city}
                        onChange={formik.handleChange}
                        error={
                          formik.touched.city && Boolean(formik.errors.city)
                        }
                        helperText={formik.touched.city && formik.errors.city}
                        disabled={isLoading}
                        required
                      />
                    </Box>
                    <Box flex="1">
                      <Autocomplete
                        fullWidth
                        id="country"
                        size="small"
                        value={formik.values.country}
                        onChange={(_, nextValue) => {
                          formik.setFieldValue("country", nextValue);
                        }}
                        options={countries}
                        getOptionLabel={(option) => option?.label || ""}
                        isOptionEqualToValue={(option, value) =>
                          option.value === value?.value
                        }
                        renderInput={(params) => (
                          <CeTextField
                            {...params}
                            InputLabelProps={{ shrink: true }}
                            error={
                              formik.touched.country &&
                              Boolean(formik.errors.country)
                            }
                            helperText={
                              formik.touched.country && formik.errors.country
                            }
                            disabled={isLoading}
                            required
                            placeholder={t("country")}
                            size="small"
                          />
                        )}
                      />
                    </Box>
                  </Box>
                </Box>
              </Box>

              {/* Phone Numbers */}
              <Box display="flex" alignItems="center" gap={2}>
                <Typography variant="body2" sx={{ flex: "30%" }}>
                  {t("phone-number")}
                </Typography>

                <Box sx={{ flex: "70%" }}>
                  <CeMuiPhoneNumber
                    fullWidth
                    defaultCountry={"be"}
                    id="phoneNumber"
                    name="phoneNumber"
                    placeholder={t("phone-number")}
                    size="small"
                    onBlur={() => formik.setFieldTouched("phoneNumber", true)}
                    InputLabelProps={{ shrink: true }}
                    value={formik.values.phoneNumber}
                    onChange={(value) =>
                      formik.setFieldValue("phoneNumber", value)
                    }
                    error={
                      formik.touched.phoneNumber &&
                      Boolean(formik.errors.phoneNumber)
                    }
                    helperText={
                      formik.touched.phoneNumber && formik.errors.phoneNumber
                    }
                    disabled={isLoading}
                    variant="outlined"
                  />
                </Box>
              </Box>

              <Box display="flex" alignItems="center" gap={2}>
                <Typography variant="body2" sx={{ flex: "30%" }}>
                  {t("phone-number-personal")}
                </Typography>

                <Box sx={{ flex: "70%" }}>
                  <CeMuiPhoneNumber
                    fullWidth
                    defaultCountry={"be"}
                    id="phoneNumberPersonal"
                    name="phoneNumberPersonal"
                    placeholder={t("phone-number-personal")}
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    value={formik.values.phoneNumberPersonal}
                    onChange={(value) =>
                      formik.setFieldValue("phoneNumberPersonal", value)
                    }
                    onBlur={() =>
                      formik.setFieldTouched("phoneNumberPersonal", true)
                    }
                    error={
                      formik.touched.phoneNumberPersonal &&
                      Boolean(formik.errors.phoneNumberPersonal)
                    }
                    helperText={
                      formik.touched.phoneNumberPersonal &&
                      formik.errors.phoneNumberPersonal
                    }
                    disabled={isLoading}
                    variant="outlined"
                  />
                </Box>
              </Box>

              {/* Email */}
              <Box display="flex" alignItems="center" gap={2}>
                <Typography variant="body2" sx={{ flex: "30%" }}>
                  {t("email-address")}
                </Typography>

                <Box sx={{ flex: "70%" }}>
                  <CeTextField
                    fullWidth
                    id="email"
                    name="email"
                    size="small"
                    placeholder={t("change-email")}
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    error={formik.touched.email && Boolean(formik.errors.email)}
                    helperText={formik.touched.email && formik.errors.email}
                    disabled={isLoading}
                    required
                  />
                </Box>
              </Box>

              {/* Change Password */}
              <Box display="flex" alignItems="center" gap={2}>
                <Typography variant="body2" sx={{ flex: "30%" }}>
                  {t("change-password")}
                </Typography>

                <Box sx={{ flex: "70%" }}>
                  <CeButton
                    variant="text"
                    onClick={() => {
                      if (formik.values.email) {
                        handleForgotPassword({ email: formik.values.email });
                      }
                    }}
                    disabled={isForgotPasswordLoading}
                  >
                    {t("send-password-link")}
                  </CeButton>
                </Box>
              </Box>
            </Box>
          </Box>

          {/* Preferences */}
          <Box sx={{ paddingY: 2, paddingX: 1.5 }}>
            <Box>
              <Typography
                variant="body1"
                color="text.disabled"
                fontWeight="bold"
                gutterBottom
              >
                {t("preferences")}
              </Typography>

              <Divider sx={{ marginBottom: 2, marginTop: 2 }} />

              <Box
                display="flex"
                flexDirection="column"
                gap={3}
                sx={{ paddingY: 1.5, paddingX: 3, width: "90%" }}
              >
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="body2" sx={{ flex: "30%" }}>
                    {t("default-language")}
                  </Typography>
                  <Box sx={{ flex: "70%" }}>
                    <LanguageSwitcher fullWidth />
                  </Box>
                </Box>

                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="body2" sx={{ flex: "30%" }}>
                    {t("theme")}
                  </Typography>
                  <FormControl fullWidth sx={{ flex: "70%" }}>
                    <CeSelect
                      value={theme}
                      onChange={handleThemeChange}
                      placeholder={t("select-theme")}
                      fullWidth
                      size="small"
                    >
                      <MenuItem value="light">{t("light-mode")}</MenuItem>
                      <MenuItem value="dark">{t("dark-mode")}</MenuItem>
                      <MenuItem value="system">{t("system-settings")}</MenuItem>
                    </CeSelect>
                  </FormControl>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Submit Button */}
      <Box
        sx={{
          paddingY: 1.5,
          paddingX: 2,
          width: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-end",
          position: "sticky",
          bottom: 0,
          pr: 4,
          zIndex: 10,
          gap: 2,
          borderTop: "1px solid",
          borderColor: (theme) => theme.palette.divider,
          backgroundColor: "background.paper",
        }}
      >
        <Grid
          item
          xs={12}
          sx={{ display: "flex", justifyContent: "flex-end", marginTop: 3 }}
        >
          <CeButton
            variant="text"
            onClick={() => formik.resetForm()}
            disabled={isLoading}
            sx={{ marginRight: 1 }}
          >
            {t("cancel")}
          </CeButton>
          <CeButton
            type="submit"
            variant="contained"
            disabled={isLoading}
            endIcon={
              <Tick02Icon size={20} color={"currentColor"} variant={"stroke"} />
            }
          >
            {t("common:save")}
          </CeButton>
        </Grid>
      </Box>
    </form>
  );
};
