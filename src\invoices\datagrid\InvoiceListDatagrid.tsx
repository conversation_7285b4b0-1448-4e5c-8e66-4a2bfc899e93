import { FC, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  BulkInvoiceLog,
  InvoiceStatus,
  InvoiceType,
  invoiceTypes
} from "src/common/types";
import {
  DataGrid,
  GridColDef,
  GridRenderCellParams,
  GridRowParams,
} from "@mui/x-data-grid";
import {
  Stack,
  Typography,
  Tooltip,
  InputLabel,
  FormControl,
  MenuItem,
  SelectChangeEvent,
  IconButton,
  Chip,
} from "@mui/material";
import { format } from "date-fns";
import { Download04Icon, Download05Icon } from "@hugeicons/react";
import {
  base64ToPdfBlobUrl,
  getInvoiceLogPdf,
  useBulkInvoiceLogPdf
} from "src/common/api/invoices";
import JSZip from "jszip";
import { LoadingButton } from "@mui/lab";
import { CeSelect, CeButton } from "src/common/components";
import { CeDataGridToolbar } from "src/common/components/custom/company/CeDataGridToolbar";
import { GridColumnTypography } from "src/common/components/custom/company/GridColumnTypography";
import { invoiceChipColors } from "src/common/components/custom/customCss";
import { GridStateSnapshot, UpdateGridStatePart } from "src/common/utils/gridState";

interface InvoiceListDatagridProps {
  data: BulkInvoiceLog[];
  isFetchingBulkInvoice: boolean;
  refetchBulkInvoice: () => void;
  shouldRenderRefreshButton: boolean;
  total: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
  gridState: GridStateSnapshot;
  updateGridStatePart: UpdateGridStatePart;
  selectedInvoiceType: InvoiceType;
  onInvoiceTypeChange: (newType: InvoiceType) => void;
}

export const InvoiceListDatagrid: FC<InvoiceListDatagridProps> = ({
  data,
  shouldRenderRefreshButton,
  isFetchingBulkInvoice,
  refetchBulkInvoice,
  total,
  onPageChange,
  onPageSizeChange,
  gridState,
  updateGridStatePart,
  selectedInvoiceType,
  onInvoiceTypeChange,
}) => {
  const { t } = useTranslation(["common"]);
  

  const [isDownloadMode, setIsDownloadMode] = useState(false);
  const [selectionBulkInvoices, setSelectionBulkInvoices] = useState<
    BulkInvoiceLog[]
  >([]);
  const [isLoadingBulkInvoices, setIsLoadingBulkInvoices] = useState(false);

  const handleInvoiceTypeChange = (event: SelectChangeEvent) => {
    const newType = event.target.value as InvoiceType;
    onInvoiceTypeChange(newType);
  };

  const handleDownloadIconClick = () => setIsDownloadMode(true);

  const handleCancelClick = () => {
    setSelectionBulkInvoices([]);
    setIsDownloadMode(false);
  };

  const handleDownloadsPdf = async () => {
    setIsLoadingBulkInvoices(true);
    try {
      const pdfPromises = selectionBulkInvoices.map((invoice) =>
        getInvoiceLogPdf({
          reservationId: invoice.reservationIds[0],
          invoiceNumber: invoice.invoiceNumber,
          invoiceType: selectedInvoiceType,
        }).then((pdf) => ({
          invoiceNumber: invoice.invoiceNumber,
          pdf: pdf.replace(/^data:application\/pdf;base64,/, ""),
        }))
      );

      const pdfResults = await Promise.all(pdfPromises);

      if (pdfResults.length === 1) {
        const pdfBlob = base64ToPdfBlobUrl(pdfResults[0].pdf);
        triggerDownload(pdfBlob as Blob, `${pdfResults[0].invoiceNumber}.pdf`);
      } else {
        const zip = new JSZip();
        pdfResults.forEach(({ invoiceNumber, pdf }) => {
          const pdfBlob = base64ToPdfBlobUrl(pdf);
          zip.file(`${invoiceNumber}.pdf`, pdfBlob);
        });

        const zipBlob = await zip.generateAsync({ type: "blob" });
        triggerDownload(zipBlob, "bulk-invoices.zip");
      }
    } catch (error) {
      console.error("Error generating PDFs", error);
    } finally {
      setIsLoadingBulkInvoices(false);
      handleCancelClick();
    }
  };

  const triggerDownload = (blob: Blob | MediaSource, fileName: string) => {
    const blobUrl = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = blobUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(blobUrl);
  };

  const renderEditActions = (params: GridRenderCellParams) => {
    const invoiceLog: BulkInvoiceLog = params.row;

    const handleDownload = async () => {
      try {
        const pdf = await getInvoiceLogPdf({
          reservationId: invoiceLog.reservationIds[0],
          invoiceNumber: invoiceLog.invoiceNumber,
          invoiceType: selectedInvoiceType,
        });
        
        const pdfBlob = base64ToPdfBlobUrl(pdf.replace(/^data:application\/pdf;base64,/, ""));
        triggerDownload(pdfBlob as Blob, `${invoiceLog.invoiceNumber}.pdf`);
      } catch (error) {
        console.error("Error downloading PDF", error);
      }
    };

    return (
      <IconButton
        aria-label="download invoice"
        disabled={invoiceLog.status !== InvoiceStatus.COMPLETED}
        size="small"
        color="primary"
        onClick={handleDownload}
      >
        <Download05Icon size={24} variant={"stroke"} color="currentColor" />
      </IconButton>
    );
  };

  const CustomToolbar = () => {
    return (
      <CeDataGridToolbar
        shouldRenderRefreshButton={shouldRenderRefreshButton}
        onRefreshButtonClick={refetchBulkInvoice}
      >
        <Stack sx={{ display: "flex", flexDirection: "row", gap: 1 }}>
          <FormControl variant="outlined" sx={{ minWidth: 200 }}>
            <InputLabel id="invoice-type-label">
              {t("common:invoices")}
            </InputLabel>
            <CeSelect
              labelId="invoice-type-label"
              value={selectedInvoiceType}
              onChange={handleInvoiceTypeChange}
              label={t("common:invoices")}
              sx={{ height: 36 }}
            >
              {invoiceTypes.map((type) => (
                <MenuItem
                  key={type}
                  value={type}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </MenuItem>
              ))}
            </CeSelect>
          </FormControl>

          <Tooltip title={t("common:download")}>
            <>
              <CeButton
                variant="contained"
                size="small"
                onClick={handleDownloadIconClick}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  minWidth: "22px",
                }}
                disabled={isDownloadMode}
              >
                <Download04Icon variant="solid" />
              </CeButton>
            </>
          </Tooltip>
          {isDownloadMode && (
            <>
              <LoadingButton
                variant="contained"
                size="small"
                loading={isLoadingBulkInvoices}
                loadingIndicator={t("common:downloading")}
                color={
                  selectionBulkInvoices.length === 0 || isLoadingBulkInvoices
                    ? "secondary"
                    : "primary"
                }
                onClick={handleDownloadsPdf}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  fontWeight: "bold",
                  px: 2,
                }}
                disabled={selectionBulkInvoices.length === 0}
              >
                <Typography sx={{ fontWeight: 600 }}>
                  {t("common:download")}
                </Typography>
              </LoadingButton>
              <CeButton
                variant="text"
                size="small"
                color="primary"
                onClick={handleCancelClick}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Typography sx={{ fontWeight: 600 }}>
                  {t("common:cancel")}
                </Typography>
              </CeButton>
            </>
          )}
        </Stack>
      </CeDataGridToolbar>
    );
  };
  const columns: GridColDef[] = [
    {
      field: "editActions",
      headerName: `${t("manager:edit")}`,
      sortable: false,
      renderCell: (params: GridRenderCellParams<string>) =>
        renderEditActions(params),
      filterable: false,
    },
    {
      field: "created_at",
      headerName: t("common:date-created"),
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => {
        const created_at = params.row.created_at;
        const formattedDate = format(new Date(created_at), "dd.MM.yyyy HH:mm");
        return <GridColumnTypography value={formattedDate} />;
      },
    },
    {
      field: "status",
      headerName: t("common:status"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 250,
      renderCell: (params) => {
        const status = params.row.status;
        const colorStyles = invoiceChipColors(status);
        return <Chip sx={{ ...colorStyles }} size="small" label={status} />;
      },
    },
    {
      field: "invoiceNumber",
      headerName: t("common:invoice-number"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 250,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.invoiceNumber} />
      ),
    },
    {
      field: "billTo",
      headerName: t("common:billed-to"),
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => {
        const companyName = params.row.dispatcherManager?.companyName || "-";
        return <GridColumnTypography value={companyName} />;
      },
    },
    {
      field: "total",
      headerName: t("common:amount"),
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => {
        const formattedSum =
          params.row.total != null ? `${params.row.total} €` : "-";
        return <GridColumnTypography value={formattedSum} />;
      },
    },
    {
      field: "reservationIds",
      headerName: t("common:num-reservations"),
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 200,
      renderCell: (params) => {
        const numReservations = params.row.reservationIds
          ? params.row.reservationIds.length
          : "-";
        return <GridColumnTypography value={numReservations} />;
      },
    },
    {
      field: "created_by",
      headerName: "Created By",
      type: "number",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.created_by} />
      ),
    },
    {
      field: "updated_at",
      headerName: "Updated At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.updated_at} />
      ),
    },
    {
      field: "deleted_at",
      headerName: "Deleted At",
      type: "string",
      headerAlign: "left",
      align: "left",
      width: 125,
      renderCell: (params) => (
        <GridColumnTypography value={params.row.deleted_at} />
      ),
    },
  ];

  return (
    <>
      <DataGrid
        sx={{
          border: "none",
          paddingTop: 0.5,
        }}
        pagination
        page={gridState.page - 1}
        pageSize={gridState.pageSize}
        rowsPerPageOptions={[20, 40, 60, 80, 100]}
        rowCount={total}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        columnVisibilityModel={gridState.columnVisibilityModel}
        onColumnVisibilityModelChange={(model) =>
          updateGridStatePart("columnVisibilityModel", model)
        }
        filterModel={gridState.filterModel}
        onFilterModelChange={(model) =>
          updateGridStatePart("filterModel", model)
        }
        sortModel={gridState.sortModel}
        onSortModelChange={(model) => updateGridStatePart("sortModel", model)}
        paginationMode="server"
        components={{ Toolbar: CustomToolbar }}
        columns={columns}
        rows={data}
        disableSelectionOnClick
      />
    </>
  );
};
