import { useEffect, useState } from 'react'
import {
  ArrowDown01Icon,
  Beach02Icon,
  Calendar03Icon,
  CheckmarkCircle02Icon,
  Delete01Icon,
  Edit02Icon
} from '@hugeicons/react'
import { Box, IconButton, Stack, Typography } from '@mui/material'
import React from 'react'
import TimeOffItem from './TimeOffItem'
import { CeButton } from 'src/common/components'
import { UnavailablePeriod, UnavailablePeriodFormValues, UnavailablePeriodStatus } from 'src/common/types'
import {
  parseISO,
  differenceInCalendarDays,
  isAfter,
  isBefore
} from 'date-fns'
import { formatDateRange, turnUnavailablePeriodIntoFormValues } from 'src/common/utils'
import { useRecoilState } from 'recoil'
import { unavailablePeriodDeleteValuesState, unavailablePeriodFormValuesState } from 'src/common/state/unavailablePeriod'
import { useCreateUnavailabilityVehicle, useCreateUnavailablePeriod, useDeleteUnavailablePeriod, useUpdateUnavailabilityVehicle, useUpdateUnavailablePeriod } from 'src/common/api'
import { UNAVAILABLE_PERIOD_DELETE_DEFAULT, UNAVAILABLE_PERIOD_FORM_VALUES } from 'src/common/constants'
import AssignTimeOffModal from './AssignTimeOffModal'
import { useTranslation } from 'react-i18next'
import { TimeOffModalDelete } from './TimeOffModalDelete'

interface TimeOffProps {
  unavailablePeriods?: UnavailablePeriod[]
  operatorId?: number;
  vehicleId?: number;
}

const TimeOff: React.FC<TimeOffProps> = ({ unavailablePeriods, operatorId, vehicleId }) => {
  const { t } = useTranslation("common");
  const now = new Date()
  const [unavailablePeriodFormValues, setUnavailablePeriodFormValues] = useRecoilState(
    unavailablePeriodFormValuesState
  );

  const [unavailablePeriodDeleteValues, setUnavailablePeriodDeleteValues] = useRecoilState(
    unavailablePeriodDeleteValuesState
  );

  const {
    mutate: handleCreateUnavailablePeriod,
    isSuccess: isCreateUnavailablePeriodSuccess,
    isLoading: isCreatingUnavailablePeriod,
  } = useCreateUnavailablePeriod();

  const {
    mutate: handleCreateUnavailabilityVehicle,
    isSuccess: isCreateUnavailabilityVehicleSuccess,
    isLoading: isCreatingUnavailabilityVehicle,
  } = useCreateUnavailabilityVehicle();

  const {
    mutate: handleDeleteUnavailablePeriod,
    isLoading: isDeletingUnavailablePeriod,
    isSuccess: isDeleteUnavailablePeriodSuccess,
  } = useDeleteUnavailablePeriod();

  const {
    mutate: handleUpdateUnavailablePeriod,
    isSuccess: isUpdateUnavailablePeriodSuccess,
    isLoading: isUpdatingUnavailablePeriod,
  } = useUpdateUnavailablePeriod();

  const {
    mutate: handleUpdateUnavailabilityVehicle,
    isSuccess: isUpdateUnavailabilityVehicleSuccess,
    isLoading: isUpdatingUnavailabilityVehicle,
  } = useUpdateUnavailabilityVehicle();

  const isLoading =
    isCreatingUnavailablePeriod ||
    isDeletingUnavailablePeriod ||
    isUpdatingUnavailablePeriod ||
    isCreatingUnavailabilityVehicle ||
    isUpdatingUnavailabilityVehicle;

  useEffect(() => {
    if (isCreateUnavailablePeriodSuccess || isCreateUnavailabilityVehicleSuccess) {
      setUnavailablePeriodFormValues(UNAVAILABLE_PERIOD_FORM_VALUES);
    }
  }, [
    isCreateUnavailablePeriodSuccess,
    isCreateUnavailabilityVehicleSuccess,
    setUnavailablePeriodFormValues,
  ]);

  useEffect(() => {
    if (isDeleteUnavailablePeriodSuccess) {
      setUnavailablePeriodDeleteValues(UNAVAILABLE_PERIOD_DELETE_DEFAULT);
    }
  }, [isDeleteUnavailablePeriodSuccess, setUnavailablePeriodDeleteValues]);

  useEffect(() => {
    if (isUpdateUnavailablePeriodSuccess || isUpdateUnavailabilityVehicleSuccess) {
      setUnavailablePeriodFormValues(UNAVAILABLE_PERIOD_FORM_VALUES);
    }
  }, [
    isUpdateUnavailablePeriodSuccess,
    isUpdateUnavailabilityVehicleSuccess,
    setUnavailablePeriodFormValues
  ]);

  const handleCloseUnavailablePeriod = () => {
    if (!isLoading) {
      setUnavailablePeriodFormValues(UNAVAILABLE_PERIOD_FORM_VALUES);
    }
  };

  const handleCloseUnavailablePeriodDelete = () => {
    if (!isLoading) {
      setUnavailablePeriodDeleteValues(UNAVAILABLE_PERIOD_DELETE_DEFAULT);
    }
  };

  const currentPeriods = unavailablePeriods?.filter(period => {
    const fromDate = parseISO(period.from)
    const toDate = parseISO(period.to)
    return fromDate <= now && toDate >= now
  })

  const upcomingPeriods = unavailablePeriods
    ?.filter(period => isAfter(parseISO(period.from), now))
    .sort((a, b) => parseISO(a.from).getTime() - parseISO(b.from).getTime())
    .slice(0, 2)

  const usedPeriods = unavailablePeriods?.filter(period =>
    isBefore(parseISO(period.to), now)
  )

  const [historyVisibleCount, setHistoryVisibleCount] = useState(4)

  const handleLoadMore = () => {
    setHistoryVisibleCount(prev => prev + 4)
  }

  const renderTimeOffItems = (
    periods: UnavailablePeriod[] | undefined,
    chipLabel: UnavailablePeriodStatus
  ) => {
    if (!periods || periods.length === 0) return null

    return periods.map(period => {
      const fromDate = parseISO(period.from)
      const toDate = parseISO(period.to)
      const days = differenceInCalendarDays(toDate, fromDate) + 1

      let chipIcon = null
      let chipSx = {}
      switch (chipLabel) {
        case 'current':
          chipIcon = <Beach02Icon size={22} variant="solid" color="#388E3C" />
          chipSx = { backgroundColor: '#E8F5E9', color: '#388E3C', textTransform:'lowercase' }
          break
        case 'upcoming':
          chipIcon = <Calendar03Icon size={22} variant="solid" color="#0e8ad2" />
          chipSx = { backgroundColor: '#E3F2FD', color: '#0e8ad2', textTransform:'lowercase' }
          break
        case 'used':
          chipIcon = (
            <CheckmarkCircle02Icon size={22} variant="solid" color="#7f57c2" />
          )
          chipSx = { backgroundColor: '#ede7f7', color: '#7f57c2', textTransform:'lowercase' }
          break
      }
      const dateRange = formatDateRange(fromDate, toDate)
      return (
        <Box key={period.id} style={{ display: 'flex', flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
          <TimeOffItem
            key={period.id}
            dateRange={dateRange}
            days={days}
            chipLabel={t(chipLabel)}
            chipIcon={chipIcon}
            chipColor="primary"
            chipSx={chipSx}
          />
          {(chipLabel === 'upcoming' || chipLabel === 'current') && (
            <Stack sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'center', alignItems: 'center', pl: 1, gap: 1 }}>
              <IconButton
                size="small"
                aria-label="update timeOff"
                onClick={() => {
                  const formValues: UnavailablePeriodFormValues = turnUnavailablePeriodIntoFormValues(
                    period,
                    operatorId ? "Update" : "UpdateVehicle"
                  );

                  setUnavailablePeriodFormValues(formValues);
                }}
              >
                <Edit02Icon size={18} variant={"stroke"} />
              </IconButton>
              {chipLabel === 'upcoming'  && (
              <IconButton
                size="small"
                color="error"
                aria-label="delete timeOff"
                onClick={(() =>
                  setUnavailablePeriodDeleteValues({
                    id: period.id,
                    timeOffTitle: dateRange,
                    flow: "Delete",
                  })
                )}
              >
                <Delete01Icon size={18} variant={"stroke"} />
              </IconButton>
              )}
            </Stack>
          )}
        </Box>
      )
    })
  }

  return (
    <>
      <Box sx={{ width: '100%', px: 2 }}>
        <Stack sx={{ display: 'flex', alignItems: 'center', m: 4 }}>
          <CeButton
            color="primary"
            variant="contained"
            onClick={() => {
              if (operatorId) {
                setUnavailablePeriodFormValues({
                  ...unavailablePeriodFormValues,
                  flow: "Create",
                  operatorId: operatorId!,
                });
              } else {
                setUnavailablePeriodFormValues({
                  ...unavailablePeriodFormValues,
                  flow: "CreateVehicle",
                  vehicleIds: [vehicleId!],
                });
              }
            }}
          >
          {operatorId ? t("assignTimeOff") : t("assign_unavailability")}
          </CeButton>
        </Stack>

        {currentPeriods && currentPeriods.length > 0 && (
          <>
            {renderTimeOffItems(currentPeriods, 'current')}
          </>
        )}

        <Typography
          variant="subtitle1"
          fontWeight={500}
          color="text.secondary"
          sx={{ marginBottom: '12px', marginTop: currentPeriods?.length ? '24px' : 0 }}
        >
          {t("upcoming")}
        </Typography>
        {upcomingPeriods && upcomingPeriods.length > 0 ? (
          renderTimeOffItems(upcomingPeriods, 'upcoming')
        ) : (
          <Typography variant="body2" color="text.secondary" sx={{ marginBottom: '12px' }}>
            {t("noUpcomingTimeOff")}
          </Typography>
        )}

        <Typography variant="subtitle1" fontWeight={500} color="text.secondary" sx={{ marginBottom: '12px' }}>
          {t("timeOffHistory")}
        </Typography>
        {usedPeriods && usedPeriods.length > 0 ? (
          <>
            {renderTimeOffItems(usedPeriods.slice(0, historyVisibleCount), 'used')}
            {usedPeriods.length > historyVisibleCount && (
              <CeButton
                color="primary"
                variant="text"
                onClick={handleLoadMore}
                endIcon={<ArrowDown01Icon size={20} />}
                sx={{ mb: 4 }}
              >
                {t("showMore")}
              </CeButton>
            )}
          </>
        ) : (
          <Typography variant="body2" color="text.secondary" sx={{ marginBottom: '12px' }}>
            {t("noTimeOffHistory")}
          </Typography>
        )}
      </Box>
      <AssignTimeOffModal
        initialFormValues={unavailablePeriodFormValues}
        isLoading={isCreatingUnavailablePeriod || isCreatingUnavailabilityVehicle}
        handleCloseUnavailablePeriod={handleCloseUnavailablePeriod}
        handleCreateUnavailablePeriod={handleCreateUnavailablePeriod}
        handleCreateUnavailabilityVehicle={handleCreateUnavailabilityVehicle}
        handleUpdateUnavailablePeriod={handleUpdateUnavailablePeriod}
        handleUpdateUnavailabilityVehicle={handleUpdateUnavailabilityVehicle}
      />

      <TimeOffModalDelete
        flow={unavailablePeriodDeleteValues.flow}
        isLoading={isLoading}
        timeOffTitle={unavailablePeriodDeleteValues.timeOffTitle}
        id={unavailablePeriodDeleteValues.id}
        handleCloseUnavailablePeriodModalDelete={handleCloseUnavailablePeriodDelete}
        handleDeleteUnavailablePeriod={handleDeleteUnavailablePeriod}
      />
    </>
  )
}

export default TimeOff
