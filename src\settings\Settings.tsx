import { Box, Divider, Tab, Tabs, Typography } from "@mui/material";
import { getCurrentUser } from "src/common/api";
import { SettingsUserProfile } from "./SettingsUserProfile";
import { CePaper } from "src/common/components";
import { useState } from "react";
import { SettingsCompanyProfile } from "./SettingsCompanyProfile";
import { useTranslation } from "react-i18next";
import { Role } from "src/common/types";
import { SettingsOperationalProfile } from "./SettingsOperationalProfile";
import { useWindowSize } from "@react-hook/window-size";

export const Settings = () => {
  const { t } = useTranslation(["common", "manager", "dispatcher"]);
  const currentUser = getCurrentUser();
  const userId = currentUser?.id;
  const [, height] = useWindowSize();

  const isManager =
    currentUser?.role === Role.DISPATCHER_MANAGER ||
    currentUser?.role === Role.OEPRATOR_MANAGER;

  const isNotManager =
    currentUser?.role === Role.DISPATCHER || currentUser?.role == Role.OPERATOR;

  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  return (
    <CePaper
      sx={{
        mb: 2,
        padding: 2,
        height: `${height - 100}px`,
        boxSizing: "border-box",
        overflow: "hidden",
      }}
    >
      {!isNotManager && (
        <Tabs
          value={selectedTab}
          onChange={handleTabChange}
          textColor="primary"
          indicatorColor="primary"
          aria-label="settings tabs"
          sx={{ marginBottom: 2 }}
        >
          <Tab
            label={t("profile")}
            sx={{ textTransform: "capitalize", fontWeight: "bold" }}
          />

          {isManager && (
            <Tab
              label={t("company")}
              sx={{ textTransform: "capitalize", fontWeight: "bold" }}
            />
          )}
          {isManager && (
            <Tab
              label={t("Operational")}
              sx={{ textTransform: "capitalize", fontWeight: "bold" }}
            />
          )}
        </Tabs>
      )}

      <Box>
        {selectedTab === 0 && <SettingsUserProfile userId={userId} />}
        {isManager && selectedTab === 1 && (
          <SettingsCompanyProfile userId={userId} />
        )}
        {isManager && selectedTab === 2 && (
          <SettingsOperationalProfile userId={userId} />
        )}
      </Box>
    </CePaper>
  );
};
