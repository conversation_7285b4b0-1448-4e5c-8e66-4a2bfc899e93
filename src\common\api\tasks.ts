import axios, { AxiosError } from "axios";
import {
  CreateTaskCommentDto,
  CreateTaskDto,
  DeleteTaskDto,
  GetTaskDto,
  Task,
  TaskActivity,
  TaskComment,
  TaskWithCount,
  UpdateTaskDto,
} from "../types/tasks";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { processApiError } from "../utils/errors";
import { wrapEachFieldIntoDoubleQuotes } from "../utils";

const backendUrl = process.env.REACT_APP_API_URL;

export const getTask = async (taskId: number) => {
  if (!taskId) {
    throw new Error("the task ID was not provided");
  }
  return axios
    .get(`${backendUrl}/tasks/${taskId}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};
export const useTask = (taskId: number, enabled: boolean = true) => {
  return useQuery<Task, AxiosError | Error>(
    ["task", taskId],
    () => getTask(taskId),
    {
      onError: (err) => processApiError("Unable to fetch task", err),
      enabled,
    }
  );
};

export const getTasks = async (queryParams: GetTaskDto) => {
  return axios
    .post(`${backendUrl}/tasks/get`, queryParams, { withCredentials: true })
    .then((response) => response.data);
};

export const useTasks = (queryParams: GetTaskDto, enabled: boolean = true) => {
  const formattedSortModel = wrapEachFieldIntoDoubleQuotes(
    queryParams.sortModel
  );
  const formattedAttrs = { ...queryParams, sortModel: formattedSortModel };
  return useQuery<TaskWithCount, AxiosError | Error>(
    ["tasks", formattedAttrs],
    () => getTasks(formattedAttrs),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch tasks", err),
      enabled,
    }
  );
};

export const createTask = async (taskData: CreateTaskDto) => {
  return axios
    .post(`${backendUrl}/tasks`, taskData, { withCredentials: true })
    .then((response) => response.data);
};

export const useCreateTask = () => {
  const queryClient = useQueryClient();

  return useMutation<Task, AxiosError | Error, CreateTaskDto, () => void>(
    (a: CreateTaskDto) => createTask(a),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("task");
        queryClient.invalidateQueries("tasks");
      },
      onError: (err) => processApiError("Unable to create task", err),
    }
  );
};

export const updateTask = (updateTaskArgs: UpdateTaskDto) => {
  const { taskId, ...task } = updateTaskArgs;
  if (!taskId) {
    throw new Error("the task ID was not provided");
  }
  return axios
    .patch(`${backendUrl}/tasks/${taskId}`, task, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useUpdateTask = () => {
  const queryClient = useQueryClient();
  return useMutation<Task, AxiosError | Error, UpdateTaskDto, () => void>(
    (updateTaskArgs: UpdateTaskDto) => updateTask(updateTaskArgs),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("task");
        queryClient.invalidateQueries("tasks");
      },
      onError: (err) => {
        processApiError("Unable to update task", err);
      },
    }
  );
};

export const deleteTask = (deleteTaskArgs: DeleteTaskDto) => {
  const { taskId } = deleteTaskArgs;
  if (!taskId) {
    throw new Error("the task ID was not provided");
  }
  return axios
    .delete(`${backendUrl}/tasks/${taskId}`, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useDeleteTask = () => {
  const queryClient = useQueryClient();
  return useMutation<Task, AxiosError | Error, DeleteTaskDto, () => void>(
    (DeleteTaskDto: DeleteTaskDto) => deleteTask(DeleteTaskDto),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("task");
        queryClient.invalidateQueries("tasks");
      },
      onError: (err) => processApiError("Unable to delete task", err),
    }
  );
};

export const getTaskComments = async (taskId: number) => {
  return axios
    .get(`${backendUrl}/tasks/${taskId}/comments`, { withCredentials: true })
    .then((response) => response.data);
};

export const useTaskComments = (taskId: number, enabled: boolean = true) => {
  return useQuery<TaskComment[], AxiosError | Error>(
    ["taskComments", taskId],
    () => getTaskComments(taskId),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch task comments", err),
      enabled,
    }
  );
};

export const createTaskComment = async (commentData: CreateTaskCommentDto) => {
  return axios
    .post(`${backendUrl}/tasks/${commentData.taskId}/comment`, commentData, {
      withCredentials: true,
    })
    .then((response) => response.data);
};

export const useCreateTaskComment = () => {
  const queryClient = useQueryClient();

  return useMutation<
    TaskComment,
    AxiosError | Error,
    CreateTaskCommentDto,
    () => void
  >((commentData: CreateTaskCommentDto) => createTaskComment(commentData), {
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries(["taskComments", variables.taskId]);
    },
    onError: (err) => processApiError("Unable to create task comment", err),
  });
};

export const getTaskActivities = async (taskId: number) => {
  return axios
    .get(`${backendUrl}/tasks/${taskId}/activities`, { withCredentials: true })
    .then((response) => response.data);
};

export const useTaskActivities = (taskId: number, enabled: boolean = true) => {
  return useQuery<TaskActivity[], AxiosError | Error>(
    ["taskActivities", taskId],
    () => getTaskActivities(taskId),
    {
      keepPreviousData: true,
      onError: (err) => processApiError("Unable to fetch task activities", err),
      enabled,
    }
  );
};
